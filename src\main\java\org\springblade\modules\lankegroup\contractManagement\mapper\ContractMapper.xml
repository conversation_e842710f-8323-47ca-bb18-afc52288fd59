<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.Contract">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="kd_fid" property="kdFid"/>
        <result column="kd_code" property="kdCode"/>
        <result column="project_id" property="projectId"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_type_id" property="projectTypeId"/>
        <result column="project_type_name" property="projectTypeName"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="contract_amount" property="contractAmount"/>
        <result column="party_b_unit_id" property="partyBUnitId"/>
        <result column="party_b_unit_code" property="partyBUnitCode"/>
        <result column="party_b_unit_name" property="partyBUnitName"/>
        <result column="use_chapter_type_ids" property="useChapterTypeIds"/>
        <result column="use_chapter_type_vals" property="useChapterTypeVals"/>
        <result column="num_of_copies" property="numOfCopies"/>
        <result column="annex" property="annex"/>
        <result column="annex_name" property="annexName"/>
        <result column="remarks" property="remarks"/>
        <result column="archive_date" property="archiveDate"/>
        <result column="sign_company_id" property="signCompanyId"/>
        <result column="sign_company_code" property="signCompanyCode"/>
        <result column="sign_company_name" property="signCompanyName"/>
        <result column="sign_company_man_id" property="signCompanyManId"/>
        <result column="sign_company_man_name" property="signCompanyManName"/>
        <result column="sign_company_tal" property="signCompanyTal"/>
        <result column="sale_man_id" property="saleManId"/>
        <result column="sale_man_name" property="saleManName"/>
        <result column="sale_dept_id" property="saleDeptId"/>
        <result column="sale_dept_name" property="saleDeptName"/>
        <result column="contract_category_id" property="contractCategoryId"/>
        <result column="contract_category_name" property="contractCategoryName"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_bank_code" property="supplierBankCode"/>
        <result column="supplier_bank_name" property="supplierBankName"/>
        <result column="pay_way" property="payWay"/>
        <result column="contract_type" property="contractType"/>
        <result column="process_definition_id" property="processDefinitionId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="task_user" property="taskUser"/>
        <result column="signing_date" property="signingDate"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="signing_mode" property="signingMode"/>
        <result column="is_expenses" property="isExpenses"/>
        <result column="operate_fee" property="operateFee"/>
<!--        <result column="pur_head_dept_id" property="purHeadDeptId"/>-->
    </resultMap>
    <resultMap id="contractVOResultMap" type="org.springblade.modules.lankegroup.contractManagement.vo.ContractVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="kd_fid" property="kdFid"/>
        <result column="kd_code" property="kdCode"/>
        <result column="project_id" property="projectId"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_type_id" property="projectTypeId"/>
        <result column="project_type_name" property="projectTypeName"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="contract_amount" property="contractAmount"/>
        <result column="party_b_unit_id" property="partyBUnitId"/>
        <result column="party_b_unit_code" property="partyBUnitCode"/>
        <result column="party_b_unit_name" property="partyBUnitName"/>
        <result column="use_chapter_type_ids" property="useChapterTypeIds"/>
        <result column="use_chapter_type_vals" property="useChapterTypeVals"/>
        <result column="num_of_copies" property="numOfCopies"/>
        <result column="annex" property="annex"/>
        <result column="annex_name" property="annexName"/>
        <result column="remarks" property="remarks"/>
        <result column="archive_date" property="archiveDate"/>
        <result column="sign_company_id" property="signCompanyId"/>
        <result column="sign_company_code" property="signCompanyCode"/>
        <result column="sign_company_name" property="signCompanyName"/>
        <result column="sign_company_man_id" property="signCompanyManId"/>
        <result column="sign_company_man_name" property="signCompanyManName"/>
        <result column="sign_company_tal" property="signCompanyTal"/>
        <result column="sale_man_id" property="saleManId"/>
        <result column="sale_man_name" property="saleManName"/>
        <result column="sale_dept_id" property="saleDeptId"/>
        <result column="sale_dept_name" property="saleDeptName"/>
        <result column="contract_category_id" property="contractCategoryId"/>
        <result column="contract_category_name" property="contractCategoryName"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_bank_code" property="supplierBankCode"/>
        <result column="supplier_bank_name" property="supplierBankName"/>
        <result column="pay_way" property="payWay"/>
        <result column="contract_type" property="contractType"/>
        <result column="process_definition_id" property="processDefinitionId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="task_user" property="taskUser"/>
        <result column="signing_date" property="signingDate"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="signing_mode" property="signingMode"/>
<!--        <result column="pur_head_dept_id" property="purHeadDeptId"/>-->
        <result column="createUserName" property="createUserName"/>
    </resultMap>
    <resultMap id="contractListResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult">
        <result column="id" property="id"/>
        <result column="contractName" property="contractName"/>
        <result column="contractStatus" property="contractStatus"/>
        <result column="contractType" property="contractType"/>
        <result column="signedName" property="signedName"/>
        <result column="create_time" property="create_time"/>
        <result column="update_time" property="update_time"/>
        <result column="amount" property="amount"/>
        <result column="sponsor" property="sponsor"/>
        <result column="cid" property="cid"/>
        <result column="taskId" property="taskId"/>
    </resultMap>


    <select id="selectContractPage"  parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam" resultMap="contractListResultMap">
        <!--select * from blade_contract where is_deleted = 0-->
        SELECT DISTINCT
        socpe.*
        FROM
        (
        SELECT DISTINCT
        c.id as cid,
        c.id as id,
        c.NAME as contractName,
        case  c.STATUS
        when -1 then 0
        else c.STATUS end  contractStatus,
        c.contract_type as contractType,
        IF(c.sign_company_name is not null and  c.sign_company_name != '',c.sign_company_name,c.supplier_name) AS signedName,
        DATE_FORMAT(c.create_time, "%Y-%m-%d %H:%i:%s") as create_time,
        DATE_FORMAT(c.update_time, "%Y-%m-%d %H:%i:%s") as update_time,
        c.contract_amount as amount,
        u.name as sponsor
        FROM
        blade_contract as c
        left join ACT_HI_TASKINST as t on t.proc_inst_id_=c.process_instance_id
        left join blade_historic_process process on process.pid = c.id
        left join blade_project_basic as b on b.id=c.project_id
        left join blade_project_group_disposition as g on g.kingdee_id=b.group_id
        left join blade_user as u on u.id=c.create_user
        LEFT JOIN blade_dept d ON d.id = u.dept_id
        WHERE
        c.is_deleted = 0
        and c.status &lt;&gt; 6
        <if test="param.projectId!=null and param.projectId!=''">
            and b.id=#{param.projectId}
        </if>
        <!-- 	合同类型-->
        <if test="param.contractType!=null and param.contractType!=''">
            and c.contract_type=#{param.contractType}
        </if>
        <!-- 	审批状态-->
        <if test="param.contractStatus!=null and param.contractStatus!=''">
            <choose>
                <when test="param.contractStatus=='3'.toString()">
                    and (c.status=3 or c.status=8)
                </when>
                <when test="param.contractStatus=='2'.toString()">
                    and (c.status=2 or c.status=7)
                </when>
                <when test="param.contractStatus=='0'.toString()">
                    and (c.status=0 or c.status=-1)
                </when>
                <otherwise>
                    and c.status=#{param.contractStatus}
                </otherwise>
            </choose>
        </if>
        <!-- 	项目类型-->
        <if test="param.projectType!=null and param.projectType!=''">
            and (g.kingdee_id=#{param.projectType} or g.parent_id=#{param.projectType})
        </if>
        <!-- 合同时间-->
        <if test="param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            <choose>
                <when test="param.startTime==param.endTime">
                    and convert(c.create_time,DATETIME) like concat(#{param.startTime},'%')
                </when>
                <otherwise>
                    and c.create_time BETWEEN concat(#{param.startTime},' 00:00:00') and concat(#{param.endTime},' 23:59:59')
                </otherwise>
            </choose>
        </if>
        <if test="(param.startTime!=null and param.startTime!='') and (param.endTime==null or param.endTime=='')">
            and c.create_time <![CDATA[>=]]> concat(#{param.startTime},' 00:00:00')
        </if>
        <if test="(param.endTime!=null and param.endTime!='') and (param.startTime==null or param.startTime=='')">
            and c.create_time <![CDATA[<=]]> concat(#{param.endTime},' 23:59:59')
        </if>

        <!-- 人员/部门-->
        <if test="param.deptOrPerson!=null and param.deptOrPerson!=''">
            AND (u.id=#{param.deptOrPerson}
            or u.dept_id LIKE concat( '%', #{param.deptOrPerson}, '%' )
            OR d.parent_id IN (
            WITH recursive temp AS (
            SELECT
            t.*
            FROM
            blade_dept t
            WHERE
            id = #{param.deptOrPerson} UNION ALL
            SELECT
            t.*
            FROM
            blade_dept t
            INNER JOIN temp ON t.parent_id = temp.id
            ) SELECT
            id
            FROM
            temp
            ))
        </if>

        <!-- 	待审批（状态为待审批0，且发起人不是登录人）-->
        <if test="param.pageType == '1'.toString() and param.userId!=null and param.userId!=''">
            AND (
            t.end_time_ IS NULL
            AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
            AND t.task_def_key_ &lt;&gt; 'userTask'
            )
            and (c.status=0 or c.status=-1 OR c.task_user != #{param.userId})
        </if>

        <!-- 我发起的（发起人是我本人的所有流程）-->
        <if test="param.pageType == '2'.toString() and param.userId!=null and param.userId!=''">
            and c.create_user=#{param.userId}
        </if>

        <!-- 已审批（状态不区分，仅展示本人已处理的所有流程，自己发起的不在其中）-->
        <if test="param.pageType == '3'.toString() and param.userId!=null and param.userId!=''">
            and (t.end_time_ is not null
            and t.assignee_ like concat('%',#{param.userId},'%')
            and t.task_def_key_ &lt;&gt; 'userTask')
        </if>

        <!-- 我收到的（待审批+已审批）-->
        <if test="param.pageType == '4'.toString() and param.userId!=null and param.userId!=''">
            and ((t.end_time_ is not null and t.assignee_ like concat('%',#{param.userId},'%') and t.task_def_key_ &lt;&gt;
            'userTask')
            or (find_in_set(#{param.userId},process.process_cc_delegate) and process.is_deleted = 0)
            or ((c.status=0 or c.task_user=#{param.userId})
            AND (
            t.end_time_ IS NULL
            AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
            AND t.task_def_key_ &lt;&gt; 'userTask'
            ))

            or(
            (#{param.userId}='10626332229065259' or #{param.userId}='107674011542948409' or
            #{param.userId}='115431515452113081')
            and (c.status=1 and (c.contract_type &lt;&gt; 1) and (c.party_b_unit_code &lt;&gt; '101'))
            )
            or(
            (#{param.userId}='10626332229065259' or #{param.userId}='14358232932874228' or
            #{param.userId}='115431515452113081')
            and (c.status=1 and (c.contract_type &lt;&gt; 1) and c.party_b_unit_code = '101')
            )
            or(
            (#{param.userId}='10626332229065259' or #{param.userId}='107674011542948409')
            and (c.status=5 and (c.contract_type = 1) and (c.party_b_unit_code &lt;&gt; '101'))
            )
            or(
            (#{param.userId}='10626332229065259' or #{param.userId}='14358232932874228')
            and (c.status=5 and (c.contract_type = 1) and c.party_b_unit_code = '101')
            )
            <if test="param.carbonCopy!=null and param.carbonCopy=='1'.toString()">
                or(
                u.id in
                (WITH recursive temp AS (
                SELECT
                t.*
                FROM
                blade_user t
                WHERE
                id =#{param.userId}  UNION ALL
                SELECT
                t.*
                FROM
                blade_user t
                INNER JOIN temp ON t.parent_id = temp.id
                ) SELECT
                id
                FROM
                temp)  and (c.status=1 and (c.contract_type  &lt;&gt; 1))
                )
                or(
                u.id in
                (WITH recursive temp AS (
                SELECT
                t.*
                FROM
                blade_user t
                WHERE
                id =#{param.userId}  UNION ALL
                SELECT
                t.*
                FROM
                blade_user t
                INNER JOIN temp ON t.parent_id = temp.id
                ) SELECT
                id
                FROM
                temp)  and (c.status=5 and (c.contract_type  = 1))
                )
            </if>

            )
        </if>
        <if test="param.searchBox!=null and param.searchBox!=''">
            and c.name like concat('%',#{param.searchBox},'%')
        </if>
        UNION
        SELECT DISTINCT
        c.id as cid,
        c.id AS id,
        c.NAME AS contractName,
        case  c.STATUS
        when -1 then 0
        else c.STATUS end  contractStatus,
        c.contract_type AS contractType,
        IF(c.sign_company_name is not null and  c.sign_company_name != '',c.sign_company_name,c.supplier_name) AS signedName,
        DATE_FORMAT(c.create_time, "%Y-%m-%d %H:%i:%s") as create_time,
        DATE_FORMAT(c.update_time, "%Y-%m-%d %H:%i:%s") as update_time,
        c.contract_amount AS amount,
        u.NAME AS sponsor
        FROM
        blade_archive_details AS ad
        LEFT JOIN blade_contract AS c ON c.id = ad.contract_id
        LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = ad.process_instance_id
        LEFT JOIN blade_project_basic b ON b.id = c.project_id
        LEFT JOIN blade_project_group_disposition g ON g.kingdee_id = b.group_id
        LEFT JOIN blade_user u ON u.id = c.create_user
        LEFT JOIN blade_dept d ON d.id = u.dept_id
        WHERE
        ad.is_deleted = 0
        AND c.is_deleted = 0
        and c.status &lt;&gt; 6
        <if test="param.projectId!=null and param.projectId!=''">
            and b.id=#{param.projectId}
        </if>
        <!-- 	合同类型-->
        <if test="param.contractType!=null and param.contractType!=''">
            and c.contract_type=#{param.contractType}
        </if>
        <!-- 	审批状态-->
        <if test="param.contractStatus!=null and param.contractStatus!=''">
            <choose>
                <when test="param.contractStatus=='3'.toString()">
                    and (c.status=3 or c.status=8)
                </when>
                <when test="param.contractStatus=='2'.toString()">
                    and (c.status=2 or c.status=7)
                </when>
                <when test="param.contractStatus=='0'.toString()">
                    and (c.status=0 or c.status=-1)
                </when>
                <otherwise>
                    and c.status=#{param.contractStatus}
                </otherwise>
            </choose>
        </if>
        <!-- 	项目类型-->
        <if test="param.projectType!=null and param.projectType!=''">
            and (g.parent_id=#{param.projectType} or g.kingdee_id=#{param.projectType})
        </if>
        <!-- 合同时间-->
        <if test="param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            <choose>
                <when test="param.startTime==param.endTime">
                    and convert(c.create_time,DATETIME) like concat(#{param.startTime},'%')
                </when>
                <otherwise>
                    and c.create_time BETWEEN #{param.startTime} and #{param.endTime}
                </otherwise>
            </choose>
        </if>
        <if test="(param.startTime!=null and param.startTime!='') and (param.endTime==null or param.endTime=='')">
            and c.create_time &gt; #{param.startTime}
        </if>
        <if test="(param.endTime!=null and param.endTime!='') and (param.startTime==null or param.startTime=='')">
            and c.create_time &lt; #{param.endTime}
        </if>

        <!-- 人员/部门-->
        <if test="param.deptOrPerson!=null and param.deptOrPerson!=''">
            AND (u.id=#{param.deptOrPerson}
            or u.dept_id LIKE concat( '%', #{param.deptOrPerson}, '%' )
            OR d.parent_id IN (
            WITH recursive temp AS (
            SELECT
            t.*
            FROM
            blade_dept t
            WHERE
            id = #{param.deptOrPerson} UNION ALL
            SELECT
            t.*
            FROM
            blade_dept t
            INNER JOIN temp ON t.parent_id = temp.id
            ) SELECT
            id
            FROM
            temp
            ))
        </if>
        <if test="param.searchBox!=null and param.searchBox!=''">
            and c.name like concat('%',#{param.searchBox},'%')
        </if>

        <!-- 	待审批（状态为待审批0，且发起人不是登录人）-->
        <if test="param.pageType == '1'.toString() and param.userId!=null and param.userId!=''">
            AND (
            t.end_time_ IS NULL
            AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
            AND t.task_def_key_ &lt;&gt; 'userTask'
            )
            and (c.status=4 or c.task_user=#{param.userId})
        </if>

        <!-- 我发起的（发起人是我本人的所有流程）-->
        <if test="param.pageType == '2'.toString() and param.userId!=null and param.userId!=''">
            and ad.create_user=#{param.userId}
        </if>

        <!-- 已审批（状态不区分，仅展示本人已处理的所有流程，自己发起的不在其中）-->
        <if test="param.pageType == '3'.toString() and param.userId!=null and param.userId!=''">
            and (t.end_time_ is not null
            and t.assignee_ like concat('%',#{param.userId},'%')
            and t.task_def_key_ &lt;&gt; 'userTask')
        </if>

        <!-- 我收到的（待审批+已审批）-->
        <if test="param.pageType == '4'.toString() and param.userId!=null and param.userId!=''">
            and ((t.end_time_ is not null and t.assignee_ like concat('%',#{param.userId},'%') and t.task_def_key_ &lt;&gt;
            'userTask')
            or ((c.status=4  or c.task_user=#{param.userId})
            AND (
            t.end_time_ IS NULL
            AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
            AND t.task_def_key_ &lt;&gt; 'userTask'
            ))
            )
        </if>

        <if test="param.contractStatus!='9'.toString()">
            UNION
            SELECT DISTINCT
            c.id as cid,
            cc.id AS id,
            cc.NAME AS contractName,
            case  cc.STATUS
            when 1 then 10
            when -1 then 0
            else cc.STATUS end  contractStatus,
            '3' AS contractType,
            c.name AS signedName,
            DATE_FORMAT(cc.create_time, "%Y-%m-%d %H:%i:%s") as create_time,
            DATE_FORMAT(cc.update_time, "%Y-%m-%d %H:%i:%s") as update_time,
            IFNULL(cc.adjusted_amount,null) AS amount,
            u.NAME AS sponsor
            FROM
            blade_contract_change cc
            LEFT JOIN blade_contract c ON c.id = cc.contract_id
            LEFT JOIN ACT_HI_TASKINST t ON t.proc_inst_id_ = cc.process_instance_id
            LEFT JOIN blade_project_basic b ON b.id = c.project_id
            LEFT JOIN blade_project_group_disposition g ON g.kingdee_id = b.group_id
            LEFT JOIN blade_user u ON u.id = cc.create_user
            LEFT JOIN blade_dept d ON d.id = u.dept_id
            WHERE
            cc.is_deleted = 0
            AND c.is_deleted = 0
            AND cc.STATUS &lt;&gt; 6
            <if test="param.projectId!=null and param.projectId!=''">
                and b.id=#{param.projectId}
            </if>
            <!-- 	合同类型-->
            <if test="param.contractType!=null and param.contractType!=''">
                and c.contract_type=#{param.contractType}
            </if>
            <!-- 	审批状态-->
            <if test="param.contractStatus!=null and param.contractStatus!=''">
                <choose>
                    <when test="param.contractStatus=='3'.toString()">
                        and (c.status=3 or c.status=8)
                    </when>
                    <when test="param.contractStatus=='2'.toString()">
                        and (c.status=2 or c.status=7)
                    </when>
                    <when test="param.contractStatus=='0'.toString()">
                        and (c.status=0 or c.status=-1)
                    </when>
                    <otherwise>
                        and c.status=#{param.contractStatus}
                    </otherwise>
                </choose>
            </if>
            <!-- 	项目类型-->
            <if test="param.projectType!=null and param.projectType!=''">
                and (g.parent_id=#{param.projectType} or g.kingdee_id=#{param.projectType})
            </if>
            <!-- 合同时间-->
            <if test="param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
                <choose>
                    <when test="param.startTime==param.endTime">
                        and convert(c.create_time,DATETIME) like concat(#{param.startTime},'%')
                    </when>
                    <otherwise>
                        and c.create_time BETWEEN #{param.startTime} and #{param.endTime}
                    </otherwise>
                </choose>
            </if>
            <if test="(param.startTime!=null and param.startTime!='') and (param.endTime==null or param.endTime=='')">
                and c.create_time &gt; #{param.startTime}
            </if>
            <if test="(param.endTime!=null and param.endTime!='') and (param.startTime==null or param.startTime=='')">
                and c.create_time &lt; #{param.endTime}
            </if>

            <!-- 人员/部门-->
            <if test="param.deptOrPerson!=null and param.deptOrPerson!=''">
                AND (u.id=#{param.deptOrPerson}
                or u.dept_id LIKE concat( '%', #{param.deptOrPerson}, '%' )
                OR d.parent_id IN (
                WITH recursive temp AS (
                SELECT
                t.*
                FROM
                blade_dept t
                WHERE
                id = #{param.deptOrPerson} UNION ALL
                SELECT
                t.*
                FROM
                blade_dept t
                INNER JOIN temp ON t.parent_id = temp.id
                ) SELECT
                id
                FROM
                temp
                ))
            </if>
            <if test="param.searchBox!=null and param.searchBox!=''">
                <!--and (c.name like concat('%',#{param.searchBox},'%') or cc.name like concat('%',#{param.searchBox},'%'))-->
                and  cc.name like concat('%',#{param.searchBox},'%')
            </if>

            <!-- 	待审批（状态为待审批0，且发起人不是登录人）-->
            <if test="param.pageType == '1'.toString() and param.userId!=null and param.userId!=''">
                AND (
                t.end_time_ IS NULL
                AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
                AND t.task_def_key_ &lt;&gt; 'userTask'
                )
                and cc.status in (0,-1)
                and  cc.task_user like concat('%',#{param.userId},'%')
            </if>

            <!-- 我发起的（发起人是我本人的所有流程）-->
            <if test="param.pageType == '2'.toString() and param.userId!=null and param.userId!=''">
                and cc.create_user=#{param.userId}
            </if>

            <!-- 已审批（状态不区分，仅展示本人已处理的所有流程，自己发起的不在其中）-->
            <if test="param.pageType == '3'.toString() and param.userId!=null and param.userId!=''">
                and (t.end_time_ is not null
                and t.assignee_ like concat('%',#{param.userId},'%')
                and t.task_def_key_ &lt;&gt; 'userTask')
            </if>

            <!-- 我收到的（待审批+已审批）-->
            <if test="param.pageType == '4'.toString() and param.userId!=null and param.userId!=''">
                and ((t.end_time_ is not null and t.assignee_ like concat('%',#{param.userId},'%') and t.task_def_key_ &lt;&gt;
                'userTask')
                or (cc.task_user=#{param.userId})
                AND (
                t.end_time_ IS NULL
                AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
                AND t.task_def_key_ &lt;&gt; 'userTask'
                )
                or(
                (#{param.userId}='10626332229065259' or #{param.userId}='107674011542948409' or #{param.userId}='115431515452113081')
                and (c.status=1 and (c.contract_type  &lt;&gt; 1) and (c.party_b_unit_code &lt;&gt; '101'))
                )
                or(
                (#{param.userId}='10626332229065259' or #{param.userId}='14358232932874228' or #{param.userId}='115431515452113081')
                and (c.status=1 and (c.contract_type  &lt;&gt; 1) and c.party_b_unit_code = '101')
                )
                or(
                (#{param.userId}='10626332229065259' or #{param.userId}='107674011542948409')
                and (c.status=5 and (c.contract_type  = 1) and (c.party_b_unit_code &lt;&gt; '101'))
                )
                or(
                (#{param.userId}='10626332229065259' or #{param.userId}='14358232932874228')
                and (c.status=5 and (c.contract_type  = 1) and c.party_b_unit_code = '101')
                )
                <if test="param.carbonCopy!=null and param.carbonCopy=='1'.toString()">
                    or(
                    u.id in
                    (WITH recursive temp AS (
                    SELECT
                    t.*
                    FROM
                    blade_user t
                    WHERE
                    id =#{param.userId}  UNION ALL
                    SELECT
                    t.*
                    FROM
                    blade_user t
                    INNER JOIN temp ON t.parent_id = temp.id
                    ) SELECT
                    id
                    FROM
                    temp)  and (c.status=1 and (c.contract_type  &lt;&gt; 1))
                    )
                    or(
                    u.id in
                    (WITH recursive temp AS (
                    SELECT
                    t.*
                    FROM
                    blade_user t
                    WHERE
                    id =#{param.userId}  UNION ALL
                    SELECT
                    t.*
                    FROM
                    blade_user t
                    INNER JOIN temp ON t.parent_id = temp.id
                    ) SELECT
                    id
                    FROM
                    temp)  and (c.status=5 and (c.contract_type  = 1))
                    )
                </if>
                )
            </if>
        </if>
        )socpe
        <choose>
            <!-- 我发起的（发起人是我本人的所有流程）-->
            <when test="param.pageType == '2'.toString() and param.userId!=null and param.userId!=''">
                ORDER BY
                case when socpe.contractStatus=1 or  socpe.contractStatus=8 or socpe.contractStatus=3 then socpe.update_time  end desc,
                socpe.create_time DESC
            </when>
            <otherwise>
                ORDER BY
                socpe.create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectTaskIdByBasic" resultType="java.lang.String">
        select
        a.ID_
        from
        blade_contract as c
        left join ACT_RU_TASK as a on a.PROC_INST_ID_=c.process_instance_id
        where
        1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
    </select>
    <select id="selectTaskIdByBasicAndUser" resultType="java.lang.String">
        select
        a.ID_
        from
        blade_contract as c
        left join ACT_RU_TASK as a on a.PROC_INST_ID_=c.process_instance_id
        where
        1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
        <if test="assignee!=null and assignee!=''">
            and a.ASSIGNEE_ = #{assignee}
        </if>
    </select>
    <select id="selectByProcessInstanceId" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        c.task_user
        from
        blade_contract as c
        left join ACT_RU_TASK as a on c.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>
    <select id="selectByProcessInstanceIdString" parameterType="java.lang.String" resultType="java.lang.String">
        select
        c.task_user
        from
        blade_contract as c
        left join ACT_RU_TASK as a on c.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
        limit 1
    </select>
    <select id="selectDetail" parameterType="java.util.Map" resultMap="contractVOResultMap">
        select c.*, u.name as createUserName
        from
        blade_contract  c
        left join blade_user as u on u.id=c.create_user
        where
        c.is_deleted = 0
         <if test="id!=null and id!=''">
        and c.id=#{id}
    </if>
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
    </select>
    <select id="selectDraft" parameterType="java.util.Map" resultMap="contractVOResultMap">
        select c.*, u.name as createUserName
        from
        blade_contract  c
        left join blade_user as u on u.id=c.create_user
        where
        c.is_deleted = 0
        and c.status=6

        AND c.contract_type = #{type}

        and c.create_user=#{userId}

        ORDER BY
        c.update_time  desc
        limit 1

    </select>
    <select id="contractFilter" parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam" resultType="java.util.Map">
        SELECT DISTINCT
            c.id as id,
            c.NAME as contractName,
            c.STATUS as contractStatus,
            c.contract_type as contractType,
            IFNULL(c.supplier_name,c.sign_company_name) as signedName,
            c.create_time,
            c.contract_amount as amount,
            u.name as sponsor
        FROM
            blade_contract as c
                left join ACT_HI_TASKINST as t on t.proc_inst_id_=c.process_instance_id
                left join blade_project_basic as b on b.id=c.project_id
                left join blade_project_group_disposition as g on g.kingdee_id=b.group_id
                left join blade_user as u on u.id=c.create_user
                LEFT JOIN blade_dept d ON d.id = u.dept_id
        WHERE
            c.is_deleted = 0
            and c.status &lt;&gt; 6
        <!-- 	合同类型-->
          <if test="contractType!=null and contractType!=''">
              and c.contract_type=#{contractType}
          </if>
        <!-- 	审批状态-->
          <if test="contractStatus!=null and contractStatus!=''">
              <choose>
                  <when test="contractStatus=='3'.toString()">
                      and (c.status=3 or c.status=8)
                  </when>
                  <when test="contractStatus=='2'.toString()">
                      and (c.status=2 or c.status=7)
                  </when>
                  <otherwise>
                      and c.status=#{contractStatus}
                  </otherwise>
              </choose>
              <!--and c.status=#{status}-->
          </if>
        <!-- 	项目类型-->
          <if test="projectType!=null and projectType!=''">
              and (g.kingdee_id=#{projectType} or g.parent_id=#{projectType})
          </if>
        <!-- 合同时间-->
          <if test="startTime!=null and startTime!='' and endTime!=null and endTime!=''">
              and c.create_time BETWEEN #{startTime} and #{endTime}
          </if>
          <if test="(startTime!=null and startTime!='') and (endTime==null or endTime=='')">
              and c.create_time &gt; #{startTime}
          </if>
          <if test="(endTime!=null and endTime!='') and (startTime==null or startTime=='')">
              and c.create_time  &lt; #{endTime}
          </if>

          <!-- 人员/部门-->
          <if test="deptOrPerson!=null and deptOrPerson!=''">
              AND (u.id=#{deptOrPerson}
              or u.dept_id LIKE concat( '%', #{deptOrPerson}, '%' )
              OR d.parent_id IN (
              WITH recursive temp AS (
              SELECT
              t.*
              FROM
              blade_dept t
              WHERE
              id = #{deptOrPerson} UNION ALL
              SELECT
              t.*
              FROM
              blade_dept t
              INNER JOIN temp ON t.parent_id = temp.id
              ) SELECT
              id
              FROM
              temp
              ))
          </if>


        <!-- 	待审批（状态为待审批0，且发起人不是登录人）-->
        <if test="pageType== '1'.toString() and userId!=null and userId!=''">
            and c.status=0 and c.create_user &lt;&gt; #{userId}
        </if>

        <!-- 我发起的（发起人是我本人的所有流程）-->
        <if test="pageType== '2'.toString() and userId!=null and userId!=''">
            and c.create_user=#{userId}
        </if>

        <!-- 已审批（状态不区分，仅展示本人已处理的所有流程，自己发起的不在其中）-->
        <if test="pageType== '3'.toString() and userId!=null and userId!=''">
            and (t.end_time_ is not null
            and t.assignee_ like concat('%',#{userId},'%')
            and t.task_def_key_ &lt;&gt; 'userTask')
        </if>

        <!-- 我收到的（待审批+已审批）-->
        <if test="pageType== '4'.toString() and userId!=null and userId!=''">
            and ((t.end_time_ is not null and t.assignee_ like concat('%',#{userId},'%') and  t.task_def_key_  &lt;&gt; 'userTask')
            or (c.status=0 and c.create_user &lt;&gt; #{userId})
            )
        </if>
        <if test="searchBox!=null and searchBox!=''">
            and c.name like concat('%',#{searchBox},'%')
        </if>
        order by c.create_time DESC
    </select>
    <select id="selectContractCount" resultType="java.lang.String">
        SELECT
            LPAD(COUNT(*)+1,3,'0')
        FROM
            blade_contract
        where
            is_deleted=0
          AND STATUS in (1,4,5,7,8,9)
          and DATE(update_time)=CURDATE()
    </select>
    <select id="number" parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam" resultType="java.lang.String">
        SELECT
        count(id)
        FROM
        (
        SELECT DISTINCT
        c.id
        FROM
        blade_contract AS c <!-- 合同-->
        LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = c.process_instance_id
        LEFT JOIN blade_archive_details AS ad ON c.id = ad.contract_id <!-- 归档-->
        LEFT JOIN ACT_HI_TASKINST AS t1 ON t1.proc_inst_id_ = ad.process_instance_id
        LEFT JOIN blade_contract_change cc ON c.id = cc.contract_id <!-- 变更/作废-->
        LEFT JOIN ACT_HI_TASKINST t2 ON t2.proc_inst_id_ = cc.process_instance_id
        WHERE
        c.is_deleted = 0
        AND c.STATUS &lt;&gt; 6
        AND c.STATUS &lt;&gt; 9 <!-- 	待审批（状态为待审批0，且发起人不是登录人）-->

        AND ( ad.is_deleted = 0 OR ad.is_deleted IS NULL )
        AND ( cc.is_deleted = 0 OR cc.is_deleted IS NULL )
        <if test="param.pageType == '1'.toString() and param.userId!=null and param.userId!=''">
            AND (((
            t.end_time_ IS NULL
            AND t.assignee_ LIKE concat( '%', #{param.userId}, '%' )
            AND t.task_def_key_ &lt;&gt; 'userTask'
            )
            AND ( c.STATUS = 0 or c.STATUS = -1 OR c.task_user != #{param.userId} ))
            OR (
            ( t1.end_time_ IS NULL AND t1.assignee_ LIKE concat( '%', #{param.userId}, '%' ) AND t1.task_def_key_ &lt;&gt; 'userTask' )
            AND ( c.STATUS = 4 OR c.task_user = #{param.userId} ))
            OR (
            ( t2.end_time_ IS NULL AND t2.assignee_ LIKE concat( '%', #{param.userId}, '%' ) AND t2.task_def_key_ &lt;&gt; 'userTask' )
            AND cc.task_user = #{param.userId}
            and (cc.status =0 or cc.status = -1)
            )
            )
        </if>
        <if test="param.projectId != null and param.projectId!='' ">
            and c.project_id=#{param.projectId}
        </if>
        <if test="param.pageType == '2'.toString() and param.userId!=null and param.userId!=''">
            and ((c.create_user=#{param.userId} and c.status in (1,3,8))
            or(ad.create_user=#{param.userId} and c.status in (1,3,8))
            or(cc.create_user=#{param.userId} and cc.status=3)
            )
        </if>
        ) socpe
    </select>
    <select id="checkContractNameSole" parameterType="java.util.Map" resultMap="contractVOResultMap">
        select c.*, u.name as createUserName
        from
            blade_contract  c
                left join blade_user as u on u.id=c.create_user
        where
            c.is_deleted = 0
            and c.status &lt;&gt; 6
            and c.status &lt;&gt; 9
          <if test="contractName!=null and contractName!=''">
              and c.name=#{contractName}
          </if>
        <if test="contractId!=null and contractId!=''">
            and c.id &lt;&gt; #{contractId}
        </if>
    </select>
    <select id="contractList" parameterType="java.util.Map" resultMap="contractResultMap">
        SELECT
            id,
            NAME,
            party_b_unit_id,
            party_b_unit_code,
            party_b_unit_name
        FROM
            blade_contract
        WHERE
            is_deleted = 0
            <if test="contractName!=null and contractName!=''">
                and name like concat('%',#{contractName},'%')
            </if>
          <if test="userId!=null and userId!=''">
              and create_user IN( WITH recursive temp AS (
              SELECT
              t.*
              FROM
              blade_user t
              WHERE
              id = #{userId} UNION ALL
              SELECT
              t.*
              FROM
              blade_user t
              INNER JOIN temp ON t.parent_id = temp.id
              ) SELECT
              id
              FROM
              temp
              )
          </if>
        AND (((
        contract_type = 0
        OR contract_type = 2
        OR contract_type = 4
        )
--         AND STATUS = 1
<!--1待归档，7归档已撤回，8归档已驳回-->
           AND STATUS in  (1,7,8)
        )
        OR ( contract_type = 1 AND STATUS = 5 ))
    </select>
    <select id="customerContractList" parameterType="java.util.Map" resultMap="contractListResultMap">
        <!-- 状态(0审批中，1待归档，2已撤回，3已驳回，4归档审批，5已归档,6草稿，7归档已撤回，8归档已驳回,9作废)-->
        SELECT DISTINCT
        c.id AS id,
        c.NAME AS contractName,
        CASE
        WHEN c.STATUS = 7 THEN 2
        WHEN c.STATUS = 8 THEN 3
        WHEN c.STATUS = -1 THEN 0
        ELSE c.STATUS
        END AS contractStatus,
        c.contract_type AS contractType,
        IF
        ( c.sign_company_name IS NOT NULL AND c.sign_company_name != '', c.sign_company_name, c.supplier_name ) AS signedName,
        DATE_FORMAT( c.create_time, "%Y-%m-%d %H:%i:%s" ) AS create_time,
        DATE_FORMAT( c.update_time, "%Y-%m-%d %H:%i:%s" ) AS update_time,
        c.contract_amount AS amount,
        u.NAME AS sponsor
        FROM
        blade_contract AS c
        LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = c.process_instance_id
        LEFT JOIN blade_project_basic AS b ON b.id = c.project_id
        LEFT JOIN blade_user AS u ON u.id = c.create_user
        WHERE
        c.is_deleted = 0
        AND c.STATUS &lt;&gt; 6
            <if test="map.busineId!=null and map.busineId!=''">
                and (c.sign_company_id=#{map.busineId} or c.supplier_id=#{map.busineId})
            </if>
        <if test="map.userId!=null and map.userId!=''">
            AND (c.create_user IN (
            WITH recursive temp AS (
            SELECT
            t.*
            FROM
            blade_user t
            WHERE
            id = #{map.userId} UNION ALL
            SELECT
            t.*
            FROM
            blade_user t
            INNER JOIN temp ON t.parent_id = temp.id
            ) SELECT
            id
            FROM
            temp
            )
            OR ((
            SELECT
            role_id
            FROM
            blade_user
            WHERE
            id = #{map.userId}
            ) LIKE concat( '%', 1631124176515588098, '%' ))
            )
        </if>
        order by create_time DESC
    </select>
    <select id="selectProjectName" resultType="java.lang.String">
        SELECT
            name
        FROM
            blade_contract
        WHERE
            project_id = #{projectId}
          AND (contract_type = 0 or contract_type = 4)
          AND is_deleted = 0
         and status in(-1,0,1,4,5,7,8)
    </select>
    <select id="selectByContactId" resultType="java.lang.String">
        SELECT
            customer_id
        FROM
            blade_customer_contact
        WHERE
            is_deleted = 0
          AND (id = #{contactId} OR contacts = #{contactId})
    </select>
    <select id="pageDetail" resultMap="contractListResultMap">
        SELECT DISTINCT
        c.id AS cid,
        c.id AS id,
        c.NAME AS contractName,
        case  c.STATUS
            when -1 then 0
            else c.STATUS end  contractStatus,
        c.contract_type AS contractType,
        IF
        ( c.sign_company_name IS NOT NULL AND c.sign_company_name != '', c.sign_company_name, c.supplier_name ) AS signedName,
        DATE_FORMAT( c.create_time, "%Y-%m-%d %H:%i:%s" ) AS create_time,
        DATE_FORMAT( c.update_time, "%Y-%m-%d %H:%i:%s" ) AS update_time,
        c.contract_amount AS amount,
        task.id as taskId,
        u.NAME AS sponsor
        FROM
        blade_contract AS c -- 合同
        LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = c.process_instance_id
        LEFT JOIN blade_archive_details AS ad ON c.id = ad.contract_id -- 归档
        LEFT JOIN ACT_HI_TASKINST AS t1 ON t1.proc_inst_id_ = ad.process_instance_id
        LEFT JOIN blade_project_basic AS b ON b.id = c.project_id
        LEFT JOIN blade_user AS u ON u.id = c.create_user
        LEFT JOIN blade_dept d ON FIND_IN_SET( d.id, u.dept_id )
        left join blade_user task on task.id=c.task_user
        WHERE
        c.is_deleted = 0
        AND c.STATUS &lt;&gt; 6
        AND ( ad.is_deleted = 0 OR ad.is_deleted IS NULL )

        AND (c.id=#{contractId} or ad.id=#{contractId})
        UNION
        SELECT DISTINCT
        c.id AS cid,
        cc.id AS id,
        cc.NAME AS contractName,
        case  cc.STATUS
            when 1 then 10
            when -1 then 0
            else cc.STATUS end  contractStatus,
        '3' AS contractType,
        c.NAME AS signedName,
        DATE_FORMAT( cc.create_time, "%Y-%m-%d %H:%i:%s" ) AS create_time,
        DATE_FORMAT( cc.update_time, "%Y-%m-%d %H:%i:%s" ) AS update_time,
        IFNULL( cc.adjusted_amount, NULL ) AS amount,
        task.id as taskId,
        u.NAME AS sponsor
        FROM
        blade_contract_change cc
        LEFT JOIN blade_contract c ON c.id = cc.contract_id
        LEFT JOIN ACT_HI_TASKINST t ON t.proc_inst_id_ = cc.process_instance_id
        LEFT JOIN blade_project_basic b ON b.id = c.project_id
        LEFT JOIN blade_user u ON u.id = cc.create_user
        LEFT JOIN blade_dept d ON d.id = u.dept_id
        left join blade_user task on task.id=c.task_user
        WHERE
        cc.is_deleted = 0
        AND c.is_deleted = 0
        AND cc.STATUS &lt;&gt; 6
        and cc.id = #{contractId}
    </select>
    <select id="getPendContractNames" resultType="java.lang.String">
        SELECT
            r.name
        FROM
            blade_contract as r
                LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = r.process_instance_id
        WHERE
            1 = 1
          AND r.task_user = #{userId}
          AND r.is_deleted = 0
          AND ( t.end_time_ IS NULL AND t.assignee_ LIKE concat( '%', r.task_user, '%' ) AND t.task_def_key_ &lt;&gt; 'userTask' )
          AND (r.STATUS = 0 or r.STATUS = -1)  -- 合同
        union
        SELECT
            r.name
        FROM
            blade_contract_change as r
                LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = r.process_instance_id
        WHERE
            1 = 1
          AND r.task_user = #{userId}
          AND r.is_deleted = 0
          AND ( t.end_time_ IS NULL AND t.assignee_ LIKE concat( '%', r.task_user, '%' ) AND t.task_def_key_ &lt;&gt; 'userTask' )
          AND (r.STATUS = 0 or r.STATUS = -1)  -- 合同变更/作废
        union
        SELECT
            r.id
        FROM
            blade_archive_details as r
                LEFT JOIN ACT_HI_TASKINST AS t ON t.proc_inst_id_ = r.process_instance_id
        WHERE
            1 = 1
          AND r.task_user = #{userId}
          AND r.is_deleted = 0
          AND ( t.end_time_ IS NULL AND t.assignee_ LIKE concat( '%', r.task_user, '%' ) AND t.task_def_key_ &lt;&gt; 'userTask' )
          AND (r.STATUS = 0 or r.STATUS = -1)  -- 归档

    </select>
    <select id="getKdContractEntry" parameterType="java.lang.Long"  resultType="map">
        SELECT
              f.FCONTRACTAMOUNT as contractAmount,-- 总价
              cd.FQTY as detailNumber,-- 数量
              cf.FTAXPRICE as detailUnitPrice ,-- 含税单价
              cf.FPRICEUNITQTY as fpriceunitqty,-- 计价数量
              cf.FALLAMOUNT as fAllAmount,-- 价税合计
              m.FNUMBER as fNumber,--物料编码
              ml.FMATERIALID as fmaterialId,
              ml.FUSEORGID as fuseorgId,
              ml.FNAME as fName -- 物料名称
        FROM
            T_CRM_CONTRACT AS c -- 合同表
            LEFT JOIN T_CRM_CONTRACTFIN AS f ON f.FID= c.FID --子单据头_财务信息
            LEFT JOIN T_CRM_CONTRACTENTRY AS cd ON c.FID= cd.FID --合同明细
            LEFT JOIN T_CRM_CONTRACTENTRY_F AS cf ON cf.FEntryID= cd.FEntryID --合同明细_拆分表_财务信息
            LEFT JOIN T_BD_MATERIAL_L AS ml ON ml.FMATERIALID= cd.FMATERIALID --物料表
            LEFT JOIN T_BD_MATERIAL AS m ON m.FMATERIALID = ml.FMATERIALID --物料编码
        WHERE
            c.F_XMDA = #{projectId}
          AND c.FDOCUMENTSTATUS = 'C' -- 单据状态（Z暂存,A创建,B审核中,C已审核,D重新审核）
          AND ml.FUSEORGID!= 1
          AND m.FNUMBER in ('2900001','2900002','8804005973','29006172')
    </select>

    <select id="getProKanBanContractFileByProjectId" parameterType="long" resultType="hashmap">
        SELECT
            con.annex_name AS fileName,
            concat( 'contract/', con.annex ) AS url
        FROM
            blade_contract as con
                join blade_project_basic as basic on basic.id=con.project_id
        WHERE
            (basic.id = #{0} or basic.kd_project_fid=#{0})
          AND basic.is_deleted = 0
          AND basic.push_down = 1
          AND con.is_deleted = 0
          AND con.STATUS = 5
          AND con.contract_type = 1
        UNION
        SELECT
            ad.archive_annex_name AS fileName,
            concat( 'contract/', ad.archive_annex ) AS url
        FROM
            blade_archive_details ad
                LEFT JOIN blade_contract c ON ad.contract_id = c.id
                left join blade_project_basic as basic on basic.id=c.project_id
        WHERE
            (basic.id = #{0} or basic.kd_project_fid=#{0})
          AND basic.is_deleted = 0
          AND basic.push_down = 1
          AND c.is_deleted = 0
          AND c.STATUS = 5
          AND ad.is_deleted = 0
          AND ad.STATUS = 1
    </select>
    <select id="contractListByBusinessId" resultMap="contractResultMap">
        SELECT bc.id,bc.contract_amount,bc.archive_date FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
        <if test="userIds != null and userIds.size() > 0">
            AND bc.create_user IN
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1 AND bpb.project_closed IN (0,1)
                   AND bpb.id IN (
                       select bopg.other_source_id from blade_other_project_group bopg
                                                   where bopg.business_group_dis_id= #{businessId}
                                                     and bopg.is_deleted = 0
                                                     and bopg.other_source_type_name='projectBasic'
            )
            )
        </if>
                                                                                 <!--  AND bpb.group_id IN (
                        SELECT gbd.group_id FROM blade_project_group_business_disposition gbd WHERE gbd.business_id = #{businessId}
                    ) -->
        <if test="businessId == null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1 AND bpb.project_closed IN (0,1)
                    AND bpb.id IN (
                        select bopg.other_source_id from blade_other_project_group bopg
                                                    where EXISTS (SELECT 1 FROM blade_business_group_disposition bgd WHERE bgd.id = bopg.business_group_dis_id AND bgd.is_deleted = 0 AND bgd.statistics = 1)
                                                    and bopg.is_deleted = 0
                                                    and bopg.other_source_type_name='projectBasic'
            )
            )
        </if>
        <!-- AND bpb.group_id IN (
                        SELECT gbd.group_id FROM blade_project_group_business_disposition gbd
                                            WHERE EXISTS (SELECT 1 FROM blade_business_group_disposition bgd WHERE bgd.id = gbd.business_id AND bgd.is_deleted = 0 AND bgd.statistics = 1)
                    ) -->
    </select>
    <select id="getProjectIdsByContractIds" resultType="java.lang.Long">
        SELECT DISTINCT project_id FROM blade_contract WHERE is_deleted = 0 AND status = 5 AND contract_type IN ('0','4') AND id IN
        <foreach collection="contractIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="contractIdsByBusinessId" resultType="java.lang.Long">
        SELECT bc.id FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
        <if test="userIds != null and userIds.size() > 0">
            AND bc.create_user IN
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1 AND bpb.project_closed IN (0,1)
                <if test="step != null">
                    AND bpb.schedule_id = #{step}
                </if>
                AND bpb.id IN (
                    select bopg.other_source_id from blade_other_project_group bopg
                    where bopg.business_group_dis_id= #{businessId}
                    and bopg.is_deleted = 0
                    and bopg.other_source_type_name='projectBasic'
                )
            )
        </if>
        <if test="businessId == null">
            AND EXISTS (SELECT 1 FROM blade_project_basic bpb WHERE bpb.is_deleted = 0
            <if test="step != null">
                AND bpb.schedule_id = #{step}
            </if>
            AND bpb.push_down = 1 AND bpb.id = bc.project_id AND bpb.project_closed IN (0,1,2))
        </if>
    </select>
    <select id="contractIdsByBusinessIdForProjectPanel" resultType="java.lang.Long">
        SELECT bc.id FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
<!--        <if test="userIds != null and userIds.size() > 0">-->
<!--            AND bc.create_user IN-->
<!--            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="projectIds != null and projectIds.size() > 0">
            AND bc.project_id IN
            <foreach collection="projectIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
            SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1 AND bpb.project_closed IN (0,1)
            <if test="step != null">
                AND bpb.schedule_id = #{step}
            </if>
                AND bpb.id IN (
                    select bopg.other_source_id from blade_other_project_group bopg
                    where bopg.business_group_dis_id= #{businessId}
                    and bopg.is_deleted = 0
                    and bopg.other_source_type_name='projectBasic'
                )
            )
        </if>
        <if test="businessId == null">
            AND EXISTS (SELECT 1 FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.forbid_status = 'A' AND bpb.schedule_id IS NOT NULL AND bpb.schedule_id  != ''
            <if test="step != null">
                AND bpb.schedule_id = #{step}
            </if>
            AND bpb.push_down = 1 AND bpb.id = bc.project_id AND bpb.project_closed IN (0,1,2,3))
        </if>
    </select>
    <select id="getTotalContractAmountByProjectId" resultType="java.math.BigDecimal">
        SELECT SUM(bc.contract_amount) FROM blade_contract bc
                                       WHERE bc.is_deleted = 0
                                    AND bc.status in (1,5,4)
                                    AND bc.contract_type IN
                                    <foreach collection="types" item="item" index="index" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                    AND bc.project_id = #{projectId}
    </select>
    <select id="contractIdsForPaybackPanelBadDebt" resultType="java.lang.Long">
        SELECT bc.id,bc.contract_amount,bc.archive_date FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
        AND EXISTS (SELECT 1 FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.push_down = 1 AND bpb.id = bc.project_id AND bpb.project_closed IN (0,1,2))
        <if test="userIds != null and userIds.size() > 0">
            AND bc.create_user IN
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1
                AND bpb.id IN (
                    select bopg.other_source_id from blade_other_project_group bopg
                    where bopg.business_group_dis_id= #{businessId}
                    and bopg.is_deleted = 0
                    and bopg.other_source_type_name='projectBasic'
                )
            )
        </if>
        <if test="businessId == null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1
                AND bpb.id IN (
                    select bopg.other_source_id from blade_other_project_group bopg
                    where EXISTS (SELECT 1 FROM blade_business_group_disposition bgd WHERE bgd.id = bopg.business_group_dis_id AND bgd.is_deleted = 0 AND bgd.statistics = 1)
                    and bopg.is_deleted = 0
                    and bopg.other_source_type_name='projectBasic'
                    )
            )
        </if>
    </select>
    <select id="contractIdsFormPanel" resultType="java.lang.Long">
        SELECT bc.id FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
        <if test="userIds != null and userIds.size() > 0">
            AND bc.create_user IN
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
                SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1 AND bpb.project_closed IN (0,1)
                AND bpb.id IN (
                select bopg.other_source_id from blade_other_project_group bopg
                where bopg.business_group_dis_id= #{businessId}
                and bopg.is_deleted = 0
                and bopg.other_source_type_name='projectBasic'
                )
            )
        </if>
        <if test="businessId == null">
            AND EXISTS (SELECT 1 FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.push_down = 1 AND bpb.id = bc.project_id AND bpb.project_closed IN (0,1))
        </if>
    </select>
    <select id="contractIdsFormProjectPanelReceiptAmount" resultType="java.lang.Long">
        SELECT bc.id FROM blade_contract bc WHERE bc.is_deleted = 0 AND bc.status = 5 AND bc.contract_type IN ('0','4')
        <if test="userIds != null and userIds.size() > 0">
            AND bc.create_user IN
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessId != null">
            AND bc.project_id IN (
            SELECT bpb.id FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.status = 1 AND bpb.push_down = 1
                AND bpb.id IN (
                select bopg.other_source_id from blade_other_project_group bopg
                where bopg.business_group_dis_id= #{businessId}
                and bopg.is_deleted = 0
                and bopg.other_source_type_name='projectBasic'
                )
            )
        </if>
        <if test="businessId == null">
            AND EXISTS (SELECT 1 FROM blade_project_basic bpb WHERE bpb.is_deleted = 0 AND bpb.push_down = 1 AND bpb.id = bc.project_id)
        </if>
    </select>
    <select id="getContractList" resultType="org.springblade.core.tool.support.Kv">
        select a.id as linkDataId,a.name as linkDataName, group_concat(dict.projectGroupId) as projectGroupId,
               any_value(dict.projectGroupParentId) as projectGroupParentId, group_concat(dict.prjectGroupName) as projectGroupName
        from blade_contract a
        inner join (
            select b.id,c.id as projectGroupId,c.name as prjectGroupName,c.parent_id  as projectGroupParentId
            from blade_project_basic b
                        left join blade_other_project_group other on b.id=other.other_source_id
                        left join blade_project_group_disposition c on other.project_group_id=c.id
                        where b.is_deleted=0 and other.is_deleted=0
        ) dict on a.project_id=dict.id
        <where>
            a.contract_type=0 and a.status in (1,4,5,7,8)
            <if test="keyword != null and keyword != ''">
                AND (a.name LIKE CONCAT('%',#{keyword},'%'))
            </if>
        </where>
        group by a.id
        order by a.create_time desc
    </select>
</mapper>
