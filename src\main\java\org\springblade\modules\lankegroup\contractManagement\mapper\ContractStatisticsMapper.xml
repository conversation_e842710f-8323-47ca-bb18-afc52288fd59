<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractStatisticsMapper">

    <select id="getContractStatus"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsGroupVO">
        SELECT
        count(con.id) statusCount,
        IF(con.status = -1, 0, con.status) statusId,
        ROUND(SUM(CAST(con.contract_amount AS DECIMAL(10, 2))) / 10000,2) AS statusTotal
        FROM blade_contract con
        JOIN blade_project_basic pro ON pro.id = con.project_id AND pro.is_deleted = 0
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1 and
        bgd.is_deleted = 0
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pgd on pgd.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id-->
        WHERE
        con.is_deleted = 0 and con.status in (-1,0,1,9) and con.contract_type in (0,4)
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.contractType != null and query.contractType != ''">
            and con.contract_type = #{query.contractType}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.conditionStatus != null">
            <if test="query.conditionStatus == 1">
                and pro.project_closed = 0
            </if>
            <if test="query.conditionStatus == 2">
                and pro.project_closed != 0
            </if>
            and con.status = 5
        </if>
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        group by con.status
        union all
        SELECT
        count(con.id) statusCount,
        IF(con.status = -1, 0, con.status) statusId,
        ROUND(SUM(CAST(con.contract_amount AS DECIMAL(10, 2))) / 10000,2) AS statusTotal
        FROM
        blade_contract con
        JOIN blade_archive_details detail
        on detail.contract_id = con.id and detail.is_deleted = 0
        join ACT_HI_ACTINST h
        on detail.process_instance_id = h.PROC_INST_ID_ and h.ACT_TYPE_ = 'endEvent'
        JOIN blade_project_basic pro ON pro.id = con.project_id
        AND pro.is_deleted = 0
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1 and
        bgd.is_deleted = 0
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pgd on pgd.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id
        join blade_business_group_disposition bgd on bgd.id = pgbd.business_id -->
        WHERE
        con.is_deleted = 0 and con.status in (5) and con.contract_type in (0,4)
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and DATE_FORMAT(h.END_TIME_, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and DATE_FORMAT(h.END_TIME_, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.contractType != null and query.contractType != ''">
            and con.contract_type = #{query.contractType}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.contractNameList != null and query.projectNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        <if test="query.conditionStatus != null">
            <if test="query.conditionStatus == 1">
                and pro.project_closed = 0
            </if>
            <if test="query.conditionStatus == 2">
                and pro.project_closed != 0
            </if>
            and con.status = 5
        </if>
        group by con.status
    </select>
    <select id="getContractStatistics"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsGroupVO">
        select count(con.id) statusCount,IF(pro.project_closed != 0, 2, 1) statusId ,
               IF(pro.project_closed != 0, '已完结', '履行中') statusName,
        ROUND(SUM(CAST(con.contract_amount AS DECIMAL(10, 2))) / 10000,2) AS statusTotal
        from blade_contract con
        JOIN blade_archive_details detail
        on detail.contract_id = con.id and detail.is_deleted = 0
        join ACT_HI_ACTINST h
        on detail.process_instance_id = h.PROC_INST_ID_ and h.ACT_TYPE_ = 'endEvent'
        join blade_project_basic pro
        on pro.id = con.project_id and pro.is_deleted = 0
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1 and
        bgd.is_deleted = 0
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pgd on pgd.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id
        join blade_business_group_disposition bgd on bgd.id = pgbd.business_id-->
        where con.is_deleted = 0 and con.contract_type in (0,4)
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and DATE_FORMAT(h.END_TIME_, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and DATE_FORMAT(h.END_TIME_, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.contractType != null and query.contractType != ''">
            and con.contract_type = #{query.contractType}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0 ">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and con.status = 5
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        group by case when pro.project_closed != 0 then 1 else 0 end
    </select>
    <select id="getContractStatisticsByUser"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsVO">
        select tuser.*,IF(tcon.conCount is null, 0, tcon.conCount) as conCount,
        IF(tcon.conTotal is null, 0.00, tcon.conTotal) as conTotal
        from (
        select user.id userId,user.name userName,dept.dept_name deptName,dept.id deptId
        from blade_user user
        join blade_user_dept udept on udept.user_id = user.id
        join blade_dept dept on dept.id = udept.dept_id
        where 1 = 1
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and user.id in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
            ) tuser
        left join (
        select
        con.create_user,
        count(con.id) conCount,
        sum(CAST(con.contract_amount AS DECIMAL(10, 2))) conTotal,
        con.contract_amount
        from blade_contract con
        JOIN blade_project_basic pro ON pro.id = con.project_id AND pro.is_deleted = 0
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1 and
        bgd.is_deleted = 0
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pgd on pgd.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id
        join blade_business_group_disposition bgd on bgd.id = pgbd.business_id-->
        where con.is_deleted = 0 and con.contract_type in (0,4) and con.status in (0,1,5,9)
        <if test="query.startTime != null and query.startTime != ''">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.contractType != null and query.contractType != ''">
            and con.contract_type = #{query.contractType}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.conditionStatus != null">
            <if test="query.conditionStatus == 1">
                and pro.project_closed = 0
            </if>
            <if test="query.conditionStatus == 2">
                and pro.project_closed != 0
            </if>
            and con.status = 5
        </if>
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        group by con.create_user
        ) tcon
        on tcon.create_user = tuser.userId
        group by tuser.userId
        order by conTotal
        <if test="query.sort != null and query.sort == 2 ">
            desc
        </if>
        <if test="query.sort != null and query.sort == 1 ">
            asc
        </if>
        <if test="query.sort == null">
            desc
        </if>
    </select>
    <select id="getContractStatisticsType"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsProjectTypeVO">
        select t1.id,t1.name,IF(t2.count is null, 0, t2.count) as  count,
               IF(t2.total is null, 0, t2.total) as total
        from (
        select id,name,sort from blade_business_group_disposition where is_deleted = 0 and statistics = 1
        ) t1
        left join
        ( select id,name,sum(count) count,sum(total) total
        from ( select bgd.id,bgd.name,
        count(con.id) count,
        ROUND(SUM(CAST(con.contract_amount AS DECIMAL(10, 2))) / 10000,2) total
        from blade_business_group_disposition bgd
        left join (
        select other.business_group_dis_id,pro.id as projectId,pro.project_closed from blade_other_project_group other
        left join blade_project_basic pro on other.other_source_id = pro.id and pro.is_deleted = 0
        where  other.is_deleted = 0 and other.other_source_type_name = 'projectBasic'
        <if test="query.conditionStatus != null">
            <if test="query.conditionStatus == 1">
                and pro.project_closed = 0
            </if>
            <if test="query.conditionStatus == 2">
                and pro.project_closed != 0
            </if>
        </if>
        group by pro.id
        ) other on other.business_group_dis_id = bgd.id
        <!--left join blade_project_group_business_disposition pgbd
        on pgbd.business_id = bgd.id
        left join blade_project_group_disposition pgd
        on pgd.id = pgbd.group_id and pgd.is_deleted = 0
        left join blade_project_basic pro
        on other.other_source_id = pro.id and pro.is_deleted = 0-->
        left join blade_contract con
        on con.project_id = other.projectId and con.is_deleted = 0 and con.contract_type in (0,2,4)
        left join blade_archive_details d
        on con.id = d.contract_id
        where bgd.is_deleted = 0 and bgd.statistics = 1
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.conditionStatus != null">
            and con.status = 5
        </if>
        <if test="query.startTime != null and query.startTime != '' and query.timeType == 0">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != '' and query.timeType == 0">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.startTime != null and query.startTime != '' and query.timeType == 1">
            and DATE_FORMAT(con.archive_date, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != '' and query.timeType == 1">
            and DATE_FORMAT(con.archive_date, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.contractTypeList != null and query.contractTypeList.size() > 0">
            and con.contract_type in
            <foreach collection="query.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.haveBeenFiled == 7">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 14 DAY) AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="query.haveBeenFiled == 14">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND DATE_SUB(CURDATE(), INTERVAL 14 DAY)
        </if>
        <if test="query.haveBeenFiled == 30">
            and con.approve_date &lt; DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        <if test="query.status != null">
            and con.status = #{query.status}
        </if>
        <if test="query.selectContractType == 1">
            and con.contract_type in (0,4)
        </if>
        group by bgd.id
        <if test="query.selectContractType == 2">
            union all
            select bgd.id,bgd.name,
            count(other.business_group_dis_id) count,
            ROUND(SUM(CAST(con.contract_amount AS DECIMAL(10, 2))) / 10000,2) total
            from blade_business_group_disposition bgd
            left join (
            select other.business_group_dis_id,pro.id as projectId,pro.project_closed from blade_other_project_group other
            left join blade_project_basic pro on other.other_source_id = pro.id and pro.is_deleted = 0
            where  other.is_deleted = 0 and other.other_source_type_name = 'projectBasic'
            <if test="query.conditionStatus != null">
                  <if test="query.conditionStatus == 1">
                    and pro.project_closed = 0
                </if>
                <if test="query.conditionStatus == 2">
                    and pro.project_closed != 0
                </if>
            </if>
            group by pro.id
            ) other on other.business_group_dis_id = bgd.id
            <!--left join blade_project_group_business_disposition pgbd
            on pgbd.business_id = bgd.id
            left join blade_project_group_disposition pgd
            on pgd.id = pgbd.group_id and pgd.is_deleted = 0
            left join blade_project_basic pro
            on other.other_source_id = pro.id and pro.is_deleted = 0-->
            left join blade_contract con
            on con.project_id = other.projectId and con.is_deleted = 0 and con.contract_type in (1)
            left join blade_archive_details d
            on con.id = d.contract_id
            where bgd.is_deleted = 0 and bgd.statistics = 1
            <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
                and con.create_user in
                <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.contractStatus != null and query.contractStatus.size() > 0">
                and con.status in
                <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.conditionStatus != null">
                and con.status = 5
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' and query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' and query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
            <if test="query.contractTypeList != null and query.contractTypeList.size() > 0">
                and con.contract_type in
                <foreach collection="query.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.contractNameList != null and query.contractNameList.size() > 0">
                <foreach collection="query.contractNameList" item="item" index="index">
                    and con.name like concat("%",#{item},"%")
                </foreach>
            </if>
            <if test="query.status != null">
                and con.status = #{query.status}
            </if>
            <if test="query.haveBeenFiled == 7">
                and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 14 DAY) AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </if>
            <if test="query.haveBeenFiled == 14">
                and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND DATE_SUB(CURDATE(), INTERVAL 14 DAY)
            </if>
            <if test="query.haveBeenFiled == 30">
                and con.approve_date &lt; DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            </if>
            group by bgd.id
        </if>
        )x
        group by x.id
        ) t2
        on t1.id = t2.id
        order by t1.sort asc
    </select>

    <select id="getContractStatisticsList"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsListVO">
        select con.id conId,
        con.name conName,
        con.contract_amount conTotal,
        IF(pro.project_closed = 0, 1, 2) conStatus,
        con.create_user userId,
        user.name userName,
        IF(con.contract_type = 1, con.supplier_id, con.sign_company_id) idStr,
        IF(con.contract_type = 1, con.supplier_name, con.sign_company_name) nameStr,
        IF(con.contract_type = 1, start_date, detail.service_start_date) startTime,
        IF(con.contract_type = 1, end_date, detail.service_end_date) endTime,
        con.create_time createTime,
        con.contract_type contractType,
        pro.principal_id createUser,
        con.create_dept createDpet,
        pro.project_name projectName,
        pro.project_closed projectClosed,
        con.archive_date examineTime
        from blade_contract con
        join blade_project_basic pro
        on pro.id = con.project_id and pro.is_deleted = 0
        join blade_user user
        on user.id = con.create_user
        left join blade_archive_details detail
        on detail.contract_id = con.id and detail.is_deleted = 0
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1
          and bgd.is_deleted = 0
        <if test="query.proType != null">
            and bgd.id = #{query.proType}
        </if>
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
       <!-- join blade_project_group_disposition pg1 on pg1.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pg1.id
        join blade_business_group_disposition pg2 on pg2.id = pgbd.business_id-->
        where con.is_deleted = 0 and con.status = 5

        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != '' and query.timeType == 0">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != '' and query.timeType == 0">
            and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.startTime != null and query.startTime != '' and query.timeType == 1">
            and DATE_FORMAT(con.archive_date, '%Y-%m-%d') >= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != '' and query.timeType == 1">
            and DATE_FORMAT(con.archive_date, '%Y-%m-%d') &lt;= #{query.endTime}
        </if>
        <if test="query.conditionStatus != null">
            <if test="query.conditionStatus == 1">
                and pro.project_closed = 0
            </if>
            <if test="query.conditionStatus == 2">
                and pro.project_closed != 0
            </if>
        </if>
        <if test="query.contractTypeList != null and query.contractTypeList.size() > 0">
            and con.contract_type in
            <foreach collection="query.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        order by con.create_time desc
    </select>

    <select id="getContractStatisticsStatusList"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsListVO">
        select con.id conId,
        con.name conName,
        con.contract_amount conTotal,
        IF(con.status = -1, 0, con.status) conStatus,
        con.create_user userId,
        user.name userName,
        con.create_time createDateTime,
        IF(con.contract_type = 1, con.supplier_id, con.sign_company_id) idStr,
        IF(con.contract_type = 1, con.supplier_name, con.sign_company_name) nameStr,
        con.contract_type contractType
        from blade_contract con
        join blade_project_basic pro
        on pro.id = con.project_id and pro.is_deleted = 0
        join blade_user user
        on user.id = con.create_user
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic'
        and bgd.statistics = 1 and bgd.is_deleted = 0
        <if test="query.proType != null">
                    and bgd.id = #{query.proType}
                </if>
                 group by other.other_source_id
        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pg1
        on pg1.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pg2
        on pg2.group_id = pg1.id
        join blade_business_group_disposition bgd on bgd.id = pg2.business_id and bgd.statistics = 1-->
        where con.is_deleted = 0
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.selectUserId != null and query.selectUserId != ''">
            and con.create_user = #{query.selectUserId}
        </if>
        <if test="query.conId != null and query.conId != ''">
            and con.id = #{query.conId}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.contractTypeList != null and query.contractTypeList.size() > 0">
            and con.contract_type in
            <foreach collection="query.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            <if test="query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') >= #{query.startTime}
            </if>
            <if test="query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
            </if>
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            <if test="query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
            <if test="query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
        </if>
        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>

        <if test="query.haveBeenFiled == 7">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 14 DAY) AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="query.haveBeenFiled == 14">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND DATE_SUB(CURDATE(), INTERVAL 14 DAY)
        </if>
        <if test="query.haveBeenFiled == 30">
            and con.approve_date &lt; DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        order by con.create_time desc
    </select>
    <select id="getContractStatisticsDetail"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsDetailVO">
        select con.id                    conId,
               con.code                  conCode,
               pro.id                    proId,
               pro.project_name          proName,
               gp.kingdee_id                     gpId,
               gp.name                   gpName,
               con.name                  conName,
               con.contract_amount       conTotal,
               con.sign_company_name     signName,
               con.sign_company_man_name signManName,
               con.sign_company_tal      signTal,
               con.party_b_unit_name     unitName,
               con.create_user           userId,
               user.name                 userName,
               con.create_dept           deptId,
               dept.dept_name            deptName,
               con.use_chapter_type_vals typeVals,
               con.num_of_copies         ofNum,
               con.annex,
               con.annex_name            annexName
        from blade_contract con
                 join blade_project_basic pro
                      on pro.id = con.project_id and pro.is_deleted = 0
                 join blade_project_group_disposition gp
                      on gp.kingdee_id = pro.group_id
                 join blade_user user
        on user.id = con.create_user
            join blade_dept dept
            on dept.id = con.create_dept
        where con.id = #{id} and con.is_deleted = 0
    </select>
    <select id="getContractStatisticsArchiveDetail"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsArchiveDetailVO">
        SELECT
            con.id conId,
            con.CODE conCode,
            con.NAME conName,
            detail.payment_method deMethod,
            detail.payment_term deTerm,
            detail.contract_signing_date deDate,
            detail.agreed_project_duration deDuration,
            detail.service_start_date deStartDate,
            detail.service_end_date deEndDate,
            detail.is_warranty deWarranty,
            detail.technical_service_fee deFee,
            detail.secondary_service_number deSeNumber,
            detail.secondary_service_price deSePrice,
            detail.secondary_service_order deSeOrder,
            detail.tertiary_service_number deTeNumber,
            detail.tertiary_service_price deTePrice,
            detail.tertiary_service_order deTeOrder,
            detail.number deNumber,
            detail.remarks deRemark,
            detail.project_progress_income deIncome,
            detail.once_collection_date deOnceDate
        FROM
            blade_contract con
                JOIN blade_archive_details detail ON detail.contract_id = con.id
                AND detail.is_deleted = 0
        WHERE
            con.is_deleted = 0
          AND con.id = #{id}
    </select>
    <select id="getContractHaveBeenFiled" resultType="java.lang.Integer">
        select
        count(con.id)
        from blade_contract con
        join blade_project_basic pro
        on pro.id = con.project_id and pro.is_deleted = 0
        join blade_user user
        on user.id = con.create_user
        join (
        select other.other_source_id from blade_other_project_group other
        join blade_business_group_disposition bgd on bgd.id = other.business_group_dis_id
        where other.is_deleted = 0 and other.other_source_type_name = 'projectBasic' and bgd.statistics = 1 and
        bgd.is_deleted = 0
        <if test="query.proType != null">
            and bgd.id = #{query.proType}
        </if>
        group by other.other_source_id

        ) dict on dict.other_source_id=pro.id
        <!--join blade_project_group_disposition pg1
        on pg1.kingdee_id = pro.group_id
        join blade_project_group_business_disposition pg2
        on pg2.group_id = pg1.id
        join blade_business_group_disposition bgd on bgd.id = pg2.business_id and bgd.statistics = 1-->
        where con.is_deleted = 0
        <if test="query.selectUserIdList != null and query.selectUserIdList.size()>0">
            and con.create_user in
            <foreach collection="query.selectUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.selectUserId != null and query.selectUserId != ''">
            and con.create_user = #{query.selectUserId}
        </if>
        <if test="query.conId != null and query.conId != ''">
            and con.id = #{query.conId}
        </if>
        <if test="query.contractStatus != null and query.contractStatus.size() > 0">
            and con.status in
            <foreach collection="query.contractStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.contractTypeList != null and query.contractTypeList.size() > 0">
            and con.contract_type in
            <foreach collection="query.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            <if test="query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') >= #{query.startTime}
            </if>
            <if test="query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') >= #{query.startTime}
            </if>
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            <if test="query.timeType == 1">
                and DATE_FORMAT(con.archive_date, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
            <if test="query.timeType == 0">
                and DATE_FORMAT(con.create_time, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
        </if>

        <if test="query.contractNameList != null and query.contractNameList.size() > 0">
            <foreach collection="query.contractNameList" item="item" index="index">
                and con.name like concat("%",#{item},"%")
            </foreach>
        </if>
        <if test="query.haveBeenFiled == 7">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 14 DAY) AND DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="query.haveBeenFiled == 14">
            and con.approve_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND DATE_SUB(CURDATE(), INTERVAL 14 DAY)
        </if>
        <if test="query.haveBeenFiled == 30">
            and con.approve_date &lt; DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
    </select>
</mapper>
