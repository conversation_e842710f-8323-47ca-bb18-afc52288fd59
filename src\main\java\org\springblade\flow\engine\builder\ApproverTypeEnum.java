package org.springblade.flow.engine.builder;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> mtf
 * 审批人类型枚举
 */
@AllArgsConstructor
@Getter
public enum ApproverTypeEnum {
	INITIATOR("发起人", 0),
	DESIGNATED("指定成员", 1),//流程指定成员
	INITIATOR_OPTIONAL("发起人自选", 2),//单据发起人从流转指定的成员范围内进行选择
	ROLE("角色", 3),
	USER_IMMEDIATE_SUPERVISOR("直属主管", 4),
	DEPT_LEADER("部门主管", 5);
//	FORM_USER_FIELD("部门成员", 6);

	private final String desc;
	private final Integer value;
}
