/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 报账技巧实体类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@TableName("crm_expense_report_skill")
@EqualsAndHashCode(callSuper = true)
public class ExpenseReportSkill extends BaseEntity {

	private static final long serialVersionUID = 1L;



	/**
	 * 要求
	 */
	private String content;

	/**
	 * 客户id
	 */
	private Long customerId;

	/**
	 * 联系人id1
	 */
	private Long contactId;

} 