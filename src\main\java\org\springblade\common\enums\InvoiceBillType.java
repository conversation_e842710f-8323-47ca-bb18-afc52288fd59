package org.springblade.common.enums;

public enum InvoiceBillType {
    NORMAL("普通发票", 0),
    VAT("增值税发票", 1),
    ELECT("电子发票", 2);

    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private InvoiceBillType(String name, int index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(int index) {
        for (InvoiceBillType c : InvoiceBillType.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public int getIndex() {
        return index;
    }

}
