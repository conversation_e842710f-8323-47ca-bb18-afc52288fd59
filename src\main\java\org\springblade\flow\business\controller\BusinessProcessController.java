/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.controller;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.dto.BusinessProcessDTO;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.business.vo.BusinessProcessVO;
import org.springblade.modules.lankegroup.historicprocess.vo.HistoricProcessVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 自定义流程审批中的所有业务单据任务 控制器
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("businessProcessTask")
@Api(value = "审批待办【新】", tags = "审批待办【新】")
public class BusinessProcessController extends BladeController {

	private final IBusinessProcessService businessProcessService;

//	/**
//	 * 自定义流程审批中的所有业务单据任务 详情
//	 */
//	@GetMapping("/detail")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "详情", notes = "传入BusinessProcessTask")
//	public R<BusinessProcessVO> detail(BusinessProcessTaskEntity businessProcessTask) {
//		BusinessProcessEntity detail = businessProcessService.getOne(Condition.getQueryWrapper(businessProcessTask));
//		return R.data(BusinessProcessWrapper.build().entityVO(detail));
//	}

//	/**
//	 * 自定义流程审批中的所有业务单据任务 分页
//	 */
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 2)
//	@ApiOperation(value = "分页", notes = "传入BusinessProcessTask")
//	public R<IPage<BusinessProcessTaskVO>> list(@ApiIgnore @RequestParam Map<String, Object> BusinessProcessTask, Query query) {
//		IPage<BusinessProcessTaskEntity> pages = BusinessProcessTaskService.page(Condition.getPage(query), Condition.getQueryWrapper(BusinessProcessTask, BusinessProcessTaskEntity.class));
//		return R.data(BusinessProcessTaskWrapper.build().pageVO(pages));
//	}

	/**
	 * 自定义流程审批中的所有业务单据任务 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "待审批列表", notes = "传入BusinessProcessTask")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "size", value = "每页展示数量", dataType = "integer", required = true),
			@ApiImplicitParam(name = "current", value = "当前页", dataType = "integer", required = true),
			@ApiImplicitParam(name = "flowTypeCode", value = "审批类型:1待审批；2已审批；3我发起的；4我收到的", dataType = "integer", required = true),
			@ApiImplicitParam(name = "status", value = "审批状态：0待审批；1审批中；2已驳回；3已撤回；4已完成", dataType = "integer", required = true),
			@ApiImplicitParam(name = "category", value = "流程分类", dataType = "integer", required = true),
			@ApiImplicitParam(name = "subclass", value = "流程分类-子类", dataType = "integer", required = true),
			@ApiImplicitParam(name = "startTime", value = "创建时间-开始", dataType = "string", required = true),
			@ApiImplicitParam(name = "endTime", value = "创建时间-结束", dataType = "string", required = true),
			@ApiImplicitParam(name = "approvedStartTime", value = "审批时间-开始", dataType = "string", required = true),
			@ApiImplicitParam(name = "approvedEndTime", value = "审批时间-结束", dataType = "string", required = true),
	})
	public R<IPage<BusinessProcessVO>> page(HistoricProcessVO historicProcess, Query query) {
		// 转化
		BusinessProcessDTO  businessProcessDTO = new BusinessProcessDTO();
		businessProcessDTO.setSearchName(historicProcess.getSearchName());
		businessProcessDTO.setFlowTypeCode(historicProcess.getPageType());
		businessProcessDTO.setStatus(Func.isEmpty(historicProcess.getStatus()) ? null : String.valueOf(historicProcess.getStatus()));
		businessProcessDTO.setCategory(Func.isBlank(historicProcess.getBillsType()) ? null : Long.valueOf(historicProcess.getBillsType()));
		if (Func.isNotBlank(historicProcess.getDateTime())) {
			Date parse = DateUtil.parse(historicProcess.getDateTime(), "yyyy-MM");
			DateTime beginOfMonth = cn.hutool.core.date.DateUtil.beginOfMonth(parse);
			DateTime endOfMonth = cn.hutool.core.date.DateUtil.endOfMonth(parse);
			businessProcessDTO.setStartTime(DateUtil.formatDateTime(beginOfMonth));
			businessProcessDTO.setEndTime(DateUtil.formatDateTime(endOfMonth));
		}

		IPage<BusinessProcessVO> pages = businessProcessService.selectBusinessProcessPage(Condition.getPage(query), businessProcessDTO);
		return R.data(pages);
	}

	@GetMapping("/transferList")
	@ApiOperationSupport(order = 3)
//	@ApiOperation(value = "转办列表", notes = "传入BusinessProcessTask")
	public R<IPage<BusinessProcessVO>> transferList(BusinessProcessDTO BusinessProcessTask, Query query) {
		IPage<BusinessProcessVO> pages = businessProcessService.selectBusinessProcessTransferPage(Condition.getPage(query), BusinessProcessTask);
		return R.data(pages);
	}


}
