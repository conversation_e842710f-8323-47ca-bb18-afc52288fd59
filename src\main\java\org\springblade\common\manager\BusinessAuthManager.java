package org.springblade.common.manager;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.RoleIdConstant;
import org.springblade.common.enums.BusinessAuthTypeEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 业务权限
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 14:15
 **/
@Slf4j
@Service
@AllArgsConstructor
public class BusinessAuthManager {

    private final UserMapper userMapper;


    /**
     * 是否有权限【只适用于不涉及businessUserId字段的判断！！！】
     *
     * @param targetUserId   要判断的目标用户
     */
    public boolean chechAuth(Long targetUserId, List<BusinessAuthTypeEnum> dictKeys) {
        return chechAuth(targetUserId, new ArrayList<>(0), dictKeys);
    }

    /**
     * 是否有权限
     *
     * @param targetUserId   要判断的目标用户
     * @param businessUserId 业务中的用户
     */
    public boolean chechAuth(Long targetUserId, Long businessUserId, List<BusinessAuthTypeEnum> dictKeys) {
        return chechAuth(targetUserId, Func.ofImmutableList(businessUserId), dictKeys);
    }

    /**
     * 是否有权限
     *
     * @param targetUserId       要判断的目标用户
     * @param businessUserIdList 业务中的用户列表
     */
    public boolean chechAuth(Long targetUserId, List<Long> businessUserIdList, List<BusinessAuthTypeEnum> dictKeys) {

        if (Func.isEmpty(targetUserId) || CollectionUtil.isEmpty(dictKeys)) {
            log.debug("权限检查失败：参数为空，targetUserId={}, dictKeys={}", targetUserId, dictKeys);
            return false;
        }

        log.debug("开始权限检查，targetUserId={}, businessUserIdList={}, dictKeys={}",
            targetUserId, businessUserIdList, dictKeys);

        boolean result = false;
        for (BusinessAuthTypeEnum dictKey : dictKeys) {
            if (result) {
                return result;
            }
            log.debug("检查权限类型: {}", dictKey);
            switch (dictKey) {
                case 目标人及其所有上级:
                    if (CollectionUtil.isEmpty(businessUserIdList)) {
                        result = false;
                        log.debug("目标人及其所有上级权限检查：业务用户列表为空，结果=false");
                    } else {
                        List<Long> aLlParentByUserId = userMapper.getALlParentByUserIdList(businessUserIdList);
                        result = CollectionUtil.isNotEmpty(aLlParentByUserId) && aLlParentByUserId.contains(targetUserId);
                        log.debug("目标人及其所有上级权限检查：上级用户列表={}, 结果={}", aLlParentByUserId, result);
                    }
                    break;
                case 目标人及其所有下级:
                    if (CollectionUtil.isEmpty(businessUserIdList)) {
                        result = false;
                        log.debug("目标人及其所有下级权限检查：业务用户列表为空，结果=false");
                    } else {
                        List<Long> lowerAndSelf = userMapper.getLowerAndSelfByUserIdList(businessUserIdList);
                        result = CollectionUtil.isNotEmpty(lowerAndSelf) && lowerAndSelf.contains(targetUserId);
                        log.debug("目标人及其所有下级权限检查：下级用户列表={}, 结果={}", lowerAndSelf, result);
                    }
                    break;
                case 财务:
                    result = Optional.ofNullable(UserCache.getUser(targetUserId)).map(User::getRoleId).map(roleId -> {
                        if (Func.isNotBlank(roleId) && Func.toLongList(roleId).contains(RoleIdConstant.FINANCE_ROLE_ID)) {
                            return true;
                        }
                        return false;
                    }).orElse(false);
                    log.debug("财务权限检查：结果={}", result);
                    break;
                case 高层:
                    result = Optional.ofNullable(UserCache.getUser(targetUserId)).map(User::getRoleId).map(roleId -> {
                        if (Func.isNotBlank(roleId) && Func.toLongList(roleId).contains(RoleIdConstant.MANAGER_ROLE_ID)) {
                            return true;
                        }
                        return false;
                    }).orElse(false);
                    log.debug("高层权限检查：结果={}", result);
                    break;
                case 部门领导:
                    // 包含 user的deptId 和userDept的deptId
                    String currentUserDeptId = AuthUtil.getDeptId();
                    log.debug("部门领导权限检查开始，当前用户部门ID: {}", currentUserDeptId);

                    if (Func.isNotBlank(currentUserDeptId)) {
                        try {
                            // 确保deptId是有效的（可以包含逗号分隔的多个ID）
                            String[] deptIds = currentUserDeptId.split(",");
                            boolean hasValidDeptId = false;
                            for (String deptId : deptIds) {
                                if (Func.isNotBlank(deptId.trim()) && deptId.trim().matches("\\d+")) {
                                    hasValidDeptId = true;
                                    break;
                                }
                            }

                            if (hasValidDeptId) {
                                List<User> leaderUsers = userMapper.getLeaderUserByDeptId(currentUserDeptId);
                                log.debug("查询到的部门领导用户列表: {}", leaderUsers != null ? leaderUsers.size() : 0);

                                result = Optional.ofNullable(leaderUsers).map(userList -> {
                                    for (User user : userList) {
                                        if (Func.isNotEmpty(user) && targetUserId.equals(user.getId())) {
                                            log.debug("找到匹配的部门领导用户: {}", user.getId());
                                            return true;
                                        }
                                    }
                                    return false;
                                }).orElse(false);
                            } else {
                                log.debug("部门ID格式无效: {}", currentUserDeptId);
                                result = false;
                            }
                        } catch (Exception e) {
                            log.error("查询部门领导时发生异常，部门ID: {}", currentUserDeptId, e);
                            result = false;
                        }
                        log.debug("部门领导权限检查：部门ID={}, 结果={}", currentUserDeptId, result);
                    } else {
                        result = false;
                        log.debug("部门领导权限检查：当前用户部门ID为空，结果=false");
                    }
                    break;
            }
        }

        log.debug("权限检查完成，最终结果: {}", result);
        return result;
    }


}
