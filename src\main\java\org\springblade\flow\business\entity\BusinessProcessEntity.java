/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 自定义流程审批中的所有业务单据任务 实体类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@TableName("business_process")
@ApiModel(value = "BusinessProcessTask对象", description = "自定义流程审批中的所有业务单据任务")
@EqualsAndHashCode(callSuper = true)
public class BusinessProcessEntity extends TenantEntity {

	/**
	 * 关联的业务表单名称
	 */
	@ApiModelProperty(value = "关联的业务表单名称")
	private String formKey;
	/**
	 * 关联的业务表单的主键id
	 */
	@ApiModelProperty(value = "关联的业务表单的主键id")
	private Long formDataId;
	/**
	 * 关联的业务表单的Code
	 */
	@ApiModelProperty(value = "关联的业务表单的Code")
	private String formDataCode;
	/**
	 * 业务分类
	 */
	@ApiModelProperty(value = "业务分类")
	private String businessCategory;
	/**
	 * 业务字段
	 */
	@ApiModelProperty(value = "业务字段")
	private String businessField;
	/**
	 * 历史节点审批人id
	 */
	@ApiModelProperty(value = "历史节点审批人id")
	private String historyAssignee;
	/**
	 * 抄送人id列表
	 */
	@ApiModelProperty(value = "抄送人id列表")
	private String processCcDelegate;
	/**
	 * 流程分类
	 */
	@ApiModelProperty(value = "流程分类")
	private Long category;
	/**
	 * 流程分类-子类
	 */
	@ApiModelProperty(value = "流程分类-子类")
	private Long subclass;
	/**
	 * 审批完成时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "审批完成时间")
	private Date approveTime;
	/**
	 * 流程实例ID
	 */
	@ApiModelProperty(value = "流程实例ID")
	private String processInstanceId;
	/**
	 * 流程ID
	 */
	@ApiModelProperty(value = "流程ID")
	private String processDefinitionId;
}
