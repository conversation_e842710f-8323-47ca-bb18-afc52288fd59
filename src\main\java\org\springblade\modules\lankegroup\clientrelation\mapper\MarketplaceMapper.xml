<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.MarketplaceMapper">


    <select id="getMarketplaceList" resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceVO">
        select id,name,num,type,status,create_name createName,status,sort from blade_marketplace mark
        <where>
            mark.is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and mark.name like CONCAT('%',#{query.name},'%')
            </if>
            <if test="status != null and status != ''">
                and mark.status = #{status}
            </if>
            <if test="query.id != null and query.id != ''">
                and market.id = #{query.id}
            </if>
            <if test="query.marketType != null and query.marketType != ''">
                and mark.type = #{query.marketType}
            </if>
            order by mark.status asc,mark.sort asc
        </where>
    </select>

    <select id="industryList" resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceVO">
        select id,name,num,status,create_name createName,status,sort from blade_marketplace mark
        <where>
            mark.is_deleted = 0
            and mark.status=0
            <if test="query.name != null and query.name != ''">
                and mark.name like CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.marketType != null and query.marketType != ''">
                and mark.type = #{query.marketType}
            </if>
            order by mark.status asc,mark.sort asc
        </where>
    </select>
    <select id="getMarketClient" resultType="java.lang.Integer">
        select  sum(market.num) from blade_profession_market market
        join blade_join_profession jo
        on jo.id = market.join_id and jo.is_deleted = 0
        join blade_profession pro
        on pro.id = jo.profession_id and pro.is_deleted = 0
        where market.market_code in (
        <foreach collection="ids" item="item" index="index" separator=",">
            #{item}
        </foreach>
        ) and market.is_deleted = 0 and pro.id = #{professionId}
    </select>
    <select id="personnelMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO">
        select CONCAT(area.counties_code,market_code) proId,area.counties_name areaName,area.counties_code areaId,market.market_code marketId,market.market_name marketName
        from blade_profession profession
                 join blade_join_profession jo on jo.profession_id = profession.id
                 join blade_profession_market market on market.join_id = jo.id
                 join blade_profession_area area on area.join_id = jo.id
        where area.is_deleted = 0 and market.is_deleted = 0 and jo.is_deleted = 0 and profession.is_deleted = 0 and
            profession.id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getMarketClientByName" resultType="java.util.Map">
        select any_value(market_id) id,any_value(market_name) name, sum(num) num
        from blade_personnel_market where personnel_id = #{personnelId} and market_id = #{marketCode} and is_deleted = 0
    </select>
    <select id="getSort" resultType="java.lang.Integer">
        select count(id) from blade_marketplace where status = 0 and is_deleted = 0
        and sort &lt;= (select sort from blade_marketplace where id = #{id} )
        <if test="null != name and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="null != type">
            and type = #{type}
        </if>
        <if test="null != status">
            and status = #{status}
        </if>
        order by sort asc
    </select>
    <select id="getMarketClientByUserId" resultType="java.lang.Integer">
        select sum(market.num )
        from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id
        where per.is_deleted = 0 and per.id = #{userId} and market.market_id = #{marketId} and market.area_id = #{areaId}
    </select>
    <select id="getMarketClientByJoinId" resultType="java.lang.Integer">
        select  sum(market.num) from blade_profession_market market
        join blade_join_profession jo
        on jo.id = market.join_id and jo.is_deleted = 0
        join blade_profession pro
        on pro.id = jo.profession_id and pro.is_deleted = 0
        where market.market_code in (
        <foreach collection="ids" item="item" index="index" separator=",">
            #{item}
        </foreach>
        ) and market.is_deleted = 0 and pro.id = #{professionId} and jo.id = #{joinId}
    </select>
    <select id="getMarketClientByPersonnelId" resultType="java.lang.Integer">

    </select>
    <select id="personnelMarketListByGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketResultByGroupVO">
        SELECT
        <if test="areaCode == null and cityCode == null">
            area.city_code code,
            any_value(area.city_name) name
        </if>
        <if test="cityCode != null and areaCode == null">
            area.counties_code code,
            any_value(area.counties_name) name
        </if>
        <if test="cityCode == null and areaCode != null">
            CONCAT(area.counties_code,market.market_code) proId,
            area.city_code cityCode,
            any_value(area.city_name) cityName,
            area.counties_code areaId,
            any_value(area.counties_name) areaName,
            market.market_code marketId,
            market.market_name marketName
        </if>
        FROM
            blade_profession profession
            JOIN blade_join_profession jo ON jo.profession_id = profession.id
            JOIN blade_profession_market market ON market.join_id = jo.id
            JOIN blade_profession_area area ON area.join_id = jo.id
        WHERE
            area.is_deleted = 0
          AND market.is_deleted = 0
          AND jo.is_deleted = 0
          AND profession.is_deleted = 0
          AND profession.id = #{id}
        <if test="cityCode != null and areaCode == null">
            AND area.city_code = #{cityCode}
        </if>
        <if test="cityCode == null and areaCode != null">
            AND area.counties_code = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaCode == null and cityCode == null">
            group by area.city_code
        </if>
        <if test="cityCode != null and areaCode == null">
            group by area.counties_code
        </if>
    </select>
</mapper>
