package org.springblade.modules.lankegroup.crm.controller;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springblade.core.log.annotation.ApiLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@RestController
@RequestMapping("/kingdee/pro")
public class ClientController {

    final static String appkey = "37f9b446a86f4e89806a5544cd6b2838";
    final static String secretKey = "908C1914C7BA9835C88D936EF9FA5106";

    /*
    "Result": [
    {
      "KeyNo": "45erpyvavmplvihjpl933ppmpisrp3lp5a",
      "StartDate": "2005-05-11",
      "Status": "存续",
      "CreditCode": "911310037744285961",
      "No": "131000000007935",
      "OperName": "常学祎",
      "Address": "廊坊市广阳区新华路193号科研办公楼实验楼1幢2层205室",
      "Name": "河北兰科网络工程集团有限公司"
    },]
    * */
    @ApiLog("企查查调用")
    @GetMapping("/selectClient")
    public JSONObject selectClient(@RequestParam String searchKey,@RequestParam Integer type) {
        String url = "http://api.qichacha.com/FuzzySearch/GetList";
        Map map = new HashMap();
        map.put("key", appkey);
        map.put("searchKey", searchKey);

        String client = getClient(url, map);
        JSONObject jsonObject = JSONObject.parseObject(client);
        if (200 == jsonObject.getInteger("Status")) {
            return jsonObject;
        }
//        修改返回文字值
        if(1 == type){
            jsonObject.put("Message", "请输入详细客户名称查询");
        }else if(2 == type){
            jsonObject.put("Message", "请输入详细渠道商名称查询");
        }else{
            jsonObject.put("Message", "请输入详细友商名称查询");
        }
        return jsonObject;
    }

    /*
    "Result": {
    "RegistCapi": "1675万元",
    "BelongOrg": "玉林市玉州区市场监督管理局",
    "OperId": "peievyv3mekpmisjihrh3lvphhshsvv39i",
    "CreditCode": "91450900753712694X",
    "EconKind": "有限责任公司（自然人投资或控股）",
    "Address": "广西玉林市玉柴坡塘工业园区铸造中心北侧",
    "UpdatedDate": "2025-06-30 02:16:32",
    "PaidUpCapitalUnit": "万",
    "Name": "广西兰科资源再生利用有限公司",
    "StartDate": "2003-07-07 00:00:00",
    "StockType": null,
    "RevokeInfo": null,
    "CheckDate": "2025-01-13 00:00:00",
    "PaidUpCapital": "1675",
    "Status": "存续（在营、开业、在册）",
    "No": "***************",
    "OperName": "戴伟平",
    "PaidUpCapitalCCY": "CNY",
    "EntType": "0",
    "RecCap": "1675万元",
    "ImageUrl": "https://image.qcc.com/logo/dc10535b4cfcfbcd03de9ff9b233dd5d.jpg",
    "RegisteredCapitalCCY": "CNY",
    "OrgNo": "75371269-4",
    "OriginalName": [
      {
        "ChangeDate": "2014-02-19",
        "Name": "广西玉林玉柴工业化工有限公司"
      }
    ],
    "EndDate": "",
    "Province": "GX",
    "TermStart": "2003-07-07 00:00:00",
    "KeyNo": "dilpryir5epva9hj395i39arr3l3is3p95",
    "TeamEnd": "",
    "RegisteredCapital": "1675",
    "RegisteredCapitalUnit": "万",
    "TermEnd": "",
    "Area": {
      "City": "玉林市",
      "County": "玉州区",
      "Province": "广西壮族自治区"
    },
    "Scope": "一般项目：非金属废料和碎屑加工处理；再生资源加工；再生资源回收（除生产性废旧金属）；固体废物治理；非金属矿物制品制造；非金属矿及制品销售；铸造用造型材料生产；铸造用造型材料销售；耐火材料生产；耐火材料销售；新型陶瓷材料销售；铸造机械制造；铸造机械销售；非金属矿物材料成型机械制造；环境保护专用设备制造；环境保护专用设备销售；专用设备制造（不含许可类专业设备制造）；机械设备销售；机械设备租赁；普通机械设备安装服务；劳务服务（不含劳务派遣）；涂料销售（不含危险化学品）；专用化学产品销售（不含危险化学品）；合成材料销售；化工产品销售（不含许可类化工产品）；润滑油销售；金属材料销售；建筑材料销售；资源再生利用技术研发；机械设备研发；新材料技术研发；农林废物资源化无害化利用技术研发；资源循环利用服务技术咨询；技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；土地使用权租赁；非居住房地产租赁。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）",
    "IsOnStock": "0",
    "StockNumber": null,
    "AreaCode": "450902"
  }
    * */
    @GetMapping("/selectInfo")
    public JSONObject selectChannel(@RequestParam String searchKey){
        String url = "https://api.qichacha.com/ECIV4/GetBasicDetailsByName";
        Map map = new HashMap();
        map.put("key", appkey);
        map.put("keyword", searchKey);
        String client = getClient(url, map);
        JSONObject jsonObject = JSONObject.parseObject(client);
        if (200 == jsonObject.getInteger("Status")) {
            return jsonObject;
        }
        // 修改返回文字值
        jsonObject.put("Message", "请输入详细客户名称查询");
        return jsonObject;
    }
    public static String getClient(String url, Map<String, String> map) {
//        log.info("调用路径" + url);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String result = "";
        CloseableHttpResponse response = null;
        try {
            URIBuilder builder = new URIBuilder(url);
            if (map != null) {
                for (String str : map.keySet()) {
                    builder.addParameter(str, map.get(str));
                }
            }
            URI uri = builder.build();
            HttpGet httpGet = new HttpGet(uri);
            //获取header
//            HttpHead reqHeader = new HttpHead();
            String[] autherHeader = RandomAuthentHeader();
            httpGet.setHeader("Token", autherHeader[0]);
            httpGet.setHeader("Timespan", autherHeader[1]);

            response = httpClient.execute(httpGet);
//            log.info("返回数据response" + response);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = EntityUtils.toString(response.getEntity(), "utf-8");
            }
        } catch (Exception e) {
//            log.info("调用错误" + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
//        log.info("返回结果：" + result);
//        out.println(result);
        return result;
    }


    /*public static void main(String[] args) {
        String url = "http://api.qichacha.com/FuzzySearch/GetList";
        Map map = new HashMap();
        map.put("key", appkey);
        map.put("searchKey", "河北兰科");

        String client = getClient(url, map);
        System.out.println(client);
    }*/

    /*public static void main(String[] args) {
        String url = "https://api.qichacha.com/ECIInfoVerify/GetInfo";
        Map map = new HashMap();
        map.put("key", appkey);
        map.put("searchKey", "河北兰科网络工程集团有限公司");

        String client = getClient(url, map);
        System.out.println(client);
    }*/

    // 获取Auth Code
    protected static final String[] RandomAuthentHeader() {
        String timeSpan = String.valueOf(System.currentTimeMillis() / 1000);
        String[] authentHeaders = new String[]{DigestUtils.md5Hex(appkey.concat(timeSpan).concat(secretKey)).toUpperCase(), timeSpan};
        return authentHeaders;
    }

}
