package org.springblade.modules.lankegroup.clientrelation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.MarketplaceDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Marketplace;
import org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

public interface MarketplaceService extends BaseService<Marketplace> {

    /**
     * 新增行业
     * @param marketplaceDTO
     * @return
     */
    R saveMarketplace(MarketplaceDTO marketplaceDTO);

    /**
     * 行业列表
     * @param marketplaceDTO
     * @return
     */
    R getMarketplaceList(MarketplaceDTO marketplaceDTO, IPage<MarketplaceVO> page,String status);

    /**
     * 行业详情
     * @param id
     * @return
     */
    R detail(String id);

    /**
     * 删除行业
     * @param id
     * @return
     */
    R deleteMarketplace(Long id);

    /**
     * 编辑行业
     * @param marketplaceDTO
     * @return
     */
    R updateMarketplace(MarketplaceDTO marketplaceDTO);

    /**
     * 修改行业状态
     * @param marketplaceDTO
     * @return
     */
    R updateMarketplaceStatus(MarketplaceDTO marketplaceDTO);

    /**
     * 客户所属行业时选择使用
     * @param marketplaceDTO
     * @return
     */
    List<MarketplaceVO> industryList(MarketplaceDTO marketplaceDTO);

    /**
     * 根据行业ID查询应有客户数量
     * @param ids
     * @return
     */
    Integer getMarketClient(List<Long> ids,Long professionId);

    /**
     * 根据行业ID查询应有客户数量
     * @param ids
     * @return
     */
    Integer getMarketClientByJoinId(List<Long> ids,Long professionId,String joinId);

    /**
     * 根据行业ID查询应有客户数量
     * @return
     */
    Integer getMarketClientByUserId(String marketId,String areaId, Long userId);

    /**
     * 查询人员各行业应有客户
     * @param ids
     * @param personnelId
     * @param joinId
     * @return
     */
    Integer getMarketClientByPersonnelId(List<Long> ids,Long personnelId,String joinId);
    /**
     * 获取市场下行业列表
     * @param marketplaceDTO
     * @return
     */
    R personnelMarketList(MarketplaceDTO marketplaceDTO);

    /**
     * 获取市场下行业列表
     * @param marketplaceDTO
     * @return
     */
    R personnelMarketListByAll(MarketplaceDTO marketplaceDTO);

    /**
     * 获取市场下行业列表
     * @param marketplaceDTO
     * @return
     */
    R personnelMarketListByGroup(MarketplaceDTO marketplaceDTO);

    Map getMarketClientByName(Long id,String personnelId);

    /**
     * 区域市场/人员管理详情
     * @param marketplaceDTO
     * @return
     */
    R marketDetailList(MarketplaceDTO marketplaceDTO);

    /**
     * 查询多个客户的详情信息
     * @param marketplaceDTO
     * @return
     */
    R marketDetail(MarketplaceDTO marketplaceDTO);

    /**
     * 获取当前ID序号
     * @param id
     * @return
     */
    R getSort(String id,String name,Integer type,Integer status);
}
