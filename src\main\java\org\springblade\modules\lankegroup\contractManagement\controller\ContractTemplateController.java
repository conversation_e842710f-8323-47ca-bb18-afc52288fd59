/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.service.IContractTemplateService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractTemplateVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 合同模板 控制器
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/template/contracttemplate")
@Api(value = "合同模板【晶莱】", tags = "合同模板接口【晶莱】")
public class ContractTemplateController extends BladeController {

    private final IContractTemplateService contractTemplateService;

    /**
     * 自定义分页 合同模板
     */
    @ApiLog("分页 合同模板")
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入contractTemplate")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "每页数量", required = true),
            @ApiImplicitParam(name = "current", value = "当前页", required = true),
            @ApiImplicitParam(name = "name", value = "名称", required = true),
    })
    public R<List<ContractTemplateVO>> page(ContractTemplateVO contractTemplate) {
        List<ContractTemplateVO> list = contractTemplateService.selectContractTemplatePage(contractTemplate);
        return R.data(list);
    }


}
