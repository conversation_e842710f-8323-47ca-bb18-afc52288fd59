/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * common_attachment表更新视图对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "AttachmentUpdateVO对象", description = "common_attachment表更新视图对象")
@EqualsAndHashCode(callSuper = true)
public class AttachmentUpdateVO extends AttachmentBaseVO {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键（重写父类的id字段，添加更新时必填的校验）
	 */
	@NotNull(message = "主键不能为空")
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键", required = true)
	private Long id;

	// 更新对象继承BaseVO的所有字段和校验规则
	// 重写了id字段，使其在更新时必填

} 