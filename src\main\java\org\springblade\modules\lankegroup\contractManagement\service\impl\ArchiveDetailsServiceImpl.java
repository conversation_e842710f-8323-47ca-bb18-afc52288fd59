/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.springblade.common.constant.FlowFormConstant;
import org.springblade.common.constant.FormNameConstant;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.utils.AesUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveDetailsMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveSystemNameMapMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveInventoryInformationService;
import org.springblade.modules.lankegroup.contractManagement.service.IProjectCollectionArchiveService;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveInventoryInformationVO;
import org.springblade.modules.lankegroup.disposition.entity.BusinessGroupDisposition;
import org.springblade.modules.lankegroup.disposition.mapper.BusinessGroupDispositionMapper;
import org.springblade.modules.lankegroup.equalProtectionKanBan.entity.EqualProtectionProjectTask;
import org.springblade.modules.lankegroup.equalProtectionKanBan.enums.EqualProtectionProjectTaskAllocationStatusEnum;
import org.springblade.modules.lankegroup.equalProtectionKanBan.service.IEqualProtectionProjectTaskService;
import org.springblade.modules.lankegroup.file.entity.FileModel;
import org.springblade.modules.lankegroup.file.service.ProjectFileService;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.enums.BusinessTypeEnum;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.project.entity.ProjectProgress;
import org.springblade.modules.lankegroup.project.entity.ProjectTeam;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectTeamMapper;
import org.springblade.modules.lankegroup.project.service.impl.ProjectTeamServiceImpl;
import org.springblade.modules.lankegroup.project.vo.ProgressEnum;
import org.springblade.modules.lankegroup.project.vo.UserProjectNodeVO;
import org.springblade.modules.lankegroup.projectKanBan.entity.ProjectCollection;
import org.springblade.modules.lankegroup.projectKanBan.service.IProjectCollectionService;
import org.springblade.modules.lankegroup.projectKanBan.service.IProjectKanBanMsgService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 归档详情 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service
public class ArchiveDetailsServiceImpl extends BaseServiceImpl<ArchiveDetailsMapper, ArchiveDetails> implements IArchiveDetailsService {
    private final IFlowService flowService;
    private final IProjectKanBanMsgService projectKanBanMsgService;

    private final ContractServiceImpl contractService;

    private final IEqualProtectionProjectTaskService equalProtectionProjectTaskService;
    private final ArchiveSystemNameMapMapper archiveSystemNameMapMapper;
    private final IProjectCollectionArchiveService iProjectCollectionArchiveService;
    private final IProjectCollectionService iProjectCollectionService;
    private final IArchiveInventoryInformationService archiveInventoryInformationService;
    private final ProjectProgressMapper projectProgressMapper;
    private final ProjectBasicMapper projectBasicMapper;
    //项目团队的mapper
    private final ProjectTeamMapper projectTeamMapper;

    //项目团队的server
    private final ProjectTeamServiceImpl projectTeamService;
    private final ControlsLogService controlsLogService;
    private final ProjectFileService projectFileService;
    private final FlowEngineService flowEngineService;

    @Autowired
    private BusinessGroupDispositionMapper businessGroupDispositionMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private IHistoricProcessService historicProcessService;

    final DecimalFormat sf = new DecimalFormat("#.00");

    public ArchiveDetailsServiceImpl(IFlowService flowService, IUserService userServiceImpl, ContractServiceImpl contractService, IEqualProtectionProjectTaskService equalProtectionProjectTaskService, ArchiveSystemNameMapMapper archiveSystemNameMapMapper, IProjectCollectionArchiveService iProjectCollectionArchiveService, IProjectCollectionService iProjectCollectionService, IArchiveInventoryInformationService archiveInventoryInformationService, ProjectProgressMapper projectProgressMapper, ProjectBasicMapper projectBasicMapper, ProjectTeamMapper projectTeamMapper, ProjectTeamServiceImpl projectTeamService, ControlsLogService controlsLogService, ProjectFileService projectFileService, IProjectKanBanMsgService projectKanBanMsgService, FlowEngineService flowEngineService) {
        this.flowService = flowService;
        this.contractService = contractService;
        this.equalProtectionProjectTaskService = equalProtectionProjectTaskService;
        this.archiveSystemNameMapMapper = archiveSystemNameMapMapper;
        this.iProjectCollectionArchiveService = iProjectCollectionArchiveService;
        this.iProjectCollectionService = iProjectCollectionService;
        this.archiveInventoryInformationService = archiveInventoryInformationService;
        this.projectProgressMapper = projectProgressMapper;
        this.projectBasicMapper = projectBasicMapper;
        this.projectTeamMapper = projectTeamMapper;
        this.projectTeamService = projectTeamService;
        this.controlsLogService = controlsLogService;
        this.projectFileService = projectFileService;
        this.projectKanBanMsgService = projectKanBanMsgService;
        this.flowEngineService = flowEngineService;
    }

    @Override
    public IPage<ArchiveDetailsVO> selectArchiveDetailsPage(IPage<ArchiveDetailsVO> page, ArchiveDetailsVO archiveDetails) {
        return page.setRecords(baseMapper.selectArchiveDetailsPage(page, archiveDetails));
    }

    @Override
    public ArchiveDetailsVO selectArchiveDetails(Map map) {
        ArchiveDetailsVO archiveDetailsVO = baseMapper.selectArchive(map);
        if (null != archiveDetailsVO && Func.isNotBlank(archiveDetailsVO.getArchiveAnnex())) {
            try {
                Base64Util.decode(archiveDetailsVO.getArchiveAnnex());
            } catch (Exception e) {
                archiveDetailsVO.setArchiveAnnex(Base64Util.encode(AesUtil.encryptBase64(archiveDetailsVO.getArchiveAnnex())));
            }
        }
        // 查询出来这个归档下的 全部的合同清单
        List<ArchiveInventoryInformation> information = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetailsVO.getId());
        List<ArchiveInventoryInformationVO> informationVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(information)) {
            for (ArchiveInventoryInformation archiveInventoryInformation : information) {
                ArchiveInventoryInformationVO archiveInventoryInformationVO = BeanUtil.copy(archiveInventoryInformation, ArchiveInventoryInformationVO.class);
                // 处理归档下的 系统名称
                archiveInventoryInformationVO.setSystemNameList(archiveSystemNameMapMapper.getSystemNameDataListByInventoryId(archiveInventoryInformationVO.getId()));
                informationVOList.add(archiveInventoryInformationVO);
            }
        }
        archiveDetailsVO.setArchiveInventoryInformation(informationVOList);
        if (archiveDetailsVO.getPaymentMethod() != null) {
            if (archiveDetailsVO.getPaymentMethod().equals("1")) {
                archiveDetailsVO.setPaymentMethodValue("一次性付款");
            }
            if (archiveDetailsVO.getPaymentMethod().equals("2")) {
                archiveDetailsVO.setPaymentMethodValue("分期付款");
            }
        }
        if (archiveDetailsVO.getContractTypeMethod() != null) {
            if (archiveDetailsVO.getContractTypeMethod().toString().equals("1") || archiveDetailsVO.getContractTypeMethod().toString().equals("4")) {
                archiveDetailsVO.setContractTypeMethodValue("信息化集成");
            }
            if (archiveDetailsVO.getContractTypeMethod().toString().equals("2")) {
                archiveDetailsVO.setContractTypeMethodValue("网盾");
            }
            if (archiveDetailsVO.getContractTypeMethod().toString().equals("3")) {
                archiveDetailsVO.setContractTypeMethodValue("其他");
            }
        }
        if (archiveDetailsVO.getSigningMode() != null && !archiveDetailsVO.getSigningMode().isEmpty()) {
            if (archiveDetailsVO.getSigningMode().equals("direct")) {
                archiveDetailsVO.setSigningModeValue("直签");
            }
            if (archiveDetailsVO.getSigningMode().equals("channel")) {
                archiveDetailsVO.setSigningModeValue("渠道");
            }
        }
//		//存放list中
        if (archiveDetailsVO.getOneCollectionPlanId() != null && archiveDetailsVO.getOneCollectionPlanId() != -1) {
            ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getOneCollectionPlanId());
            extracted(archiveDetailsVO, archive);
        }
        if (archiveDetailsVO.getTwoCollectionPlanId() != null && archiveDetailsVO.getTwoCollectionPlanId() != -1) {
            ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getTwoCollectionPlanId());
            extracted(archiveDetailsVO, archive);
        }
        if (archiveDetailsVO.getThreeCollectionPlanId() != null && archiveDetailsVO.getThreeCollectionPlanId() != -1) {
            ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getThreeCollectionPlanId());
            extracted(archiveDetailsVO, archive);
        }
        if (archiveDetailsVO.getFourCollectionPlanId() != null && archiveDetailsVO.getFourCollectionPlanId() != -1) {
            ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getFourCollectionPlanId());
            extracted(archiveDetailsVO, archive);
        }

        if (archiveDetailsVO.getProcessDefinitionId() != null && (!archiveDetailsVO.getProcessDefinitionId().equals(""))) {
            //未审批
            if (archiveDetailsVO.getStatus() == ApprovedEnum.UNAPRROVED.getIndex()) {
                if (AuthUtil.getUserId().longValue() == archiveDetailsVO.getCreateUser().longValue()) {
                    archiveDetailsVO.setWithdraw(true);
                } else {
                    HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(archiveDetailsVO.getId());
                    if (historicProcess != null) {
                        if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                            archiveDetailsVO.setApproval(true);
                        }
                    }
                }
            }
            //审批中
            if (archiveDetailsVO.getStatus() == ApprovedEnum.APPROVING.getIndex()) {
                HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(archiveDetailsVO.getId());
                if (historicProcess != null) {
                    if (historicProcess.getAssigneeId().contains(AuthUtil.getUserId().toString())) {
                        archiveDetailsVO.setApproval(true);
                    }
                    if (AuthUtil.getUserId().longValue() == archiveDetailsVO.getCreateUser().longValue() && Func.isEmpty(historicProcess.getHistoryAssignee())){
                        archiveDetailsVO.setWithdraw(true);
                    }
                }
            }

            Contract contract = contractService.getById(archiveDetailsVO.getContractId());
            if (contract.getStatus() == 1 && contract.getCreateUser().equals(AuthUtil.getUserId())) {
                archiveDetailsVO.setArchive(true);
            }
        }
        //回显上传的附件 2025-0414 update by hz
        ArchiveDetailsFiles fileList= baseMapper.countArchiveDetailsFiles(archiveDetailsVO.getId());
        if(fileList!=null){
            archiveDetailsVO.setArchiveDetailsFiles(fileList);
        }
        return archiveDetailsVO;
    }

    private void extracted(ArchiveDetailsVO archiveDetailsVO, ProjectCollectionArchive archive) {
        EstimatedCollection estimatedCollection = new EstimatedCollection();
        estimatedCollection.setEstimatedCollectionAmount(archive.getEstimatedCollectionAmount());
        estimatedCollection.setEstimatedCollectionDate(archive.getEstimatedCollectionDate());
        estimatedCollection.setExpectedPaymentPercentage(archive.getExpectedPaymentPercentage());
        estimatedCollection.setPaymentCondition(archive.getPaymentCondition());
        archiveDetailsVO.getEstimatedCollectionList().add(estimatedCollection);
    }

    @Override
    public Map saveArchive(ArchiveDetailsVO archiveDetailsVO) {
        // 不质保，没有值保期
        if (archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0)) {
            archiveDetailsVO.setWarrantyPeriod(null);
        }

        ArchiveDetails archiveDetails = new ArchiveDetails();
        //转化 归档详情实体类
        ConvertUtils.register(new DateConverter(null), java.util.Date.class);
        BeanUtils.copyProperties(archiveDetailsVO, archiveDetails);

        ProjectCollectionArchive projectCollectionArchive = new ProjectCollectionArchive();
//		去set赋值
        extractedArchive(archiveDetailsVO, projectCollectionArchive);

        List<Long> list = new ArrayList<>();
        Map<String, String> result = new HashMap<>();
//        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.ARCHIVE_KEY);
        BladeFlow flow = null;

        //  状态为审批中
//        archiveDetails.setStatus(0);
//        有id更新、无id保存
        if (Func.isEmpty(archiveDetails.getId())) {
//			去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
            extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
            //当前审批人 是 当前登录人
            Long userId = AuthUtil.getUserId();
            archiveDetails.setTaskUser(userId.toString());
            archiveDetails.setId(IdWorker.getId());
            archiveDetails.setCreateTime(DateUtil.now());
            archiveDetails.setCreateUser(userId);
            archiveDetails.setUpdateTime(DateUtil.now());
            archiveDetails.setUpdateUser(userId);

            if (save(archiveDetails)) {
                //把归档的合同清单 添加到 合同清单的小表中
                if(archiveDetailsVO.getArchiveInventoryInformation() != null && !archiveDetailsVO.getArchiveInventoryInformation().isEmpty()){
                    List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
                    for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
                        archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
                        ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
                        archiveInventoryInformationService.save(archiveInventoryInformation);
                        // 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
                        saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
                    }
                }
                //update 2025-01-14 hz  调整文件上传
                saveArchiveFiles(archiveDetailsVO.getArchiveDetailsFiles(),archiveDetails.getId());

                //启动审批流
                initCompleteTask(archiveDetails);
            }
//            }
        } else {

            //更新的话 就先去进行完成置空  添加  在进行替换
            extractedDelect(archiveDetails);
            //去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
            extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
            if (archiveDetails.getPaymentMethod().equals("2")) {
                baseMapper.update(
                        archiveDetails,
                        Wrappers.<ArchiveDetails>lambdaUpdate()
                                .set(ArchiveDetails::getOnceCollectionPlanId, null)
                                .set(ArchiveDetails::getOnceCollectionDate, null)
                                .eq(ArchiveDetails::getId, archiveDetails.getId())
                );
            }
            if (archiveDetails.getPaymentMethod().equals("1")) {
                baseMapper.update(
                        archiveDetails,
                        Wrappers.<ArchiveDetails>lambdaUpdate()
                                .set(ArchiveDetails::getOneCollectionPlanId, null)
                                .set(ArchiveDetails::getTwoCollectionPlanId, null)
                                .set(ArchiveDetails::getThreeCollectionPlanId, null)
                                .set(ArchiveDetails::getFourCollectionPlanId, null)
                                .set(ArchiveDetails::getPeriodsNumber, null)
                                .eq(ArchiveDetails::getId, archiveDetails.getId())
                );
            }
            if (archiveDetails.getPaymentMethod() == null || archiveDetailsVO.getPaymentMethod().isEmpty()) {
                updateById(archiveDetails);
            }
//			查询出来这个归档下的 全部的合同清单 然后删除了 从新添加
            List<ArchiveInventoryInformation> information = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetails.getId());
            archiveInventoryInformationService.removeBatchByIds(information);
            // 查询出来这个归档下的保测评总系统个数 对应的系统名称  保存到映射表，然后删除，重新添加
            deleteArchiveSystemNameMap(archiveDetails.getId());
//			把归档的合同清单 添加到 合同清单的小表中
            if(archiveDetailsVO.getArchiveInventoryInformation() != null && !archiveDetailsVO.getArchiveInventoryInformation().isEmpty()){
                List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
                for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
                    archiveInventoryInformationVO.setId(null);
                    archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
                    ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
                    archiveInventoryInformationService.save(archiveInventoryInformation);
                    // 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
                    saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
                }
            }
            //update 2025-01-14 hz  删除关联附件的表数据 然后 重新添加
            baseMapper.deleteArchiveFilesByArchiveId(archiveDetails.getId());
            saveArchiveFiles(archiveDetailsVO.getArchiveDetailsFiles(),archiveDetails.getId());
            //启动审批流
            initCompleteTask(archiveDetails);
        }
        result.put("contractName", archiveDetails.getName());
        if (flow != null) {
            result.put("processInstanceId", flow.getProcessInstanceId());
        } else {
            result.put("processInstanceId", archiveDetails.getProcessInstanceId());
        }
        result.put("archiveId", archiveDetails.getId().toString());


        return result;
    }

    private int saveArchiveFiles(ArchiveDetailsFiles files, Long archiveDetailsId) {
        files.setArchiveId(archiveDetailsId);
        files.setId(IdWorker.getId());
        files.setCreateTime(LocalDateTime.now());
        files.setCreateUser(AuthUtil.getUserId());
       return baseMapper.saveArchiveFiles(files);
    }

    /**
     * @param projectId      项目ID
     * @param detailsId      归合同档ID
     * @param inventoryId    合同清单信息ID
     * @param systemNameList 系统名称列表
     */
    private void saveArchiveSystemNameMap(String projectId, Long detailsId, Long inventoryId, List<ArchiveSystemNameMap> systemNameList) {
        if (CollectionUtil.isEmpty(systemNameList)) {
            return;
        }
        Long aLong = Func.isNotBlank(projectId) ? Long.valueOf(projectId) : null;
        ;
        for (ArchiveSystemNameMap model : systemNameList) {
            ArchiveSystemNameMap archiveSystemNameMap = new ArchiveSystemNameMap();
            archiveSystemNameMap.setProjectId(aLong);
            archiveSystemNameMap.setDetailsId(detailsId);
            archiveSystemNameMap.setInventoryId(inventoryId);
            archiveSystemNameMap.setSystemName(model.getSystemName());
            archiveSystemNameMap.setIsDeleted(0);
            try {
                archiveSystemNameMapMapper.insert(archiveSystemNameMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void deleteArchiveSystemNameMap(Long detailsId) {
        this.archiveSystemNameMapMapper.deleteByDetailsId(detailsId);
    }

    private void extracted(ArchiveDetailsVO archiveDetailsVO, ArchiveDetails archiveDetails, ProjectCollectionArchive projectCollectionArchive, List<Long> list) {
        //			付款方式（1一次性付款/2分期付款）
        if (archiveDetails.getPaymentMethod().equals("1")) {
            projectCollectionArchive.setEstimatedCollectionAmount(archiveDetailsVO.getContractAmount());
            projectCollectionArchive.setEstimatedCollectionDate(archiveDetails.getOnceCollectionDate());
//				去添加回款计划
            boolean save = iProjectCollectionArchiveService.save(projectCollectionArchive);
            archiveDetails.setOnceCollectionPlanId(projectCollectionArchive.getId());
        }
        if (archiveDetails.getPaymentMethod().equals("2")) {
            for (EstimatedCollection estimatedCollection : archiveDetailsVO.getEstimatedCollectionList()) {
                projectCollectionArchive.setEstimatedCollectionAmount(estimatedCollection.getEstimatedCollectionAmount());
                projectCollectionArchive.setEstimatedCollectionDate(estimatedCollection.getEstimatedCollectionDate());
                projectCollectionArchive.setExpectedPaymentPercentage(estimatedCollection.getExpectedPaymentPercentage());
                projectCollectionArchive.setPaymentCondition(estimatedCollection.getPaymentCondition());
//				去添加回款计划
                iProjectCollectionArchiveService.save(projectCollectionArchive);
                list.add(projectCollectionArchive.getId());
                projectCollectionArchive.setId(null);
            }
            if (list.size() == 1) {
                archiveDetails.setOneCollectionPlanId(list.get(0));
            }
            if (list.size() == 2) {
                archiveDetails.setOneCollectionPlanId(list.get(0));
                archiveDetails.setTwoCollectionPlanId(list.get(1));
            }
            if (list.size() == 3) {
                archiveDetails.setOneCollectionPlanId(list.get(0));
                archiveDetails.setTwoCollectionPlanId(list.get(1));
                archiveDetails.setThreeCollectionPlanId(list.get(2));
            }
            if (list.size() == 4) {
                archiveDetails.setOneCollectionPlanId(list.get(0));
                archiveDetails.setTwoCollectionPlanId(list.get(1));
                archiveDetails.setThreeCollectionPlanId(list.get(2));
                archiveDetails.setFourCollectionPlanId(list.get(3));
            }
            archiveDetails.setPeriodsNumber(archiveDetailsVO.getPeriodsNumber());
        }
    }

    private void extracted(ArchiveDetailsVO archiveDetailsVO) {
//		Contract contractServiceById = contractService.getById(archiveDetailsVO.getContractId());
        String url = "contract/" + archiveDetailsVO.getArchiveAnnex();
        FileModel fileModel = new FileModel(Long.valueOf(archiveDetailsVO.getProjectId()), Long.valueOf(ProgressEnum.合同签订.getCode()),
                "contract", null, archiveDetailsVO.getArchiveAnnexName(), url, "合同扫描件");
        projectFileService.saveFile(fileModel);
    }

    public ProjectProgress saveProjectData(ProjectBasic projectBasic, Integer code, UserProjectNodeVO userProjectNodeVO, ArchiveDetailsVO archiveDetailsVO) {
        ProjectProgress projectProgress = new ProjectProgress();
        projectProgress.setProjectId(Long.valueOf(archiveDetailsVO.getProjectId()));
        projectProgress.setCurrentProgressKey(code);
        Long userId = userProjectNodeVO.getUserId();
        String name = userProjectNodeVO.getName();
        if (code.toString().equals(ProgressEnum.合同签订.getCode().toString())) {
            userId = projectBasic.getPrincipalId();
            name = projectProgressMapper.userName(projectBasic.getPrincipalId());
        }
        projectProgress.setStageLeaderId(userId);
        projectProgress.setStageLeaderName(name);
        Long currentProgressId = code.toString().equals(ProgressEnum.合同签订.getCode().toString()) ? archiveDetailsVO.getId() : null;
        projectProgress.setCurrentProgressId(currentProgressId);
        projectProgress.setStatus(2);
        // 前人栽树，后人乘凉
        projectProgress.setCreateUser(AuthUtil.getUserId());
        projectProgress.setCreateTime(new Date());
        if (currentProgressId != null) {
            projectProgress.setUpdateUser(projectProgress.getCreateUser());
            projectProgress.setUpdateTime(projectProgress.getCreateTime());
        }
        projectProgressMapper.insert(projectProgress);

        //            添加到团队表中
        ProjectTeam projectTeam = new ProjectTeam();
        projectTeam.setProjectId(Long.valueOf(archiveDetailsVO.getProjectId()));
        projectTeam.setUserId(projectProgress.getStageLeaderId());
        projectTeam.setAdmin(1);
        projectTeam.setNodeId(Long.valueOf(projectProgress.getCurrentProgressKey()));
        projectTeam.setCreatePost(projectTeamMapper.UserPostId(projectProgress.getStageLeaderId()));
        projectTeamService.save(projectTeam);
        return projectProgress;
    }

    private void saveLog(Long pid, String msg, Long userId, Integer type) {
        LogModel log = new LogModel();
        log.setUserId(userId);
        log.setMsg(msg);
        log.setPid(pid);
        log.setType(type);
        controlsLogService.saveLog(log);
    }

    private void extracted(Long CollectionPlanId) {
        if (null == CollectionPlanId || "-1".equals(CollectionPlanId.toString())) {
            return;
        }
        ProjectCollectionArchive byId = iProjectCollectionArchiveService.getById(CollectionPlanId);
        ProjectCollection projectCollection = new ProjectCollection();
        //转化 归档详情实体类
        ConvertUtils.register(new DateConverter(null), Date.class);
        BeanUtils.copyProperties(byId, projectCollection);
//		添加进 回款计划表中
        iProjectCollectionService.save(projectCollection);
//		iProjectCollectionArchiveService.removeById(CollectionPlanId);
    }

    @Override
    public List initContractArchiveDetails() {

        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId());
        Map<String, Object> variable = variables;
        // TODO 刘源兴工作流
//        String processId = flowEngineService.getProcessId("归档审批", "filing");
//        List<BladeFlow> bladeFlowList = flowEngineService.getFLowPrediction(processId, variable);
//
//        return bladeFlowList;
        return null;
    }

    /**
     * 原流程不变，现增加一个流程：已归档后能修改系统名称（已归档后和等保看板有联系，所以不能采用先清除后创建的策略！！！）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateArchive(ArchiveDetailsVO archiveDetailsVO) {
        // 状态（0审批中/1已审批/2已驳回/3撤回）
        if (1 != archiveDetailsVO.getStatus()) {
            // 原来的正常流程
            return updateArchiveByOriginalProcess(archiveDetailsVO);
        } else {
            // 已审批只能修改系统名称
            return updateArchiveInventory(archiveDetailsVO).isSuccess();
        }
    }

    /**
     * 原修改流程
     */
    private Boolean updateArchiveByOriginalProcess(ArchiveDetailsVO archiveDetailsVO) {
        ArchiveDetails archiveDetails = new ArchiveDetails();
        List<Long> list = new ArrayList<>();
        //转化 归档详情实体类
        ConvertUtils.register(new DateConverter(null), java.util.Date.class);
        BeanUtils.copyProperties(archiveDetailsVO, archiveDetails);

        ProjectCollectionArchive projectCollectionArchive = new ProjectCollectionArchive();
        //	去set赋值
        extractedArchive(archiveDetailsVO, projectCollectionArchive);

        //更新的话 就先去进行完成置空   添加  在进行替换
        extractedDelect(archiveDetails);
        //去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
        extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
        // 查询出来这个归档下的 全部的合同清单 然后删除了 从新添加
        List<ArchiveInventoryInformation> information =
                archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetails.getId());
        boolean removeBatchByIds = archiveInventoryInformationService.removeBatchByIds(information);
        // 查询出来这个归档下的保测评总系统个数 对应的系统名称  保存到映射表，然后删除，重新添加
        deleteArchiveSystemNameMap(archiveDetails.getId());
        // 把归档的合同清单 添加到 合同清单的小表中
        List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
        for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
            archiveInventoryInformationVO.setId(null);
            archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
            ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
            archiveInventoryInformationService.save(archiveInventoryInformation);
            // 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
            saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
        }

        int update = 0;
        if (archiveDetails.getPaymentMethod().equals("2")) {
            update = baseMapper.update(
                    archiveDetails,
                    Wrappers.<ArchiveDetails>lambdaUpdate()
                            .set(ArchiveDetails::getOnceCollectionPlanId, null)
                            .set(ArchiveDetails::getOnceCollectionDate, null)
                            .set(archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0), ArchiveDetails::getWarrantyPeriod, null)
                            .eq(ArchiveDetails::getId, archiveDetails.getId())
            );
        }
        if (archiveDetails.getPaymentMethod().equals("1")) {
            update = baseMapper.update(
                    archiveDetails,
                    Wrappers.<ArchiveDetails>lambdaUpdate()
                            .set(ArchiveDetails::getOneCollectionPlanId, null)
                            .set(ArchiveDetails::getTwoCollectionPlanId, null)
                            .set(ArchiveDetails::getThreeCollectionPlanId, null)
                            .set(ArchiveDetails::getFourCollectionPlanId, null)
                            .set(ArchiveDetails::getPeriodsNumber, null)
                            .set(archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0), ArchiveDetails::getWarrantyPeriod, null)
                            .eq(ArchiveDetails::getId, archiveDetails.getId())
            );
        }
        if (archiveDetails.getPaymentMethod() == null || archiveDetailsVO.getPaymentMethod().isEmpty()) {
            return updateById(archiveDetails);
        }
        // return updateById(archiveDetails);
        return (update == 1);
    }

    /**
     * 已归档情况下，只修改系统名称
     */
    private R updateArchiveInventory(ArchiveDetailsVO archiveDetailsVO) {
        // 只有归档创建人有权限
        BladeUser user = AuthUtil.getUser();
        if (Func.hasEmpty(user, user.getUserId(), archiveDetailsVO.getCreateUser()) || !archiveDetailsVO.getCreateUser().equals(user.getUserId())) {
            return R.fail("暂无权限");
        }

        List<ArchiveInventoryInformationVO> newInformationList = archiveDetailsVO.getArchiveInventoryInformation();
        if (CollectionUtil.isEmpty(newInformationList)) {
            return R.fail("参数中未发现合同清单");
        }
        for (ArchiveInventoryInformationVO informationVO : newInformationList) {
            List<ArchiveSystemNameMap> systemNameList = informationVO.getSystemNameList();
            if (CollectionUtil.isEmpty(systemNameList)) {
                continue;
            }
            for (ArchiveSystemNameMap systemNameMap : systemNameList) {
                if (Func.isNotBlank(systemNameMap.getSystemName())) {
                    archiveSystemNameMapMapper.updateSystemNameById(systemNameMap.getId(), systemNameMap.getSystemName());
                }
            }
        }
        return R.success("操作成功");
    }


    private void extractedArchive(ArchiveDetailsVO archiveDetailsVO, ProjectCollectionArchive projectCollectionArchive) {
        //		负责人名称
        projectCollectionArchive.setName(archiveDetailsVO.getUserName());
//		负责人id
        projectCollectionArchive.setUserId(archiveDetailsVO.getUserId());
//		项目id
        projectCollectionArchive.setProjectId(archiveDetailsVO.getProjectId());
//		客户id
        projectCollectionArchive.setCustomerId(archiveDetailsVO.getCustomerId());
    }

    private void extractedDelect(ArchiveDetails archiveDetails) {
        if (archiveDetails.getOnceCollectionPlanId() != null && archiveDetails.getOnceCollectionPlanId() != -1) {
//			iProjectCollectionArchiveService.removeById(archiveDetails.getOnceCollectionPlanId());
            archiveDetails.setOnceCollectionPlanId(null);
//			archiveDetails.setOnceCollectionDate(null);
        }
        if (archiveDetails.getOneCollectionPlanId() != null && archiveDetails.getOneCollectionPlanId() != -1) {
//			iProjectCollectionArchiveService.removeById(archiveDetails.getOneCollectionPlanId());
            archiveDetails.setOneCollectionPlanId(null);
            archiveDetails.setPeriodsNumber(null);
        }
        if (archiveDetails.getTwoCollectionPlanId() != null && archiveDetails.getTwoCollectionPlanId() != -1) {
//			iProjectCollectionArchiveService.removeById(archiveDetails.getTwoCollectionPlanId());
            archiveDetails.setTwoCollectionPlanId(null);
            archiveDetails.setPeriodsNumber(null);
        }
        if (archiveDetails.getThreeCollectionPlanId() != null && archiveDetails.getThreeCollectionPlanId() != -1) {
//			iProjectCollectionArchiveService.removeById(archiveDetails.getThreeCollectionPlanId());
            archiveDetails.setThreeCollectionPlanId(null);
            archiveDetails.setPeriodsNumber(null);
        }
        if (archiveDetails.getFourCollectionPlanId() != null && archiveDetails.getFourCollectionPlanId() != -1) {
//			iProjectCollectionArchiveService.removeById(archiveDetails.getFourCollectionPlanId());
            archiveDetails.setFourCollectionPlanId(null);
            archiveDetails.setPeriodsNumber(null);
        }
    }

    @Override
    public List<ContractChange> contractChange(Long contractId) {
        return baseMapper.contractChange(contractId);
    }

    @Override
    public ArchiveDetails queryByContractId(Long contractId) {
        return baseMapper.queryByContractId(contractId);
    }

    @Override
    public boolean backups() {
        boolean save = false;
//		先把 原先的 归档表的 等保二级 等保三级 查询出来
//		然后挨个保存到 新的项目清单中
        List<ArchiveDetails> archiveDetails = baseMapper.selectAll();
        for (ArchiveDetails archiveDetail : archiveDetails) {
            //等保测评二级服务
            if (archiveDetail.getSecondaryServiceNumber() != null && !archiveDetail.getSecondaryServiceNumber().isEmpty()
                    && archiveDetail.getSecondaryServicePrice() != null && !archiveDetail.getSecondaryServicePrice().isEmpty()
                    && archiveDetail.getSecondaryServiceOrder() != null && !archiveDetail.getSecondaryServiceOrder().isEmpty()) {
                ArchiveInventoryInformation information = new ArchiveInventoryInformation();
                information.setDetailsId(archiveDetail.getId());
                information.setSecondaryServiceNumber(archiveDetail.getSecondaryServiceNumber());
                information.setSecondaryServicePrice(archiveDetail.getSecondaryServicePrice());
                information.setSecondaryServiceOrder(archiveDetail.getSecondaryServiceOrder());
                information.setGrade("3");
                save = archiveInventoryInformationService.save(information);
            }
            //等保测评三级服务
            if (archiveDetail.getTertiaryServiceNumber() != null && !archiveDetail.getTertiaryServiceNumber().isEmpty()
                    && archiveDetail.getTertiaryServicePrice() != null && !archiveDetail.getTertiaryServicePrice().isEmpty()
                    && archiveDetail.getTertiaryServiceOrder() != null && !archiveDetail.getTertiaryServiceOrder().isEmpty()
            ) {
                ArchiveInventoryInformation information = new ArchiveInventoryInformation();
                information.setDetailsId(archiveDetail.getId());
                information.setTertiaryServiceNumber(archiveDetail.getTertiaryServiceNumber());
                information.setTertiaryServicePrice(archiveDetail.getTertiaryServicePrice());
                information.setTertiaryServiceOrder(archiveDetail.getTertiaryServiceOrder());
                information.setGrade("4");
                save = archiveInventoryInformationService.save(information);
            }
        }
        return save;
    }

    @Override
    public R rollBack(Long id) {
        try {

            ArchiveDetails archiveDetails = getById(id);
            contractService.update(Wrappers.<Contract>update().lambda()
                    .set(Contract::getStatus, 7)
                    .eq(Contract::getId, archiveDetails.getContractId()));
            update(Wrappers.<ArchiveDetails>update().lambda()
                    .set(ArchiveDetails::getStatus, 3)
                    .eq(ArchiveDetails::getId, id));
            flowEngineService.deleteProcessInstance(archiveDetails.getProcessInstanceId(), "用户撤销");

            return R.success("撤回成功");
        } catch (Exception e) {
            return R.fail("撤回失败");
        }
    }

    private BigDecimal calculateTargetValue(String price, String number) {
        BigDecimal priceBd = new BigDecimal(price);
        BigDecimal numberBd = new BigDecimal(number);
        BigDecimal multiply = priceBd.multiply(numberBd);
        return multiply;
    }

    /**
     *
     */
    private void createProjectTaskData(ArchiveDetailsVO archiveDetailsVO, ProjectBasic projectBasic) {
        // 需要生成几个项目任务
        List<ArchiveInventoryInformation> list = null;
        try {
            list = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetailsVO.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 获取二级或者三级等保测评的最大次数
        long secondaryServiceMaxNumber = 0L;
        long tertiaryServiceMaxNumber = 0L;
        BigDecimal secondaryServicePrice = BigDecimal.ZERO;
        BigDecimal tertiaryServicePrice = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(list)) {
            secondaryServiceMaxNumber = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServiceNumber()) ? e.getSecondaryServiceNumber() : StringPool.ZERO).map(Integer::valueOf).reduce(Integer::sum).orElse(0);
            tertiaryServiceMaxNumber = list.stream().map(e -> Func.isNotBlank(e.getTertiaryServiceNumber()) ? e.getTertiaryServiceNumber() : StringPool.ZERO).map(Integer::valueOf).reduce(Integer::sum).orElse(0);
            secondaryServicePrice = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServicePrice()) ? calculateTargetValue(e.getSecondaryServicePrice(), e.getSecondaryServiceNumber()) : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
            tertiaryServicePrice = list.stream().map(e -> Func.isNotBlank(e.getTertiaryServicePrice()) ? calculateTargetValue(e.getTertiaryServicePrice(), e.getTertiaryServiceNumber()) : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (secondaryServiceMaxNumber < 0L) {
                secondaryServiceMaxNumber = 0L;
            }
            if (tertiaryServiceMaxNumber < 0L) {
                tertiaryServiceMaxNumber = 0L;
            }
            if (secondaryServicePrice == null) {
                secondaryServicePrice = BigDecimal.ZERO;
            }
            if (tertiaryServicePrice == null) {
                tertiaryServicePrice = BigDecimal.ZERO;
            }
        }
        BigDecimal secondaryAndTertiaryServicePrice = secondaryServicePrice.add(tertiaryServicePrice);

        Integer mAxOrder = 0;
        if (CollectionUtil.isNotEmpty(list)) {
            mAxOrder = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServiceOrder()) ? e.getSecondaryServiceOrder() : e.getTertiaryServiceOrder()).map(Integer::valueOf).max(Integer::compareTo).orElse(0);
            if (mAxOrder == null || mAxOrder < 0) {
                mAxOrder = 0;
            }
        }
//		boolean isNoHasMaxOrder = (mAxOrder == 0);
//
//
//		List<Integer> secondaryServiceList = null;
//		List<Integer> tertiaryServiceList = null;
//		List<String> expectMoneyList = null;
//		if (!isNoHasMaxOrder) {
//			secondaryServiceList = new ArrayList(Collections.nCopies(mAxOrder, 0));
//			tertiaryServiceList = new ArrayList(Collections.nCopies(mAxOrder, 0));
//			expectMoneyList = new ArrayList(Collections.nCopies(mAxOrder, StringPool.ZERO));
//			for (ArchiveInventoryInformation information : list) {
//				if (ArchiveInventoryInformationGradeEnum.等保二级.getCode().equals(information.getGrade())) {
//					getTargetArray(secondaryServiceList, information.getSecondaryServiceOrder(), mAxOrder, information.getSecondaryServiceNumber(),
//							information.getSecondaryServicePrice(), expectMoneyList);
//				}
//				if (ArchiveInventoryInformationGradeEnum.等保三级.getCode().equals(information.getGrade())) {
//					getTargetArray(tertiaryServiceList, information.getTertiaryServiceOrder(), mAxOrder, information.getTertiaryServiceNumber(),
//							information.getTertiaryServicePrice(), expectMoneyList);
//				}
//			}
//		}

        for (int i = 0; i < mAxOrder; i++) {
            EqualProtectionProjectTask model = new EqualProtectionProjectTask();
            model.setProjectId(projectBasic.getId());
            model.setProjectName(projectBasic.getProjectName());
            model.setKdProjectFid(projectBasic.getKdProjectFid());
            model.setArchiveDetailsId(archiveDetailsVO.getId());
            int iTemp = i;
            // model.setSecondaryServiceNumber(Optional.ofNullable(secondaryServiceList).map(e -> e.get(iTemp)).orElse(0).toString());
            model.setSecondaryServiceNumber(String.valueOf(secondaryServiceMaxNumber));
            model.setAllocatedSecondaryServiceNumber(StringPool.ZERO);
            // model.setTertiaryServiceNumber(Optional.ofNullable(tertiaryServiceList).map(e -> e.get(iTemp)).orElse(0).toString());
            model.setTertiaryServiceNumber(String.valueOf(tertiaryServiceMaxNumber));
            model.setAllocatedTertiaryServiceNumber(StringPool.ZERO);
            model.setProjectTaskOrder(iTemp + 1);
            model.setAllocationStatus(EqualProtectionProjectTaskAllocationStatusEnum.未分配.getCode());
            model.setAllocationTime(null);
            // model.setExpectMoney(Optional.ofNullable(expectMoneyList).map(e -> e.get(iTemp)).orElse(StringPool.ZERO));
            model.setExpectMoney(secondaryAndTertiaryServicePrice.setScale(2, RoundingMode.HALF_UP).toPlainString());
            model.setAllocatedMoney(StringPool.ZERO);
            model.setExpectAllocationTime(i == 0 ? DateUtil.now() : DateUtil.plusYears(DateUtil.parse(DateUtil.format(DateUtil.now(), "yyyy-01-01"), DateUtil.DATE_FORMAT), i));
            try {
                equalProtectionProjectTaskService.save(model);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return;
    }

    /**
     * 发起合同开启审批流程
     *
     * @param contract
     * @return
     */
    public boolean initCompleteTask(ArchiveDetails contract) {
        // 启动流程
        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId())
                .set(FlowFormConstant.PROC_INSTANCE_FORM_NAME, FormNameConstant.CONTRACTARCHIVE)
                .set(FlowFormConstant.PROC_INSTANCE_FORM_DATA_ID, contract.getId());
        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.ARCHIVE_KEY);
        // TODO 刘源兴工作流
//        BladeFlow flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
        BladeFlow flow = null;
        if (Func.isNotEmpty(flow)) {
            contract.setProcessInstanceId(flow.getProcessInstanceId());
            //反写流程id
            update(Wrappers.<ArchiveDetails>update().lambda()
                    .set(ArchiveDetails::getProcessInstanceId, flow.getProcessInstanceId())
                    .eq(ArchiveDetails::getId, contract.getId()));

            HistoricProcessEntity historicProcessEntity = new HistoricProcessEntity();
            historicProcessEntity.setPid(contract.getId());
            historicProcessEntity.setBillName(contract.getName());
            historicProcessEntity.setType(FormNameConstant.CONTRACT);
            historicProcessEntity.setTypeName("合同归档");
            historicProcessEntity.setBusinessType(BusinessTypeEnum.合同.getCode());
            historicProcessEntity.setBillsType(contractService.getById(contract.getContractId()).getContractType());

            historicProcessService.saveHitoricProcessStart(historicProcessEntity, flow.getProcessInstanceId());
            return true;
        }
        return false;
    }

    /**
     * 归档审批结束更新各种信息
     *
     * @param processInstanceId
     */
    public void updateOtherInfo(String processInstanceId) {
        Map map = new HashMap();
        map.put("processInstanceId", processInstanceId);
        ArchiveDetailsVO archiveDetailsVO = baseMapper.selectArchive(map);
        String voTaskUser = archiveDetailsVO.getTaskUser();
//			查询出来这个归档下的 全部的合同清单
        List<ArchiveInventoryInformation> information = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetailsVO.getId());
        List<ArchiveInventoryInformationVO> informationVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(information)) {
            for (ArchiveInventoryInformation archiveInventoryInformation : information) {
                informationVOList.add(BeanUtil.copy(archiveInventoryInformation, ArchiveInventoryInformationVO.class));
            }
        }
        archiveDetailsVO.setArchiveInventoryInformation(informationVOList);
        archiveDetailsVO.setStatus(1);
        archiveDetailsVO.setCreateUser(null);
        updateById(archiveDetailsVO);

//        流程走完了 然后去把 回款计划临时表中的 信息  存储到 回款计划表中
        if (archiveDetailsVO.getPaymentMethod().equals("1")) {
            extracted(archiveDetailsVO.getOnceCollectionPlanId());
        } else if (archiveDetailsVO.getPaymentMethod().equals("2")) {
            extracted(archiveDetailsVO.getOneCollectionPlanId());
            extracted(archiveDetailsVO.getTwoCollectionPlanId());
            extracted(archiveDetailsVO.getThreeCollectionPlanId());
            extracted(archiveDetailsVO.getFourCollectionPlanId());
        }
        //									添加日志
        saveLog(Long.valueOf(archiveDetailsVO.getProjectId()), "添加回款计划", Long.valueOf(archiveDetailsVO.getUserId()), 0);
//								查询一下 这条数据 是否有过了 合同签订 的id 有的话 不进行 更新操作
//								都保存完了 在项目进度表中更新一条数据  更新这个合同挂的项目的 的合同签订的 小表的id
        ProjectProgress progressId = projectProgressMapper.selcetProgressId(Long.valueOf(archiveDetailsVO.getProjectId()),
                ProgressEnum.合同签订.getCode());
        //查询出来这个项目的 所有信息
        ProjectBasic projectBasic = projectBasicMapper.selectById(archiveDetailsVO.getProjectId());
        if (progressId != null) {
            if (progressId.getCurrentProgressId() == null) {
                boolean updateProgressId = projectProgressMapper.updateProgressId(archiveDetailsVO.getId(), Long.valueOf(archiveDetailsVO.getProjectId()), ProgressEnum.合同签订.getCode());
                if (!updateProgressId) {
                    return;
                }
                UserProjectNodeVO userProjectNodeVO = new UserProjectNodeVO();

                BusinessGroupDisposition businessGroupDisposition = businessGroupDispositionMapper.getBusinessGroupDispositionByProjectGroupKingdeeId(projectBasic.getGroupId());
                if (Func.isNotEmpty(businessGroupDisposition)) {
                    userProjectNodeVO = new UserProjectNodeVO() {{
                        setUserId(businessGroupDisposition.getChargeUserId());
                        setName(Optional.ofNullable(userMapper.selectById(businessGroupDisposition.getChargeUserId())).map(User::getName).orElse(null));
                    }};
                }
                ProjectProgress projectProgress = saveProjectData(projectBasic, ProgressEnum.项目交付.getCode(), userProjectNodeVO, archiveDetailsVO);
                //					添加附件地址
                extracted(archiveDetailsVO);
                //先去把项目基本信息表的 这个项目 的 状态给更新了
                projectBasic.setScheduleId(Long.valueOf(projectProgress.getCurrentProgressKey()));
                projectBasic.setPushDown(1);
                // 更新成交概率为100% 2024-04-19
                projectBasic.setTransactionProbability(6);
                projectBasic.setTransactionProbabilityName("100%");
                //项目的 进度状态给更新了
                int updateById = projectBasicMapper.updateById(projectBasic);
                if (updateById != 1) {
                    return;
                }
//										添加日志
                saveLog(Long.valueOf(archiveDetailsVO.getProjectId()), "项目进度更新至项目交付阶段", Long.valueOf(archiveDetailsVO.getUserId()), 2);
                //金蝶中的 项目进度 对应的 进度id
//                String xmjd = ProjectXMJD.XMJD(projectBasic.getScheduleId().toString());
                //下面就是 到 金蝶 去更新
//                boolean b = projectProgressMapper.updateXmjd(projectBasic.getKdProjectFid(), xmjd);
                this.projectKanBanMsgService.sendMsgProgress(Long.valueOf(archiveDetailsVO.getProjectId()));
//                if (!b) {
//                    return false;
//                }
            }
        } else {
//								查询一下 这个项目 是否已经过了项目签订 阶段 但是 销售合同归档了  这样的话 就从新写入一条数据
//									合同签订阶段之后的阶段起合同，合同归档了，再将合同信息同步到合同签订阶段，阶段状态为已完成
            UserProjectNodeVO userProjectNodeVO = new UserProjectNodeVO();
            userProjectNodeVO.setUserId(projectBasic.getPrincipalId());
            userProjectNodeVO.setName(Optional.ofNullable(userMapper.selectById(projectBasic.getPrincipalId())).map(User::getName).orElse(null));
            saveProjectData(projectBasic, ProgressEnum.合同签订.getCode(), userProjectNodeVO, archiveDetailsVO);
            //					添加附件地址
            extracted(archiveDetailsVO);
//									添加日志
            saveLog(Long.valueOf(archiveDetailsVO.getProjectId()), "完成合同归档", Long.valueOf(archiveDetailsVO.getUserId()), 2);
        }

        // 是否是等保测评项目，若是，插入等保看板表
//        if (projectBasic.getGroupId().equals(100037L)) {
//            // 等保看板项目任务数据创建
//            createProjectTaskData(archiveDetailsVO, projectBasic);
//        }
    }

}
