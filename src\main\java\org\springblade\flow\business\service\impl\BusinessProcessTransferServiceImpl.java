/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springblade.flow.business.entity.BusinessProcessTransferEntity;
import org.springblade.flow.business.mapper.BusinessProcessTransferMapper;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.business.service.IBusinessProcessTransferService;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 自定义流程审批中的所有业务单据任务 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
@RequiredArgsConstructor
public class BusinessProcessTransferServiceImpl extends ServiceImpl<BusinessProcessTransferMapper, BusinessProcessTransferEntity> implements IBusinessProcessTransferService {

	private final BusinessProcessTransferMapper businessProcessTransferMapper;

	@Override
	public void saveTransferTask(String processInstanceId,String taskId, String remark) {
		BusinessProcessTransferEntity businessProcessTransferEntity = new BusinessProcessTransferEntity();
		businessProcessTransferEntity.setProcessInstanceId(processInstanceId);
		businessProcessTransferEntity.setTaskId(taskId);
		businessProcessTransferEntity.setRemark(remark);
		businessProcessTransferEntity.setCreateTime(new Date());
		businessProcessTransferEntity.setIsDeleted(0);
		businessProcessTransferMapper.insert(businessProcessTransferEntity);
	}
}
