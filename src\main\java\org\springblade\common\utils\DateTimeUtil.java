package org.springblade.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.SimpleTimeZone;

public class DateTimeUtil {

    public static String[] getCurrentMonthScope() throws Exception {
        Calendar cal = Calendar.getInstance();
        int lastDayOfMonth = cal.getActualMaximum(Calendar.DATE);
        Date date = new Date();
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int month = localDate.getMonthValue();
        String monthStr = (month < 10)? "0" + month : String.valueOf(month);
        System.out.println(monthStr + ", " + localDate.getYear());
        String begin = localDate.getYear() + "-" + monthStr + "-01 00:00:00";
        String end = localDate.getYear() + "-" + monthStr + "-" + lastDayOfMonth + " 23:59:59";


        return new String[]{begin, end};
    }

    public static LocalDateTime convertToLocalDateViaInstant(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    public static void main(String[] args) throws Exception{
        System.out.println(getCurrentMonthScope()[0]);
        System.out.println(getCurrentMonthScope()[1]);
    }


        /** * mongo 日期查询isodate * @param dateStr * @return */
        public static Date dateToISODate(String dateStr){
            //T代表后面跟着时间，Z代表UTC统一时间
            Date date = formatD(dateStr);
            SimpleDateFormat format =
                    new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            format.setCalendar(new GregorianCalendar(new SimpleTimeZone(0, "GMT")));
            String isoDate = format.format(date);
            try {
                return format.parse(isoDate);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return null;
        }

        /** 时间格式(yyyy-MM-dd HH:mm:ss) */
        public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

        public static Date formatD(String dateStr){
            return formatD(dateStr,DATE_TIME_PATTERN);
        }

        public static Date formatD(String dateStr, String format)  {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            Date ret = null ;
            try {
                ret = simpleDateFormat.parse(dateStr) ;
            } catch (ParseException e) {
                //
            }
            return ret;
        }

}
