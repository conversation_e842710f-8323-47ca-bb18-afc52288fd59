//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class SaveParam<T> extends SaveParamBase {
    T Model;

    public SaveParam(T data) {
        this.setModel(data);
    }

    public T getModel() {
        return this.Model;
    }

    public void setModel(T model) {
        this.Model = model;
    }
}

