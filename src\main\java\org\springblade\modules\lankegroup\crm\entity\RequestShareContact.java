/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏实体类
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@TableName("blade_request_share_contact")
@EqualsAndHashCode(callSuper = true)
public class RequestShareContact extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 联系人id
	*/
		private Long contactId;
	/**
	* 联系人名称
	*/
		private String contactName;
	/**
	* 申请人id
	*/
		private Long requestId;
	/**
	* 被申请人id
	*/
		private Long principalId;
	/**
	* 理由
	*/
		private String reason;
	/**
	* 排序
	*/
		private Integer sort;
	/**
	 * 状态(默认0；1同意；2拒绝)
	 */
	private  Integer status=0;


}
