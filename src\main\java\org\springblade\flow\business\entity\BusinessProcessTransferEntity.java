package org.springblade.flow.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 9:30
 */
@Data
@TableName("business_process_transfer")
@ApiModel(value = "BusinessProcessTransfer对象", description = "任务转交记录")
public class BusinessProcessTransferEntity {
	@ApiModelProperty(value = "主键")
	private Long id;

	@ApiModelProperty(value = "流程实例主键")
	private String processInstanceId;

	@ApiModelProperty(value = "任务id")
	private String taskId;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "代理人（管理员分配的审批人员）")
	private String tragetUser;

	@ApiModelProperty(value = "转交人（那个管理员转交的）")
	private String updateUser;

	@ApiModelProperty(value = "创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@ApiModelProperty(value = "转交时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	@ApiModelProperty(value = "0正常，1删除（已转交）")
	private int isDeleted;
}
