package org.springblade.flow.demo.controller;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.flow.demo.dto.FlowTestBusinessDTO;
import org.springblade.flow.demo.entity.FlowTestBusinessEntity;
import org.springblade.flow.demo.service.FlowTestBusinessService;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Liu
 * @since 2025年06月16日 15:45
 **/
@TenantIgnore
@RestController
@AllArgsConstructor
@RequestMapping("/flow/demo/testBusiness")
public class FlowTestBusinessController {

    private final FlowTestBusinessService flowTestBusinessService;


    /**
     * 新增
     */
    @PostMapping("save")
    public R saveModel(@RequestBody FlowTestBusinessDTO flowTestBusinessEntity) {
        return flowTestBusinessService.saveModle(flowTestBusinessEntity);
    }

    /**
     * 修改
     */
    @PostMapping("update")
    public R updateModel(@RequestBody FlowTestBusinessDTO flowTestBusinessEntity) {
        return flowTestBusinessService.updateModle(flowTestBusinessEntity);
    }

    /**
     * 列表
     */
    @GetMapping("list")
    public R listModel(FlowTestBusinessEntity flowTestBusinessEntity, Query query) {
        query.setDescs("id");
        return flowTestBusinessService.listModel(Condition.getPage(query), flowTestBusinessEntity);
    }

    /**
     * 详情
     */
    @GetMapping("detail")
    public R detailModel(FlowTestBusinessEntity flowTestBusinessEntity) {
        return flowTestBusinessService.detailModel(flowTestBusinessEntity);
    }


}
