package org.springblade.modules.lankegroup.clientrelation.dto;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.Marketplace;

import java.util.List;

@Data
public class MarketplaceDTO extends Marketplace {

    /**
     * 区划编码
     */
    private String areaCode;

    /**
     * 行业编码
     */
    private String marketCode;

    /**
     * 有无客户
     */
    private Integer haveOrNot;

    /**
     * 查询类型（1 个人 2 市场）
     */
    private Integer type;

    /**
     * 行业类型：1企业 2政府
     */
    private Integer marketType;

    /**
     * 类型ID
     */
    private String typeId;

    private List<String> marketCodeList;

    private List<String> areaCodeList;

    private Integer status;

    /**
     * 是否需要过滤用户 0 过滤 否则不过滤
     */
    private Integer count;

    private String code;
}
