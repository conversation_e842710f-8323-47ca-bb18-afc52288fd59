package org.springblade.modules.lankegroup.contractManagement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20 14:10
 */
@Getter
@AllArgsConstructor
public enum ContractTypeEnum {
    销售合同("0", "销售合同"),
    采购合同("1", "采购合同"),
    其他合同("2", "其他合同"),
    渠道合同("4", "渠道合同");

    private final String code;
    private final String name;

}
