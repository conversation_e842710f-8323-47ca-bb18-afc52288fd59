/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.dto.CustomerDTO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerChargeUser;
import org.springblade.modules.lankegroup.crm.vo.CustomerChargeUserVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerChargeUserService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户分配使用组织关联表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/customerCharge/customerchargeuser")
@Api(value = "客户分配使用组织关联表", tags = "客户分配使用组织关联表接口")
public class CustomerChargeUserController extends BladeController {

	private final ICustomerChargeUserService customerChargeUserService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerChargeUser")
	public R<CustomerChargeUser> detail(CustomerChargeUser customerChargeUser) {
		CustomerChargeUser detail = customerChargeUserService.getOne(Condition.getQueryWrapper(customerChargeUser));
		return R.data(detail);
	}

	/**
	 * 分页 客户分配使用组织关联表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerChargeUser")
	public R<IPage<CustomerChargeUser>> list(CustomerChargeUser customerChargeUser, Query query) {
		IPage<CustomerChargeUser> pages = customerChargeUserService.page(Condition.getPage(query), Condition.getQueryWrapper(customerChargeUser));
		return R.data(pages);
	}

	/**
	 * 自定义分页 客户分配使用组织关联表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerChargeUser")
	public R<IPage<CustomerChargeUserVO>> page(CustomerChargeUserVO customerChargeUser, Query query) {
		IPage<CustomerChargeUserVO> pages = customerChargeUserService.selectCustomerChargeUserPage(Condition.getPage(query), customerChargeUser);
		return R.data(pages);
	}

	/**
	 * 新增 客户分配使用组织关联表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerChargeUser")
	public R save(@Valid @RequestBody CustomerChargeUser customerChargeUser) {
		return R.status(customerChargeUserService.save(customerChargeUser));
	}

	/**
	 * 修改 客户分配使用组织关联表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerChargeUser")
	public R update(@Valid @RequestBody CustomerChargeUser customerChargeUser) {
		return R.status(customerChargeUserService.updateById(customerChargeUser));
	}

	/**
	 * 新增或修改 客户分配使用组织关联表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerChargeUser")
	public R submit(@Valid @RequestBody CustomerChargeUser customerChargeUser) {
		return R.status(customerChargeUserService.saveOrUpdate(customerChargeUser));
	}

	
	/**
	 * 删除 客户分配使用组织关联表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerChargeUserService.deleteLogic(Func.toLongList(ids)));
	}
	/***
	 * 2023-09-11
	 * 客户由单一负责人改为多人负责
	 * 故此接口用于打粮队组长及以上人员，以及公司领导在客户列表上，对客户进行负责人分配
	 * 此操作要求：客户可多选，负责人单次只能分配一个
	 */
	@PostMapping("/batchAssignmentMan")
	public R batchAssignmentMan(@RequestBody CustomerDTO customerDTO){
		return R.status(customerChargeUserService.batchAssignmentMan(customerDTO));
	}
	
}
