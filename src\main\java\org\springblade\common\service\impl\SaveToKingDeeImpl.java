package org.springblade.common.service.impl;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.kingdee.bos.webapi.sdk.*;
import org.springblade.common.enums.KingDeeFormEnum;
import org.springblade.common.enums.OrgEnum;
import org.springblade.common.service.SaveToKingDee;
import org.springblade.modules.lankegroup.kingdeeApi.KingDeeAPI;
import org.springblade.modules.lankegroup.otherpayables.entity.OtherPayables;
import org.springblade.modules.lankegroup.otherpayables.entity.kingdee.KingDeeOtherPayablesBody;
import org.springblade.modules.lankegroup.otherpayables.entity.kingdee.KingDeeOtherPayablesHead;
import org.springblade.modules.lankegroup.otherpayables.mapper.OtherPayablesMapper;
import org.springblade.modules.lankegroup.otherpayables.service.IOtherPayablesService;
import org.springblade.modules.lankegroup.outbound.entity.kingdee.OutBill;
import org.springblade.modules.lankegroup.outbound.entity.kingdee.OutBillItem;
import org.springblade.modules.lankegroup.outbound.service.IOutboundMaterialsService;
import org.springblade.modules.lankegroup.outbound.service.IOutboundService;
import org.springblade.modules.lankegroup.outbound.vo.OutboundVO;
import org.springblade.modules.lankegroup.receivable.mapper.ReceivableMapper;
import org.springblade.modules.lankegroup.reim.service.IReimService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.alibaba.excel.util.DateUtils.DATE_FORMAT_10;

@Service
public class SaveToKingDeeImpl implements SaveToKingDee {
    @Autowired
    private  IUserService userService;
    @Autowired
    private  IOtherPayablesService payablesService;
    @Autowired
    private  KingDeeAPI kingDeeAPI;
    @Autowired
    @Lazy
    private  IOutboundService outboundService;
    @Autowired
    private  IOutboundMaterialsService outboundMaterialsService;

    @Override
    public Map uploadKingDee(String formName, String formDataId) throws Exception {
        SaveParam saveParam = null;
        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FID");
        needReturnFields.add("FBillNo");
        Map result=new HashMap();
        Boolean status=false;
        String msg=null;
//       其他应付单
        if (formName.equals(KingDeeFormEnum.挂账应付单.getFormName())) {
            KingDeeOtherPayablesHead kingDeeOtherPayablesHead = otherPayablesUpload(formDataId);
            saveParam = new SaveParam(kingDeeOtherPayablesHead);
            saveParam.setNeedReturnFields(needReturnFields);
        }
//        表单id获取
        String formId = KingDeeFormEnum.getformIdByFormName(formName);
//        金蝶接口返回值接收
        SuccessEntity successEntity = null;
        System.out.println(JSON.toJSONString(saveParam));
        //            保存
        SaveResult saveResult = kingDeeAPI.save(saveParam, formId);
        status= saveResult.getResult().getResponseStatus().isIsSuccess();
        //金蝶审批完成，将金蝶单据信息反写入本地
        if (saveResult.isSuccessfully()) {
            successEntity = saveResult.getResult().getResponseStatus().getSuccessEntitys().get(0);
            //                提交
            OperatorResult submit = kingDeeAPI.submit(successEntity.getNumber(),formId);
            status=submit.getResult().getResponseStatus().isIsSuccess();
            if(!submit.getResult().getResponseStatus().isIsSuccess()){
                msg=submit.getResult().getResponseStatus().getErrors().get(0).getMessage();
            }
        }else{
            msg=saveResult.getResult().getResponseStatus().getErrors().get(0).getMessage();
        }


//        金蝶上传成功，更新本地数据
        if (status) {
//        其他应付单
            if (formName.equals(KingDeeFormEnum.挂账应付单.getFormName())) {
                OtherPayables detail = payablesService.getById(formDataId);
                detail.setKdBillId(Long.valueOf(successEntity.getId()));
                detail.setBillNo(successEntity.getNumber());
                payablesService.updateById(detail);
            }
        }
        result.put("status",status);
        result.put("msg",msg);
        if(successEntity!=null){
            result.put("billNo",successEntity.getNumber());
        }
        return result;
    }

    /**
     * 金蝶审批
     * @param formName
     * @param formDataId
     * @return
     */
    @Override
    public Map auditKingDee(String formName, String formDataId) throws Exception {
        String number="";
        String formId="";
        Map rs=new HashMap();
        Boolean status=false;
        String msg="";
        SaveParam saveParam = null;
        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FID");
        needReturnFields.add("FBillNo");
//       其他应付单
        if (formName.equals(KingDeeFormEnum.挂账应付单.getFormName())) {
            KingDeeOtherPayablesHead kingDeeOtherPayablesHead = otherPayablesUpload(formDataId);
            saveParam = new SaveParam(kingDeeOtherPayablesHead);
            saveParam.setNeedReturnFields(needReturnFields);
            formId=KingDeeFormEnum.挂账应付单.getFormId();
        }
//        出库单
        if(formName.equals(KingDeeFormEnum.出库单.getFormName())){
            OutBill outBill = outboundUpload(formDataId);
            saveParam = new SaveParam(outBill);
            saveParam.setNeedReturnFields(needReturnFields);
            formId=KingDeeFormEnum.出库单.getFormId();
        }
//        金蝶接口返回值接收
        SuccessEntity successEntity = null;
        System.out.println(JSON.toJSONString(saveParam));
        //            保存
        SaveResult saveResult = kingDeeAPI.save(saveParam, formId);
        status=saveResult.isSuccessfully();
        if (status) {
            successEntity = saveResult.getResult().getResponseStatus().getSuccessEntitys().get(0);
            number= successEntity.getNumber();
        }else{
            msg=saveResult.getResult().getResponseStatus().getErrors().get(0).getMessage();
        }
        if(!number.isEmpty()&&!number.isBlank()&&!formId.isBlank()&&!formId.isEmpty()){
            String audit = kingDeeAPI.audit(number, formId);
            OperatorResult result = JSON.parseObject(audit, OperatorResult.class);
            status=result.isSuccessfully();
            if(!status){
                msg=result.getResult().getResponseStatus().getErrors().get(0).getMessage();
            }
        }
        rs.put("status",status);
        rs.put("msg",msg);
        return rs;

    }

    /**
     * 报销单本地字段与金蝶字段对接
     *
     * @return
     */
    private KingDeeOtherPayablesHead otherPayablesUpload(String formDataId) {
        KingDeeOtherPayablesHead kingdeeHead = new KingDeeOtherPayablesHead();
//        其他应付单详情查询
        OtherPayables detail = payablesService.getById(formDataId);
//        发起人信息查询
        User createUser = userService.getById(detail.getCreateUser());
//        单据类型默认其他应付单==》QTYFD01_SYS
        kingdeeHead.FBillTypeID.put("FNumber", "QTYFD01_SYS");
//        保存/更新/审批的时候不给金蝶写日期，就不会出现结账后无法审批
        kingdeeHead.FCURRENCYID.put("FNumber", "PRE001");
//        付款组织默认兰科
        kingdeeHead.FPAYORGID.put("FNumber", OrgEnum.LK.getCode());
        kingdeeHead.FMAINBOOKSTDCURRID.put("FNumber", "PRE001");
//        采购组织默认兰科
        kingdeeHead.FPURCHASEORGID.put("FNumber", OrgEnum.LK.getCode());
        kingdeeHead.F_RWSQ__LRUSER.put("FSTAFFNUMBER", createUser.getCode());
//        往来单位类型
        kingdeeHead.setFCONTACTUNITTYPE(detail.getCorrespondentsType());
//        项目
        kingdeeHead.F_XMDA.put("FNumber",detail.getProjectCode());
//        往来单位编码
        kingdeeHead.FCONTACTUNIT.put("FNumber", detail.getCorrespondentsCode());
//       结算组织默认兰科
        kingdeeHead.FSETTLEORGID.put("FNumber", OrgEnum.LK.getCode());
//        未借款金额==总金额
        kingdeeHead.setFNOTSETTLEAMOUNTFOR(Double.valueOf(detail.getAmount()));
//        摘要==备注
        kingdeeHead.setFRemarks(detail.getRemark());
//        费用承担部门（自选）
        if(detail.getKdAssumeDeptCode()!=null && detail.getKdAssumeDeptCode()!=""){
            kingdeeHead.F_RWSQ__BM.put("FNumber",detail.getKdAssumeDeptCode());
        }
//        更新的单号
        if(detail.getBillNo()!=null && detail.getBillNo()!=""){
            kingdeeHead.setFBillNo(detail.getBillNo());
            kingdeeHead.setFID(detail.getKdBillId());
        }
//        科目
        if(detail.getSubjectCode()!=null&&detail.getSubjectCode()!=""){
            kingdeeHead.F_FYKM.put("FNumber",detail.getSubjectCode());
        }
        List<KingDeeOtherPayablesBody> FEntity = new ArrayList<>();
        KingDeeOtherPayablesBody body = new KingDeeOtherPayablesBody();
//        总金额==不含税金额==未借款金额
        body.setFTOTALAMOUNTFOR(Double.valueOf(detail.getAmount()));
        body.setFNOTAXAMOUNTFOR(Double.valueOf(detail.getAmount()));
        body.setFNOTSETTLEAMOUNTFOR_D(Double.valueOf(detail.getAmount()));
        body.setFINVOICETYPE(detail.getInvoiceType());
        body.setFCOMMENT(detail.getRemark());
        FEntity.add(body);
        kingdeeHead.setFEntity(FEntity);

        return kingdeeHead;

    }

    /**
     * 本地出库单转换为金蝶出库单接口所需参数
     *
     * @param formDataId  出库单id
     * @return
     */
    private OutBill outboundUpload(String formDataId) {
        OutboundVO outboundVO=new OutboundVO();
        Map map=new HashMap();
        map.put("outboundId",formDataId);
        OutboundVO detailById = outboundService.getDetailById(map);
        ConvertUtils.register(new DateConverter(null), java.util.Date.class);
        BeanUtils.copyProperties(detailById,outboundVO);
//        分录
        outboundVO.setOutboundMaterialsVOS(outboundMaterialsService.getMaterialsByOutId(Long.valueOf(formDataId)));
//        发起人信息查询
        User createUser = userService.getById(outboundVO.getCreateUser());
        OutBill outBill = new OutBill();
        outBill.FBillTypeID.put("FNUMBER", outboundVO.getOutStockTypeCode());
        outBill.FPickOrgId.put("FNumber", outboundVO.getOutStockOrgCode());
        outBill.F_SQR.put("FSTAFFNUMBER", createUser.getCode());
        outBill.F_XMDA.put("FNUMBER", outboundVO.getProjectCode());
        outBill.FNote = outboundVO.getRemarks();
        if(outboundVO.getKdBillId()!=null && outboundVO.getKdBillId()!=-1){
            outBill.FID=outboundVO.getKdBillId();
            outBill.FBillNo = outboundVO.getKdBillNo();
        }
//            申请库存组织固定为兰科网络集团有限公司(库存组织确定组织分隔)
        outBill.FStockOrgId.put("FNUMBER", OrgEnum.LK.getCode());
//            实际业务组织只有明鉴和兰科
        outBill.F_SJYWZZ.put("FNUMBER", outboundVO.getOutStockOrgCode());
        if(outboundVO.getProjectOpportunityId()!=null && outboundVO.getProjectOpportunityId()!=-1){
            outBill.F_MySql_opp_id= outboundVO.getProjectOpportunityId().toString();
        }
        List<OutBillItem> bodyList = new ArrayList<>();
        outboundVO.getOutboundMaterialsVOS().forEach(r -> {
            OutBillItem body = new OutBillItem();
            body.FMaterialId.put("FNumber", r.getMaterialCode());
            body.FUnitID.put("FNumber", r.getUnitCode());
            body.FQty = r.getCount();
            body.FStockId.put("FNumber", outboundVO.getStorehouseCode());
            bodyList.add(body);
        });
        outBill.FEntity.addAll(bodyList);
        return outBill;
    }
}
