/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.word;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.modules.lankegroup.contractManagement.annotation.FieldToWord;

import java.io.Serializable;

/**
 * 实验合同子表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ContractExperimentWord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同主表ID
     */
    private Long mainId;
    /**
     * 项目名称
     */
    @FieldToWord
    private String projectName;
    /**
     * 甲方提供的材料
     */
    @FieldToWord
    private String materialsProvided;
    /**
     * 乙方实验周期
     */
    @FieldToWord
    private String experimentCycle;
    /**
     * 乙方实验所需全部材料
     */
    @FieldToWord
    private String materialsRequired;
    /**
     * 付款方式：一次性付款，分期付款，其他
     */
    @FieldToWord
    private String paymentMethod;
    /**
     * 付款条件
     */
    @FieldToWord
    private String paymentTerms;


}
