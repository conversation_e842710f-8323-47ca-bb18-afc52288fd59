/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 通用附件实体类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@TableName("common_attachment")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通用附件")
public class Attachment extends BaseEntity {

	private static final long serialVersionUID = 1L;


	/**
	 * 业务类型
	 */
	private String businessType;

	/**
	 * 业务ID
	 */
	private Long businessId;

	/**
	 * 子业务类型
	 */
	private String subBusinessType;

	/**
	 * 附件名称
	 */
	@ApiModelProperty("附件名称")
	private String fileName;

	/**
	 * 附件地址
	 */
	@ApiModelProperty("附件地址")
	private String fileUrl;

	/**
	 * 附件类型
	 */
	private String fileType;

	/**
	 * 备注
	 */
	private String remark;

}
