/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收藏联系人--定时消息提醒实体类
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@TableName("blade_message_timing")
@EqualsAndHashCode(callSuper = true)
public class MessageTiming extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 提醒内容
	*/
		private String remarks;
	/**
	* 频次 day  week month
	*/
		private String frequency;
	/**
	 * 频次 list  传 一周的周几 和月的 几号
	 */
		private String frequencyTime;
	/**
	* 时间  小时 和 分钟
	*/
		private String time;
	/**
	* 定时器id
	*/
		private Long timerId;
	/**
	 * 客户联系人id
	 */
	private String customerContactId;
	/**
	 * 频次提醒状态 0.不按频次 1.按频次
	 */
	private Integer frequencyStatus;
	/**
	 * 指定时间
	 */
	private String specifiedTime;


}
