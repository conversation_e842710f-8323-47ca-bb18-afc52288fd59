package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 人员
 */
@TableName("blade_personnel")
@Data
public class Personnel extends BaseEntity {

    private Long id;

    private String userId;

    private String userName;

    private String deptId;

    private String deptName;

    /**
     * 市场ID
     */
    private String professionId;

    /**
     * 市场名称
     */
    private String professionName;

    /**
     * 应有客户
     */
    private Integer oughtClient;

    /**
     * 实有客户
     */
    private Integer actualClient;

}
