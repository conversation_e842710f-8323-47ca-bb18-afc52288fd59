<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.common.mapper.AttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="attachmentResultMap" type="org.springblade.modules.lankegroup.common.entity.Attachment">
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="sub_business_type" property="subBusinessType"/>
        <result column="file_name" property="fileName"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_type" property="fileType"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 自定义分页查询 -->
    <select id="selectAttachmentPage" resultType="org.springblade.modules.lankegroup.common.vo.AttachmentVO">
        SELECT * FROM common_attachment 
        WHERE is_deleted = 0
        <if test="query.businessType != null and query.businessType != ''">
            AND business_type LIKE CONCAT('%', #{query.businessType}, '%')
        </if>
        <if test="query.businessId != null">
            AND business_id = #{query.businessId}
        </if>
        <if test="query.subBusinessType != null and query.subBusinessType != ''">
            AND sub_business_type LIKE CONCAT('%', #{query.subBusinessType}, '%')
        </if>
        <if test="query.fileName != null and query.fileName != ''">
            AND file_name LIKE CONCAT('%', #{query.fileName}, '%')
        </if>
        <if test="query.fileUrl != null and query.fileUrl != ''">
            AND file_url LIKE CONCAT('%', #{query.fileUrl}, '%')
        </if>
        <if test="query.fileType != null and query.fileType != ''">
            AND file_type LIKE CONCAT('%', #{query.fileType}, '%')
        </if>
        <if test="query.remark != null and query.remark != ''">
            AND remark LIKE CONCAT('%', #{query.remark}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

</mapper> 