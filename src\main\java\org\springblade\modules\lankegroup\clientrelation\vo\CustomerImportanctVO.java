package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

@Data
public class CustomerImportanctVO {
     /**
      * 客户重要程度（公司重要1/部门重要2/个人重要3）
      * 业务字典客户重要程度
      */
     private String importanceKey;
     /**
      * 客户重要程度（公司重要1/部门重要2/个人重要3）
      * 业务字典客户重要程度
      */
     private String importanceValue;
     /**
      * 客户id
      */
     private Long customerId;
     /**
      * 客户名称
      */
     private String customerName;
     /**
      * 拜访次数
      */
     private Integer visitCount=0;
     /**
      * 重要程度类型中客户数
      */
     private Integer importanceNum=0;

}
