//package org.springblade.modules.lankegroup.contractManagement.listener;
//
//import lombok.AllArgsConstructor;
//import org.flowable.engine.delegate.TaskListener;
//import org.flowable.task.service.delegate.DelegateTask;
//import org.springframework.stereotype.Component;
//
//@Component
//@AllArgsConstructor
//public class CountersignListener implements TaskListener {
//    private static final long serialVersionUID = 1l;
//    @Override
//    public void notify(DelegateTask delegateTask) {
//        System.out.println("会签监听");
//        boolean pass = (Boolean) delegateTask.getVariable("pass");
//        if(pass == false){
//            delegateTask.setVariable("pass", false);
//        }else{
//            Integer complete = (Integer) delegateTask.getVariable("nrOfCompletedInstances");
//            Integer all = (Integer) delegateTask.getVariable("nrOfInstances");
//            if((complete + 1) / all == 1){
//                delegateTask.setVariable("pass", true);
//            }
//        }
//    }
//}
