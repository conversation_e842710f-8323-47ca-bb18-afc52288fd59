<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.flow.business.mapper.BusinessProcessTransferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BusinessProcessResultMap" type="org.springblade.flow.business.entity.BusinessProcessTransferEntity">
        <result column="id" property="id"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="remark" property="remark"/>
        <result column="traget_user" property="tragetUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

</mapper>
