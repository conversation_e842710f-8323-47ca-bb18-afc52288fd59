package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.PersonnelDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.*;
import org.springblade.modules.lankegroup.clientrelation.mapper.PersonnelMapper;
import org.springblade.modules.lankegroup.clientrelation.service.AreaService;
import org.springblade.modules.lankegroup.clientrelation.service.PersonnelMarketService;
import org.springblade.modules.lankegroup.clientrelation.service.PersonnelService;
import org.springblade.modules.lankegroup.clientrelation.vo.*;
import org.springblade.modules.lankegroup.crm.mapper.CustomerMapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerChargeUserService;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.*;
import static java.util.stream.Collectors.groupingBy;


@Service
@AllArgsConstructor
public class PersonnelServiceImpl extends BaseServiceImpl<PersonnelMapper, Personnel> implements PersonnelService {

    private final PersonnelMapper personnelMapper;

    private final PersonnelMarketService personnelMarketService;

    private final AreaService areaService;

    private final DeptByUserService deptByUserService;

    private final ICustomerChargeUserService customerChargeUserService;

    private final CustomerMapper customerMapper;

    private final IUserService userService;

    private final IDeptService deptService;

    @Override
    public R savePersonnel(PersonnelDTO personnel) {
        // 判断是否创建
        QueryWrapper<Personnel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", personnel.getUserId());
        queryWrapper.eq("is_deleted", 0);
        Personnel personnel1 = personnelMapper.selectOne(queryWrapper);
        if (null != personnel1) {
            return R.fail("人员已存在，请勿重复创建");
        }
        personnel.setCreateDept(Long.valueOf(AuthUtil.getDeptId().split(",")[0]));
        personnel.setCreateUser(AuthUtil.getUserId());
        personnel.setUpdateUser(AuthUtil.getUserId());
        personnel.setStatus(0);
        personnel.setIsDeleted(0);
        if (personnelMapper.insert(personnel) > 0) {
            if (null != personnel.getList() && personnel.getList().size() > 0) {
                savePersonnelExtend(personnel);
            }
            // 查询当前区域当前行业客户列表
            List<Long> customerByIdList = customerMapper.getCustomerByIdList(personnel.getList());
            if (null != customerByIdList && customerByIdList.size() > 0) {
                // 新增全部关系
                customerChargeUserService.insertMoreProfessionToChargeUser(personnel.getUserId(), customerByIdList);
            }
            Controls controls = new Controls();
            controls.setContent("新增了人员信息");
            controls.setTargetId(String.valueOf(personnel.getId()));
            controls.setUserId(String.valueOf(AuthUtil.getUserId()));
            controls.setUserName(AuthUtil.getNickName());
            controls.setCreateTime(new Date());
            areaService.saveControlsLog(controls);
            return R.success("新增人员成功");
        }
        return R.fail("新增人员失败");
    }

    @Override
    public R personnelList(IPage<PersonnelListVO> page, PersonnelDTO personnelDTO) {
        List<String> specialList = deptByUserService.getSpecialList();
        if(!specialList.contains(AuthUtil.getUserId().toString())){
            List<Long> userIdByLevel = deptByUserService.getUserIdByLevel();
            userIdByLevel.add(AuthUtil.getUserId());
            personnelDTO.setUserList(userIdByLevel);
        }
        IPage<PersonnelListVO> personnelList = personnelMapper.getPersonnelList(personnelDTO, page);
        return R.data(personnelList);
    }

    @Override
    public R updatePersonnel(PersonnelDTO personnelDTO) {
        if (personnelMapper.updateById(personnelDTO) > 0) {
            personnelMarketService.deletePersonnelMarket(personnelDTO.getId());
            savePersonnelExtend(personnelDTO);
            // 查询当前区域当前行业客户列表
            List<Long> customerByIdList = customerMapper.getCustomerByIdList(personnelDTO.getList());
//            // 删除全部关系
//            if (null != customerByIdList && customerByIdList.size() > 0) {
//                customerChargeUserService.deleteMoreProfessionToChargeUser(personnelDTO.getUserId(), customerByIdList);
//            }
//            if(null != personnelDTO.getDeleteList() && personnelDTO.getDeleteList().size() > 0){
//                List<Long> deletedCustomerByIdList = customerMapper.getCustomerByIdList(personnelDTO.getDeleteList());
//                if (null != deletedCustomerByIdList && deletedCustomerByIdList.size() > 0) {
//                    customerChargeUserService.deleteMoreProfessionToChargeUser(personnelDTO.getUserId(), deletedCustomerByIdList);
//                }
//            }
            // 新增全部关系
            customerChargeUserService.insertMoreProfessionToChargeUser(personnelDTO.getUserId(), customerByIdList);
            Controls controls = new Controls();
            controls.setContent("编辑了人员信息");
            controls.setTargetId(String.valueOf(personnelDTO.getId()));
            controls.setUserId(String.valueOf(AuthUtil.getUserId()));
            controls.setUserName(AuthUtil.getNickName());
            controls.setCreateTime(new Date());
            areaService.saveControlsLog(controls);
            return R.success("修改人员信息成功");
        }
        return R.fail("修改人员信息失败");
    }

    @Override
    public R deletePersonnel(Long id) {
        Personnel personnel = personnelMapper.selectById(id);
        if (null == personnel) {
            return R.fail("为查询得到信息");
        }
        if (personnelMapper.deleteById(id) > 0) {
//            // 删除全部关系
//            customerChargeUserService.deleteMoreProfessionToChargeUser(personnel.getUserId(), null);
//            personnelMarketService.deletePersonnelMarket(id);
            return R.success("删除人员信息成功");
        }
        return R.fail("删除人员信息失败");
    }

    @Override
    public R getUserByDept(String userId) {
        User user = userService.getById(userId);
        if (user != null) {
            // 查询部门信息
            Dept dept = deptService.getById(user.getDeptId());
            if (null == dept) {
                return R.fail("未查询到部门信息");
            }
            Map<String, Object> result = new HashMap<>();
            result.put("deptId", dept.getId());
            result.put("deptName", dept.getDeptName());
            return R.data(result);
        }
        return R.fail("未查询到人员信息");
    }

    @Override
    public R detailPersonnel(Long id) {
        Personnel personnel = baseMapper.selectById(id);
        PersonnelVO personnelVO = new PersonnelVO();
        // 判断当前登录人是否是创建人的上级
        List<Long> userIdByLevel = deptByUserService.getUserIdByLevel();
        if (userIdByLevel.contains(personnel.getCreateUser())) {
            personnelVO.setParent(true);
        }
        if (null != personnel) {
            BeanUtils.copyProperties(personnel, personnelVO);
            List<PersonnelMarketResultVO> result = new ArrayList<>();
            final List<PersonnelMarketVO> personnelMarket = personnelMarketService.getPersonnelMarketList(personnel.getId());
            if (null != personnelMarket && personnelMarket.size() > 0) {
                personnelMarket.stream().collect(groupingBy(PersonnelMarketVO::getAreaName)).entrySet().stream().forEach(obj -> {
                    PersonnelMarketResultVO personnelMarketResultVO = new PersonnelMarketResultVO();
                    personnelMarketResultVO.setAreaName(obj.getKey());
                    // 查询当前区域有多少行业
                    Integer marketCountByArea = personnelMapper.getMarketCountByArea(id, obj.getKey());
                    personnelMarketResultVO.setNum(obj.getValue().size());
                    personnelMarketResultVO.setAllNum(marketCountByArea);
                    personnelMarketResultVO.setMarketVOList(obj.getValue());
                    result.add(personnelMarketResultVO);
                });
            }
            personnelVO.setMakerList(result);
        }
        return R.data(personnelVO);
    }

    @Override
    public R industryInformation(Map map) {
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        List<ProfessionPolymerization> resultActual = new ArrayList<>();
        List<ProfessionPolymerization> resultOught = new ArrayList<>();
        Personnel personnel = personnelMapper.selectById(Long.valueOf(map.get("id").toString()));
        if (null == personnel) {
            return R.fail("未查询到数据");
        }
        List<ProfessionPolymerization> result = personnelMapper.getPersonnelMarketList(map.get("id").toString());
        return R.data(result);
    }

    @Override
    public R getAreaList(Map map) {
        // 查询当前人员对应用户id
        Personnel personnel = personnelMapper.selectById(map.get("id").toString());
        if (null == personnel) {
            return R.fail("无效数据");
        }
        List<AreaResultListVO> result = personnelMapper.getPersonnelUrbanList(map.get("id").toString());
        if(null != result && result.size() > 0){
            result.stream().forEach(area ->{
                List<AreaResultListVO> id = personnelMapper.getPersonnelCityList(map.get("id").toString(), area.getAreaCode());
                area.setChildAreaList(id);
            });
        }
        return R.data(result);
    }

    @Override
    public R getRegionalizationList(Map map) {
        List<Map> list = new ArrayList<>();
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        // 查省
        if (null == map.get("parentCode")) {
            list = personnelMapper.getProvince(map.get("id").toString());
        }
        // 查地级市
        if (null != map.get("parentCode") && 2 == map.get("parentCode").toString().length()) {
            list = personnelMapper.getCity(map.get("id").toString(), map.get("parentCode").toString());
        }
        // 查区县
        if (null != map.get("parentCode") && 4 == map.get("parentCode").toString().length()) {
            list = personnelMapper.getCounties(map.get("id").toString(), map.get("parentCode").toString());
        }
        return R.data(list);
    }

    @Override
    public R getProfessionList(Map map) {
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        // 查询人员应有行业
        List<Map> professionList = personnelMapper.getProfessionList(map.get("id").toString());
        return R.data(professionList);
    }

    @Override
    public Long getIdByUserId(Long userId) {
        return baseMapper.getIdByUserId(userId);
    }

    @Override
    public R getUserData(String id) {
        QueryWrapper<Personnel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", id);
        queryWrapper.eq("is_deleted", 0);
        List<Personnel> personnels = personnelMapper.selectList(queryWrapper);
        if(null != personnels && personnels.size() > 0){
            return R.status(true);
        }
        return R.status(false);
    }

    /**
     * 新增人员区域行业信息
     *
     * @param personnel
     */
    private void savePersonnelExtend(PersonnelDTO personnel) {
        personnel.getList().stream().forEach(obj -> {
            obj.setPersonnelId(String.valueOf(personnel.getId()));
            obj.setCreateDept(Long.valueOf(AuthUtil.getDeptId().split(",")[0]));
            obj.setCreateUser(AuthUtil.getUserId());
            obj.setUpdateUser(AuthUtil.getUserId());
            // 查询客户应有数
            Integer marketNum = personnelMarketService.getMarketNum(personnel.getProfessionId(), obj.getMarketId(), obj.getAreaId());
            obj.setNum(null == marketNum ? 0 : marketNum);
        });
        personnelMarketService.savePersonnelMarket(personnel.getList());
    }
    /**
     *根据行业id、区域id匹配区域市场下的所有人
     * @return
     */
    @Override
    public List<String> getUserIdByMarketAndDeptAndProList(String marketId, String cityId) {
        return baseMapper.getUserIdByMarketAndDeptAndProList(marketId,cityId);
    }

    @Override
    public String getProfessIdByUserId(Long userId) {
        return baseMapper.getProfessIdByUserId(userId);
    }
}

