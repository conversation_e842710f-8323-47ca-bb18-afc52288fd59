package org.springblade.modules.auth.service;

import com.alibaba.fastjson.JSON;
import org.kingdee.bos.webapi.sdk.*;
import org.springblade.modules.auth.entity.kingdee.KdEmployee;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
public class KdEmployeeService {
    public SaveResult save(KdEmployee bill) throws Exception {
        SaveParam<KdEmployee> saveParam = new SaveParam<>(bill);
        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FID");
        needReturnFields.add("FStaffNumber");
        saveParam.setNeedReturnFields(needReturnFields);
        System.out.println(JSON.toJSONString(saveParam));
        K3CloudClienter clienter = K3CloudClienterHelper.getClienter();
        boolean flag = clienter.loginByAppSecret();
        if (flag == true) {
            K3CloudApi api = new K3CloudApi(clienter.getIdentify());
            SaveResult saveResult = api.save("BD_Empinfo", saveParam);
            return saveResult;
        }

        return null;
    }

}
