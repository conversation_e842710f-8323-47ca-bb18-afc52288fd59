package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;

import java.util.List;

@Data
public class ProfessionListVO {

    private Long id;

    /**
     * 市场名称
     */
    private String professionName;

    /**
     * 负责部门ID
     */
    private String chargeDeptId;

    /**
     * 负责部门名称
     */
    private String chargeDeptName;

    /**
     * 负责人ID
     */
    private String chargeId;

    /**
     * 负责人名称
     */
    private String chargeName;

    private List<ProfessionJoinVO> joinVOList;

    /**
     * 是否是创建人上级
     */
    private boolean parent;

    private String createId;

    private String createName;

}

