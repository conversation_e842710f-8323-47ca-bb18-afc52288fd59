package org.springblade.modules.lankegroup.crm.controller;

import org.springblade.common.constant.SzhConstant;
import org.springblade.common.enums.DocumentType;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.NotifyRecord;
import org.springblade.modules.lankegroup.crm.service.ICustomerContactCollectService;
import org.springblade.modules.lankegroup.crm.service.INotifyRecordService;
import org.springblade.modules.lankegroup.crm.vo.CustomerContactCollectVO;
import org.springblade.modules.lankegroup.customerVisit.service.ICustomerVisitService;

import org.springblade.modules.lankegroup.message.dto.SystemMessageDTO;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

//@RestController
//@RequestMapping("/time")
@Component
@Async
public class ReminderTimerController {

    private final ICustomerContactCollectService customerContactCollectService;
    private final ICustomerVisitService customerVisitService;

    private final INotifyRecordService notifyRecordService;
    private final IUserService userService;
    private final SystemMessageService systemMessageService;


    public ReminderTimerController(ICustomerContactCollectService customerContactCollectService, ICustomerVisitService customerVisitService, INotifyRecordService notifyRecordService, IUserService userService, SystemMessageService systemMessageService) {
        this.customerContactCollectService = customerContactCollectService;
        this.customerVisitService = customerVisitService;
        this.notifyRecordService = notifyRecordService;
        this.userService = userService;
        this.systemMessageService = systemMessageService;
    }


    /**
     * 定时器 每天早上10.30进行跑
     * @return
     *
     * 1、首先 是需要收藏的联系人 才会需要发通知
     * 2、判断最近的拜访时间 是否超过了设置的 提醒时间
     * 3、查询是否发过了消息提醒
     *  3.1 如何没有发过消息提醒  发送消息提醒（进行记录）
     *  3.2 发过了消息提醒了 判断是否 超过了 设置的提醒时间 （超过了再次发送，没有超过就不发送）
     */
    @Scheduled(cron = "0 30 10 * * ?") //每天上午10.30触发
//    @GetMapping("/test")
    public void timer(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long timeMillis = System.currentTimeMillis();
        //查询出来 所有的 收藏的联系人信息
        List<CustomerContactCollectVO> customerContactCollectVOS = customerContactCollectService.selectCustomerContactCollect();

        for (CustomerContactCollectVO customerContactCollectVO : customerContactCollectVOS) {
            if (customerContactCollectVO.getCustomerId() == null || customerContactCollectVO.getContactsId()== null){
                continue;
            }
            User user = userService.getById(customerContactCollectVO.getCreateUser());
            if(null == user || user.getAccount() == null){
                continue;
            }
//            判断是第二天消息提醒 还是第三天的违规记录
//                查询 最近一次的消息通知 时间
            String notifyTime = notifyRecordService.selectNotify(customerContactCollectVO.getCustomerId(), customerContactCollectVO.getContactsId(), user.getAccount());
            int i = 1;
            if(null == notifyTime){
//                收藏人的创建时间
                long mTime = customerContactCollectVO.getCreateTime().getTime();
//                        当前时间 减去 最近一次的消息提醒的时间 转化为 分钟
                long mdiff = (timeMillis - mTime)/1000/60;
//                        设置的提醒时间 转换为 分钟
                long mkiff = customerContactCollectVO.getRemind() * 24 * 60 + 630;
//                前一天
                long yesterdayNotkiff = mkiff - 1440;
//                最近一次没有过消息通知  比较是否到达了前一天的 消息通知时间点
                if (mdiff > yesterdayNotkiff) {
                    // TODO: 2023/11/17  进前一天的消息提醒
                    customerContactCollectVO.setRemind(customerContactCollectVO.getRemind()- i);
                    extracted(df, timeMillis, customerContactCollectVO, user,1);
                }
            }else {
                //                查询 最近一次的消息通知 时间
                String selectNotifyStatus = notifyRecordService.selectNotifyStatus(customerContactCollectVO.getCustomerId(), customerContactCollectVO.getContactsId(), user.getAccount());

//                比较 最近一次 发送的提醒消息的时间 是否超过了 设置的 提醒记录
                long notTime = 0;
                try {
                    Date parse = df.parse(notifyTime);
                    notTime = parse.getTime();

                } catch (ParseException e) {
                    e.printStackTrace();
                }
//                        当前时间 减去 最近一次的消息提醒的时间 转化为 分钟
                long notdiff = (timeMillis - notTime)/1000/60;
//                        设置的提醒时间 转换为 分钟
                long notkiff = 24 * 60 + 630;
                if (notdiff <= notkiff && "1".equals(selectNotifyStatus)){
                    // TODO: 2023/11/17 进第三天的违规提醒
                    extracted(df, timeMillis, customerContactCollectVO, user,2);
                }else {
                    // TODO: 2023/11/17 进前一天的消息提醒
                    customerContactCollectVO.setRemind(customerContactCollectVO.getRemind()- i);
                    extracted(df, timeMillis, customerContactCollectVO, user,1);
                }
            }
        }
    }

    //    第三天的  违规记录
//    number: 1 前一天  ，2 当天
    private void extracted(SimpleDateFormat df, long timeMillis, CustomerContactCollectVO customerContactCollectVO, User user,Integer number) {
        //            判断最近的拜访时间 是否超过了设置的 提醒时间
        String visitTime = customerVisitService.selectVisitTime(customerContactCollectVO.getCustomerId(), customerContactCollectVO.getContactsId(), customerContactCollectVO.getCreateUser());
//            visitTime = "2023-05-1 14:49:53";
        //如何查询的最近的拜访时间 是空的话 再去判断 是否 发送过消息通知了，没有的话 发送 消息通知，发送过了 再去判断是否 超过了 设置的提醒时间
        if (visitTime == null || visitTime.isEmpty()){
//                      比较 当前时间 减去 收藏的创建时间  是否超过了 设置的 提醒记录
//                收藏人的创建时间
            long mTime = customerContactCollectVO.getCreateTime().getTime();
//                        当前时间 减去 最近一次的消息提醒的时间 转化为 分钟
            long mdiff = (timeMillis - mTime)/1000/60;
//                        设置的提醒时间 转换为 分钟
            long mkiff = customerContactCollectVO.getRemind() * 24 * 60 + 630;
//                拜访时间是空的 再去判断 是否到达了 设置提醒的时间
            if (mdiff>mkiff) {
                //                查询 最近一次的消息通知 时间
                String notifyTime = notifyRecordService.selectNotify(customerContactCollectVO.getCustomerId(), customerContactCollectVO.getContactsId(), user.getAccount());
//                    如何这个最近的消息通知的时间为空的话 那么就发送消息提醒 并写入消息提醒记录表中
                if (notifyTime == null || notifyTime.isEmpty()) {
                    //当前时间 减去 最近一次拜访记录的时间（分钟）
                    long diff = (timeMillis - mTime)/1000/60;
//                        写入消息通知 记录表中
                    NotifyRecord notifyRecord = new NotifyRecord();
                    notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                    notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                    notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                    notifyRecord.setAccount(user.getAccount());
                    notifyRecord.setStatus(number);
                    notifyRecordService.save(notifyRecord);

                    //当前时间 减去   近期的拜访的时间  就是多久没有拜访的时间  (转为天数)
                    long day = diff/60/24;
//                    最近一次的通知时间为空   最近一次的拜访时间 是空的话 默认通知  当前时间 - 设置的提醒的 创建时间
                    if (number == 1){
                        msg(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                    }else {
                        msg1(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        msg2(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                    }
                }else {
//                      比较 最近一次 发送的提醒消息的时间 是否超过了 设置的 提醒记录
                    long notTime = 0;
                    try {
                        Date parse = df.parse(notifyTime);
                        notTime = parse.getTime();

                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
//                        当前时间 减去 最近一次的消息提醒的时间 转化为 分钟
                    long notdiff = (timeMillis - notTime)/1000/60;
//                        设置的提醒时间 转换为 分钟
                    long notkiff = customerContactCollectVO.getRemind() * 24 * 60 + 630;
                    //                    违规记录
                    if (number == 2){
//                        设置的提醒时间 转换为 分钟
                        long notkiffTime = 24 * 60 + 630;
                        if (notdiff <= notkiffTime){
                            //                            true的话 就去发送消息提醒通知
//                            写入消息通知 记录表中
                            NotifyRecord notifyRecord = new NotifyRecord();
                            notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                            notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                            notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                            notifyRecord.setAccount(user.getAccount());
                            notifyRecord.setStatus(number);
                            notifyRecordService.save(notifyRecord);
//                          发送消息通知
                            if (number == 1){
                                msg(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            }else {
                                msg1(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                                msg2(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            }
                        }
                    }
                    if (notdiff > notkiff){
//                            true的话 就去发送消息提醒通知
//                            写入消息通知 记录表中
                        NotifyRecord notifyRecord = new NotifyRecord();
                        notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                        notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                        notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                        notifyRecord.setAccount(user.getAccount());
                        notifyRecord.setStatus(number);
                        notifyRecordService.save(notifyRecord);
//                          发送消息通知
                        if (number == 1){
                            msg(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        }else {
                            msg1(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            msg2(customerContactCollectVO.getRemind().toString(), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        }
                    }
//                            不用发送消息提醒
                }
            }
        }else {
//            不是空的话  判断是否 超过了 设置的 提醒时间
//                当前时间（10.30） 减去 最近拜访时间  是否大于 设置的 消息提醒时间（例：3天    3*24 + 10:30）  3天 4320 分钟  10：30 = 630分钟
//                3*24*60 + 630 （3天 10小时30分钟）
            long time = 0;
//                long timeMillis = 0;
            try {
                Date parse = df.parse(visitTime);
                time = parse.getTime();

            } catch (ParseException e) {
                e.printStackTrace();
            }

//                String endTime = "2023-05-19 10:30:00";
//                try {
//                    Date parse = df.parse(endTime);
//                    timeMillis = parse.getTime();
//
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                }

            //当前时间 减去 最近一次拜访记录的时间（分钟）
            long diff = (timeMillis - time)/1000/60;
            long kiff = customerContactCollectVO.getRemind() * 24 * 60 + 630;
//                true的话就是超过了提醒时间 就需要去 下一步  是否需要发送 消息
            if (diff > kiff ){
//                    第三步 先去查询 是否 已经在设置的提醒时间内 进行了提醒了
//                    没有的话 进行提醒(提醒记录 写进消息记录表中)，提醒过了就不提醒了
                String notifyTime = notifyRecordService.selectNotify(customerContactCollectVO.getCustomerId(), customerContactCollectVO.getContactsId(), user.getAccount());
//                    如何这个最近的消息通知的时间为空的话 那么就发送消息提醒 并写入消息提醒记录表中
                if (notifyTime == null || notifyTime.isEmpty()){
//                        写入消息通知 记录表中
                    NotifyRecord notifyRecord = new NotifyRecord();
                    notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                    notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                    notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                    notifyRecord.setAccount(user.getAccount());
                    notifyRecord.setStatus(number);
                    notifyRecordService.save(notifyRecord);

                    //当前时间 减去   近期的拜访的时间  就是多久没有拜访的时间  (转为天数)
                    long day = diff/60/24;
//                          发送消息通知
                    if (number == 1){
                        msg(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                    }else {
                        msg1(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        msg2(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                    }
                }else{
//                        比较 最近一次 发送的提醒消息的时间 是否超过了 设置的 提醒记录
                    long notTime = 0;
                    try {
                        Date parse = df.parse(notifyTime);
                        notTime = parse.getTime();

                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
//                        当前时间 减去 最近一次的消息提醒的时间 转化为 分钟
                    long notdiff = (timeMillis - notTime)/1000/60;
//                        设置的提醒时间 转换为 分钟
                    long notkiff = customerContactCollectVO.getRemind() * 24 * 60 + 630;
//                    违规记录
                    if (number == 2){
//                        设置的提醒时间 转换为 分钟
                        long notkiffTime = 24 * 60 + 630;
                        if (notdiff <= notkiffTime){
                            //                            true的话 就去发送消息提醒通知
//                            写入消息通知 记录表中
                            NotifyRecord notifyRecord = new NotifyRecord();
                            notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                            notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                            notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                            notifyRecord.setAccount(user.getAccount());
                            notifyRecord.setStatus(number);
                            notifyRecordService.save(notifyRecord);
                            //当前时间 减去   近期的拜访的时间  就是多久没有拜访的时间  (转为天数)
                            long day = diff/60/24;
//                          发送消息通知
                            if (number == 1){
                                msg(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            }else {
                                msg1(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                                msg2(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            }
                        }
                    }
                    if (notdiff > notkiff){
//                            true的话 就去发送消息提醒通知
//                            写入消息通知 记录表中
                        NotifyRecord notifyRecord = new NotifyRecord();
                        notifyRecord.setCustomerId(customerContactCollectVO.getCustomerId());
                        notifyRecord.setContactsId(customerContactCollectVO.getContactsId());
                        notifyRecord.setContactsName(customerContactCollectVO.getContactsName());
                        notifyRecord.setAccount(user.getAccount());
                        notifyRecord.setStatus(number);
                        notifyRecordService.save(notifyRecord);

                        //当前时间 减去   近期的拜访的时间  就是多久没有拜访的时间  (转为天数)
                        long day = diff/60/24;
//                          发送消息通知
                        if (number == 1){
                            msg(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        }else {
                            msg1(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                            msg2(String.valueOf(day), user.getAccount(), customerContactCollectVO.getContactsName(),String.valueOf(customerContactCollectVO.getId()),String.valueOf(customerContactCollectVO.getContactsId()));
                        }
                    }
//                            不用发送消息提醒
                }

            }
//                    没有超过他自己设置的 提醒的时间呢 不用去提醒
        }
    }


    public R msg(String days, String userId, String name,String targetId,String contactsId) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        String content = "";
        content = "您已@days@天没有与客户 @name@ 互动，请保持与收藏客户互动";
        content = content.replace("@days@", days);
        content = content.replace("@name@", name);
        systemMessageDTO.setMsgContent(content);
        User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, userId);
        systemMessageDTO.setMsgTitle("遗忘提醒");
        systemMessageDTO.setCrowdRelevance(String.valueOf(user.getId()));
        systemMessageDTO.setCrowdRelevanceName(user.getName());
        systemMessageDTO.setTargetId(targetId);
        systemMessageDTO.setTargetType(DocumentType.联系人管理.getType());
        systemMessageDTO.setSkipUrl("/lxr/ContactsDetails/lxrDetails&"+contactsId);
        systemMessageDTO.setPinnedStatus(1);
        systemMessageDTO.setStatus(0);
        // 企微
//        systemMessageDTO.setRoute("/lxr/ContactsDetails/lxrDetails");
//        systemMessageDTO.setUserList(Func.ofImmutableList(String.valueOf(user.getId())));
//        Map<String, Object> wxcpmsgparams = systemMessageDTO.getWxcpmsgparams();
//        wxcpmsgparams.put("type", systemMessageDTO.getTargetType());
//        wxcpmsgparams.put("id", contactsId);
        return systemMessageService.sendSystemMessage(systemMessageDTO);

    }

    public R msg1(String days, String userId, String name,String targetId,String contactsId) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        String content = "";
        content = "您已@days@天没有与客户 @name@ 互动，记录一次违规，请保持与收藏客户互动";
        content = content.replace("@days@", days);
        content = content.replace("@name@", name);
        systemMessageDTO.setMsgContent(content);
        User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, userId);
        systemMessageDTO.setMsgTitle( user.getName() + "的遗忘提醒");
        systemMessageDTO.setCrowdRelevance(String.valueOf(user.getId()));
        systemMessageDTO.setCrowdRelevanceName(user.getName());
        systemMessageDTO.setTargetId(targetId);
        systemMessageDTO.setSkipUrl("/lxr/ContactsDetails/lxrDetails&"+contactsId);
        systemMessageDTO.setTargetType("errorPerson");
        systemMessageDTO.setPinnedStatus(1);
        systemMessageDTO.setStatus(3);
        // 企微
//        systemMessageDTO.setRoute("/lxr/ContactsDetails/lxrDetails");
//        systemMessageDTO.setUserList(Func.ofImmutableList(String.valueOf(user.getId())));
//        Map<String, Object> wxcpmsgparams = systemMessageDTO.getWxcpmsgparams();
//        wxcpmsgparams.put("type", systemMessageDTO.getTargetType());
//        wxcpmsgparams.put("id", contactsId);
        return systemMessageService.sendSystemMessage(systemMessageDTO);

    }
    public R msg2(String days, String userId, String name,String targetId,String contactsId) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        String content = "";
        content = "您已@days@天没有与客户 @name@ 互动，记录一次违规，请保持与收藏客户互动";
        content = content.replace("@days@", days);
        content = content.replace("@name@", name);
        systemMessageDTO.setMsgContent(content);
        User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, userId);
        systemMessageDTO.setMsgTitle("遗忘提醒");
        systemMessageDTO.setCrowdRelevance(String.valueOf(user.getId()));
        systemMessageDTO.setCrowdRelevanceName(user.getName());
        systemMessageDTO.setTargetId(targetId);
        systemMessageDTO.setTargetType(DocumentType.联系人管理.getType());
        systemMessageDTO.setSkipUrl("/lxr/ContactsDetails/lxrDetails&"+contactsId);
        systemMessageDTO.setPinnedStatus(1);
        systemMessageDTO.setStatus(0);
        // 企微
//        systemMessageDTO.setRoute(systemMessageDTO.getSkipUrl());
//        systemMessageDTO.setUserList(Func.ofImmutableList(String.valueOf(user.getId())));
//        Map<String, Object> wxcpmsgparams = systemMessageDTO.getWxcpmsgparams();
//        wxcpmsgparams.put("type", systemMessageDTO.getTargetType());
//        wxcpmsgparams.put("id", contactsId);
        return systemMessageService.sendSystemMessage(systemMessageDTO);
    }
}
