/**
 *  Copyright (c) 2018-2028, Chill <PERSON>ang 庄骞 (<EMAIL>).
 *  <p>
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *  <p>
 *  http://www.apache.org/licenses/LICENSE-2.0
 *  <p>
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.springblade.modules.lankegroup.crm.config;

import java.util.*;

/**
 * 客户类型配置类
 * 维护客户类型与部门类型的映射关系
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public class CustomerTypeConfig {

    /**
     * 客户类型与部门类型的映射关系
     */
    private static final Map<String, List<String>> CUSTOMER_TYPE_DEPT_TYPE_MAP = new HashMap<>();

    static {
        // 大学类型：专业和课题组
        CUSTOMER_TYPE_DEPT_TYPE_MAP.put("大学", Arrays.asList("专业", "课题组"));
        
        // 医院类型：科室和课题组
        CUSTOMER_TYPE_DEPT_TYPE_MAP.put("医院", Arrays.asList("科室", "课题组"));
        
        // 默认类型：部门
        CUSTOMER_TYPE_DEPT_TYPE_MAP.put("默认", Arrays.asList("部门"));
    }

    /**
     * 根据客户类型获取对应的部门类型列表
     *
     * @param customerType 客户类型
     * @return 部门类型列表
     */
    public static List<String> getDeptTypesByCustomerType(String customerType) {
        if (customerType == null) {
            return CUSTOMER_TYPE_DEPT_TYPE_MAP.get("默认");
        }
        
        // 检查是否包含大学关键字
        if (customerType.contains("大学")|| customerType.equals("高校")) {
            return CUSTOMER_TYPE_DEPT_TYPE_MAP.get("大学");
        }
        
        // 检查是否包含医院关键字
        if (customerType.contains("医院")) {
            return CUSTOMER_TYPE_DEPT_TYPE_MAP.get("医院");
        }
        
        // 默认返回部门类型
        return CUSTOMER_TYPE_DEPT_TYPE_MAP.get("默认");
    }

    /**
     * 获取所有支持的客户类型
     *
     * @return 客户类型列表
     */
    public static Set<String> getAllCustomerTypes() {
        return CUSTOMER_TYPE_DEPT_TYPE_MAP.keySet();
    }

    /**
     * 获取所有支持的部门类型
     *
     * @return 部门类型列表
     */
    public static Set<String> getAllDeptTypes() {
        Set<String> allDeptTypes = new HashSet<>();
        for (List<String> deptTypes : CUSTOMER_TYPE_DEPT_TYPE_MAP.values()) {
            allDeptTypes.addAll(deptTypes);
        }
        return allDeptTypes;
    }
}
