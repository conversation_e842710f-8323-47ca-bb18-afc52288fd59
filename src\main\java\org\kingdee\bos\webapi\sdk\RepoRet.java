//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class RepoRet extends JsonBase {
    RepoResult Result;

    public RepoRet() {
    }

    public boolean isSuccessfully() {
        if (this.Result == null) {
            return false;
        } else {
            return this.Result.getResponseStatus() == null ? false : this.Result.getResponseStatus().isIsSuccess();
        }
    }

    public RepoResult getResult() {
        return this.Result;
    }

    public void setResult(RepoResult result) {
        this.Result = result;
    }
}
