/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springblade.common.utils.Update;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 归档详情实体类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@TableName("blade_archive_details")
@EqualsAndHashCode(callSuper = true)
public class ArchiveDetails extends BaseEntity {

	private static final long serialVersionUID = 1L;
	/**
	 *合同编号
	 */
		private String code;
	/**
	 *合同名称
	 */
		private String name;
	/**
	 * 合同id
	 */
		private Long contractId;
	/**
	* 付款方式（1一次性付款/2分期付款）
	*/
		private String paymentMethod;
	/**
	* 合同类型方式（1信息化集成/2网盾/3其他）
	*/
		private Integer contractTypeMethod;
	/**
	* 签订方式（direct直签/channel渠道）
	*/
		private String signingMode;
	/**
	* 渠道商成份额
	*/
		private String contentChannelMerchants;
	/**
	 * 渠道商id
	 */
	private String channelSupplierId;
	/**
	* 渠道商名称
	*/
		private String channelSupplierName;
	/**
	* 业绩合同金额
	*/
		private String performanceContractAmount;
	/**
	* 合同签订日期
	*/
		private String contractSigningDate;
	/**
	* 约定项目工期
	*/
		private String agreedProjectDuration;
	/**
	* 服务起始日期
	*/
		private String serviceStartDate;
	/**
	* 服务终止日期
	*/
		private String serviceEndDate;
	/**
	* 项目经理id/测评师id
	*/
//		private Long projectManagerId;
	/**
	* 项目经理姓名//测评师姓名
	*/
//		private String projectManagerName;
	/**
	* 出报告日期
	*/
//		private String reportDate;
	/**
	* 是否招投标（1是/2否）
	*/
		private Integer bid;
	/**
	* 采购代理机构名称
	*/
		private String procurementAgencyName;
	/**
	* 采购代理机构联系方式
	*/
		private String procurementAgencyContactInformation;
	/**
	* 招标编号
	*/
		private String biddingNo;
	/**
	* 开标日期
	*/
		private String bidOpeningDate;
	/**
	* 中标日期
	*/
		private String bidWinningDate;
	/**
	* 领取中标通知书日期
	*/
		private String noticeDate;
	/**
	* 项目进度收入
	*/
		private String projectProgressIncome;
	/**
	* 技术服务费
	*/
		private String technicalServiceFee;
	/**
	* 等保测评二级服务系统个数
	*/
		private String secondaryServiceNumber;
	/**
	* 等保测评二级服务单价
	*/
		private String secondaryServicePrice;
	/**
	 * 等保测评二级服务次数
	 */
	private String secondaryServiceOrder;
	/**
	* 等保测评三级服务系统个数
	*/
		private String tertiaryServiceNumber;
	/**
	* 等保测评三级服务单价
	*/
		private String tertiaryServicePrice;
	/**
	 * 等保测评三级服务次数
	 */
	private String tertiaryServiceOrder;
	/**
	* 合同管理员留存份数
	*/
		private Integer number;
	/**
	* 备注
	*/
		private String remarks;
	/**
	 * 当前审批人
	 */
		private String taskUser;
	/**
	* 驳回原因
	*/
		private String rejectCause;
	/**
	* 流程定义主键
	*/
		private String processDefinitionId;
	/**
	* 流程实例主键
	*/
		private String processInstanceId;

	/**
	 *付款条件
	 */
	private String paymentTerm;

	/**
	 * 期数
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String periodsNumber;
	/**
	 * 一次性付款 回款计划id
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long onceCollectionPlanId;
	/**
	 * 一次性预计回款 日期
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String onceCollectionDate;
	/**
	 * 分期付款 一期回款计划id
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long oneCollectionPlanId;
	/**
	 * 分期付款 二期回款计划id
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long twoCollectionPlanId;
	/**
	 * 分期付款 三期回款计划id
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long threeCollectionPlanId;
	/**
	 * 分期付款 四期回款计划id
	 */
//	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long fourCollectionPlanId;
	/**
	 * 附件地址
	 */
	private String archiveAnnex;
	/**
	 * 附件名称
	 */
	private String archiveAnnexName;
	/**
	 * 质保期
	 */
	private Integer warrantyPeriod;

	/**
	 * 是否包含质保
	 */
	private Integer isWarranty;
}
