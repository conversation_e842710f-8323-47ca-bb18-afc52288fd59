/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.mapper;

import org.springblade.modules.auth.entity.RoleDept;
import org.springblade.modules.auth.vo.RoleDeptVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import java.util.Map;

/**
 * 角色与部门的对照表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface RoleDeptMapper extends BaseMapper<RoleDept> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param roleDept
	 * @return
	 */
	List<RoleDeptVO> selectRoleDeptPage(IPage page, RoleDeptVO roleDept);

	/**
	 * 根据部门id获取部门对应的角色id
	 */
	List<Long> roleSelectByDeptId(Map map);

}
