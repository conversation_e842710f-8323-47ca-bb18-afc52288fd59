# 通用数据权限使用指南（简化版）

本文档说明如何在业务模块中使用简化版的通用数据权限控制方案。

## 概述

这套数据权限方案基于用户级别和负责人字段进行权限控制，**极简设计**：

- **高层、财务**：查看所有数据
- **部门领导**：查看本部门及下属部门的数据  
- **普通员工**：查看自己负责的数据和下级数据（可选）

## 核心特点

### ✅ 极简使用
- **一行代码**完成权限设置
- **一行XML**完成SQL权限控制
- **只需3个字段**：targetUserLevel、allowedUserIds、dataAttribute

## 使用步骤

### 步骤1：在VO对象中添加权限字段

```java
public class YourBusinessVO {
    // ... 其他业务字段

    // 数据权限相关字段（只需要这3个）
    private Integer targetUserLevel;      // 用户级别
    private List<Long> allowedUserIds;    // 允许查看的用户ID列表（null=无限制）
    private String dataAttribute;         // 数据属性（如"sub"表示查看下级）
}
```

### 步骤2：在Service中使用DataPermissionManager

```java
@Service
@AllArgsConstructor
public class YourBusinessServiceImpl implements IYourBusinessService {
    
    private final DataPermissionManager dataPermissionManager;
    
    @Override
    public IPage<YourBusinessVO> selectPage(IPage<YourBusinessVO> page, YourBusinessVO query) {
        // 一行代码完成权限设置
        dataPermissionManager.applyDataPermission(query, query.getDataAttribute());
        
        // 执行查询
        List<YourBusinessVO> records = baseMapper.selectPage(page, query);
        return page.setRecords(records);
    }
}
```

### 步骤3：在Mapper.xml中使用SQL片段

#### 单个负责人字段
```xml
<select id="selectPage" resultType="com.example.vo.YourBusinessVO">
    SELECT * FROM your_table 
    WHERE is_deleted = 0
    <!-- 其他业务查询条件 -->
    
    <!-- 一行代码完成数据权限控制 -->
    <include refid="DataPermissionSql.dataPermissionCondition">
        <property name="ownerField" value="responsible_user_id"/>
    </include>
    
    ORDER BY create_time DESC
</select>
```

#### 多个负责人字段
```xml
<select id="selectPage" resultType="com.example.vo.YourBusinessVO">
    SELECT * FROM your_table 
    WHERE is_deleted = 0
    
    <!-- 支持多个负责人字段的权限控制 -->
    <include refid="DataPermissionSql.multiFieldDataPermissionCondition">
        <property name="ownerFields" value="responsible_user_id,create_user"/>
    </include>
    
    ORDER BY create_time DESC
</select>
```

## 使用示例

### 报价单模块示例
```java
// QuotationServiceImpl.java
@Override
public IPage<QuotationVO> selectQuotationPage(IPage<QuotationVO> page, QuotationVO quotation) {
    // 一行代码完成权限设置
    dataPermissionManager.applyDataPermission(quotation, quotation.getDataAttribute());
    
    List<QuotationVO> records = baseMapper.selectQuotationPage(page, quotation);
    return page.setRecords(records);
}
```

```xml
<!-- QuotationMapper.xml -->
<select id="selectQuotationPage" resultType="org.springblade.modules.lankegroup.jl.vo.QuotationVO">
    SELECT * FROM jl_quotation 
    WHERE is_deleted = 0
    <!-- 其他查询条件... -->
    
    <!-- 一行代码完成权限控制 -->
    <include refid="DataPermissionSql.dataPermissionCondition">
        <property name="ownerField" value="quotation_user_id"/>
    </include>
    
    ORDER BY create_time DESC
</select>
```

## 权限级别说明

| 用户级别 | 代码 | allowedUserIds | 权限范围 |
|---------|------|---------------|----------|
| 高层    | 1    | null | 查看所有数据 |
| 财务    | 2    | null | 查看所有数据 |
| 部门领导 | 3    | [部门用户ID列表] | 查看本部门及下属部门数据 |
| 普通员工 | 4    | [自己的ID, 下级ID...] | 查看自己负责的数据 |

## 权限逻辑说明

### 自动权限计算
`DataPermissionManager.applyDataPermission()` 会自动：

1. **获取当前用户级别**
2. **计算允许查看的用户ID列表**：
   - 高层/财务：`allowedUserIds = null`（无限制）
   - 部门领导：`allowedUserIds = [部门所有用户ID]`
   - 普通员工：`allowedUserIds = [自己ID + 下级ID]`
3. **设置到VO对象**

### SQL权限过滤
SQL片段会自动：

1. **高层/财务**：不添加任何WHERE条件
2. **其他级别**：添加 `WHERE 负责人字段 IN (allowedUserIds)`
3. **无权限时**：添加 `WHERE 1=0`（不返回任何数据）

## 特殊功能

### 查看下级数据
```java
// 设置dataAttribute = "sub"，普通员工可以查看下级数据
quotationVO.setDataAttribute("sub");
dataPermissionManager.applyDataPermission(quotationVO, "sub");
```

### 多负责人字段支持
```xml
<!-- 同时基于多个字段进行权限控制 -->
<include refid="DataPermissionSql.multiFieldDataPermissionCondition">
    <property name="ownerFields" value="quotation_user_id,responsible_user_id,create_user"/>
</include>
```

## 最佳实践

### 1. 统一集成方式
所有业务模块都使用相同的集成方式：
```java
// Service中
dataPermissionManager.applyDataPermission(queryVO, queryVO.getDataAttribute());

// Mapper.xml中
<include refid="DataPermissionSql.dataPermissionCondition">
    <property name="ownerField" value="your_owner_field"/>
</include>
```

### 2. 字段命名规范
建议负责人字段使用统一命名：`xxx_user_id`

### 3. 性能优化
为负责人字段建立索引：
```sql
CREATE INDEX idx_owner_user_id ON your_table(owner_user_id);
```

## 与原方案对比

### 简化前
```java
// 复杂的配置类和多个方法调用
DataPermissionConfig config = dataPermissionManager.buildDataPermissionConfig(dataAttribute);
dataPermissionManager.setDataPermissionToVO(quotationVO, config);

// VO中需要很多字段
private Integer targetUserLevel;
private Long currentUserId;
private List<Long> deptUserIds;
private List<Long> allowedUserIds;
private String dataAttribute;
```

### 简化后
```java
// 一行代码完成
dataPermissionManager.applyDataPermission(quotationVO, dataAttribute);

// VO中只需3个字段
private Integer targetUserLevel;
private List<Long> allowedUserIds;
private String dataAttribute;
```

## 总结

简化版方案的优势：

1. **✅ 使用更简单**：一行代码完成权限设置
2. **✅ 字段更少**：VO中只需3个权限字段
3. **✅ 逻辑更清晰**：权限计算逻辑集中在一个方法中
4. **✅ 性能更好**：减少了不必要的字段和计算
5. **✅ 维护更容易**：代码更简洁，易于理解和维护 