package org.springblade.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 出库单单据类型
 *     OTHER("其他出库","QTCKD01_SYS"),<br>
 *     GIFT("礼品领用","QTCKD06_SYS"),<br>
 *     MATERIAL("材料领用","QTCKD07_SYS");<br>
 */
@Getter
public enum OutStockBillType {
    OTHER("其他出库","QTCKD01_SYS"),
    GIFT("礼品领用","QTCKD07"),
    FEE("费用出库","QTCKD13"),
    MATERIAL("材料领用","QTCKD06_SYS");

    // get set 方法
    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private OutStockBillType(String name, String index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(String index) {
        for (OutStockBillType c : OutStockBillType.values()) {
            if (Objects.equals(c.getIndex(), index)) {
                return c.name;
            }
        }
        return null;
    }
    public static String getIndex(String name) {
        for (OutStockBillType c : OutStockBillType.values()) {
            if (Objects.equals(c.getName(), name)) {
                return c.index;
            }
        }
        return null;
    }

}
