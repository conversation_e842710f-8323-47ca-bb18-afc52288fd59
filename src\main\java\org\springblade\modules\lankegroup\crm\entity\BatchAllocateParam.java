package org.springblade.modules.lankegroup.crm.entity;

import lombok.Data;
import org.springblade.modules.lankegroup.crm.vo.CustomerUseorgVO;
import org.springblade.modules.lankegroup.crm.vo.SupplierUseorgVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户批量分配接口参数
 * 供应商批量分配接口参数
 */
@Data
public class BatchAllocateParam {
    /**
     * 客户主键id
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 供应商主键id
     */
    private  Long supplierId;
    /**
     * 供应商名称
     */
    private  String supplierName;
    /**
     * 客户使用组织列表
     */
    private List<CustomerUseorg> orgIds=new ArrayList();
    /**
     * 供应商使用组织列表
     */
    private  List<SupplierUseorg> supplierOrgIds=new ArrayList<>();
}
