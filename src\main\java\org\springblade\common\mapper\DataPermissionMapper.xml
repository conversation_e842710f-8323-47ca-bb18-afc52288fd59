<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataPermissionSql">

    <!-- 
        通用数据权限条件 - 单个负责人字段
        使用方式：
        <include refid="DataPermissionSql.dataPermissionCondition">
            <property name="ownerField" value="quotation_user_id"/>
        </include>
    -->
    <sql id="dataPermissionCondition">
        <!-- 数据权限控制：只有高层/财务无限制，其他根据allowedUserIds过滤 -->
        <if test="query.targetUserLevel != null and query.targetUserLevel != 1 and query.targetUserLevel != 2">
            <if test="query.allowedUserIds != null and query.allowedUserIds.size > 0">
                AND ${ownerField} IN
                <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="query.allowedUserIds == null or query.allowedUserIds.size == 0">
                AND 1=0 <!-- 无权限时不返回任何数据 -->
            </if>
        </if>
    </sql>

    <!-- 
        通用数据权限条件 - 多个负责人字段（OR关系）
        使用方式：
        <include refid="DataPermissionSql.multiFieldDataPermissionCondition">
            <property name="ownerFields" value="quotation_user_id,responsible_user_id,create_user"/>
        </include>
    -->
    <sql id="multiFieldDataPermissionCondition">
        <!-- 数据权限控制：只有高层/财务无限制，其他根据allowedUserIds过滤 -->
        <if test="query.targetUserLevel != null and query.targetUserLevel != 1 and query.targetUserLevel != 2">
            <if test="query.allowedUserIds != null and query.allowedUserIds.size > 0">
                AND (
                <foreach collection="ownerFields.split(',')" item="field" separator=" OR ">
                    ${field} IN
                    <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </foreach>
                )
            </if>
            <if test="query.allowedUserIds == null or query.allowedUserIds.size == 0">
                AND 1=0 <!-- 无权限时不返回任何数据 -->
            </if>
        </if>
    </sql>

    <!-- 
        通用数据权限条件 - 自动根据业务类型使用配置的字段
        使用方式：
        <include refid="DataPermissionSql.autoDataPermissionCondition">
            <property name="businessType" value="quotation"/>
        </include>
        注意：需要配合DataPermissionManager.applyDataPermissionWithBusinessType()使用
    -->
    <sql id="autoDataPermissionCondition">
        <!-- 数据权限控制：只有高层/财务无限制，其他根据allowedUserIds过滤 -->
        <if test="query.targetUserLevel != null and query.targetUserLevel != 1 and query.targetUserLevel != 2">
            <if test="query.allowedUserIds != null and query.allowedUserIds.size > 0">
                <if test="query.dataPermissionFields != null and query.dataPermissionFields.size > 0">
                    AND (
                    <foreach collection="query.dataPermissionFields" item="field" separator=" OR ">
                        ${field} IN
                        <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                            #{userId}
                        </foreach>
                    </foreach>
                    )
                </if>
                <if test="query.dataPermissionFields == null or query.dataPermissionFields.size == 0">
                    <!-- 默认使用create_user字段 -->
                    AND create_user IN
                    <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>
            </if>
            <if test="query.allowedUserIds == null or query.allowedUserIds.size == 0">
                AND 1=0 <!-- 无权限时不返回任何数据 -->
            </if>
        </if>
    </sql>

    <!-- 
        关联表数据权限条件 - 支持基于关联表的权限控制
        使用方式：
        <include refid="DataPermissionSql.relatedTableDataPermissionCondition">
            <property name="mainTable" value="jl_quotation"/>
        </include>
        注意：需要配合DataPermissionManager.applyDataPermissionWithBusinessType()使用
    -->
    <sql id="relatedTableDataPermissionCondition">
        <!-- 数据权限控制：只有高层/财务无限制，其他根据allowedUserIds过滤 -->
        <if test="query.targetUserLevel != null and query.targetUserLevel != 1 and query.targetUserLevel != 2">
            <if test="query.allowedUserIds != null and query.allowedUserIds.size > 0">
                AND (
                    <!-- 主表权限：基于配置的字段 -->
                    <if test="query.dataPermissionFields != null and query.dataPermissionFields.size > 0">
                        (
                        <foreach collection="query.dataPermissionFields" item="field" separator=" OR ">
                            ${mainTable}.${field} IN
                            <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                                #{userId}
                            </foreach>
                        </foreach>
                        )
                    </if>
                    <if test="query.dataPermissionFields == null or query.dataPermissionFields.size == 0">
                        <!-- 默认使用create_user字段 -->
                        (
                            ${mainTable}.create_user IN
                            <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                                #{userId}
                            </foreach>
                        )
                    </if>
                    
                    <!-- 关联表权限：基于关联表的负责人字段 -->
                    <if test="query.relatedTablePermissions != null and query.relatedTablePermissions.size > 0">
                        <foreach collection="query.relatedTablePermissions" item="relatedPermission">
                            OR EXISTS (
                                SELECT 1 FROM ${relatedPermission.joinTable}
                                WHERE ${relatedPermission.joinCondition}
                                AND (
                                    <foreach collection="relatedPermission.ownerFields" item="field" separator=" OR ">
                                        ${field} IN
                                        <foreach collection="query.allowedUserIds" item="userId" open="(" close=")" separator=",">
                                            #{userId}
                                        </foreach>
                                    </foreach>
                                )
                            )
                        </foreach>
                    </if>
                )
            </if>
            <if test="query.allowedUserIds == null or query.allowedUserIds.size == 0">
                AND 1=0 <!-- 无权限时不返回任何数据 -->
            </if>
        </if>
    </sql>

</mapper>