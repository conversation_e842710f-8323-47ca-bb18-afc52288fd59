/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.word;

import lombok.Data;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.lankegroup.contractManagement.annotation.FieldToWord;
import org.springblade.modules.lankegroup.contractManagement.constant.ContractToWordConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同主表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ContractMainWord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同类型(1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)
     */
    private String category;
    /**
     * 合同名称
     */
    @FieldToWord
    private String name;
    /**
     * 合同编号 【后端生成】
     */
    @FieldToWord
    private String code;
    /**
     * 商机ID
     */
    @FieldToWord
    private Long businessOpportunityId;
    /**
     * 商机名称
     */
    @FieldToWord
    private String businessOpportunityName;
    /**
     * 业务类型
     */
    @FieldToWord
    private String businessType;
    /**
     * 签约方式 客户签约，机构签约
     */
    @FieldToWord
    private String signMethod;
    /**
     * 签约日期
     */
    private LocalDate signDate;
    /**
     * 签约日期 示例：2024年03月03日
     */
    @FieldToWord
    private String signDateStr;
    /**
     * 合同总金额
     */
    @FieldToWord
    private BigDecimal amount;
    /**
     * 合同金额大写
     */
    @FieldToWord
    private String amountText;
    /**
     * 截止日期、交货日期
     */
    private LocalDate endDate;
    /**
     * 截止日期、交货日期
     */
    @FieldToWord(type = ContractToWordConstant.SPLIT, split = StringPool.DASH, splitSum = 3)
    private String endDateStr;
    /**
     * 客户ID
     */
    private Long customerContactId;
    /**
     * 客户名称
     */
    @FieldToWord
    private String customerContactName;
    /**
     * 客户电话
     */
    @FieldToWord
    private String customerContactPhone;
    /**
     * 机构ID
     */
    private Long customerId;
    /**
     * 机构名称
     */
    @FieldToWord
    private String customerName;
    /**
     * 机构通讯地址
     */
    @FieldToWord
    private String customerAddress;
    /**
     * 乙方ID
     */
    private Long partyBId;
    /**
     * 乙方名称
     */
    @FieldToWord
    private String partyBName;
    /**
     * 乙方通讯地址
     */
    @FieldToWord
    private String partyBAddress;
    /**
     * 乙方 【签约公司属性-银行卡号】需要后端静默获取并保存
     */
    @FieldToWord
    private String partyBScBankCode;
    /**
     * 乙方 【签约公司属性-开户行名称】需要后端静默获取并保存
     */
    @FieldToWord
    private String partyBScAccountName;
    /**
     * 乙方 【签约公司属性-纳税登记号】需要后端静默获取并保存
     */
    @FieldToWord
    private String partyBScTaxNumber;
    /**
     * 乙方 【签约公司属性-联系电话】需要后端静默获取并保存
     */
    @FieldToWord
    private String partyBScPhone;
    /**
     * 乙方联系人【销售】
     */
    @FieldToWord
    private String partyBPerson;
    /**
     * 乙方电话【销售电话】
     */
    @FieldToWord
    private String partyBPhone;
    /**
     * 用章类型：合同章、公章、法人章、财务章
     */
    @FieldToWord
    private String sealType;
    /**
     * 模板路径
     */
    private String templateUrl;
    /**
     * 预览路径
     */
    private String previewUrl;
    /**
     * 创建时间
     */
    @FieldToWord
    private String createTimeStr;
    /**
     * 商机编号
     */
    @FieldToWord
    private String businessOpportunityCode;


}
