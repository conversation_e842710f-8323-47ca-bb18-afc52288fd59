package org.springblade.common.enums;

/**
 * 1	000000	北京布鲁家族农业发展有限公司	100
 * 100002	000000	北京布鲁家族农业发展有限公司	202
 * 100003	000000	石家庄方能通信技术有限公司	203
 * 100004	000000	河北若宸网络技术有限公司	204
 * 100005	000000	北京三畅未来信息技术有限公司	205
 * 100006	000000	石家庄云宸信息科技有限公司	206
 * 100007	000000	河北翼忆食品有限公司	207
 * 100008	000000	湖南湘食百年食品有限公司	208
 * 100009	000000	湖南湘食百年食品有限公司长沙分公司	209
 */
public enum OrgEnum {
    JT("1","100","北京信工博特智能科技有限公司"),
    //    SMB("172805","101","北京实名宝网络科技有限责任公司","SMB"),
    LK("100002","202","北京信工博特智能科技有限公司","BL"),
    FN("100003","203","石家庄方能通信技术有限公司","FN"),
    RC("100004","204","河北若宸网络技术有限公司","RC"),
    SC("100005","205","北京三畅未来信息技术有限公司","SC"),
    YC("100006","206","石家庄云宸信息科技有限公司","YC"),
    YY("100007","207","河北翼忆食品有限公司","YY"),
    XS("100008","208","湖南湘食百年食品有限公司","XS"),
    XSCS("100009","209","湖南湘食百年食品有限公司长沙分公司","XSCS"),
    SMB("172805","101","北京实名宝网络科技有限责任公司","SMB"),
    //新增组织
    CZJTAK("172544","111","沧州金汤安可网络科技有限公司","JT"),
    YYCY("172808","113","廊坊市雅盈餐饮服务有限公司","YY"),
    BJMDME("205550","117","北京门大门二信息技术有限公司","MDME");
    private String id;
    private String code;
    private String name;

    private String value;

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    OrgEnum(String id, String code,String name,String value) {
        this.id = id;
        this.code = code;
        this.name= name;
        this.value=value;
    }
    OrgEnum(String id, String code,String name) {
        this.id = id;
        this.code = code;
        this.name= name;
    }
    public String getId() {
        return id;
    }

    public String getCode() {
        return code;
    }
    public static String getValueBykey(String id) {
        for (OrgEnum type : OrgEnum.values()) {
            if (id.equals(type.getId())) {
                return type.name;
            }
        }
        return null;
    }
    //根据id返回组织缩写（合同编码的前缀）
    public static String getAbbreviationBykey(String id) {
        for (OrgEnum type : OrgEnum.values()) {
            if (id.equals(type.getId())) {
                return type.value;
            }
        }
        return null;
    }
}
