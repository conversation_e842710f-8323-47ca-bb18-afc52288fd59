/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自定义流程审批中的所有业务单据任务 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class BusinessProcessDTO {
	private static final long serialVersionUID = 1L;
	/**
	 * 审批类型
	 */
	@ApiModelProperty(value = "审批类型:1待审批；2已审批；3我发起的；4我收到的")
	private Integer flowTypeCode = 1;

	@ApiModelProperty(value = "单据编号")
	private String formDataCode;
	/**
	 * 流程分类
	 */
	@ApiModelProperty(value = "流程分类")
	private Long category;
	/**
	 * 流程分类-子类
	 */
	@ApiModelProperty(value = "流程分类-子类")
	private Long subclass;

	@ApiModelProperty(value = "审批状态")
	private String status;

	@ApiModelProperty("创建开始时间")
	private String startTime;

	@ApiModelProperty("创建结束时间")
	private String endTime;

	@ApiModelProperty("创建时间范围类型 today:今天 yesterday:昨天 week:本周 lastWeek:上周 month:本月 lastMonth:上月 quarter:本季度 lastQuarter:上季度 year:本年度 lastYear:上年度")
	private String type;

	@ApiModelProperty("审批通过开始时间")
	private String approvedStartTime;

	@ApiModelProperty("审批通过结束时间")
	private String approvedEndTime;

	@ApiModelProperty("审批通过时间范围类型 today:今天 yesterday:昨天 week:本周 lastWeek:上周 month:本月 lastMonth:上月 quarter:本季度 lastQuarter:上季度 year:本年度 lastYear:上年度")
	private String approvedType;

	@ApiModelProperty(value = "创建人")
	private Long createUser;

	@ApiModelProperty(value = "租户id")
	private String tenantId;

	@ApiModelProperty(value = "当前审批人id")
	private Long userId;

	@ApiModelProperty(value = "审批状态")
	private String auditStatus;

	@ApiModelProperty(value = "搜索字段")
	private String searchName;
}
