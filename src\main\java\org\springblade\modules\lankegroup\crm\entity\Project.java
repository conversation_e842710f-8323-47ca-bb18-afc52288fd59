/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目档案表实体类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@TableName("blade_project")
@EqualsAndHashCode(callSuper = true)
public class Project extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 项目id（主键）
	*/
		private Long projectId;
	/**
	* 项目编码
	*/
		private String projectCode;
	/**
	* 项目名称
	*/
		private String projectName;
	/**
	* 项目费用承担公司id
	*/
		private String assumeCompanyId;
	/**
	* 项目费用承担公司编码
	*/
		private String assumeCompanyCode;
	/**
	* 项目费用承担公司名称
	*/
		private String assumeCompanyName;
	/**
	* 项目费用承担部门id
	*/
		private String assumeDeptId;
	/**
	* 项目费用承担部门编码
	*/
		private String assumeDeptCode;
	/**
	* 项目费用承担部门名称
	*/
		private String assumeDeptName;
	/**
	* 业务类型id
	*/
		private String salestypeId;
	/**
	* 业务类型名称
	*/
		private String salestypeName;
	/**
	* 业务员id
	*/
		private Long salesmanId;
	/**
	* 业务员名称
	*/
		private String salesmanName;
	/**
	* 客户id
	*/
		private Long customerId;
	/**
	* 客户编码
	*/
		private String customerCode;
	/**
	* 客户名称
	*/
		private String customerName;
	/**
	* 项目合同金额
	*/
		private String contractMoney;
	/**
	* 项目累计开票金额
	*/
		private String invoiceCountMoney;
	/**
	* 累计回款金额
	*/
		private String receivedCountMoney;
	/**
	* 项目关闭状态（0未关闭/1已关闭）
	*/
		private String projectClosed;


}
