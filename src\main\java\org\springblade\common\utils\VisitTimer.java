package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.http.HttpRequest;
import org.springblade.modules.lankegroup.customerVisit.mapper.CustomerVisitMapper;
import org.springblade.modules.lankegroup.customerVisit.vo.VisitRank;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.wxcp.config.WxcpRobotConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@Async
public class VisitTimer {

    private final CustomerVisitMapper customerVisitMapper;
    private final IUserService userService;
    private final SystemMessageService systemMessageService;
    private final WxcpRobotConfiguration wxcpRobotConfiguration;
//    @Value("${roBotAppKey}")
//    private String roBotAppKey;
//    @Value("${groupKey}")
//    private String groupKey;


    public VisitTimer(CustomerVisitMapper customerVisitMapper, IUserService userService, SystemMessageService systemMessageService, WxcpRobotConfiguration wxcpRobotConfiguration) {
        this.customerVisitMapper = customerVisitMapper;
        this.userService = userService;
        this.systemMessageService = systemMessageService;
        this.wxcpRobotConfiguration = wxcpRobotConfiguration;
    }


    /**
     * 定时器 每天早上 8点跑 本周的 排名
     * 然后 发送到群里
     */
//    @Scheduled(cron = "0 0 8 * * ?") //每天上午8触发
//    @GetMapping("/test")
    public void timer() throws Exception {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HashMap<String, String> map = new HashMap<>();
        Map<String, Object> lists = new HashMap<>();
        // 获取当天日期
        LocalDate now = LocalDate.now();
        // 周一
        LocalDate monday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 周日
        LocalDate sunday = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        // 本周开始时间
        LocalDateTime weekStart = monday.atStartOfDay();
        // 本周结束时间
        LocalDateTime weekEnd = LocalDateTime.of(sunday, LocalTime.MAX);
        map.put("startTime", weekStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("endTime", weekEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("ranking", "desc");
        String start = "";
        String end = "";
        List<VisitRank > rankDesc = customerVisitMapper.rankDesc(map);
        if (rankDesc.size() != 0) {
            lists.put("rankDesc", rankDesc);
            for (VisitRank map1 : rankDesc) {
                start = start + map1.getName() + " " + map1.getRanking() + "次、";
            }
        } else {
            lists.put("rankDesc", new ArrayList<>());
        }
        if (rankDesc.size() == 3) {
            map.put("ranking", "asc");
            List<VisitRank> rankAsc = customerVisitMapper.rankDesc(map);
            List<VisitRank> rankAll = customerVisitMapper.rankAll(map);
            rankAsc.addAll(rankAll);
            List<VisitRank> collect = rankAsc.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(VisitRank::getId).reversed())), ArrayList::new));
            List<VisitRank> contactVOList = collect.stream()
                    .sorted(Comparator.comparing(VisitRank::getRanking)).limit(3)
                    .collect(Collectors.toList());
            lists.put("rankAsc",contactVOList);
            for (VisitRank map1 : contactVOList) {
                end = end + map1.getName() + " " + map1.getRanking() + "次、";
            }
        } else {
            lists.put("rankAsc", new ArrayList<>());
        }
        if (start.length() == 0 && end.length() == 0) {
            return;
        }
        String param = "{ \"msgtype\": \"text\", \"text\": {\"content\": " +
                "\"本周拜访排名：\n";
        if (start.length() > 0) {
            param += "前三名：\n" +
                    "      " + start.substring(0, start.length() - 1) + ";\n";
        }
        if (end.length() > 0) {
            param += "后三名：\n" +
                    "      " + end.substring(0, end.length() - 1) + ";";
        }
        param += "\"}}";
        send(param);
    }

    private void send(String param) throws Exception {
        // TODO 企微待优化
        String result = HttpRequest.post(wxcpRobotConfiguration.getSalespeopleVisitRanking())
                .connectTimeout(Duration.ofSeconds(5)).readTimeout(Duration.ofSeconds(5))
                .bodyJson(param).execute().asString();
        log.info("【群机器人】销售群机器人 发送 本周拜访排名，返回结果：{}", result);

        /*DingTalkSingleMessage dingTalkSingleMessage = new DingTalkSingleMessage();
        dingTalkSingleMessage.setMsgParam(param);
        dingTalkSingleMessage.setMsgKey("sampleText");
        dingTalkSingleMessage.setOpenConversationId(groupKey);
        dingTalkSingleMessage.setRobotCode(roBotAppKey);
        SendDingTalkMessageUtil.sendMessgae(dingTalkSingleMessage, DingAppFactory.app(SystemRoBot.class));*/
    }

}
