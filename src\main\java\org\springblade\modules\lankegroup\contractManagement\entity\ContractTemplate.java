/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 合同模板实体类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@ApiModel(description = "合同模板实体类")
@Data
@TableName("cm_contract_template")
@EqualsAndHashCode(callSuper = true)
public class ContractTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;
    /**
     * 模板类别 (1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)
     */
    @ApiModelProperty(value = "模板类别 (1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)")
    private String type;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String templateDescription;
    /**
     *  附件地址
     */
    @ApiModelProperty(value = " 附件地址")
    private String filePath;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String fileName;


}
