package org.springblade.flow.engine.service.delegate;

import lombok.experimental.FieldNameConstants;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.core.constant.WorkFlowConstants;

import static org.springblade.flow.core.constant.WorkFlowConstants.PROC_INSTANCE_FORM_DATA_ID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 14:59
 * 抄送代理
 */
@FieldNameConstants
public class ProcessCcDelegate implements JavaDelegate {


	private Expression copyToUserList;

	public void execute(DelegateExecution execution) {
		// 获取抄送人列表
		String userListValue = (String) copyToUserList.getValue(execution);
		String processInstanceId = execution.getProcessInstanceId();
//		Object formKey = ((ExecutionEntityImpl) execution).getParent().getVariable("formKey");
//		ProcessMessageUtils messageUtils = SpringContextUtils.getBean(ProcessMessageUtils.class);
//		// 抄送把节点Id当成taskId放入 查询时检测到是serviceTask_62c19a0855d2c9e915b942c7 格式则抄送
//		String openUrl = ProcessMessageUtils.BPMN_INFO_URL + processInstanceId + "&taskId=" + ((ExecutionEntityImpl) execution).getActivityId();
//		messageUtils.batchSendSystemMessage(StrUtil.format("【流程抄送】{}发起的{}", ((ExecutionEntityImpl) execution).getParent().getStartUserId(), messageUtils.getProcessName(StrUtil.toString(formKey))), "流程抄送", processInstanceId, openUrl, StrUtil.split(userListValue, ","));

		String formKey = execution.getVariable(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		String formDataId = execution.getVariable(PROC_INSTANCE_FORM_DATA_ID).toString();

		SpringContextUtils.getBean(IBusinessProcessService.class).businessProcessCcDelegate(formKey, formDataId, userListValue, processInstanceId);

		execution.setVariable("ccUsers_" + execution.getCurrentActivityId(), userListValue);
	}

}
