/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerIndustryInvolved;
import org.springblade.modules.lankegroup.crm.vo.CustomerIndustryInvolvedVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerIndustryInvolvedService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户所属行业表 控制器
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/industry/customerindustryinvolved")
@Api(value = "客户所属行业表", tags = "客户所属行业表接口")
public class CustomerIndustryInvolvedController extends BladeController {

	private final ICustomerIndustryInvolvedService customerIndustryInvolvedService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerIndustryInvolved")
	public R<CustomerIndustryInvolved> detail(CustomerIndustryInvolved customerIndustryInvolved) {
		CustomerIndustryInvolved detail = customerIndustryInvolvedService.getOne(Condition.getQueryWrapper(customerIndustryInvolved));
		return R.data(detail);
	}

	/**
	 * 分页 客户所属行业表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerIndustryInvolved")
	public R<IPage<CustomerIndustryInvolved>> list(CustomerIndustryInvolved customerIndustryInvolved, Query query) {
		IPage<CustomerIndustryInvolved> pages = customerIndustryInvolvedService.page(Condition.getPage(query), Condition.getQueryWrapper(customerIndustryInvolved));
		return R.data(pages);
	}

	/**
	 * 自定义分页 客户所属行业表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerIndustryInvolved")
	public R<IPage<CustomerIndustryInvolvedVO>> page(CustomerIndustryInvolvedVO customerIndustryInvolved, Query query) {
		IPage<CustomerIndustryInvolvedVO> pages = customerIndustryInvolvedService.selectCustomerIndustryInvolvedPage(Condition.getPage(query), customerIndustryInvolved);
		return R.data(pages);
	}

	/**
	 * 新增 客户所属行业表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerIndustryInvolved")
	public R save(@Valid @RequestBody CustomerIndustryInvolved customerIndustryInvolved) {
		return R.status(customerIndustryInvolvedService.save(customerIndustryInvolved));
	}

	/**
	 * 修改 客户所属行业表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerIndustryInvolved")
	public R update(@Valid @RequestBody CustomerIndustryInvolved customerIndustryInvolved) {
		return R.status(customerIndustryInvolvedService.updateById(customerIndustryInvolved));
	}

	/**
	 * 新增或修改 客户所属行业表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerIndustryInvolved")
	public R submit(@Valid @RequestBody CustomerIndustryInvolved customerIndustryInvolved) {
		return R.status(customerIndustryInvolvedService.saveOrUpdate(customerIndustryInvolved));
	}

	
	/**
	 * 删除 客户所属行业表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerIndustryInvolvedService.deleteLogic(Func.toLongList(ids)));
	}

	
}
