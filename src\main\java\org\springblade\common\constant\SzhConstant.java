package org.springblade.common.constant;

/**
 * SzhConstant
 */
public interface SzhConstant {



	/**
	 * 数字化-默认根菜单ID
	 */
	Long SZH_DEFAULT_ROOT_MENU_ID = 1670739327203921922L;
	/**
	 * 数字化默认角色ID
	 */
	String SZH_DEFAULT_ROLE_ID = "1552895891959816194";
	/**
	 * 默认密码SmxzemhAMjAyNQ
	 */
	String SZH_DEFAULT_PASSWORD = "e5901147d85f64ba2fadd00cac5cc94120002d82";
	/**
	 * 默认租户
	 */
	String SZH_DEFAULT_TENANT_ID = "110112";

	/**
	 * 数字化启动类名称前缀
	 */
	String SZH_APPLICATION_NAME_AND_EXCLUDING_CONNECTOR_PREFIX = "szh";

	/**
	 * 数字化启动类名称前缀
	 */
	String SZH_APPLICATION_NAME_PREFIX = SZH_APPLICATION_NAME_AND_EXCLUDING_CONNECTOR_PREFIX + "-";

	/**
	 * 数字化-财务启动类名称
	 */
	String SZH_CW_APPLICATION_NAME = SZH_APPLICATION_NAME_PREFIX + "cw";

	/**
	 * 数字化-项目管理启动类名称
	 */
	String SZH_XMGL_APPLICATION_NAME = SZH_APPLICATION_NAME_PREFIX + "xmgl";

	/**
	 * 数字化-数字化框架基础业务（首页、消息提醒等等）启动类名称
	 */
	String SZH_DIGITIZE_APPLICATION_NAME = SZH_APPLICATION_NAME_PREFIX + "digitize";

	/**
	 * 数字化-企业微信启动类名称
	 */
	String SZH_WXCP_APPLICATION_NAME = SZH_APPLICATION_NAME_PREFIX + "wxcp";


}
