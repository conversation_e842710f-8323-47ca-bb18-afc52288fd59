package org.springblade.modules.auth.controller;

import lombok.AllArgsConstructor;
import org.springblade.common.utils.AesUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.time.Duration;
import java.util.UUID;

/**
 * 获取密钥
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("getAuthCode")
@AllArgsConstructor
public class AuthCodeController {

    private final BladeRedis bladeRedis;

    @GetMapping
    public R getAuthCode() {
        String uuid = UUID.randomUUID().toString();
        String key = "blade:authCode:" + AuthUtil.getUserId() + ":" + uuid;
        bladeRedis.setEx(key, AuthUtil.getUserId().toString(), Duration.ofMinutes(1440));
        return R.data(AesUtil.encryptBase64(key));
    }
}
