package org.springblade.modules.lankegroup.clientrelation.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.entity.Area;
import org.springblade.modules.lankegroup.clientrelation.entity.Controls;

import java.util.List;

public interface AreaService extends BaseService<Area> {

    /**
     * 新增行业
     * @param parentCode
     * @return
     */
    R getAreaList(String parentCode);

    /**
     * 保存操作记录
     * @param controls
     * @return
     */
    boolean saveControlsLog(Controls controls);

    /**
     * 批量查询
     * @param codeList
     * @return
     */
    List<Area> getList(List<String> codeList);
}
