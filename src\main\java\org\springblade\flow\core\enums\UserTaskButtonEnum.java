package org.springblade.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23 16:51
 * @description: 用户任务(UserTask)显示的按钮枚举
 */
@Getter
@AllArgsConstructor
public enum UserTaskButtonEnum {


	APPROVE("approve", "同意"),
	REBACK("reback", "驳回"),//对新销客应前端驳回按钮，统一处理节点退回到发起人
//	REJECT("reject", "驳回/拒绝"),
	REVOKE("revoke", "撤回"),
	;

	final String status;

	final String remark;
}
