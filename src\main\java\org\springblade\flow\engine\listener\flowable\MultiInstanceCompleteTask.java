package org.springblade.flow.engine.listener.flowable;

import lombok.AllArgsConstructor;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 会签完成的条件
 * 对应多任务(审批节点)xml里面的 <completionCondition>${multiInstance.accessCondition(execution)}</completionCondition>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 17:37
 */
@Component("multiInstance")
@AllArgsConstructor
public class MultiInstanceCompleteTask implements Serializable {

	//	private final IBusinessProcessTaskClient businessProcessTaskClient;
	private final FlowEngineService flowEngineService;

	private final HistoryService historyService;

	/**
	 * 评估结果判定条件
	 *
	 * @param execution 分配执行实例
	 */
	public boolean accessCondition(DelegateExecution execution) {
		//否决判断，一票否决
		boolean pass = (Boolean) execution.getVariable("pass");
		if (pass == false) {
			//一票否决其他实例没必要做，结束
			return true;
		}

		Integer reviewRound = (Integer) execution.getVariable("reviewRound");
		if (reviewRound == null) {
			reviewRound = 0;
		}
		int completedInstance = flowEngineService.getCompletedInstance(execution.getProcessInstanceId(), execution.getCurrentActivityId(), execution.getId(), reviewRound) + 1;
		//所有实例任务未全部做完则继续其他实例任务
		FlowElement element = execution.getCurrentFlowElement();
		List<String> assigneeList = new ArrayList<>();
		if (element instanceof UserTask) {
			UserTask userTask = (UserTask) element;
			String inputDateItem = userTask.getLoopCharacteristics().getInputDataItem();
			assigneeList = (List<String>) execution.getVariable(inputDateItem);
		}

		return completedInstance == assigneeList.size();
	}

}
