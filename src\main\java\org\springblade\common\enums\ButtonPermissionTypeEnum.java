package org.springblade.common.enums;

/**
 * 按钮权限类型枚举
 *
 * <AUTHOR> <PERSON>
 * @since 2025年07月01日 16:00
 **/
public enum ButtonPermissionTypeEnum {
    
    /**
     * 添加按钮
     */
    ADD_BUTTON("addBtn", "添加按钮"),
    
    /**
     * 编辑按钮
     */
    EDIT_BUTTON("editBtn", "编辑按钮"),
    
    /**
     * 删除按钮
     */
    DELETE_BUTTON("deleteBtn", "删除按钮"),
    
    /**
     * 查看按钮
     */
    VIEW_BUTTON("viewBtn", "查看按钮"),

    /**
     * 更多按钮
     */
    MORE_BUTTON("moreBtn", "更多按钮"),

    /**
     * 审核按钮
     */
    AUDIT_BUTTON("auditBtn", "审核按钮");
    
    private final String code;
    private final String name;
    
    ButtonPermissionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public static ButtonPermissionTypeEnum getByCode(String code) {
        for (ButtonPermissionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 