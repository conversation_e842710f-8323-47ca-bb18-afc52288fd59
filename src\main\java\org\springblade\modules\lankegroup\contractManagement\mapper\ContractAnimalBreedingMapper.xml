<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractAnimalBreedingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractAnimalBreedingResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ContractAnimalBreeding">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="main_id" property="mainId"/>
        <result column="deposited_amount" property="depositedAmount"/>
    </resultMap>


    <select id="selectContractAnimalBreedingPage" resultMap="contractAnimalBreedingResultMap">
        select *
        from cm_contract_animal_breeding
        where is_deleted = 0
    </select>

</mapper>
