/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.Project;
import org.springblade.modules.lankegroup.crm.vo.ProjectVO;
import org.springblade.modules.lankegroup.crm.wrapper.ProjectWrapper;
import org.springblade.modules.lankegroup.crm.service.IProjectService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 项目档案表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/project/project")
@Api(value = "项目档案表", tags = "项目档案表接口")
public class ProjectController extends BladeController {

	private final IProjectService projectService;

	/**
	 * 详情
	 */
	@ApiLog("详情")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入project")
	public R<ProjectVO> detail(Project project) {
		Project detail = projectService.getOne(Condition.getQueryWrapper(project));
		return R.data(ProjectWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 项目档案表
	 */
	@ApiLog("分页")
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入project")
	public R<IPage<ProjectVO>> list(Project project, Query query) {
		IPage<Project> pages = projectService.page(Condition.getPage(query), Condition.getQueryWrapper(project));
		return R.data(ProjectWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 项目档案表
	 */
	@ApiLog("分页")
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入project")
	public R<IPage<ProjectVO>> page(ProjectVO project, Query query) {
		IPage<ProjectVO> pages = projectService.selectProjectPage(Condition.getPage(query), project);
		return R.data(pages);
	}

	/**
	 * 新增 项目档案表
	 */
	@ApiLog("新增")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入project")
	public R save(@Valid @RequestBody Project project) {
		return R.status(projectService.save(project));
	}

	/**
	 * 修改 项目档案表
	 */
	@ApiLog("修改")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入project")
	public R update(@Valid @RequestBody Project project) {
		return R.status(projectService.updateById(project));
	}

	/**
	 * 新增或修改 项目档案表
	 */
	@ApiLog("新增/修改")
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入project")
	public R submit(@Valid @RequestBody Project project) {
		return R.status(projectService.saveOrUpdate(project));
	}

	
	/**
	 * 删除 项目档案表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(projectService.deleteLogic(Func.toLongList(ids)));
	}

	
}
