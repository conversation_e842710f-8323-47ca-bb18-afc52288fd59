package org.springblade.flow.engine.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.flow.core.entity.FlowModel;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 11:18
 * @description: 保存并发布流程，添加流程公有配置
 */
@Data
public class FlowModelDeployVO extends FlowModel  {
	@ApiModelProperty(value = "自定义流程配置主键")
	private Long customProcessConfigId;

	@ApiModelProperty(value = "关联流程定义的id")
	private String processDefinitionId;

	@ApiModelProperty(value = "无审批人（离职/找不到人）对应系统字典表（1转交管理员；2自动通过；3自动拒绝）")
	private String noApprover;

	@ApiModelProperty(value = "是否自动去重(1去重，0不去重)")
	private int deduplication;
}
