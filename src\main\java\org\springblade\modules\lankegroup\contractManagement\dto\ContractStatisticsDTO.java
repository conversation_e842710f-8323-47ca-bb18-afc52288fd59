package org.springblade.modules.lankegroup.contractManagement.dto;


import lombok.Data;

import java.util.List;

/**
 * 合同统计入参
 * <AUTHOR>
 */
@Data
public class ContractStatisticsDTO {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 合同类型（0销售合同/1采购合同/2其他合同/4渠道合同）
     */
    private String contractType;
    private List<String> contractTypeList;

    /**
     * 合同类型选择（1、默认筛选渠道和销售 2、不做过滤筛选）
     */
    private Integer selectContractType;

    /**
     * 排序（1、升序 2、降序）
     */
    private Integer sort;

    /**
     * 查询ID（部门/人员ID）
     */
    private String selectId;
    private Long selectUserId;
    private Long selectDeptId;
    private List<Long> selectDeptIdList;
    private List<Long> selectUserIdList;

    /**
     * 合同状态（0审批中，1待归档，2已撤回，3已驳回，4归档审批，5已归档,6草稿，7归档已撤回，8归档已驳回,9作废）
     */
    private List<String> contractStatus;

    /**
     * 履行情况筛选（1、履行中 2、已完结）
     */
    private Integer conditionStatus;

    /**
     * 项目名称
     */
    private String projectName;
    private String contractName;

    /**
     * 查询ID列表
     */
    private List<Long> idList;

    /**
     * 项目类型
     */
    private Long proType;

    private Integer current = 1;

    private Integer size = 10;

    /**
     * 查询合同状态
     */
    private Integer status;

    /**
     * 合同ID
     */
    private String conId;


    /**
     * 时间类型筛选（0、合同发起时间 1、合同归档时间）
     */
    private Integer timeType;

    private List<String> projectNameList;
    private List<String> contractNameList;

    /**
     * 项目负责人ID
     */
    private List<String> projectUserList;

    /**
     * 查询归档期限（7、七天 14、十四天 30、三十天）
     */
    private Integer haveBeenFiled;
}
