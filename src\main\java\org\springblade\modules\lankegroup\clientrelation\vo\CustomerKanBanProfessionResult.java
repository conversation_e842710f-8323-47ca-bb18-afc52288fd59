package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

import java.util.List;

@Data
public class CustomerKanBanProfessionResult {

    /**
     * 行业编码
     */
    private String professionId;

    private String personnelId;

    /**
     * 行业名称
     */
    private String professionName;

    private String areaId;

    private String areaName;

    /**
     * 应有客户
     */
    private Integer yCustomerCount;

    /**
     * 实有客户
     */
    private Integer sCustomerCount;

    /**
     * 客户覆盖度
     */
    private float customerCoverage;

    private List<CustomerKanBanProfessionResult> list;

    /**
     * 数据类型
     */
    private String type;

}
