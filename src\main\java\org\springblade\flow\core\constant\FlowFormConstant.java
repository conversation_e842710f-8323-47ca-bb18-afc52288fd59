package org.springblade.flow.core.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/18 16:21
 */
public interface FlowFormConstant {
	String FLOWTEST_KEY = "flow_test";
	//开票
	String FLOW_TICKET_MANAGE_KEY = "ticket_manage";
	//销售合同('sale','channel','other')
	String FLOW_CONTRACT_MANAGE_KEY = "contract_manage";
	//合同归档
	String FLOW_CONTRACT_ARCHIVE_KEY = "contract_archive";
	//合同作废
	String FLOW_CONTRACT_CANCELLATION_KEY = "contract_cancellation";
	//测试业务
	String FLOW_TEST_BUSINESS_KEY = "flow_test_business";
	//机构管理
	String FLOW_CUSTOMER_KEY = "blade_customer";
	//销售合同
	String FLOW_CONTRACT_KEY = "cm_contract_main";

	//报价审批
	String JL_QUOTATION_APPROVAL_KEY = "jl_quotation";
	// 商机审批 商机表
	String PM_BUSINESS_OPPORTUNITY_APPROVAL_KEY = "pm_business_opportunity";

	String JL_QUOTATION_KEY = "jl_quotation_biz";


}
