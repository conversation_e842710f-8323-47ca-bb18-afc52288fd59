// 浏览
PUT /ding_task_look
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "beLookVisit": {
        "type": "keyword"
      },
      "lookPersonName": {
        "type": "keyword"
      },
      "lookPerson": {
        "type": "keyword"
      },
      "type": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      }
    }
  }
}


//点赞
PUT /ding_task_like
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "beLikeVisit": {
        "type": "keyword"
      },
      "likePersonName": {
        "type": "keyword"
      },
      "likePerson": {
        "type": "keyword"
      },
      "type": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      }
    }
  }
}

//评论表
PUT /ding_task_comment
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "beCommentVisit": {
        "type": "keyword"
      },
      "pid": {
        "type": "keyword"
      },
      "commenters": {
        "type": "keyword"
      },
      "beCommented": {
        "type": "keyword"
      },
       "comment": {
        "type": "keyword"
      },
      "type": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      }
    }
  }
}

// 评论点赞表

PUT /ding_task_comment_like
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "beLikeComment": {
        "type": "keyword"
      },
      "likePersonName": {
        "type": "keyword"
      },
      "likePerson": {
        "type": "keyword"
      },
      "type": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
      }
    }
  }
}


Api:
    save:
        List<BulkOperation> bulkOperations = new ArrayList<>();
        CompletePlanLog completePlanLog = new CompletePlanLog();
        elasticsearchClient.index( i -> i.index("ding_task_log_view").document(completePlanLog));

        List<BulkOperation> bulkOperations = new ArrayList<>();
        for(ViewMessageLog messageLog : list) {
            bulkOperations.add(BulkOperation.of(o -> o.index(i -> i.document(messageLog))));
        }
        elasticsearchClient.bulk(b -> b.index("ding_task_log_view").operations(bulkOperations));
query:
        SearchResponse<ViewLog> search = elasticsearchClient.search(srBuilder -> srBuilder.index("ding_task_log_view")
                                    .query(queryBuilder -> queryBuilder.term(term -> term.field("userId").value(AuthUtil.getUserId()).field("logId").value(completePlanLogListVO.getId())))
                            , ViewLog.class);
                    if (0 < search.hits().total().value()) {
                        completePlanLogListVO.setIsView(true);
                    }



//        List<BulkOperation> bulkOperations = new ArrayList<>();
//        Date date = new Date();
//        for (ViewMessageLog messageLog : list) {
//            messageLog.setUserId(AuthUtil.getUserId().toString());
//            messageLog.setCreateTime(date);
//            bulkOperations.add(BulkOperation.of(o -> o.index(i -> i.document(messageLog))));
//        }
//        try {
//            elasticsearchClient.bulk(b -> b.index("ding_task_message_view").operations(bulkOperations));
//        } catch (Exception e) {
//            log.error("保存系统消息记录异常->" + e.getMessage());
//        }
