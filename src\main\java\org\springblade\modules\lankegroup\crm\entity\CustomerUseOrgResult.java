package org.springblade.modules.lankegroup.crm.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户分配返回值实体
 *
 *客户分配传值实体
 */
@Data
public class CustomerUseOrgResult {
//    分配的组织列表
    List<CustomerUseorg> customerUseorgs=new ArrayList<>();
//    已经分配的组织提示
    String msg;
//    客户id
    String KdCustomerId;
//    分配组织id列表
    List<Long>  orgIds=new ArrayList<>();
//    供应商id
    String kdSupplierId;
//    供应商分配组织列表
    List<SupplierUseorg> supplierUseorgs=new ArrayList<>();
}
