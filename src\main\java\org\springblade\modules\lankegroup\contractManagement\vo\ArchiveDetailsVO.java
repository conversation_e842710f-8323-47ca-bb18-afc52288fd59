/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetailsFiles;
import org.springblade.modules.lankegroup.contractManagement.entity.EstimatedCollection;

import java.util.ArrayList;
import java.util.List;

/**
 * 归档详情视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveDetailsVO extends ArchiveDetails {
    private static final long serialVersionUID = 1L;
    /**
     * 发起人姓名
     */
    private String createUserName;

    /**
     * 付款方式（1一次性付款/2分期付款）
     */
    private String paymentMethodValue;

    /**
     * 合同类型方式（1信息化集成/2安服/3其他）
     */
    private String contractTypeMethodValue;

    /**
     * 签订方式（direct直签/channel渠道）
     */
    private String signingModeValue;

    /**
     * 负责人名称
     */
    private String userName;
    /**
     * 负责人id
     */
    private String userId;
    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 项目id
     */
    private String projectId;
    /**
     * 回款计划list
     */
    private List<EstimatedCollection> estimatedCollectionList = new ArrayList<>();

    /**
     * 合同额
     */
    private Double contractAmount;
    /**
     * 合同清单list
     */
    private List<ArchiveInventoryInformationVO> archiveInventoryInformation = new ArrayList<>();
    /**
     * 阶段负责人
     */
    private String stageLeaderName;

    //    当前登录人是否可撤回
    private Boolean withdraw=false;
    //    当前登录人是否可审批
    private Boolean approval=false;
    // 申请归档
    private Boolean archive=false;

    private ArchiveDetailsFiles archiveDetailsFiles = new ArchiveDetailsFiles();
}
