package org.springblade.common.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.common.mapper.FormMapper;
import org.springblade.common.service.IForm;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/28 9:32
 */
@Service
@AllArgsConstructor
public class FormImpl implements IForm {


    private FormMapper formMapper;

    @Override
    public void UpdateFormData(String formName, String columnKey, String columnValue, String id) {
        formMapper.updateFormDate(formName, columnKey, columnValue, id);
    }
    @Override
    public void updateMongoFormDate(String formName, String columnKey, String columnValue, String id) {
        formMapper.updateMongoFormDate(formName, columnKey, columnValue, id);
    }

    @Override
    public void updateFormStutusAndTaskUser(String tableName, String status, String taskUser, String id) {
        formMapper.updateFormStutusAndTaskUser(tableName, status, taskUser, id);
    }

    @Override
    public void updateFormStatus(String tableName, String status, String id) {
        formMapper.updateFormStatus(tableName, status, id);
    }

    @Override
    public void updateFormRelStatus(String tableName, Integer enable, String fieldName, String id) {
        formMapper.updateFormRelStatus(tableName, enable, fieldName, id);
    }

    @Override
    public Map selectFromData(String formName, String id) {
        return formMapper.selectFromData(formName, id);
    }
    @Override
    public Map selectMongoFromData(String formName, String id) {
        return formMapper.selectMongoFromData(formName, id);
    }

    @Override
    public Boolean updateFromDataByReceipt(String formName, String id, String bankType, String bankAccount, String bankAccountName) {
        return formMapper.updateFromDataByReceipt(formName, id, bankType, bankAccount, bankAccountName);
    }

    @Override
    public String getPageSkipUrl(Map map) {
        return formMapper.getPageSkipUrl(map);
    }

}
