package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程抄送人
 */
@Data
public class CarbonApproval {
    /**
     * 抄送人数
     */
    private Integer num;
    /**
     * 抄送状态
     */
    private Integer approvedStatus = -1;
    /**
     * 类型
     */
    private String approvalType="抄送人";
    /**
     * 部分流程有抄送人，用此字段给前端区分当前人员是抄送人还是审批人
     *true==》流程中的审批人
     */
    private Boolean personType=false;
    /**
     * 抄送人列表
     */
    private List<Approval> carbonList=new ArrayList<>();
}
