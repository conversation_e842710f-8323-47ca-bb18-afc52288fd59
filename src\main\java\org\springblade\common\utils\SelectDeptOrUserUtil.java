package org.springblade.common.utils;

import cn.hutool.core.util.ReflectUtil;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.SysCache;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/18 10:39
 * 根据搜索部门或者人员返回所有可查看人员
 */
@Service
@AllArgsConstructor
public class SelectDeptOrUserUtil {
    private final DeptByUserService deptByUserService;
    private final IUserService userService;
    private final IUserDeptService userDeptService;

    public List<Long> selectDeptOrUser(Long selectDeptId, Long selectUserId) {
        List<Long> userIdList = new ArrayList<>();//我可以查看的权限范围内的人员
        List<Long> selectDeptIds = new ArrayList<>();//记录我要查看的部门
        if (!Func.isEmpty(selectDeptId)) {
            selectDeptIds.add(selectDeptId);
            List<Long> collect = deptByUserService.getJuniorDept(selectDeptId).stream().map(Dept::getId).collect(Collectors.toList());
            selectDeptIds.addAll(collect);
        }

        // 查询特殊人员列表
        List<String> specialList = deptByUserService.getSpecialList();
        //不是高层
        Long userId = AuthUtil.getUserId();
        if (!specialList.contains(userId.toString())) {
            User loginUser = userService.getById(userId);
            if (Func.equals(1,loginUser.getLeader())) {
                List<Long> teamDeptIds = new ArrayList<>();//记录我能查看的部门（本部门及其下属部门）
                Func.toLongList(AuthUtil.getDeptId()).stream().forEach(dept -> {
                    //获取子部门ID集合
                    teamDeptIds.addAll(SysCache.getDeptChildIds(dept));
                });

                if (!Func.isEmpty(selectDeptId)) {
                    //获取权限范围内搜索的部门人员
                    userIdList.addAll(userDeptService.selectUserByDeptId(selectDeptIds.stream().filter(item -> teamDeptIds.contains(item)).collect(Collectors.toList())));
                } else {
                    //获取权限范围内的所有人
                    List<Long> teamUserIdList = userDeptService.selectUserByDeptId(teamDeptIds);
                    if (!Func.isEmpty(selectUserId)) {
                        userIdList.addAll(teamUserIdList.stream().filter(item -> selectUserId.longValue() == item.longValue()).collect(Collectors.toList()));
                    } else {
                        userIdList.addAll(teamUserIdList);
                    }
                }
            } else {
                userIdList.add(AuthUtil.getUserId());
            }
        } else {
            if (!Func.isEmpty(selectDeptId)) {
                userIdList.addAll(userDeptService.selectUserByDeptId(selectDeptIds));
            }
            if (!Func.isEmpty(selectUserId)) {
                userIdList.add(selectUserId);
            }
        }
        if (!Func.isEmpty(selectDeptId) || !Func.isEmpty(selectUserId)) {
            if (userIdList.size() < 1) {
                userIdList.add(0L);
            }
        }
        return userIdList;
    }

    public void selectDeptOrUser(Object obj) {
        Long selectUserId = (Long) ReflectUtil.getFieldValue(obj, "selectUserId");
        Long selectDeptId = (Long) ReflectUtil.getFieldValue(obj, "selectDeptId");
        List<Long> userIdList = new ArrayList<>();//我可以查看的权限范围内的人员
        List<Long> selectDeptIds = new ArrayList<>();//记录我要查看的部门

        if (!Func.isEmpty(selectDeptId)) {
            selectDeptIds.add(selectDeptId);
            List<Long> collect = deptByUserService.getJuniorDept(selectDeptId).stream().map(Dept::getId).collect(Collectors.toList());
            selectDeptIds.addAll(collect);
            ReflectUtil.invoke(obj, "setSelectDeptIdList", selectDeptIds);
        }

        // 查询特殊人员列表
        List<String> specialList = deptByUserService.getSpecialList();
        //不是高层
        if (!specialList.contains(String.valueOf(AuthUtil.getUserId()))) {
            User loginUser = userService.getById(AuthUtil.getUserId());
            if (Func.equals(1,loginUser.getLeader())) {
                List<Long> teamDeptIds = new ArrayList<>();//记录我能查看的部门（本部门及其下属部门）
                Func.toLongList(AuthUtil.getDeptId()).stream().forEach(dept -> {
                    //获取子部门ID集合
                    teamDeptIds.addAll(SysCache.getDeptChildIds(dept));
                });

                if (!Func.isEmpty(selectDeptId)) {
                    //获取权限范围内搜索的部门人员
                    selectDeptIds = selectDeptIds.stream().filter(item -> teamDeptIds.contains(item)).collect(Collectors.toList());
                    ReflectUtil.invoke(obj, "setSelectDeptIdList", selectDeptIds);
                    userIdList.addAll(userDeptService.selectUserByDeptId(selectDeptIds));
                } else {
                    //获取权限范围内的所有人
                    ReflectUtil.invoke(obj, "setSelectDeptIdList", teamDeptIds);
                    List<Long> teamUserIdList = userDeptService.selectUserByDeptId(teamDeptIds);
                    if (!Func.isEmpty(selectUserId)) {
                        userIdList.addAll(teamUserIdList.stream().filter(item -> selectUserId.longValue() == item.longValue()).collect(Collectors.toList()));
                    } else {
                        userIdList.addAll(teamUserIdList);
                    }
                }
            } else {
                userIdList.add(AuthUtil.getUserId());
            }
        } else {
            if (!Func.isEmpty(selectDeptId)) {
                userIdList.addAll(userDeptService.selectUserByDeptId(selectDeptIds));
            }
            if (!Func.isEmpty(selectUserId)) {
                userIdList.add(selectUserId);
            }
        }
        if (!Func.isEmpty(selectDeptId) || !Func.isEmpty(selectUserId)) {
            if (userIdList.size() < 1) {
                userIdList.add(0L);
            }
        }
        ReflectUtil.invoke(obj, "setSelectUserId", userIdList);
    }
}
