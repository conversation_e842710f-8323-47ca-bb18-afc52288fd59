//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.Proxy;
import java.net.Socket;
import java.net.URL;
import java.net.Proxy.Type;

public class HttpUtils {
    static int proxyRunning = -1;

    public HttpUtils() {
    }

    static Proxy getProxy() {
        AppCfg cfg = CfgUtil.getAppDefaultCfg();
        if (cfg != null && cfg.getProxy() != null) {
            try {
                URL url = new URL(cfg.getProxy());
                if (isHostConnectable(url.getHost(), url.getPort())) {
                    System.out.println(String.format("Used proxy,Host:%s,port:%s", url.getHost(), url.getPort()));
                    return new Proxy(Type.HTTP, new InetSocketAddress(url.getHost(), url.getPort()));
                } else {
                    System.out.println(String.format("is not host %s", url.toString()));
                    return null;
                }
            } catch (MalformedURLException var2) {
                var2.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
    }

    static boolean proxyIsRunning() {
        if (proxyRunning > -1) {
            return proxyRunning != 0;
        } else if (isHostConnectable("127.0.0.1", 8888)) {
            proxyRunning = 1;
            return true;
        } else {
            proxyRunning = 0;
            return false;
        }
    }

    static boolean isHostConnectable(String host, int port) {
        Socket socket = new Socket();

        try {
            socket.connect(new InetSocketAddress(host, port));
            return true;
        } catch (IOException var12) {
        } finally {
            try {
                socket.close();
            } catch (IOException var11) {
            }

        }

        return false;
    }
}
