/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.ExportTemplateUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.dto.CustomerContactDTO;
import org.springblade.modules.lankegroup.crm.service.IUnassignedCustomerService;
import org.springblade.modules.lankegroup.crm.vo.CustomerContactVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 * 公海客户控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/contact/unassignedCustomer")
@Api(value = "公海客户", tags = "公海客户接口")
public class UnassignedCustomerController extends BladeController {

    private final IUnassignedCustomerService unassignedCustomerService;


    /**
     * 导出模板
     */
    @GetMapping("export-template")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "导出模板")
    public void exportUser(HttpServletResponse response) {
        ExportTemplateUtil.export(response, "unassignedCustomerImportTemplates.xlsx", "公海客户导入模版");
    }

    /**
     * TODO 导入
     */
    @PostMapping("import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入", notes = "传入excel")
    public R importRegion(MultipartFile file) {
        return R.success("操作成功");
    }

    /**
     * 认领
     * - 将客户的负责销售附上认领人
     * - 将status改成1（正常状态）
     */
    @GetMapping("/claim")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "认领", notes = "传入customerContactId客户ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "操作失败")
    })
    public R claimData(Long customerContactId) {
        if (Func.isEmpty(customerContactId)) {
            return R.fail("请选择客户");
        }
        return unassignedCustomerService.claimData(customerContactId);
    }

    /**
     * 分配
     * - 将客户的负责销售附上指定人
     * - 将status改成1（正常状态）
     */
    @GetMapping("/assign")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分配", notes = "传入customerContactId客户ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true),
            @ApiImplicitParam(name = "targetUserId", value = "分配人ID", dataType = "string", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "操作失败")
    })
    public R assignData(Long customerContactId, Long targetUserId) {
        if (Func.isEmpty(customerContactId)) {
            return R.fail("请选择客户");
        }
        return unassignedCustomerService.assignData(customerContactId, targetUserId);
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "详情", notes = "传入customerContactId客户ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "操作失败")
    })
    public R detail(Long customerContactId) {
        if (Func.isEmpty(customerContactId)) {
            return R.fail("请选择客户");
        }
        return unassignedCustomerService.detail(customerContactId);
    }

    /**
     * 列表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "列表", notes = "传入customerContac")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "每页展示数量", dataType = "integer", required = true),
            @ApiImplicitParam(name = "current", value = "当前页", dataType = "integer", required = true),
            @ApiImplicitParam(name = "createStartTime", value = "创建开始时间", dataType = "string", required = true),
            @ApiImplicitParam(name = "createEndTime", value = "创建结束时间", dataType = "string", required = true),
            @ApiImplicitParam(name = "source", value = "客户来源", dataType = "string", required = true),
            @ApiImplicitParam(name = "category", value = "客户类型", dataType = "string", required = true),
            @ApiImplicitParam(name = "contacts", value = "客户名称", dataType = "string", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "操作失败")
    })
    public R<IPage<CustomerContactVO>> unassignedCustomerPage(CustomerContactDTO dto, Query query) {
        if (Func.isEmpty(query.getSize())) {
            query.setSize(20);
        }
        try {
            IPage<CustomerContactVO> pages = unassignedCustomerService.unassignedCustomerPage(Condition.getPage(query), dto);
            return R.data(CollectionUtil.isEmpty(pages.getRecords()) ? pages.setRecords(new ArrayList<>()) : pages);
        }catch (Exception e){
            e.printStackTrace();
            return R.fail("系统繁忙，请稍后重试");
        }
    }

}
