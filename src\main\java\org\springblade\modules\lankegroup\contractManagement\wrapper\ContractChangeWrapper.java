/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.wrapper;


import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;

import java.util.Objects;

/**
 * 项目基础信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
public class ContractChangeWrapper extends BaseEntityWrapper<ContractChange, ContractChangeVO>  {

	public static ContractChangeWrapper build() {
		return new ContractChangeWrapper();
 	}

	@Override
	public ContractChangeVO entityVO(ContractChange change) {
		ContractChangeVO contractChangeVO = Objects.requireNonNull(BeanUtil.copy(change, ContractChangeVO.class));
		return contractChangeVO;
	}

}
