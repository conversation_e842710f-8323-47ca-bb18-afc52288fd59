package org.springblade.flow.core.enums;

import lombok.Getter;
import org.springblade.core.tool.api.IResultCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/7 13:47
 */
@Getter
public enum FlowResultCode implements IResultCode {

	FLOW_NOT_FOUND(5001, "审批流不存在或已禁用"),
	START_PROCESS_ERROR(5002, "审批流启动异常，请联系管理员"),
	;

	final int code;
	final String message;

	FlowResultCode(final int code, final String message) {
		this.code = code;
		this.message = message;
	}
}
