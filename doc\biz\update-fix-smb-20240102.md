# fix-smb-0102分支更新内容

## mysql

### 字典表-项目阶段
```sql
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1610162243926941699, '000000', 1551847956036984834, 'project_progress', '11', '项目结项', 15, '', 0, 0);
```

### 项目进度-项目结项
```mysql
CREATE TABLE `blade_project_progress_closure_stage` (
  `id` bigint NOT NULL COMMENT '主键',
  `closure_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结项描述',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT NULL COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目进度-项目结项';
```
### 历史数据处理-安服类项目交付阶段负责人由白金雪换成马天午
####查询阶段成员表，成员是否有马天午，有就删除
update `blade_project_team` 
set is_deleted = 1 
where 
node_id = 5
and admin = 0
and is_deleted = 0
and user_id = 118631362553871914
and project_id in (select id from blade_project_basic where group_id in 
(100037,
104430,
104431,
104432,
104433,
104434,
104435,
104436,
146699));

####查询阶段成员表，阶段负责人是否有白金雪，有就改成马天午
update `blade_project_team`
set
user_id = 118631362553871914
where 
node_id = 5
and admin = 1
and is_deleted = 0
and user_id = 120120507160
and project_id in (select id from blade_project_basic where group_id in 
(100037,
104430,
104431,
104432,
104433,
104434,
104435,
104436,
146699));

#### 查询阶段表，是否有白金雪，有就修改成马天午
update `blade_project_progress` 
set 
stage_leader_id = 118631362553871914,
stage_leader_name = '马天午'
where
is_deleted = 0
and current_progress_key = 5
and stage_leader_id = '120120507160'
and project_id in (select id from blade_project_basic where group_id in 
(100037,
104430,
104431,
104432,
104433,
104434,
104435,
104436,
146699));

### 项目完成-进行中，改为项目结项
#### 0. 备份
#### 1. 本次更改的项目ID，用于以后追踪
```sql
SET SESSION group_concat_max_len = 102400;
select GROUP_CONCAT(kd_project_fid) from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0;
```
> 104859,104863,104873,104878,107627,107878,109120,146742,149691,163256,165304,165347,169039,172315,172753,173167,174559,174821,175067,175122,176443,180489,180769,183580,183595,183623,183713,183846,184462,185266,185574,188823,188821,196031,197655,200460,204996,208349,209312,217050,217847,218187,218391,221018,222523,224177,226934,226933,230967,230968,231388,231390,232816,232821,233760,233540,233537,233759,234318,234478,234917,235542,235584,236005,237161
#### 2. 更改金蝶SQLServer
```sql
update T_BAS_PREBDONE set F_RWSQ_XMJD = '6594b1f6218d78' where F_RWSQ_XMJD = '60ffe03d982b50' and FID in (↑);
```
#### 3. 更新业务MySQL
```sql
update blade_project_progress set current_progress_key = 11 where current_progress_key = 9 and project_id in
(select id from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0);

update blade_project_team set node_id = 11 where node_id = 9 and project_id in
(select id from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0);

update blade_project_basic set schedule_id = '11' where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0;
```
