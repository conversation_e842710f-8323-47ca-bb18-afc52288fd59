package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ContractUnionInfoResult {
    /**
     * 钉钉表项目id
     */
    private Long projectId;
    /**
     * 金蝶下推后正式项目id
     */
    private Long kdProjectFid;
    /**
     * 钉钉合同表id
     */
    private Long contractId;
    /**
     * 金蝶合同表id
     */
    private Long kdContractFid;
    /**
     * 钉钉合同表合同金额
     */
    private BigDecimal contractAmount;
    /**
     * 金蝶合同表合同额
     */
    private BigDecimal kdContractAmount;
    /**
     * 合同单据日期
     */
    private Date kdDate;
    /**
     * 审批通过时间（钉钉表归档时间）
     */
    private Date approveDate;
    /**
     * 合同次数
     */
    private Integer contractNumber;
    //合同中明细条数
    private Integer mxs;
    /**
     * 所在年份
     */
    private Integer inyear;
}
