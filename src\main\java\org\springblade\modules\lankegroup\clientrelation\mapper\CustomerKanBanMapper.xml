<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.CustomerKanBanMapper">
    <insert id="saveCustomerVisitData">
        insert into blade_customer_visit_data(id,user_id)
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.contact},#{item.userId})
        </foreach>
    </insert>
    <insert id="saveProfessionListGroup">
        insert into profession_list_group(professionId, professionName, scustomerCount, ycustomerCount,
                                          customerCoverage)
        values (#{param.professionId}, #{param.professionName}, #{param.sCustomerCount}, #{param.yCustomerCount},
                #{param.customerCoverage})
    </insert>
    <insert id="saveProfessionChartGroup">
        insert into profession_chart_group(marketName, marketNum)
        values (#{param.marketName}, #{param.marketNum})
    </insert>
    <delete id="deleteCustomerVisitData">
        delete
        from blade_customer_visit_data
    </delete>
    <delete id="deleteProfessionListGroup">
        delete
        from profession_list_group
    </delete>
    <delete id="deleteProfessionChartGroup">
        delete
        from profession_chart_group
    </delete>
    <select id="getCustomerCount" resultType="java.lang.Long">
<!--        <if test="query.professionIdList != null and query.professionIdList.size() == 1">-->
<!--            select DISTINCT customerId from customer_all_profession-->
<!--            where professionId in (-->
<!--            <foreach collection="query.professionIdList" item="item" separator="," index="index">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )-->
<!--            <if test="1 != query.type">-->
<!--                <if test="query.startTime != null and query.startTime != ''">-->
<!--                    and createTime <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')-->
<!--                </if>-->
<!--                <if test="query.endTime != null and query.endTime != ''">-->
<!--                    and createTime <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')-->
<!--                </if>-->
<!--            </if>-->
<!--        </if>-->
        <if test="query.professionIdList != null and (query.authUserList == null or query.authUserList.size() == 0)">
            select DISTINCT cust.id
            from blade_customer cust
            where cust.forbid_status = 'A'
            and cust.is_deleted = 0
            <if test="1 != query.type">
                <if test="query.startTime != null and query.startTime != ''">
                    and cust.create_time <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
                </if>
                <if test="query.endTime != null and query.endTime != ''">
                    and cust.create_time <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
                </if>
            </if>
        </if>

            select DISTINCT cust.id from
            blade_customer cust
            left join blade_customer_charge_user charge
            on charge.customer_id = cust.id and charge.is_deleted = 0
        where
            1=1
        <if test="query.authUserList != null and query.authUserList.size() >0">
          and  charge.user_id in (
            <foreach collection="query.authUserList" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
            <if test="1 != query.type">
                <if test="query.startTime != null and query.startTime != ''">
                    and cust.create_time <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
                </if>
                <if test="query.endTime != null and query.endTime != ''">
                    and cust.create_time <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
                </if>
            </if>
    </select>
    <select id="getCallCustomerCount" resultType="java.lang.String">
        select DISTINCT 	cust.id
        from blade_customer_visit_contact visit
        JOIN blade_customer cust ON cust.id = visit.customer_id
        and cust.forbid_status = 'A'
        where visit.is_deleted = 0
        <if test="1 != query.type">
            <if test="query.startTime != null and query.startTime != ''">
                and visit.create_time <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and visit.create_time <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
            </if>
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and visit.create_user in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getCallCustomerCountByTask" resultType="java.util.Map">
        select DISTINCT visit.customer_contact contact,visit.create_user userId
        from blade_customer_visit visit
        join blade_customer cust
        on JSON_EXTRACT(visit.customer_contact,'$[*].customerId') like CONCAT('%',cust.id,'%') and cust.is_deleted = 0
        and cust.forbid_status = 'A'
        where visit.is_deleted = 0
        <if test="1 != query.type">
            <if test="query.startTime != null and query.startTime != ''">
                and visit.create_time <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and visit.create_time <![CDATA[<=]]> concat(#{query.endTime},' 23:59:59')
            </if>
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and visit.create_user in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getProjectApprovalCustomerCount" resultType="java.lang.Long">
        select DISTINCT cust.id
        from blade_project_basic visit
        join ACT_HI_TASKINST hi
        on visit.process_instance_id = hi.PROC_INST_ID_
        and hi.task_def_key_ = 'InvoicerTask'
        join blade_customer cust
        on cust.id = visit.contracting_unit_id
        and cust.is_deleted = 0
        and cust.forbid_status = 'A'
        where visit.is_deleted = 0
        <if test="1 != query.type">
            <if test="query.startTime != null and query.startTime != ''">
                and hi.end_time_ <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and hi.end_time_ <![CDATA[<=]]> concat(#{query.endTime},' 23:59:59')
            </if>
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and visit.create_user in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getBaseDiscCustomerCount" resultType="java.lang.Long">
        select distinct cust.id from blade_contract c join blade_archive_details ad on ad.contract_id = c.id and
        ad.is_deleted = 0
        join blade_customer cust on cust.id = c.sign_company_id and cust.forbid_status = 'A' and cust.is_deleted = 0
        where c.contract_type = 0 and c.is_deleted = 0 and c.archive_date is not null and c.status = 5
        <if test="1 != query.type">
            <if test="query.startTime != null and query.startTime != ''">
                and c.archive_date <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and c.archive_date <![CDATA[<=]]> concat(#{query.endTime},' 23:59:59')
            </if>
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and c.create_user in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getAreaAndMarket"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select market.market_code marketCode, area.counties_code areaCode
        from blade_profession pro
                 join blade_join_profession jo
                      on jo.profession_id = pro.id and jo.is_deleted = 0
                 join blade_profession_area area
                      on area.join_id = jo.id and area.is_deleted = 0
                 join blade_profession_market market
                      on market.join_id = jo.id and market.is_deleted = 0
        where pro.is_deleted = 0
          and pro.id = #{id}
    </select>
    <select id="getCustomerProfessionPieChart"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerProfessionPieChartVO">
        <if test="query.leader == 1">
            select market.name marketName,count(DISTINCT alldata.customerId) marketNum
            from customer_all_data alldata
            join blade_marketplace market
            on market.id = alldata.marketId
            group by market.id
        </if>
        <if test="query.leader == 2||query.leader ==3">
            select market.name marketName,count(DISTINCT alldata.customerId) marketNum
            from
            customer_all_data alldata
            join blade_marketplace market on market.id = alldata.marketId
            join blade_customer as cust on cust.id=alldata.customerId
            where alldata.userId in (
            <foreach collection="query.authUserList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
            and cust.forbid_status = 'A' and cust.is_deleted = 0
            group by market.id
        </if>
<!--
        <if test="query.leader == 3">
            select market.name marketName,count(DISTINCT pro.customerId) marketNum
            from customer_all_user pro
            join blade_marketplace market
            on market.id = pro.marketId
            where pro.userId in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
            group by market.id
        </if>
-->
    </select>
<!--    <select id="getImportancePage" resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerImportanctVO">-->
<!--        SELECT-->
<!--        any_value ( importanceKey ) importanceKey,-->
<!--        any_value ( importanceValue ) importanceValue,-->
<!--        any_value ( customerId ) customerId,-->
<!--        any_value ( customerName ) customerName,-->
<!--        count(visit.id) visitCount-->
<!--        FROM-->
<!--        (-->
<!--        SELECT DISTINCT -->
<!--            dict.dict_key      importanceKey,-->
<!--            dict.dict_value    importanceValue,-->
<!--            cust.id            customerId,-->
<!--            cust.customer_name customerName-->
<!--        FROM-->
<!--            blade_customer AS cust &#45;&#45; 客户表-->
<!--                LEFT JOIN blade_customer_charge_user AS charge ON charge.customer_id = cust.id &#45;&#45; 客户负责人表-->
<!--                LEFT JOIN blade_dict_biz AS dict ON dict.dict_key = cust.importance_key &#45;&#45; 客户重要程度-->
<!--                LEFT JOIN blade_user AS USER ON USER.id = charge.user_id &#45;&#45; 客户负责人【员工表】-->
<!--            LEFT JOIN blade_profession AS prof ON prof.charge_dept_id &#45;&#45; 区域市场表-->
<!--            LEFT JOIN blade_join_profession AS j ON j.profession_id = prof.id &#45;&#45; 中间表-->
<!--            LEFT JOIN blade_profession_area AS area ON area.join_id = j.id &#45;&#45; 区域表-->
<!--            LEFT JOIN blade_profession_market AS market ON market.join_id = j.id &#45;&#45; 行业表-->
<!--        WHERE-->
<!--            cust.is_deleted = 0-->
<!--          AND cust.forbid_status = 'A'-->
<!--        AND charge.is_deleted = 0-->
<!--        AND (dict.parent_id=1714890700588249090 and dict.is_deleted=0)-->
<!--        <if test="query.leader != 1">-->
<!--            AND FIND_IN_SET( prof.charge_dept_id, USER.dept_id )-->
<!--        </if>-->
<!--        AND (-->
<!--        1=1-->
<!--        <if test="query.authUserList != null and query.authUserList.size() >0 ">-->
<!--            and charge.user_id in (-->
<!--            <foreach collection="query.authUserList" index="index" item="item" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )&#45;&#45; 本人及下级的实有客户-->
<!--        </if>-->
<!--        <if test="query.authUserList != null and query.authUserList.size() > 1">-->
<!--            OR (-->
<!--            prof.is_deleted = 0-->
<!--            AND j.is_deleted = 0-->
<!--            AND area.is_deleted = 0-->
<!--            AND market.is_deleted = 0-->
<!--            AND area.counties_code = cust.city &#45;&#45; 县-->
<!--            AND market.market_code = cust.industry_involved &#45;&#45; 行业编码) &#45;&#45; 客户所属区域下的客户-->
<!--            )-->
<!--        </if>-->
<!--        )-->
<!--        <if test="query.searchBox!=null and query.searchBox!=''">-->
<!--            and cust.customer_name like concat('%',#{query.searchBox},'%')-->
<!--        </if>-->
<!--        <if test="query.importanceKey!=null and query.importanceKey!=''">-->
<!--            and cust.importance_key=#{query.importanceKey}-->
<!--        </if>-->
<!--        GROUP BY-->
<!--            cust.id,-->
<!--            dict.dict_key,-->
<!--            dict.dict_value)t1-->
<!--        left  JOIN blade_customer_visit visit ON (JSON_EXTRACT( customer_contact, '$[*].customerId' ) LIKE CONCAT( '%', t1.customerId, '%' )-->
<!--        <if test="query.authUserList != null and query.authUserList.size() >0 ">-->
<!--           and visit.create_user IN (-->
<!--            <foreach collection="query.authUserList" index="index" item="item" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )&#45;&#45; 本人及下级的实有客户-->
<!--        </if>-->
<!--        <if test="query.dateYear != null and query.dateYear != ''">-->
<!--            and YEAR(visit.create_time) = #{query.dateYear}-->
<!--        </if>-->
<!--        <if test="query.dateMonth != null and query.dateMonth != ''">-->
<!--            and MONTH(visit.create_time) = #{query.dateMonth}-->
<!--        </if>-->
<!--        and visit.is_deleted=0)-->
<!--        GROUP BY-->
<!--        t1.customerId-->
<!--        ORDER BY-->
<!--        count(visit.id) DESC-->
<!--        <if test="size!=null and size!=''">-->
<!--            limit #{size}-->
<!--        </if>-->
<!--    </select>-->
    <select id="getImportancePage" resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerImportanctVO">
        select cust.id customerId,cust.customer_name customerName,cust.importance_key importanceKey,
        case cust.importance_key when 1 then '公司重要' when 2 then '部门重要' when 3 then '个人重要' end importanceValue,
        count(distinct contact.customer_visit_id) visitCount,any_value(contact.create_user) createUser
        from blade_customer cust
        join blade_customer_visit_contact contact
        on cust.id = contact.customer_id
        <where>
            1 = 1
            <if test="query.importanceKey != null and query.importanceKey != ''">
                and cust.importance_key = #{query.importanceKey}
            </if>
            <if test="query.customerList != null and query.customerList.size() > 0">
                <foreach collection="query.customerList" index="index" item="item">
                    and cust.customer_name like concat('%',#{item},'%')
                </foreach>
            </if>
            <if test="query.authUserList != null and query.authUserList.size() > 0">
                and contact.create_user in (
                <foreach collection="query.authUserList" index="index" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and contact.create_time <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and contact.create_time <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
            </if>
        </where>
        group by cust.id
        order by count(contact.customer_visit_id) desc
        <if test="query.leader != 1">
            ,any_value(contact.create_user)
        </if>
        <if test="size!=null and size!=''">
            limit #{size}
        </if>
    </select>
<!--    <select id="getImportance" resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerImportanctVO">-->
<!--        SELECT-->
<!--        T.importanceKey,-->
<!--        T.importanceValue,-->
<!--        sum( T.importanceNum ) AS importanceNum-->
<!--        FROM-->
<!--        (-->
<!--            SELECT DISTINCT-->
<!--            dict.dict_key      importanceKey,-->
<!--            dict.dict_value    importanceValue,-->
<!--            IFNULL(count(DISTINCT cust.id),0) importanceNum-->
<!--        FROM-->
<!--            blade_customer AS cust &#45;&#45; 客户表-->
<!--                left join blade_customer_industry_involved industry on industry.customer_id=cust.id-->
<!--                LEFT JOIN blade_customer_charge_user AS charge ON charge.customer_id = cust.id &#45;&#45; 客户负责人表-->
<!--                LEFT JOIN blade_dict_biz AS dict ON dict.dict_key = cust.importance_key &#45;&#45; 客户重要程度-->
<!--                LEFT JOIN blade_user AS USER ON USER.id = charge.user_id &#45;&#45; 客户负责人【员工表】-->
<!--            LEFT JOIN blade_profession AS prof ON FIND_IN_SET( prof.charge_dept_id, USER.dept_id ) &#45;&#45; 区域市场表-->
<!--            LEFT JOIN blade_join_profession AS j ON j.profession_id = prof.id &#45;&#45; 中间表-->
<!--            LEFT JOIN blade_profession_area AS area ON area.join_id = j.id &#45;&#45; 区域表-->
<!--            LEFT JOIN blade_profession_market AS market ON market.join_id = j.id &#45;&#45; 行业表-->

<!--        WHERE-->
<!--            cust.is_deleted = 0-->
<!--          AND cust.forbid_status = 'A'-->
<!--          AND charge.is_deleted = 0-->
<!--          AND (dict.parent_id=1714890700588249090 and dict.is_deleted=0)-->
<!--        AND (-->
<!--        1=1-->
<!--        <if test="query.authUserList != null and query.authUserList.size() >0 ">-->
<!--            and charge.user_id in (-->
<!--            <foreach collection="query.authUserList" index="index" item="item" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )&#45;&#45; 本人及下级的实有客户-->
<!--        </if>-->
<!--        <if test="query.authUserList != null and query.authUserList.size() > 1">-->
<!--            OR (-->
<!--            prof.is_deleted = 0-->
<!--            AND j.is_deleted = 0-->
<!--            AND area.is_deleted = 0-->
<!--            AND market.is_deleted = 0-->
<!--            AND area.counties_code = cust.city &#45;&#45; 县-->
<!--            AND market.market_code = industry.industry_id &#45;&#45; 行业编码) &#45;&#45; 客户所属区域下的客户-->
<!--            )-->
<!--        </if>-->
<!--        )-->
<!--&lt;!&ndash;        <if test="query.dateYear != null and query.dateYear != ''">&ndash;&gt;-->
<!--&lt;!&ndash;            and YEAR(cust.create_time) = #{query.dateYear}&ndash;&gt;-->
<!--&lt;!&ndash;        </if>&ndash;&gt;-->
<!--&lt;!&ndash;        <if test="query.dateMonth != null and query.dateMonth != ''">&ndash;&gt;-->
<!--&lt;!&ndash;            and MONTH(cust.create_time) = #{query.dateMonth}&ndash;&gt;-->
<!--&lt;!&ndash;        </if>&ndash;&gt;-->
<!--        GROUP BY-->
<!--            dict.dict_key,-->
<!--            dict.dict_value-->
<!--        UNION-->
<!--        SELECT-->
<!--        db1.dict_key AS importanceKey,-->
<!--        db1.dict_value AS importanceValue,-->
<!--        0 AS importanceNum-->
<!--        FROM-->
<!--        blade_dict_biz db1-->
<!--        WHERE-->
<!--        db1.parent_id = 1714890700588249090-->
<!--        AND db1.is_deleted = 0-->
<!--        ) T-->
<!--        GROUP BY-->
<!--        T.importanceKey,-->
<!--        T.importanceValue-->
<!--        ORDER BY-->
<!--        T.importanceKey-->

<!--    </select>-->
    <select id="getImportance" resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerImportanctVO">
        select importanceKey,case importanceKey when 1 then '公司重要' when 2 then '部门重要' when 3 then '个人重要' end
        importanceValue,count(customerId) importanceNum
        from (
        select cust.id customerId,cust.customer_name customerName,cust.importance_key importanceKey,
        count(contact.customer_visit_id) cnt,any_value(contact.create_user) createUser
        from blade_customer cust
        join blade_customer_visit_contact contact
        on cust.id = contact.customer_id
        <where>
         1 = 1
            <if test="query.authUserList != null and query.authUserList.size() > 0">
                and contact.create_user in (
                <foreach collection="query.authUserList" index="index" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and contact.create_time <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and contact.create_time <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
            </if>
        </where>
        group by cust.id
        <if test="query.leader != 1">
            ,contact.create_user
        </if>) visit
        group by importanceKey

    </select>
<!--    <select id="getCustomerProfessionValue"-->
<!--            resultType="org.springblade.modules.lankegroup.clientrelation.vo.OutputListVO">-->
<!--        SELECT-->
<!--        marketplace.id,-->
<!--        marketplace.name as marketplac_name, &#45;&#45; 客户所属行业名称-->
<!--        SUM(contract.contract_amount) as contract_amount,&#45;&#45; 通过行业分组的项目合同额合计-->
<!--        group_concat(contract.project_id) projectId-->
<!--        FROM-->
<!--        blade_customer AS customer &#45;&#45; 客户基础信息-->
<!--        join blade_customer_industry_involved industry on industry.customer_id = customer.id and industry.is_deleted = 0-->
<!--        LEFT JOIN blade_marketplace marketplace ON marketplace.id = industry.industry_id-->
<!--&#45;&#45;         LEFT JOIN blade_marketplace as marketplace ON marketplace.id = customer.industry_involved &#45;&#45; 客户所属行业表-->
<!--        LEFT JOIN blade_project_basic as project on project.contracting_unit_id = customer.id &#45;&#45; 项目基础信息表-->
<!--        LEFT JOIN blade_contract as contract on contract.sign_company_id = customer.id AND contract.project_id =-->
<!--        project.id &#45;&#45; 合同表&#45;&#45; 合同表-->
<!--        LEFT JOIN blade_archive_details details on details.contract_id = contract.id and details.is_deleted = 0-->
<!--        JOIN ACT_HI_TASKINST hi ON details.process_instance_id = hi.PROC_INST_ID_ AND hi.task_def_key_ =-->
<!--        'ConfirmArchiving'-->
<!--        WHERE contract.contract_type = 0 &#45;&#45; 合同类型（0销售合同)-->
<!--        AND contract.STATUS = 5 &#45;&#45; 合同状态：5已归档-->
<!--        AND project.STATUS = 1 &#45;&#45; 1已审批-->
<!--        AND project.push_down = 1 &#45;&#45; 1下推-->
<!--        <if test="query.startTime != null and query.startTime != ''">-->
<!--            and hi.end_time_ <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')-->
<!--        </if>-->
<!--        <if test="query.endTime != null and query.endTime != ''">-->
<!--            and hi.end_time_ <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')-->
<!--        </if>-->
<!--        <if test="query.marketplaceIdList != null and query.marketplaceIdList.size() > 0">-->
<!--            and marketplace.id in (-->
<!--            <foreach collection="query.marketplaceIdList" separator="," item="item" index="index">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        <if test="query.authUserList != null and query.authUserList.size() > 0">-->
<!--            and project.principal_id in (-->
<!--            <foreach collection="query.authUserList" index="index" item="item" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        GROUP BY-->
<!--&#45;&#45;         customer.industry_involved-->
<!--        industry.industry_id-->
<!--        ORDER BY contract_amount desc-->

<!--    </select>-->
    <select id="getCustomerProfessionValue"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.OutputListVO">
        SELECT
        contract.project_id as  projectId ,
        industry.industry_id as id,
        project.kd_project_fid as kdProjectId,
         marketplace.name as marketplacName,
        '0.0' contractAmount
        FROM
        blade_customer AS customer -- 客户基础信息
        join blade_customer_industry_involved industry on industry.customer_id = customer.id and industry.is_deleted = 0
         LEFT JOIN blade_marketplace marketplace ON marketplace.id = industry.industry_id
        LEFT JOIN blade_project_basic as project on project.contracting_unit_id = customer.id -- 项目基础信息表
        LEFT JOIN blade_contract as contract on contract.sign_company_id = customer.id AND contract.project_id = project.id <!--合同表 -->
        LEFT JOIN blade_archive_details details on details.contract_id = contract.id and details.is_deleted = 0
        <!--left join (
                select other.other_source_id from blade_other_project_group other
                                             left join blade_business_group_disposition b on other.business_group_dis_id =b.id
                                             where other.is_deleted = 0 and b.is_deleted = 0 and b.statistics = 1
                                             group by other.other_source_id
        ) otherg on otherg.other_source_id=project.id
        JOIN blade_project_group_disposition pgd ON pgd.kingdee_id = project.group_id AND pgd.is_deleted = 0
        JOIN blade_project_group_business_disposition pgbd ON pgbd.group_id = pgd.id
        JOIN blade_business_group_disposition bgd ON bgd.id = pgbd.business_id
        AND bgd.statistics = 1
        AND bgd.is_deleted = 0-->
        WHERE contract.contract_type in (0,4)  <!-- 合同类型（0销售合同) -->
        AND contract.STATUS = 5 <!-- 合同状态：5已归档-->
        AND project.STATUS = 1 <!-- 1已审批-->
        AND project.push_down = 1 <!-- 1下推-->
        <if test="query.startTime != null and query.startTime != ''">
            and contract.archive_date <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and contract.archive_date <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
        </if>
        <if test="query.marketplaceIdList != null and query.marketplaceIdList.size() > 0">
            and marketplace.id in (
            <foreach collection="query.marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and project.principal_id in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        industry.industry_id,
        contract.project_id
    </select>
    <select id="getCustomerProfessionValueGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.OutputListVO">
        SELECT
        marketplace.id,
        marketplace.NAME AS marketplac_name,
        concat(otherg.businessId,marketplace.id) AS groupServiceId,
        otherg.businessId  AS project_type_id,
        otherg.businessName as project_type,
        0.0 contract_amount,
        contract.project_id,
        project.kd_project_fid kdProjectId
        FROM
        blade_customer AS customer
        LEFT JOIN blade_project_basic AS project ON project.contracting_unit_id = customer.id
        join blade_customer_industry_involved industry on industry.customer_id = customer.id and industry.is_deleted = 0
        <!-- join blade_project_group_disposition pgd on pgd.kingdee_id = project.group_id and pgd.is_deleted = 0
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id
        join blade_business_group_disposition bgd  on bgd.id = pgbd.business_id and bgd.statistics = 1 and bgd.is_deleted = 0-->
        left join (
            select other.other_source_id,b.id as businessId,b.name as businessName from blade_other_project_group other
            left join blade_business_group_disposition b on other.business_group_dis_id = b.id
            where other.is_deleted = 0 and b.is_deleted = 0 and b.statistics=1
            <if test="query.projectTypeId != null and query.projectTypeId != ''">
                AND b.id = #{query.projectTypeId}
            </if>
            ) otherg on otherg.other_source_id=project.id
        LEFT JOIN blade_marketplace AS marketplace ON marketplace.id = industry.industry_id
        LEFT JOIN blade_contract AS contract ON contract.sign_company_id = customer.id
        AND contract.project_id = project.id
        LEFT JOIN blade_archive_details details on details.contract_id = contract.id and details.is_deleted = 0
        WHERE contract.contract_type = 0
        AND project.STATUS = 1
        AND project.push_down = 1
        AND contract.STATUS = 5
        AND contract.is_deleted = 0

        <if test="query.startTime != null and query.startTime != ''">
            and contract.archive_date <![CDATA[>=]]>   concat(#{query.startTime},' 00:00:00')
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and contract.archive_date <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
        </if>
        <if test="query.marketplaceIdList != null and query.marketplaceIdList.size() > 0">
            and marketplace.id in (
            <foreach collection="query.marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and project.principal_id in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        industry.industry_id,
        otherg.businessId,
        contract.project_id
    </select>

    <select id="getCustomerValue" resultType="org.springblade.modules.lankegroup.clientrelation.vo.OutputListVO">
        SELECT
        marketplace.id,
        marketplace.name as marketplac_name, <!-- 客户所属行业名称-->
        customer.customer_name, <!-- 客户名称-->
        SUM(contract.contract_amount) as contract_amount,<!-- 通过行业分组的项目合同额合计-->
        group_concat(contract.project_id) projectId
        FROM
        blade_customer AS customer <!-- 客户基础信息-->
        join blade_customer_industry_involved industry on industry.customer_id = customer.id and industry.is_deleted = 0
        LEFT JOIN blade_marketplace marketplace ON marketplace.id = industry.industry_id
        LEFT JOIN blade_project_basic as project on project.contracting_unit_id = customer.id <!-- 项目基础信息表-->
        LEFT JOIN blade_contract as contract on contract.sign_company_id = customer.id AND contract.project_id = project.id <!-- 合同表-->
        LEFT JOIN blade_archive_details details on details.contract_id = contract.id and details.is_deleted = 0
        <!--left join (
            select other.other_source_id from blade_other_project_group other
                                         left join blade_business_group_disposition b on other.business_group_dis_id = b.id
                                         where other.is_deleted = 0 and b.is_deleted = 0 and b.statistics=1
        ) otherg on otherg.other_source_id=project.id
        JOIN blade_project_group_disposition pgd ON pgd.kingdee_id = project.group_id AND pgd.is_deleted = 0
        JOIN blade_project_group_business_disposition pgbd ON pgbd.group_id = pgd.id
        JOIN blade_business_group_disposition bgd ON bgd.id = pgbd.business_id
        AND bgd.statistics = 1
        AND bgd.is_deleted = 0-->
        WHERE contract.contract_type in (0,4)  <!-- 合同类型（0销售合同4渠道)-->
        AND contract.STATUS = 5 <!-- 合同状态：5已归档-->
        <if test="query.startTime != null and query.startTime != ''">
            and contract.archive_date  <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and contract.archive_date <![CDATA[<=]]>  concat(#{query.endTime},' 23:59:59')
        </if>
        <if test="query.searchBox!=null and query.searchBox!=''">
            and customer.customer_name like concat('%',#{query.searchBox},'%')
        </if>
        <if test="query.marketplaceIdList != null and query.marketplaceIdList.size() > 0">
            and marketplace.id in (
            <foreach collection="query.marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and project.principal_id in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>

        GROUP BY
        industry.industry_id,customer.customer_name
        ORDER BY contract_amount desc
    </select>
    <select id="getCustomerValueGroup" resultType="org.springblade.modules.lankegroup.clientrelation.vo.OutputListVO">
        SELECT
        marketplace.id,
        marketplace.NAME as marketplac_name,
        customer.customer_name,
        <!--if(bgd.id=0,pgd.id,bgd.id) as project_type_id,
        if(bgd.id=0,pgd.NAME,bgd.name ) as project_type,-->
        SUM( contract.contract_amount ) AS contract_amount,
        group_concat(contract.project_id) projectId
        FROM
        blade_customer AS customer
        LEFT JOIN blade_project_basic AS project ON project.contracting_unit_id = customer.id
        join blade_customer_industry_involved industry on industry.customer_id = customer.id and industry.is_deleted = 0
        <!--join blade_project_group_disposition pgd on pgd.kingdee_id = project.group_id and pgd.is_deleted = 0
        join blade_project_group_business_disposition pgbd on pgbd.group_id = pgd.id
        join blade_business_group_disposition bgd  on bgd.id = pgbd.business_id and bgd.statistics = 1 and bgd.is_deleted = 0-->
        LEFT JOIN blade_marketplace AS marketplace ON marketplace.id = industry.industry_id
        LEFT JOIN blade_contract AS contract ON contract.sign_company_id = customer.id AND contract.project_id = project.id
        LEFT JOIN blade_archive_details details on details.contract_id = contract.id and details.is_deleted = 0
        WHERE
        contract.contract_type in (0,4)
        AND project.STATUS = 1
        AND project.push_down = 1
        AND contract.STATUS = 5
        AND contract.is_deleted = 0
        <!--if test="query.projectTypeId != null and query.projectTypeId != ''">
            AND bgd.id = #{query.projectTypeId}
        </if-->
        <if test="query.searchBox!=null and query.searchBox!=''">
            and customer.customer_name like concat('%',#{query.searchBox},'%')
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and contract.archive_date <![CDATA[>=]]>  concat(#{query.startTime},' 00:00:00')
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and contract.archive_date <![CDATA[<=]]> concat(#{query.endTime},' 23:59:59')
        </if>
        <if test="query.marketplaceIdList != null and query.marketplaceIdList.size() > 0">
            and marketplace.id in (
            <foreach collection="query.marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            and project.principal_id in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        industry.industry_id,customer.customer_name
        ORDER BY contract_amount desc
    </select>
    <select id="getCustomerProfessionY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select any_value(mp.id) marketCode,any_value(mp.name) marketName,(count(mp.id) * mp.num) yCustomerCount
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_marketplace mp
        on mp.id = market.market_code and mp.is_deleted = 0 and mp.status = 0
        <if test="(query.leader != null and query.leader == 1) and (query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0)">
        join blade_personnel per
        on per.profession_id = pro.id and per.is_deleted = 0
        </if>
        where pro.is_deleted = 0
        <if test="query.marketName != null and query.marketName != ''">
            and mp.name like concat('%',#{query.marketName},'%')
        </if>
        <if test="query.leader != null and query.leader == 1">
            <if test="query.professionId != null and query.professionId != ''">
                and pro.id = #{query.professionId}
            </if>
            <if test="query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0">
                <choose>
                    <when test="query.professionId != null and query.professionId != ''">
                        or
                    </when>
                    <otherwise>
                        and
                    </otherwise>
                </choose>
                per.user_id in (
                <foreach collection="query.areaAndMarketUserList" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        group by mp.id
    </select>
    <select id="getCustomerProfessionS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select any_value(mp.id) marketCode,any_value(mp.name) marketName,count(distinct cust.id) sCustomerCount
        from blade_customer cust
        join blade_marketplace mp
        on mp.id = cust.industry_involved and mp.status = 0 and mp.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.is_deleted = 0 and cust.forbid_status = 'A'
        <if test="query.marketName != null and query.marketName != ''">
            and mp.name like concat('%',#{query.marketName},'%')
        </if>
        <if test="(query.areaAndMarketList != null and query.areaAndMarketList.size() > 0) or
        (query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0)">
            and (
            <if test="query.areaAndMarketList != null and query.areaAndMarketList.size() > 0">
                EXISTS (
                SELECT 1 FROM blade_customer sub_cust
                WHERE sub_cust.id = cust.id
                and (
                <foreach collection="query.areaAndMarketList" index="index" item="item" separator="or">
                    (cust.industry_involved = #{item.marketCode} and cust.city = #{item.areaCode} )
                </foreach>
                )
                )
            </if>
<!--            <if test="query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0">-->
<!--                <choose>-->
<!--                    <when test="query.areaAndMarketList != null and query.areaAndMarketList.size() > 0">-->
<!--                        or-->
<!--                    </when>-->
<!--                </choose>-->
<!--                charge.user_id in (-->
<!--                <foreach collection="query.areaAndMarketUserList" index="index" item="item" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
            )
        </if>
        group by mp.id
    </select>
    <select id="getCustomerProfessionPieChartCount"
            resultType="java.lang.Integer">
        <!-- 高层 -->
        <if test="query.leader == 1">
            select count(DISTINCT cust.id)
            from blade_customer cust
            join blade_customer_industry_involved industry
            on industry.customer_id = cust.id and industry.is_deleted = 0
            where cust.forbid_status = 'A' and cust.is_deleted = 0
        </if>
        <!-- 组长 -->
        <if test="query.leader == 2||query.leader==3">
     <!--       select count(distinct customerId) cnt from customer_all_profession where
            professionId in(
            <foreach collection="query.professionIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )-->

            select count(DISTINCT alldata.customerId) cnt
            from
            customer_all_data alldata
            join blade_marketplace market on market.id = alldata.marketId
            join blade_customer as cust on cust.id=alldata.customerId
            where alldata.userId in (
            <foreach collection="query.authUserList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
            and cust.forbid_status = 'A' and cust.is_deleted = 0
        </if>
        <!-- 组员 -->
        <!--<if test="query.leader == 3">
            select count(distinct customerId) cnt from customer_all_user where userId in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>-->
    </select>
    <select id="getCustomerRegionY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select professionId,professionName,sum(num) yCustomerCount from (
        select any_value(pro.id) professionId,any_value(pro.profession_name) professionName,any_value(any_value(mp.num)
        * count(mp.id)) num
        from blade_profession pro
        left join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        left join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        left join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        left join blade_marketplace mp
        on mp.id = market_code and mp.status = 0 and mp.is_deleted = 0
        left join blade_personnel per
        on per.profession_id = pro.id and per.is_deleted = 0
        where pro.is_deleted = 0
        <if test="query.professionId != null and query.professionId != ''">
            and pro.id = #{query.professionId}
        </if>
        <if test="query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0">
            and per.user_id in (
            <foreach collection="query.areaAndMarketUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by mp.id,pro.id
        ) t group by t.professionName
    </select>
    <select id="getCustomerRegionS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select any_value(pro.id) professionId,any_value(pro.profession_name) professionName,count(cust.id)
        sCustomerCount
        from blade_profession pro
        left join blade_join_profession jo
        on pro.id = jo.profession_id and jo.is_deleted = 0
        left join blade_profession_market market
        on market.is_deleted = 0 and market.join_id = jo.id
        left join blade_profession_area area
        on area.is_deleted = 0 and area.join_id = jo.id
        left join blade_personnel per
        on per.profession_id = pro.id and per.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.user_id = per.user_id and charge.is_deleted = 0
        left join blade_customer cust
        on cust.id = charge.customer_id and cust.forbid_status = 'A' and cust.is_deleted = 0
        where pro.is_deleted = 0 and cust.id is not null
        <if test="query.professionId != null and query.professionId != ''">
            and pro.id = #{query.professionId}
        </if>
        <if test="query.areaAndMarketUserList != null and query.areaAndMarketUserList.size() > 0">
            and per.user_id in (
            <foreach collection="query.areaAndMarketUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cust.id,pro.id
    </select>
    <select id="getCustomerRegionSingleY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select any_value(per.user_name) professionName,any_value(per.id) professionId,(count(market.market_id) * mp.num)
        yCustomerCount from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        join blade_marketplace mp
        on mp.id = market.market_id and mp.status = 0 and mp.is_deleted = 0
        where per.user_id in (
        <foreach collection="query.areaAndMarketUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        ) and per.is_deleted = 0
        group by per.user_id,market.market_id
    </select>
    <select id="getCustomerRegionSingleS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select any_value(per.id) professionId,count(distinct cust.id) sCustomerCount from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        where cust.is_deleted = 0 and cust.forbid_status = 'A' and charge.user_id in (
        <foreach collection="query.areaAndMarketUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        group by charge.user_id
    </select>
    <select id="getUserIdByProfessionId" resultType="java.lang.Long">
        select u.id
        from blade_profession pro
                 join blade_user u
                      on u.dept_id in (pro.charge_dept_id) and u.is_deleted = 0
        where pro.is_deleted = 0
          and pro.id = #{professionId}
    </select>
    <select id="getPersonnelByAreaAndMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanPersonnelDetailResult">
        select per.user_id userId,per.user_name userName,market.market_id marketCode,market.market_name marketName,
        market.area_id areaCode,market.area_name areaName from blade_personnel per
        left join blade_personnel_market market
        on market.is_deleted = 0 and per.id = market.personnel_id
        where per.is_deleted = 0
        and per.user_id in (
        <foreach collection="userIdByProfessionId" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and market.market_code in (
            <foreach collection="marketplaceIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and market.area_code in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        union all
        select u.id userId,u.name userName,cust.industry_involved marketCode,mp.name marketName,cust.city
        areaCode,area.name areaName from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_user u
        on u.id = charge.user_id and u.is_deleted = 0
        join blade_marketplace mp
        on mp.id = cust.industry_involved and mp.status = 0 and mp.is_deleted = 0
        join blade_area area
        on area.code = cust.city
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        and charge.user_id in(
        <foreach collection="userIdByProfessionId" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and cust.industry_involved in (
            <foreach collection="marketplaceIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and cust.city in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getCustomerByAreaAndMarket"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanPersonnelDetailResult">
        select cust.id customerId, cust.customer_name customerName
        from blade_customer cust
                 join blade_customer_charge_user charge
                      on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A'
          and cust.is_deleted = 0
          and cust.industry_involved = #{marketCode}
          and cust.city = #{areaCode}
          and charge.user_id = #{userId} limit 1
    </select>
    <select id="getDeptByProfessionByUserId" resultType="java.lang.Long">
        select distinct per.user_id
        from blade_personnel per
        join blade_profession pro
        on pro.id = per.profession_id and pro.is_deleted = 0
        where per.is_deleted = 0
        and pro.charge_dept_id in (
            <foreach collection="deptId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
    </select>
    <select id="getDeptByProfessionId" resultType="java.lang.Long">
        select pro.id
        from blade_profession pro
        where pro.is_deleted = 0
        and pro.charge_dept_id in (
        <foreach collection="deptId" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </select>
    <select id="getMarketByUserId"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerProfessionPieChartVO">
        select market.name marketName,count(alluser.customerId) marketNum
        from customer_all_user alluser
        join blade_marketplace market
        on market.id = alluser.marketId
        where userId in (
        <foreach collection="userId" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
        group by market.id
    </select>
    <select id="getMarketByUserIdCount" resultType="java.lang.Double">
        select count(customerId) marketNum from customer_all_user where
        userId in (
        <foreach collection="userId" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </select>
    <select id="getMarketByUserIdAndProfessionId"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerProfessionPieChartVO">
        select user.id userId,user.name marketName,count(alluser.customerId) marketNum
        from blade_profession pro
        join blade_personnel per
        on per.profession_id = pro.id and per.is_deleted = 0
        join customer_all_user alluser
        on alluser.userId = per.user_id
        join blade_user user
        on user.id = alluser.userId
        where pro.id in (
        <foreach collection="professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        group by alluser.userId
    </select>
    <select id="getMarketByUserIdAndProfessionIdCount" resultType="java.lang.Double">
        select count(distinct customerId) marketNum from customer_all_profession
        where professionId in (
        <foreach collection="professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="getCustomerCountByAllUser" resultType="java.lang.Double">
        select count(DISTINCT cust.id)
        from blade_customer cust
                 join blade_customer_industry_involved industry
                      on industry.customer_id = cust.id and industry.is_deleted = 0
        where cust.forbid_status = 'A'
          and cust.is_deleted = 0
    </select>
    <select id="getMarketByProfessionId"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerProfessionPieChartVO">
        select (select pro.profession_name from blade_profession pro where pro.id = #{professionId})
        marketName,count(DISTINCT cust.id) marketNum
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        and (
        <foreach collection="marketList" separator="or" item="item" index="index">
            (industry.industry_id = #{item.marketCode} and cust.city = #{item.areaCode})
        </foreach>
        <if test="userId != null and userId.size() > 0">
            or charge.user_id in (
            <foreach collection="userId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        )
    </select>
    <select id="getCustomerProfessionOneY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select marketCode,marketName,yCustomerCount,sCustomerCount,(sCustomerCount / yCustomerCount) * 100
        customerCoverage from (
        select t1.marketCode,t1.marketName,t1.yCustomerCount,case when t2.sCustomerCount is null then 0 else
        t2.sCustomerCount end sCustomerCount from (
        select market.market_code marketCode,market.market_name marketName,sum(market.num) yCustomerCount
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id
        join blade_profession_market market
        on market.join_id = jo.id
        join blade_profession_area area
        on area.join_id = jo.id
        <where>
            <if test="query.marketName != null and query.marketName != ''">
                market.market_name like concat('%',#{query.marketName},'%')
            </if>
        </where>
        group by market.market_code
        ) t1
        left join
        (select marketId,count(DISTINCT customerId) sCustomerCount from customer_all_data
    where
        1=1
        <if test="query.authUserList!=null and query.authUserList.size()>0">
            and  userId in (
            <foreach collection="query.authUserList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by marketId) t2
        on t2.marketId = t1.marketCode ) t
        where
        t.sCustomerCount>0
    </select>
    <select id="getCustomerProfessionTwoY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select marketCode,marketName,yCustomerCount,sCustomerCount,(sCustomerCount / yCustomerCount) * 100
        customerCoverage from (
        select marketCode marketCode, marketName marketName, oughtClient yCustomerCount, count(allpro.customerId)
        sCustomerCount
        from (
        select marketCode, marketName, oughtClient, max(type)
        from (
        select market.market_code marketCode,
        market.market_name marketName,
        sum(market.num) oughtClient,
        1 type
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id in (
        <foreach collection="query.professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and market.market_name like concat('%',#{query.marketName},'%')
        </if>
        group by market.market_code
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            union all
            select market.id marketCode, market.name marketName, 0 oughtClient, 0 type
            from customer_all_data alldata
            join blade_marketplace market
            on market.id = alldata.marketId and market.is_deleted = 0 and market.status = 0
            where userId in (
            <foreach collection="query.authUserList" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
            <if test="query.marketName != null and query.marketName != ''">
                and market.name like concat('%',#{query.marketName},'%')
            </if>
            group by market.id
        </if>
        ) t
        group by marketCode) t1
        left join customer_all_profession allpro
        on allpro.marketId = t1.marketCode and allpro.professionId in (
        <foreach collection="query.professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        group by t1.marketCode
        order by marketCode ) t
    </select>
    <select id="getCustomerProfessionThreeY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select marketCode,marketName,yCustomerCount,sCustomerCount,(sCustomerCount / yCustomerCount) * 100
        customerCoverage from (
        select marketId marketCode,
        marketName marketName,
        oughtClient yCustomerCount,
        case when t2.actualClient is null then 0 else actualClient end sCustomerCount
        from (
        select
            marketId,
            any_value(marketName) as marketName,
            any_value(oughtClient) as oughtClient,
            max(type)
        from (
        select market.market_id marketId,
        any_value(market.market_name) marketName,
        sum(market.num) oughtClient,
        1 type
        from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        where per.user_id in (
        <foreach collection="query.authUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and market.market_name like concat('%',#{query.marketName},'%')
        </if>
        and per.is_deleted = 0
        group by market.market_id
        union all
        select alldata.marketId, market.name marketName, 0 oughtClient, 0 type
        from customer_all_data alldata
        join blade_marketplace market
        on market.id = alldata.marketId
        join blade_personnel per
        on per.user_id = alldata.userId
        where per.user_id in (
        <foreach collection="query.authUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and market.name like concat('%',#{query.marketName},'%')
        </if>
        group by market.id
        ) t1
        group by marketId) t1
        left join
        (select market.id, market.name, count(alluser.customerId) actualClient
        from customer_all_user alluser
        join blade_personnel per
        on per.user_id = alluser.userId and per.is_deleted = 0
        join blade_marketplace market
        on market.id = alluser.marketId
        where per.user_id in (
        <foreach collection="query.authUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and market.name like concat('%',#{query.marketName},'%')
        </if>
        group by alluser.marketId) t2
        on t1.marketId = t2.id ) t
    </select>
    <select id="getCustomerProfessionOneS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select industry.industry_id marketCode,any_value(mp.name) marketName,count(DISTINCT cust.id) sCustomerCount
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_marketplace mp
        on mp.id = industry.industry_id and mp.is_deleted = 0
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        <if test="query.marketName != null and query.marketName != ''">
            and mp.name like concat('%',#{query.marketName},'%')
        </if>
        group by industry.industry_id
    </select>
    <select id="getCustomerProfessionTwoS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select industry.industry_id marketCode,any_value(mp.name) marketName,count(DISTINCT cust.id) sCustomerCount, 0
        yCustomerCount
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_marketplace mp
        on mp.id = industry.industry_id and mp.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        and (
        <foreach collection="query.areaAndMarketList" separator="or" item="item" index="index">
            (industry.industry_id = #{item.marketCode} and cust.city = #{item.areaCode})
        </foreach>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            or charge.user_id in (
            <foreach collection="query.authUserList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and mp.name like concat('%',#{query.marketName},'%')
        </if>
        group by industry.industry_id
    </select>
    <select id="getCustomerProfessionThreeS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanResult">
        select industry.industry_id marketCode,any_value(mp.name) marketName,count(DISTINCT cust.id) sCustomerCount, 0
        yCustomerCount
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_marketplace mp
        on mp.id = industry.industry_id and mp.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        and charge.user_id in (
        <foreach collection="query.authUserList" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
        <if test="query.marketName != null and query.marketName != ''">
            and mp.name like concat('%',#{query.marketName},'%')
        </if>
        group by industry.industry_id
    </select>
    <select id="getAllCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        select
        market.marketCode,market.marketName,market.areaCode,market.areaName,customer.customerId,customer.customerName
        from (
        select DISTINCT marketCode,marketName,areaCode,areaName from (
        select proMarket.market_code marketCode,proMarket.market_name marketName,area.counties_code
        areaCode,area.counties_name areaName
        from blade_profession_market proMarket
        join blade_join_profession jo
        on jo.id = proMarket.join_id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        where proMarket.is_deleted = 0
        union all
        select perMarket.market_id marketCode,perMarket.market_name marketName,perMarket.area_id
        areaCode,perMarket.area_name areaName
        from blade_personnel_market perMarket
        where perMarket.is_deleted = 0
        union all
        SELECT
        industry.industry_id AS marketCode,
        market.name AS marketName,
        cust.city AS areaCode,
        cust.city_name AS areaName
        FROM
        blade_customer cust
        JOIN blade_customer_industry_involved industry ON industry.customer_id = cust.id
        AND industry.is_deleted = 0
        JOIN blade_marketplace market
        ON market.id = industry.industry_id and market.is_deleted = 0
        LEFT JOIN blade_customer_charge_user charge ON charge.customer_id = cust.id
        AND charge.is_deleted = 0
        WHERE
        cust.forbid_status = 'A'
        AND cust.is_deleted = 0
        ) market ) market
        left join (
        select alldata.marketId marketCode, alldata.city areaCode,customerId ,cust.customer_name customerName,userId
        from customer_all_data alldata
        join blade_customer cust
        on cust.id = alldata.customerId
        group by customerId,alldata.marketId
        ) customer on customer.areaCode = market.areaCode and customer.marketCode = market.marketCode
        where 1 = 1
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and market.marketCode in (
            <foreach collection="marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and market.areaCode in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="isData == 1">
            and customer.customerName is not null
        </if>
        <if test="isData == 2">
            and customer.customerName is null
        </if>
<if test="authUserList!=null and authUserList.size()>0">
    and customer.userId in
    (
    <foreach collection="authUserList" index="index" item="item" separator=",">
        #{item}
    </foreach>
    )
</if>
    </select>
    <select id="getGroupCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        select DISTINCT
        market.marketCode,market.marketName,market.areaCode,market.areaName,customer.customerId,customer.customerName
        from (
        select DISTINCT marketCode,marketName,areaCode,areaName from (
        select proMarket.market_code marketCode,proMarket.market_name marketName,area.counties_code
        areaCode,area.counties_name areaName
        from blade_profession_market proMarket
        join blade_join_profession jo
        on jo.id = proMarket.join_id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        where proMarket.is_deleted = 0
        union all
        select perMarket.market_id marketCode,perMarket.market_name marketName,perMarket.area_id
        areaCode,perMarket.area_name areaName
        from blade_personnel_market perMarket
        where perMarket.is_deleted = 0
        union all
        SELECT
        industry.industry_id AS marketCode,
        market.name AS marketName,
        cust.city AS areaCode,
        cust.city_name AS areaName
        FROM
        blade_customer cust
        JOIN blade_customer_industry_involved industry ON industry.customer_id = cust.id
        AND industry.is_deleted = 0
        JOIN blade_marketplace market
        ON market.id = industry.industry_id and market.is_deleted = 0
        LEFT JOIN blade_customer_charge_user charge ON charge.customer_id = cust.id
        AND charge.is_deleted = 0
        WHERE
        cust.forbid_status = 'A'
        AND cust.is_deleted = 0
        <if test="authUserList != null and authUserList.size() > 0">
            and charge.user_id in (
            <foreach collection="authUserList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        ) market ) market
        left join (
        SELECT
        alldata.marketId AS marketCode,
        alldata.city AS areaCode,
        customerId,
        cust.customer_name AS customerName
        FROM
        customer_all_profession alldata
        JOIN blade_customer cust ON cust.id = alldata.customerId
        where professionId in (
        <foreach collection="professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        GROUP BY
        customerId
        ) customer on customer.areaCode = market.areaCode and customer.marketCode = market.marketCode
        where 1 = 1
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and market.marketCode in (
            <foreach collection="marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and market.areaCode in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="isData == 1">
            and customer.customerName is not null
        </if>
        <if test="isData == 2">
            and customer.customerName is null
        </if>
    </select>
    <select id="getPersonnelCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        SELECT
        alldata.marketId AS marketCode,
        market.name marketName,
        alldata.city AS areaCode,
        cust.city_name areaName,
        customerId,
        cust.customer_name AS customerName
        FROM
        customer_all_user alldata
        JOIN blade_customer cust ON cust.id = alldata.customerId
        JOIN blade_marketplace market ON market.id = alldata.marketId
        where userId = #{userId}
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and alldata.marketId in (
            <foreach collection="marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and alldata.city in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="isData == 1">
            and cust.customer_name is not null
        </if>
        <if test="isData == 2">
            and cust.customer_name is null
        </if>
        GROUP BY
        customerId
    </select>
    <select id="getAllAreaCustomerListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select pro.id professionId, pro.profession_name professionName, sum(market.num) yCustomerCount, 0 sCustomerCount
        from blade_profession pro
                 join blade_join_profession jo
                      on jo.profession_id = pro.id and jo.is_deleted = 0
                 join blade_profession_market market
                      on market.join_id = jo.id and market.is_deleted = 0
                 join blade_profession_area area
                      on area.join_id = jo.id and area.is_deleted = 0
        where pro.is_deleted = 0
        group by pro.id
    </select>
    <select id="getGroupAreaCustomerListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select pro.id professionId,pro.profession_name professionName,sum(market.num) yCustomerCount, 0 sCustomerCount
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        where pro.is_deleted = 0 and pro.id in (
        <foreach collection="professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        group by pro.id
    </select>
    <select id="getPersonnelAreaCustomerListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select market.area_id professionId,any_value(market.area_name) professionName,sum(market.num) yCustomerCount, 0
        sCustomerCount
        from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        where per.user_id in (
        <foreach collection="authUserList" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
        group by market.area_id
    </select>
    <select id="getGroupAreaCustomerListS"
            resultType="java.lang.Integer">
        SELECT
        count( DISTINCT cust.id )
        FROM
        blade_customer cust
        JOIN blade_customer_industry_involved industry ON industry.customer_id = cust.id
        AND industry.is_deleted = 0
        JOIN blade_marketplace mp ON mp.id = industry.industry_id
        AND mp.is_deleted = 0
        AND mp.STATUS = 0
        LEFT JOIN blade_customer_charge_user charge ON charge.customer_id = cust.id
        AND charge.is_deleted = 0
        WHERE
        cust.forbid_status = 'A'
        AND cust.is_deleted = 0
        and (
        <foreach collection="query.areaAndMarketList" separator="or" item="item" index="index">
            (industry.industry_id = #{item.marketCode} and cust.city = #{item.areaCode})
        </foreach>
        <if test="query.authUserList != null and query.authUserList.size() > 0">
            or charge.user_id in (
            <foreach collection="query.authUserList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        )
    </select>
    <select id="getPersonnelAreaCustomerListS"
            resultType="java.lang.Integer">
        select count(DISTINCT cust.id)
        from blade_customer cust
                 join blade_customer_charge_user charge
                      on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A'
          and cust.is_deleted = 0
          and charge.user_id = #{userId}
    </select>
    <select id="getProfessionIdByUserId" resultType="java.lang.Long">
        select user_id
        from blade_personnel
        where profession_id = #{professionId}
    </select>
    <select id="getGroupAreaCustomerPersonnelListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select any_value(per.id) personnelId,any_value(per.user_id) professionId,any_value(per.user_name) professionName,
        sum(market.num) ycustomerCount,any_value(allcust.actualClient) scustomerCount,(any_value(allcust.actualClient) / sum(market.num)) *
        100 customerCoverage
        from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        join personnel_all_customer allcust
        on allcust.personnelId = per.id
        where per.profession_id in (
        <foreach collection="professionIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        ) and per.is_deleted = 0
        group by per.id
    </select>
    <select id="getGroupAreaCustomerPersonnelSingleListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select allcust.userId professionId,per.id personnelId,per.user_name professionName,oughtClient
        yCustomerCount,actualClient sCustomerCount,
        (actualClient / oughtClient) * 100 customerCoverage
        from personnel_all_customer allcust
        join blade_personnel per
        on per.user_id = allcust.userId and per.is_deleted = 0
        where allcust.userId in (
        <foreach collection="authUserList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="getPersonnelByMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        select DISTINCT
        market.marketCode,market.marketName,market.areaCode,market.areaName,customer.customerId,customer.customerName
        from (
        select DISTINCT marketCode,marketName,areaCode,areaName,userId from (
        select proMarket.market_code marketCode,proMarket.market_name marketName,area.counties_code
        areaCode,area.counties_name areaName,0 userId
        from blade_profession_market proMarket
        join blade_join_profession jo
        on jo.id = proMarket.join_id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        where proMarket.is_deleted = 0
        union all
        select perMarket.market_id marketCode,perMarket.market_name marketName,perMarket.area_id
        areaCode,perMarket.area_name areaName,personnel.user_id userId
        from blade_personnel_market perMarket
        join blade_personnel personnel
        on personnel.id = perMarket.personnel_id and personnel.is_deleted = 0
        where perMarket.is_deleted = 0
        union all
        SELECT
        industry.industry_id AS marketCode,
        market.name AS marketName,
        cust.city AS areaCode,
        cust.city_name AS areaName,
        0 userId
        FROM
        blade_customer cust
        JOIN blade_customer_industry_involved industry ON industry.customer_id = cust.id
        AND industry.is_deleted = 0
        JOIN blade_marketplace market
        ON market.id = industry.industry_id and market.is_deleted = 0
        LEFT JOIN blade_customer_charge_user charge ON charge.customer_id = cust.id
        AND charge.is_deleted = 0
        WHERE
        cust.forbid_status = 'A'
        AND cust.is_deleted = 0
        <if test="authUserList != null and authUserList.size() > 0">
            and charge.user_id in (
            <foreach collection="authUserList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        ) market ) market
        left join (
        select customerId,customerName,marketCode,areaCode,userId from (
        select cust.id customerId,cust.customer_name customerName,industry.industry_id marketCode,cust.city
        areaCode,charge.user_id userId
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        where cust.forbid_status = 'A' and cust.is_deleted = 0
        <if test="professionId != null">
            and charge.user_id = #{professionId}
        </if>
        )customer
        ) customer on customer.areaCode = market.areaCode and customer.marketCode = market.marketCode
        where 1 = 1
        <if test="authUserList != null and authUserList.size() > 0">
            and customer.userId in (
            <foreach collection="authUserList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
            and market.marketCode in (
            <foreach collection="marketplaceIdList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaIdList != null and areaIdList.size() > 0">
            and market.areaCode in (
            <foreach collection="areaIdList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="professionId != null">
            and customer.userId = #{professionId} or (market.userId = #{professionId} and (customer.userId =
            #{professionId} or customer.userId is null)
            <if test="marketplaceIdList != null and marketplaceIdList.size() > 0">
                and market.marketCode in (
                <foreach collection="marketplaceIdList" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
            <if test="areaIdList != null and areaIdList.size() > 0">
                and market.areaCode in (
                <foreach collection="areaIdList" index="index" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            )
        </if>
        order by areaCode,marketCode asc
    </select>
    <select id="getAreaAndMarketByUser"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select DISTINCT market.market_id marketCode, market.area_id areaCode
        from blade_personnel per
                 join blade_personnel_market market
                      on market.personnel_id = per.id and market.is_deleted = 0
        where per.is_deleted = 0
          and per.user_id = #{id}
    </select>
    <select id="getMarketByUserIdAndProfessionIdByCustomerId"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        select cust.city areaCode,cust.city_name areaName,market.id marketCode,market.name marketName,cust.id customerId,cust.customer_name customerName
        from blade_customer cust
        join (
        select DISTINCT t1.customerId from (
        select DISTINCT allpro.customerId
        from customer_all_profession allpro
        where allpro.professionId in (
        <foreach collection="professionIdList" index="index" separator="," item="item">
            #{item}
        </foreach>
        )
        ) t1
        left join
        (
        select DISTINCT alluser.customerId
        from customer_all_user alluser
        where alluser.userId in (
            <foreach collection="authUserList" index="index" separator="," item="item">
                #{item}
            </foreach>
        )
        ) t2
        on t1.customerId = t2.customerId where t2.customerId is null) t3 on t3.customerId = cust.id
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_marketplace market
        on market.id = industry.industry_id
    </select>
    <select id="getCustomerListById"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanDetailResult">
        select market.id marketCode,market.name marketName,cust.city areaCode,cust.city_name areaName,cust.id
        customerId,cust.customer_name customerName from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id
        join blade_marketplace market
        on market.id = industry.industry_id
        where cust.id in (
        <foreach collection="list" item="item" separator="," index="index">
            #{item}
        </foreach>
        )
    </select>
    <select id="getCallCustomerCountByData" resultType="java.lang.String">
        select customer_id from blade_customer_visit_contact
        <where>
            <if test="query.authUserList != null and query.authUserList.size() > 0">
                and create_user in (
                <foreach collection="query.authUserList" index="index" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        group by customer_id
    </select>
    <select id="getProfessionListGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select professionId,
               professionName,
               oughtClient                        yCustomerCount,
               actualClient                       sCustomerCount,
               (actualClient / oughtClient) * 100 customerCoverage
        from profession_all_customer
    </select>
    <select id="getProfessionChartGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerProfessionPieChartVO">
        select professionName marketName, actualClient marketNum
        from profession_all_customer
    </select>
    <select id="getQiTaCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select '1' professionId,
        '其他' professionName,
        count(cust.id) scustomerCount,
        0 ycustomerCount,
        100 customerCoverage
        from blade_customer cust
        join (
        select DISTINCT t1.customerId
        from (
        select DISTINCT allpro.customerId
        from customer_all_profession allpro
        where allpro.professionId in (
        <foreach collection="professionId" item="item" separator="," index="index">
            #{item}
        </foreach>
        )
        ) t1
        left join
        (
        select DISTINCT alluser.customerId
        from customer_all_user alluser
        where alluser.userId in (
        <foreach collection="userList" item="item" separator="," index="index">
            #{item}
        </foreach>
        )
        ) t2
        on t1.customerId = t2.customerId
        where t2.customerId is null) t3 on t3.customerId = cust.id
    </select>
    <select id="getQiTaCustomerListS"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.CustomerKanBanProfessionResult">
        select '1' professionId,
        '其他' professionName,
        count(cust.id) scustomerCount,
        0 ycustomerCount,
        100 customerCoverage
        from blade_customer cust
        join (
        select DISTINCT t1.customerId
        from (
        select DISTINCT allpro.customerId
        from customer_all_profession allpro
        where allpro.professionId = #{professionId}
        ) t1
        left join
        (
        select DISTINCT alluser.customerId
        from customer_all_user alluser
        where alluser.userId in (
        <foreach collection="userList" item="item" separator="," index="index">
            #{item}
        </foreach>
        )
        ) t2
        on t1.customerId = t2.customerId
        where t2.customerId is null) t3 on t3.customerId = cust.id
    </select>
    <select id="getProfessionByUserId" resultType="java.lang.Long">
        select user_id from blade_personnel where profession_id  =#{professionId}
    </select>
</mapper>
