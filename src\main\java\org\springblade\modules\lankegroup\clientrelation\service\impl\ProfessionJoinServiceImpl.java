package org.springblade.modules.lankegroup.clientrelation.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;
import org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionJoinMapper;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionJoinService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@AllArgsConstructor
public class ProfessionJoinServiceImpl extends BaseServiceImpl<ProfessionJoinMapper, ProfessionJoin> implements ProfessionJoinService {


    @Override
    public ProfessionJoin saveProfession(ProfessionJoin profession) {
        baseMapper.insert(profession);
        return profession;
    }

    @Override
    public List<Long> getJoinList(Long id) {
        return baseMapper.getJoinList(id);
    }

    @Override
    public List<AreaAndMarket> getJoinListByObject(Long id,List<Long> prohibitList) {
        return baseMapper.getJoinListByObject(id,prohibitList);
    }

    @Override
    public void deleteProfessionJoin(Long id) {
        baseMapper.deleteProfessionJoin(id);
    }
}
