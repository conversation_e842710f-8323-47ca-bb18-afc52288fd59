package org.springblade.modules.lankegroup.crm.entity.kingdee;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JSONType(orders = {"FCUSTID", "FCreateOrgId", "FUseOrgId", "FName", "FADDRESS", "FTEL",
        "FTAXREGISTERCODE", "F_KHH", "F_YHZH", "FGroup"})
public class KDCustomer {
    //实体主键
    public String FCUSTID;
    //创建组织
    public Map FCreateOrgId = new HashMap();
    //使用组织
    public  Map FUseOrgId = new HashMap();
    //客户名称
    public String FName;
    //公司地址
    public String FADDRESS;
    //联系电话
    public String FTEL;
    //       税号
    public String FTAXREGISTERCODE;
    //开户行
    public String F_KHH;
    //银行账号
    public String F_YHZH;

    //客户分组
    public Map FGroup = new HashMap();
    //公司类别
//    public Map FCompanyClassify = new HashMap();
    //公司性质
//    public Map FCompanyNature = new HashMap();
    //所属行业
//    public Map F_RWSQ_SSHY = new HashMap();

}
