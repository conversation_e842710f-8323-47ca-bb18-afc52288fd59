package org.springblade.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class PinYinConverter {
    public static String toPinyinInitials(String str) {
        // 处理空字符串或null的情况
        if (str == null || str.isEmpty()) {
            return "";
        }

        // 配置拼音输出格式
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE); // 小写拼音
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 不带声调

        StringBuilder sb = new StringBuilder();
        for (char c : str.toCharArray()) {
            // 判断是否为汉字（Unicode范围：\u4E00-\u9FA5）
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]")) {
                try {
                    // 将汉字转换为拼音数组（处理多音字）
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        String pinyin = pinyinArray[0]; // 取第一个拼音（默认处理多音字）
                        if (pinyin.length() > 0) {
                            // 提取首字母并大写
                            sb.append(Character.toUpperCase(pinyin.charAt(0)));
                        }
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace(); // 异常处理
                }
            } else {
                // 非汉字字符直接保留
                sb.append(c);
            }
        }
        return sb.toString();
    }

}
