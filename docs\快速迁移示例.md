# 快速迁移示例

## 示例：为商机模块添加数据权限

假设你有一个商机模块需要添加数据权限控制，下面是完整的迁移步骤：

### 步骤1：更新VO对象

在`BusinessOpportunityVO`中添加数据权限字段：

```java
@Data
@ApiModel(value = "BusinessOpportunityVO", description = "商机视图对象")
public class BusinessOpportunityVO extends BusinessOpportunityBaseVO {
    
    // ... 原有业务字段
    
    // ========== 新增：数据权限字段 ==========
    /**
     * 当前用户级别 1.高层；2.财务；3.部门领导；4.普通员工
     */
    @ApiModelProperty(value = "当前用户级别")
    private Integer targetUserLevel;

    /**
     * 允许查看的用户ID列表（null=无限制）
     */
    @ApiModelProperty(value = "允许查看的用户ID列表")
    private List<Long> allowedUserIds;

    /**
     * 数据权限字段列表（自动配置）
     */
    @ApiModelProperty(value = "数据权限字段列表")
    private List<String> dataPermissionFields;

    /**
     * 数据属性（如"sub"表示查看下级数据）
     */
    @ApiModelProperty(value = "数据属性")
    private String dataAttribute;
}
```

### 步骤2：更新Service实现

在`BusinessOpportunityServiceImpl`中添加权限应用：

```java
@Service
@AllArgsConstructor
public class BusinessOpportunityServiceImpl implements IBusinessOpportunityService {
    
    // 新增：注入数据权限管理器
    private final DataPermissionManager dataPermissionManager;
    
    @Override
    public IPage<BusinessOpportunityVO> selectPage(IPage<BusinessOpportunityVO> page, BusinessOpportunityVO query) {
        
        // 新增：一行代码应用数据权限
        dataPermissionManager.applyDataPermissionWithBusinessType(query, query.getDataAttribute(), "business_opportunity");
        
        List<BusinessOpportunityVO> records = baseMapper.selectPage(page, query);
        return page.setRecords(records);
    }
}
```

### 步骤3：更新Mapper.xml

在`BusinessOpportunityMapper.xml`中添加权限控制：

```xml
<mapper namespace="org.example.mapper.BusinessOpportunityMapper">
    
    <select id="selectPage" resultType="org.example.vo.BusinessOpportunityVO">
        SELECT * FROM business_opportunity 
        WHERE is_deleted = 0
        
        <!-- 原有查询条件 -->
        <if test="query.businessName != null and query.businessName != ''">
            AND business_name LIKE CONCAT('%', #{query.businessName}, '%')
        </if>
        
        <!-- 新增：一行代码应用数据权限 -->
        <include refid="DataPermissionSql.autoDataPermissionCondition" />
        
        ORDER BY create_time DESC
    </select>
    
</mapper>
```

## 权限效果演示

### 高层用户查询
```sql
-- 无任何权限限制，查看所有数据
SELECT * FROM business_opportunity WHERE is_deleted = 0 ORDER BY create_time DESC
```

### 部门领导查询  
```sql
-- 根据部门成员进行过滤
SELECT * FROM business_opportunity 
WHERE is_deleted = 0 
AND (responsible_user_id IN (1001, 1002, 1003) OR create_user IN (1001, 1002, 1003))
ORDER BY create_time DESC
```

### 普通员工查询
```sql
-- 只能查看自己负责的数据
SELECT * FROM business_opportunity 
WHERE is_deleted = 0 
AND (responsible_user_id IN (1001) OR create_user IN (1001))
ORDER BY create_time DESC
```

## 总结

通过以上3个简单步骤，你就为商机模块添加了完整的数据权限控制：

1. **VO添加4个字段**：数据权限相关字段
2. **Service添加1行代码**：应用数据权限
3. **Mapper.xml添加1行代码**：引用权限SQL片段

整个过程非常简单，对现有代码的侵入性极小，是一个真正"极简"的数据权限解决方案。 