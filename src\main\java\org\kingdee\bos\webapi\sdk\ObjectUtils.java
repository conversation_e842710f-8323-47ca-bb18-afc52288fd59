//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class ObjectUtils {
    public ObjectUtils() {
    }

    public static boolean anyNotNull(Object obj) {
        return obj != null;
    }

    public static int ConvertToInt(Object obj) {
        if (obj == null) {
            return 0;
        } else {
            String cv = obj.toString();
            int pIndex = cv.indexOf(".");
            if (pIndex > -1) {
                cv = cv.substring(0, pIndex);
            }

            return Integer.parseInt(cv);
        }
    }

    public static String ConvertToString(Object obj) {
        return obj == null ? null : obj.toString();
    }
}
