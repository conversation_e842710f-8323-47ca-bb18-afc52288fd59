package org.springblade.common.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.modules.lankegroup.customerVisit.entity.CompletePlanLog;
import org.springblade.modules.lankegroup.customerVisit.mapper.CompletePlanLogMapper;
import org.springblade.modules.lankegroup.customerVisit.service.ICompletePlanLogService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 定时发送日志
 */
@Component
@AllArgsConstructor
@Async
public class SendLogTask {

    private final ICompletePlanLogService completePlanLogService;

    private final CompletePlanLogMapper completePlanLogMapper;

    private final IUserService userService;

    /**
     * 销售每日日志发送
     */
//    @Scheduled(cron = "0 40 8 * * ?")
    public void sendLogTask() {
        // 查询销售人员
        List<Long> userList = getUserList();
        if (null == userList && userList.size() < 1) {
            return;
        }
        LocalDateTime dateTime = LocalDateTime.now();
        LocalDateTime yesterday = dateTime.minusDays(1);
        String startTime = yesterday.withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = yesterday.withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 循环判断新增或修改销售数据
        userList.stream().forEach(user -> {
            try {
                Map jobStatistics = completePlanLogService.getJobStatistics(user);
                // 判断当前销售今日是否产生数据
                if ((null != jobStatistics.get("callCount") && "[]" != jobStatistics.get("callCount") && ((List<Long>) jobStatistics.get("callCount")).size() > 0) ||
                        (null != jobStatistics.get("clientCount") && "[]" != jobStatistics.get("clientCount") && ((List<Long>) jobStatistics.get("clientCount")).size() > 0) ||
                        (null != jobStatistics.get("contactCount") && "[]" != jobStatistics.get("contactCount") && ((List<Long>) jobStatistics.get("callCount")).size() > 0) ||
                        (null != jobStatistics.get("contractCount") && "[]" != jobStatistics.get("contractCount") && ((List<Long>) jobStatistics.get("callCount")).size() > 0) ||
                        (null != jobStatistics.get("projectCount") && "[]" != jobStatistics.get("projectCount") && ((List<Long>) jobStatistics.get("callCount")).size() > 0)) {
                    QueryWrapper<CompletePlanLog> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("create_id", user);
                    queryWrapper.eq("dept_type", 2);
                    queryWrapper.eq("status", 1);
                    queryWrapper.eq("is_deleted", 0);
                    queryWrapper.ge("ownership_time", startTime);
                    queryWrapper.le("ownership_time", endTime);
                    List<CompletePlanLog> completePlanLog = completePlanLogMapper.selectList(queryWrapper);
                    jobStatistics.put("toDaySummary", JSON.toJSONString(jobStatistics));
                    if (completePlanLog.size() == 0) {
                        User byId = userService.getById(user);
                        jobStatistics.put("status", 1);
                        jobStatistics.put("createId", byId.getId());
                        jobStatistics.put("createName", byId.getName());
                        jobStatistics.put("type", 1);
                        jobStatistics.put("deptType", 2);
                        // 新增
                        completePlanLogMapper.saveSellTaskDate(jobStatistics);
                    } else {
                        if (completePlanLog.size() > 1) {
                            for (int i = 0; i <= completePlanLog.size() - 2; i++){
                                completePlanLogMapper.deleteById(completePlanLog.get(i).getId());
                            }
                        }
                        completePlanLog.stream().forEach(completePlanLogList -> {
                            // 修改
                            jobStatistics.put("id", completePlanLogList.getId());
                            jobStatistics.put("createId", completePlanLogList.getCreateId());
                            completePlanLogMapper.updateSellTaskDate(jobStatistics);
                        });
                    }
                }
            } catch (Exception e) {
                return;
            }
        });
    }

    /**
     * 获取销售人员用户ID
     *
     * @return
     */
    private List<Long> getUserList() {
        return userService.getUserIdByDeptId(null);
    }
}

