//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.util.List;

public class BatchSave<T> extends SaveParamBase {
    List<T> Model;

    public BatchSave(List<T> data) {
        this.setModel(data);
    }

    public List<T> getModel() {
        return this.Model;
    }

    public void setModel(List<T> models) {
        this.Model = models;
    }
}
