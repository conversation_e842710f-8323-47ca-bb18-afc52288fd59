<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveDetailsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="archiveDetailsResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="contract_id" property="contractId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="contract_type_method" property="contractTypeMethod"/>
        <result column="signing_mode" property="signingMode"/>
        <result column="content_channel_merchants" property="contentChannelMerchants"/>
        <result column="channel_supplier_name" property="channelSupplierName"/>
        <result column="channel_supplier_id" property="channelSupplierId"/>
        <result column="performance_contract_amount" property="performanceContractAmount"/>
        <result column="contract_signing_date" property="contractSigningDate"/>
        <result column="agreed_project_duration" property="agreedProjectDuration"/>
        <result column="service_start_date" property="serviceStartDate"/>
        <result column="service_end_date" property="serviceEndDate"/>
        <result column="project_manager_id" property="projectManagerId"/>
        <result column="project_manager_name" property="projectManagerName"/>
        <result column="report_date" property="reportDate"/>
        <result column="bid" property="bid"/>
        <result column="procurement_agency_name" property="procurementAgencyName"/>
        <result column="procurement_agency_contact_information" property="procurementAgencyContactInformation"/>
        <result column="bidding_no" property="biddingNo"/>
        <result column="bid_opening_date" property="bidOpeningDate"/>
        <result column="bid_winning_date" property="bidWinningDate"/>
        <result column="notice_date" property="noticeDate"/>
        <result column="project_progress_income" property="projectProgressIncome"/>
        <result column="technical_service_fee" property="technicalServiceFee"/>
        <result column="secondary_service_number" property="secondaryServiceNumber"/>
        <result column="secondary_service_price" property="secondaryServicePrice"/>
        <result column="secondary_service_order" property="secondaryServiceOrder"/>
        <result column="tertiary_service_number" property="tertiaryServiceNumber"/>
        <result column="tertiary_service_price" property="tertiaryServicePrice"/>
        <result column="tertiary_service_order" property="tertiaryServiceOrder"/>
        <result column="number" property="number"/>
        <result column="remarks" property="remarks"/>
        <result column="task_user" property="taskUser"/>
        <result column="reject_cause" property="rejectCause"/>
        <result column="process_definition_id" property="processDefinitionId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="payment_term" property="paymentTerm"/>
        <result column="once_collection_plan_id" property="onceCollectionPlanId"/>
        <result column="one_collection_plan_id" property="oneCollectionPlanId"/>
        <result column="two_collection_plan_id" property="twoCollectionPlanId"/>
        <result column="three_collection_plan_id" property="threeCollectionPlanId"/>
        <result column="four_collection_plan_id" property="fourCollectionPlanId"/>
        <result column="periods_number" property="periodsNumber"/>
        <result column="once_collection_date" property="onceCollectionDate"/>
        <result column="warranty_period" property="warrantyPeriod"/>
        <result column="is_warranty" property="isWarranty"/>
        <result column="archive_annex" property="archiveAnnex"/>
        <result column="archive_annex_name" property="archiveAnnexName"/>
    </resultMap>
    <insert id="saveArchiveFiles">
        insert into blade_archive_details_files (id, archive_id, create_user, create_time,jie_suan_file_name,jie_suan_file_url,yan_shou_file_name,yan_shou_file_url,ding_file_name,ding_file_url)
        values (#{id},#{archiveId},#{createUser},#{createTime},#{jieSuanFileName},#{jieSuanFileUrl},#{yanShouFileName},#{yanShouFileUrl},#{dingFileName},#{dingFileUrl})
    </insert>
    <update id="updateContractId">
        UPDATE blade_contract SET
        <if test="kdFid != null and kdFid != ''">
            kd_fid = #{kdFid},
        </if>
        <if test="kdCode != null and kdCode != ''">
            kd_code = #{kdCode},
        </if>
        <if test="updateTime!=null">
            update_time=#{updateTime},
        </if>
        <if test="status != null and status != ''">
            `status` = #{status}
        </if>
        WHERE id = #{id}
    </update>
    <update id="deleteArchiveFilesByArchiveId">
        update blade_archive_details_files set is_deleted = 1 where archive_id = #{archiveId}
    </update>
    <select id="selectArchiveDetailsPage" resultMap="archiveDetailsResultMap">
        select * from blade_archive_details where is_deleted = 0
    </select>
    <select id="selectTaskIdByarchiveDetails" resultType="java.lang.String">
        select
        a.ID_
        from
        blade_archive_details as d
        left join ACT_RU_TASK as a on a.PROC_INST_ID_=d.process_instance_id
        where
        1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and d.process_instance_id=#{processInstanceId}
        </if>
        for update
    </select>
    <select id="selectArchive" resultType="org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO">
        SELECT
        d.*,
        u.name as createUserName,
        c.project_id as projectId,
        c.create_user as userId,
        c.contract_amount as contractAmount
        FROM
        blade_archive_details d
        LEFT JOIN blade_user u on u.id=d.create_user
        LEFT JOIN blade_contract c on c.id=d.contract_id
        WHERE
            1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and d.process_instance_id=#{processInstanceId}
        </if>
        <if test="contractId!=null and contractId!=''">
            and d.contract_id=#{contractId}
        </if>
        and d.is_deleted=0
    </select>
    <select id="selectSignCompanyCode" resultType="map">
        SELECT
            c.sign_company_code as signCompanyCode,
            b.`code`  as code
        FROM
            blade_contract c
                LEFT JOIN blade_project_basic b ON c.project_id = b.id
        WHERE
            c.id = #{id}
    </select>
    <select id="contractChange" resultType="org.springblade.modules.lankegroup.contractManagement.entity.ContractChange">
        SELECT * FROM blade_contract_change WHERE contract_id = #{contractId} AND is_deleted = 0
    </select>
    <select id="queryByContractId" resultMap="archiveDetailsResultMap">
        select * from blade_archive_details where contract_id=#{contractId} and is_deleted = 0  order by update_time desc limit 1
    </select>
    <select id="selectAll"
            resultType="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails">
        SELECT
            id,
            secondary_service_number,
            secondary_service_price,
            secondary_service_order,
            tertiary_service_number,
            tertiary_service_price,
            tertiary_service_order
        FROM
            blade_archive_details
        WHERE
            is_deleted = 0
    </select>

    <select id="selectIdByProjectIdWithFirstTimeGuiDang" parameterType="long" resultType="long">
        SELECT
            bad.id
        FROM
            blade_archive_details bad
            left join blade_contract bc on bad.contract_id = bc.id
            LEFT JOIN ACT_HI_ACTINST aha ON bad.process_instance_id = aha.PROC_INST_ID_
        WHERE
            bc.project_id = #{0}
            AND (bc.contract_type = 0 or bc.contract_type = 4)
            AND bc.STATUS = 5
            AND bc.is_deleted = 0
            AND bad.is_deleted = 0
            AND aha.ACT_TYPE_ = 'endEvent'
            order by aha.END_TIME_ asc limit 1
    </select>
    <select id="countArchiveDetailsFiles"
            resultType="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetailsFiles">
        select * from blade_archive_details_files where archive_id = #{archiveId} and is_deleted = 0
    </select>



</mapper>
