/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tool.support.Kv;

import java.util.Date;
import java.util.List;

/**
 * 自定义流程审批中的所有业务单据任务 视图实体类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@ApiModel(value = "BusinessProcess对象", description = "自定义流程审批中的所有业务单据任务")
public class BusinessProcessVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("创建人")
	private Long createUser;

	@ApiModelProperty("创建人名称")
	private String createUserName;

	@ApiModelProperty(value = "当前审批人")
	private String assigneeId;
	@ApiModelProperty(value = "当前审批人")
	private String assigneeName;
	/**
	 * 关联的业务表单名称
	 */
	@ApiModelProperty(value = "关联的业务表单名称")
	private String formKey;

	@ApiModelProperty(value = "关联的业务表单名称")
	private String formName;
	/**
	 * 关联的业务表单的主键id
	 */
	@ApiModelProperty(value = "关联的业务表单的主键id")
	private Long formDataId;
	/**
	 * 关联的业务表单的Code
	 */
	@ApiModelProperty(value = "关联的业务表单的Code")
	private String formDataCode;

	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("创建时间")
	private Date createTime;

	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("审批通过时间")
	private Date approveTime;

	@ApiModelProperty("业务状态：0待审批；1审批中；2已驳回；3已撤回；4已完成")
	private Integer status;

	@ApiModelProperty(value = "流程实例ID")
	private String processInstanceId;

	@ApiModelProperty(value = "任务ID")
	private String taskId;
	/**
	 * 流程ID
	 */
	@ApiModelProperty(value = "流程ID")
	private String processDefinitionId;

	private String assigneeList;

	private String createUserAvatar;

	private String businessCategory;

	private String businessField;

	private List<Kv> businessFieldJson;

	private String statusName;

}
