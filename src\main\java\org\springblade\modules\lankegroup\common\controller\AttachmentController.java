/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.controller;

import io.swagger.annotations.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.common.entity.Attachment;
import org.springblade.modules.lankegroup.common.vo.AttachmentVO;
import org.springblade.modules.lankegroup.common.vo.AttachmentBaseVO;
import org.springblade.modules.lankegroup.common.vo.AttachmentCreateVO;
import org.springblade.modules.lankegroup.common.vo.AttachmentUpdateVO;
import org.springblade.modules.lankegroup.common.dto.AttachmentDTO;
import org.springblade.modules.lankegroup.common.service.IAttachmentService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 通用附件 控制器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common/attachment")
@Api(value = "通用附件", tags = "通用附件接口")
public class AttachmentController extends BladeController {

	private final IAttachmentService attachmentService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功", response = AttachmentVO.class)
	})
	public R<AttachmentVO> detail(@ApiParam(value = "主键id", required = true) @RequestParam Long id) {
		Attachment detail = attachmentService.getById(id);
		AttachmentVO vo = new AttachmentVO();
		BeanUtils.copyProperties(detail, vo);
		return R.data(vo);
	}

	/**
	 * 分页查询 通用附件
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页查询", notes = "传入查询条件")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "current", value = "当前页", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "size", value = "每页显示数量", paramType = "query", dataType = "int")
	})
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功", response = AttachmentVO.class)
	})
	public R<IPage<AttachmentVO>> page(Attachment attachment, Query query) {
		IPage<Attachment> pages = attachmentService.page(Condition.getPage(query), Condition.getQueryWrapper(attachment));
		IPage<AttachmentVO> voPages = pages.convert(entity -> {
			AttachmentVO vo = new AttachmentVO();
			BeanUtils.copyProperties(entity, vo);
			return vo;
		});
		return R.data(voPages);
	}

	/**
	 * 新增 通用附件
	 */
	@PostMapping("/create")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入attachmentCreateVO")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R save(@Valid @RequestBody @ApiParam(value = "通用附件创建对象", required = true) AttachmentCreateVO attachmentCreateVO) {
		Attachment attachment = new Attachment();
		BeanUtils.copyProperties(attachmentCreateVO, attachment);
		return R.status(attachmentService.save(attachment));
	}

	/**
	 * 修改 通用附件
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "修改", notes = "传入attachmentUpdateVO")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R update(@Valid @RequestBody @ApiParam(value = "通用附件更新对象", required = true) AttachmentUpdateVO attachmentUpdateVO) {
		Attachment attachment = new Attachment();
		BeanUtils.copyProperties(attachmentUpdateVO, attachment);
		return R.status(attachmentService.updateById(attachment));
	}

	/**
	 * 删除 通用附件
	 */
	@PostMapping("/delete")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(attachmentService.deleteLogic(Func.toLongList(ids)));
	}

}
