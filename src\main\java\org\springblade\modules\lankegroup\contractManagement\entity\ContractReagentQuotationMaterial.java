/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;

/**
 * 试剂合同子表-实验材料实体类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@TableName("cm_contract_reagent_quotation_material")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "试剂合同子表-实验材料实体类") // 类级别Swagger注解
public class ContractReagentQuotationMaterial extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 合同主表ID
     */
    @ApiModelProperty(value = "合同主表ID") // 字段级别Swagger注解
    private Long mainId;
    /**
     * 材料ID
     */
    @ApiModelProperty(value = "材料ID")
    private String materialId;
    /**
     * 材料名称
     */
    @ApiModelProperty(value = "材料名称")
    private String materialName;
    /**
     * 材料规格
     */
    @ApiModelProperty(value = "材料规格")
    private String materialSpec;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 最终金额
     */
    @ApiModelProperty(value = "最终金额")
    private BigDecimal finalAmount;
    /**
     * 货号
     */
    @ApiModelProperty(value = "货号")
    private String itemNumber;


}
