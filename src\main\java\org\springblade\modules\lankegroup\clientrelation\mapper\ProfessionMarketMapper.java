package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionMarket;

import java.util.List;

public interface ProfessionMarketMapper extends BaseMapper<ProfessionMarket> {

    /**
     * 根据关联ID查询行业数据
     * @param ids
     * @return
     */
    List<Long> getProfessionMarketList(List<Long> ids);

    /**
     * 批量删除市场行业信息
     * @param ids
     */
    void deleteProfessionMarkets(List<Long> ids);

}
