package org.springblade.common.enums;

public enum EnvironmentUrlEnum {

    测试("https://lkszh.shimingbao.cn/#/","test"),
    正式("https://dingding.lankecloud.com/#/","dev");

    private  String url;
    private String environment;

    EnvironmentUrlEnum(String url, String environment) {
        this.url = url;
        this.environment = environment;
    }

    public String getUrl() {
        return url;
    }

    public String getEnvironment() {
        return environment;
    }
}
