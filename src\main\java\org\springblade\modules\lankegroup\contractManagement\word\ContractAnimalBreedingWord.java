/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.word;

import lombok.Data;
import org.springblade.modules.lankegroup.contractManagement.annotation.FieldToWord;

import java.io.Serializable;

/**
 * 试剂合同子表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ContractAnimalBreedingWord implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 合同主表ID
     */
    private Long mainId;
    /**
     * 预存金额
     */
    @FieldToWord
    private String depositedAmount;


}
