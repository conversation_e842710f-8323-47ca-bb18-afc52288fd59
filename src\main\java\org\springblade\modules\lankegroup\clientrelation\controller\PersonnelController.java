package org.springblade.modules.lankegroup.clientrelation.controller;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.PersonnelDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Controls;
import org.springblade.modules.lankegroup.clientrelation.service.PersonnelService;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 人员
 */
@AllArgsConstructor
@RestController
@RequestMapping("personnel")
public class PersonnelController {

    private final PersonnelService personnelService;

    private final ElasticsearchClient elasticsearchClient;

    /**
     * 新增人员
     *
     * @param personnelDTO
     */
    @PostMapping
    public R savePersonnel(@RequestBody PersonnelDTO personnelDTO) {
        return personnelService.savePersonnel(personnelDTO);
    }

    @PostMapping("/page")
    public R getPersonnelList(@RequestBody PersonnelDTO personnelDTO, @RequestBody Query query) {
        return personnelService.personnelList(Condition.getPage(query), personnelDTO);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping
    public R deletePersonnel(Long id) {
        return personnelService.deletePersonnel(id);
    }

    /**
     * 编辑
     *
     * @param personnelDTO
     * @return
     */
    @PutMapping
    public R updatePersonnel(@RequestBody PersonnelDTO personnelDTO) {
        return personnelService.updatePersonnel(personnelDTO);
    }

    @GetMapping("getUserByDept")
    public R getUserByDept(String userId) {
        return personnelService.getUserByDept(userId);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("detail")
    public R detail(Long id) {
        return personnelService.detailPersonnel(id);
    }

    /**
     * 动态
     *
     * @return
     */
    @GetMapping("dynamic")
    public R dynamic(Long id) {
        List<Controls> list = new ArrayList<>();
        BoolQuery.Builder query = new BoolQuery.Builder();
        query.filter(f -> f.term(t -> t.field("targetId").value(id)));
        SearchRequest searchRequest = SearchRequest.of(a -> a.index("ding_talk_clientrelation_log").query(b ->
                b.bool(query.build())).from(0).size(10000).sort(c -> c.field(d ->
                d.field("createTime").order(SortOrder.Desc))).trackTotalHits(t -> t.enabled(Boolean.TRUE)));
        try {
            SearchResponse<Controls> search = elasticsearchClient.search(searchRequest, Controls.class);
            search.hits().hits().stream().forEach(controlsHit -> {
                list.add(controlsHit.source());
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.data(list);
    }

    /**
     * 行业信息
     *
     * @return
     */
    @PostMapping("industryInformation")
    public R industryInformation(@RequestBody Map map) {
        return personnelService.industryInformation(map);
    }

    /**
     * 区域详情
     *
     * @param map
     * @return
     */
    @PostMapping("getAreaList")
    public R getAreaList(@RequestBody Map map) {
        try {
            return personnelService.getAreaList(map);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取某人员下区划列表
     *
     * @param map
     * @return
     */
    @PostMapping("getRegionalizationList")
    public R getRegionalizationList(@RequestBody Map map) {
        return personnelService.getRegionalizationList(map);
    }

    /**
     * 获取某人员下行业列表
     *
     * @param map
     * @return
     */
    @PostMapping("getProfessionList")
    public R getProfessionList(@RequestBody Map map) {
        return personnelService.getProfessionList(map);
    }

    /**
     * 判断当前人员是否创建
     *
     * @param id
     * @return
     */
    @GetMapping("getUserData")
    public R getUserData(String id) {
        return personnelService.getUserData(id);
    }

}
