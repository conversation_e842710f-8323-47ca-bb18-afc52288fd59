package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;

import java.util.List;

@Data
public class ProfessionVO {

    private Long id;

    /**
     * 市场名称
     */
    private String professionName;

    /**
     * 负责部门ID
     */
    private String chargeDeptId;


    /**
     * 负责部门名称
     */
    private String chargeDeptName;

    /**
     * 负责人ID
     */
    private String chargeId;

    /**
     * 负责人名称
     */
    private String chargeName;

    /**
     * 应有客户
     */
    private Integer oughtClient;

    /**
     * 实有客户
     */
    private Integer actualClient;

    /**
     * 行业/区域集合
     */
    private List<ProfessionJoin> joinList;
}
