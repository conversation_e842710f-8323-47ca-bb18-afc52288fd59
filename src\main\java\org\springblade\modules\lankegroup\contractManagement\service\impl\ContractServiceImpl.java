/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springblade.common.constant.FlowFormConstant;
import org.springblade.common.constant.FormNameConstant;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.enums.DocumentType;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.AesUtil;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.controller.ContractUseChapterType;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractChangeService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.lankegroup.contractManagement.wrapper.ContractWrapper;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.disposition.bo.OtherProjectGroupBO;
import org.springblade.modules.lankegroup.disposition.entity.ProjectGroupDisposition;
import org.springblade.modules.lankegroup.disposition.enums.SourceTypeEnum;
import org.springblade.modules.lankegroup.disposition.mapper.OtherProjectGroupMapper;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.enums.BusinessTypeEnum;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.project.entity.ProjectProgress;
import org.springblade.modules.lankegroup.project.entity.ProjectProgressBiddingDecision;
import org.springblade.modules.lankegroup.project.entity.ProjectProgressSaleStage;
import org.springblade.modules.lankegroup.project.entity.ProjectTeam;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressBiddingDecisionMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressSaleStageMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectTeamMapper;
import org.springblade.modules.lankegroup.project.service.impl.ProjectTeamServiceImpl;
import org.springblade.modules.lankegroup.project.vo.ProgressEnum;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service
public class ContractServiceImpl extends BaseServiceImpl<ContractMapper, Contract> implements IContractService {
    public static final String DING_ID = "076740115429484095";
    @Autowired
    private final IFlowService flowService;

    @Autowired
    private final IUserService userService;
    @Autowired
    private final FlowEngineService flowEngineService;
    @Autowired
    private final TaskService taskService;
    @Autowired
    @Lazy
    private final IContractChangeService contractChangeService;
    @Autowired
    @Lazy
    private final IArchiveDetailsService archiveDetailsService;
    @Autowired
    private final ICustomerConnectLogService customerConnectLogService;
    @Autowired
    private final ProjectBasicMapper projectBasicMapper;
    @Autowired
    private final ProjectProgressMapper projectProgressMapper;
    //项目团队的mapper
    @Autowired
    private final ProjectTeamMapper projectTeamMapper;
    //项目团队的server
    @Autowired
    private final ProjectTeamServiceImpl projectTeamService;
    @Autowired
    private final ControlsLogService controlsLogService;
    @Autowired
    private final SendBillMsgUtil sendBillMsgUtil;
    @Autowired
    private ProjectProgressSaleStageMapper projectProgressSaleStageMapper;
    @Autowired
    private ProjectProgressBiddingDecisionMapper projectProgressBiddingDecisionMapper;
    @Autowired
    private IHistoricProcessService historicProcessService;

    @Resource
    private OtherProjectGroupMapper otherProjectGroupMapper;

    public ContractServiceImpl(IFlowService flowService, IUserService userService, FlowEngineService flowEngineService, TaskService taskService, @Lazy IContractChangeService contractChangeService, @Lazy IArchiveDetailsService archiveDetailsService, ICustomerConnectLogService customerConnectLogService, ProjectBasicMapper projectBasicMapper, ProjectProgressMapper projectProgressMapper, ProjectTeamMapper projectTeamMapper, ProjectTeamServiceImpl projectTeamService, ControlsLogService controlsLogService, SendBillMsgUtil sendBillMsgUtil) {
        this.flowService = flowService;
        this.userService = userService;
        this.flowEngineService = flowEngineService;
        this.taskService = taskService;
        this.contractChangeService = contractChangeService;
        this.archiveDetailsService = archiveDetailsService;
        this.customerConnectLogService = customerConnectLogService;
        this.projectBasicMapper = projectBasicMapper;
        this.projectProgressMapper = projectProgressMapper;
        this.projectTeamMapper = projectTeamMapper;
        this.projectTeamService = projectTeamService;
        this.controlsLogService = controlsLogService;
        this.sendBillMsgUtil = sendBillMsgUtil;
    }

    @Override
    public IPage<ContractListResult> selectContractPage(IPage<ContractListResult> page, ContractListParam param) {
        User user = userService.getById(AuthUtil.getUserId());

        param.setUserId(user.getId());
        List<ContractListResult> maps = baseMapper.selectContractPage(page, param);
        for (ContractListResult c : maps) {
            Map map = new HashMap();
//            map.put("allowArchivingCondition",null);
            map.put("contractId", c.getCid());
//            是否有变更
            Integer isChangeCount = contractChangeService.countChange(map);
            if (isChangeCount > 0) {
                c.setIsChanges(0);
                map.put("status", 1);
                map.put("isFlag", "void");
                Integer voidCount = contractChangeService.countChange(map);
                if (voidCount > 0) {
                    c.setIsChanges(2);
                }
            } else {
                c.setIsChanges(1);
            }

            Map m = new HashMap();
            m.put("allowArchivingCondition", "true");
            m.put("contractId", c.getId());
            Integer allowArchivingCount = contractChangeService.countChange(m);
            if (allowArchivingCount > 0) {
                c.setAllowArchiving(true);
            }
            //        判断当前合同是否有归档数据,有归档数则全员三页，无归档数据则仅合同发起人有归档页
            ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(c.getCid());
            Contract contract = getById(c.getCid());
            if (archiveDetails != null) {
                c.setIsOwner(true);
            } else {
                if (contract.getCreateUser().longValue() == AuthUtil.getUserId().longValue()) {
                    c.setIsOwner(true);
                }
            }
            // update 20250421 zh 回显产品类型
            List<OtherProjectGroupBO> bySourceId = otherProjectGroupMapper.getBySourceId(c.getId());
            c.setContractProjectGroupList(bySourceId);
        }
        return page.setRecords(maps);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map startProcess(ContractVO contract) {
        BladeFlow flow = null;
//        返回值
        Map result = new HashMap();
//        用章类型修改类型
        StringBuilder ids = new StringBuilder();
        StringBuilder vals = new StringBuilder();
        if (contract.getUseChapterTypeList() != null && !contract.getUseChapterTypeList().isEmpty()) {
            for (ContractUseChapterType c : contract.getUseChapterTypeList()) {
                ids.append(c.getId());
                ids.append(",");
                vals.append(c.getName());
                vals.append(",");
            }
        }
        ids.deleteCharAt(ids.length() - 1);
        vals.deleteCharAt(vals.length() - 1);
        contract.setUseChapterTypeIds(ids.toString());
        contract.setUseChapterTypeVals(vals.toString());
        //        有id且状态位置不为草稿则更新、无id保存,
        if (Func.isEmpty(contract.getId())) {
            if (save(contract)) {
                saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
                //启动审批流
                initCompleteTask(contract);
                saveLog(contract.getProjectId());
                extracted(contract);
            }
        } else {
            if (updateById(contract)) {
                saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
                //启动审批流
                initCompleteTask(contract);
                extracted(contract);
            }
        }
        //                                    写入记录日志表
        CustomerConnectLog contractChangeLog = new CustomerConnectLog();
        switch (contract.getContractType()) {
            case "0":
                contractChangeLog.setCustomerId(contract.getSignCompanyId());
                contractChangeLog.setAboutDescription("添加了销售合同");
                contractChangeLog.setDescription("添加了销售合同");
                break;
            case "2":
                contractChangeLog.setCustomerId(contract.getSignCompanyId());
                contractChangeLog.setAboutDescription("添加了其他合同");
                contractChangeLog.setDescription("添加了其他合同");
                break;
            case "1":
                contractChangeLog.setCustomerId(contract.getSupplierId());
                contractChangeLog.setAboutDescription("添加了采购合同");
                contractChangeLog.setDescription("添加了采购合同");
                break;
        }
        contractChangeLog.setChangeTable(TableIdEnum.合同.getTableId());
        contractChangeLog.setContactId(contract.getId());
        contractChangeLog.setChargeUser(AuthUtil.getUserId());
        customerConnectLogService.save(contractChangeLog);

        result.put("name", contract.getName());
        if (flow != null) {
            result.put("processInstanceId", flow.getProcessInstanceId());
        } else {
            result.put("processInstanceId", contract.getProcessInstanceId());
        }
        result.put("contractId", contract.getId());

        return result;
    }

    private void extracted(ContractVO contract) {
        //                判断是否是这个项目第一次起合同，如果是 第一次发起合同 在去判断 阶段是这什么阶段，
//                如果是合同签订之前 直接跳到合同签订阶段，之后的话不管。
        List<String> strings = baseMapper.selectProjectName(contract.getProjectId());
        if (strings.size() == 1) {
            Integer integer = projectBasicMapper.selectScheduledId(contract.getProjectId());
            if (integer != null && integer < ProgressEnum.合同签订.getCode()) {
//                  跳到 合同签订 阶段  如果之前的阶段有空的话 就直接删除掉
                List<ProjectProgress> progressList = projectProgressMapper.selectProgress(contract.getProjectId());
                for (ProjectProgress projectProgress : progressList) {
                    if (projectProgress.getCurrentProgressKey() < ProgressEnum.合同签订.getCode() && projectProgress.getCurrentProgressId() == null) {
                        // 如果是洽谈阶段，创建这一详情
                        if (projectProgress.getCurrentProgressKey().equals(ProgressEnum.售前阶段.getCode())) {
                            ProjectProgressSaleStage saleStageEntity = this.getDefaultSaleStageEntity();
                            this.projectProgressSaleStageMapper.insert(saleStageEntity);
                            projectProgress.setCurrentProgressId(saleStageEntity.getId());
                        } else if (projectProgress.getCurrentProgressKey().equals(ProgressEnum.投标决策.getCode())) {
                            // 如果是投标决策阶段，创建这一详情
                            ProjectProgressBiddingDecision biddingDecisionEntity = this.getDefaultBiddingDecisionEntity();
                            this.projectProgressBiddingDecisionMapper.insert(biddingDecisionEntity);
                            projectProgress.setCurrentProgressId(biddingDecisionEntity.getId());
                        } else {
                            projectProgress.setCurrentProgressId(11111L);
                        }
                        projectProgressMapper.updateById(projectProgress);
                    }
                }
                // 更新上个进度的更新时间
                ProjectProgress lastProgress = this.projectProgressMapper.selectProgresByProjectId(contract.getProjectId());
                if (lastProgress != null) {
                    lastProgress.setUpdateTime(DateUtil.now());
                    projectProgressMapper.updateById(lastProgress);
                }

//                UserProjectNodeVO userProjectNodeVO = userProjectNodeMapper.selectAll(ProgressEnum.合同签订.getCode(), 0);
//                        合同签订的负责人 加入到小表中
                ProjectProgress projectProgress = new ProjectProgress();
                projectProgress.setProjectId(contract.getProjectId());
                projectProgress.setCurrentProgressKey(ProgressEnum.合同签订.getCode());
                Long principalId = Optional.ofNullable(projectBasicMapper.selectById(contract.getProjectId())).map(ProjectBasic::getPrincipalId).orElse(null);
                projectProgress.setStageLeaderId(principalId);
                projectProgress.setStageLeaderName(projectProgressMapper.userName(principalId));
                projectProgress.setCurrentProgressId(null);
                projectProgress.setCreateTime(DateUtil.now());
                projectProgress.setStatus(2);
                projectProgressMapper.insert(projectProgress);

                //            添加到团队表中
                ProjectTeam projectTeam = new ProjectTeam();
                projectTeam.setProjectId(contract.getProjectId());
                projectTeam.setUserId(projectProgress.getStageLeaderId());
                projectTeam.setAdmin(1);
                projectTeam.setNodeId(Long.valueOf(projectProgress.getCurrentProgressKey()));
                projectTeam.setCreatePost(projectTeamMapper.UserPostId(projectProgress.getStageLeaderId()));
                projectTeamService.save(projectTeam);

                //先去把项目基本信息表的 这个项目 的 状态给更新了
                ProjectBasic basic = new ProjectBasic();
                basic.setId(contract.getProjectId());
                basic.setScheduleId(Long.valueOf(projectProgress.getCurrentProgressKey()));
                basic.setPushDown(1);
                //项目的 进度状态给更新了
                int updateById = projectBasicMapper.updateById(basic);

//                //金蝶中的 项目进度 对应的 进度id
//                String xmjd = ProjectXMJD.XMJD(basic.getScheduleId().toString());
//                //下面就是 到 金蝶 去更新
//                boolean b = projectProgressMapper.updateXmjd(basic.getKdProjectFid(), xmjd);

            }
        }
    }

    private void saveLog(Long pid) {
        Integer integer = projectBasicMapper.selectScheduledId(pid);
        if (integer != null && integer < ProgressEnum.合同签订.getCode()) {
            LogModel log = new LogModel();
            log.setUserId(AuthUtil.getUserId());
            log.setMsg("项目进度更新至合同签订");
            log.setPid(pid);
            log.setType(2);
            controlsLogService.saveLog(log);
        }
    }

    @Override
    public IPage<ContractListResult> customerContractList(IPage<ContractListResult> page, Long customerId) {
        Map m = new HashMap();
        m.put("busineId", customerId);
        m.put("userId", AuthUtil.getUserId());
//        m.put("allowArchivingCondition",null);
        List<ContractListResult> maps = baseMapper.customerContractList(page, m);
        for (ContractListResult c : maps) {
            Map map = new HashMap();
            map.put("contractId", c.getId());
//            是否有变更
            Integer isChangeCount = contractChangeService.countChange(map);
            if (isChangeCount > 0) {
                c.setIsChanges(0);
                //                     已审批的作废数量
                map.put("status", 1);
                map.put("isFlag", "void");
                Integer voidCount = contractChangeService.countChange(map);
                if (voidCount > 0) {
                    c.setIsChanges(2);
                }
            } else {
                c.setIsChanges(1);
            }
            Map map1 = new HashMap();
            map1.put("allowArchivingCondition", "true");
            map1.put("contractId", c.getId());
            Integer allowArchivingCount = contractChangeService.countChange(map1);
            if (allowArchivingCount > 0) {
                c.setAllowArchiving(true);
            }
            //        判断当前合同是否有归档数据,有归档数则全员三页，无归档数据则仅合同发起人有归档页
            ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(c.getId());
            Contract contract = getById(c.getId());
            if (archiveDetails != null) {
                c.setIsOwner(true);
            } else {
                if (contract.getCreateUser().longValue() == AuthUtil.getUserId().longValue()) {
                    c.setIsOwner(true);
                }
            }
        }
        return page.setRecords(maps);
    }

    @Override
    public ContractVO selectDetail(Map map) {
        return baseMapper.selectDetail(map);
    }

    @Override
    public ContractVO selectDraft(Map map) {
        return baseMapper.selectDraft(map);
    }

    /**
     * 初始化合同页面
     *
     * @param type 合同类型（0销售合同、1采购合同、2其他合同、4渠道合同）
     * @return
     */
    @Override
    public R initContract(String type) {

        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId());
        Map<String, Object> variable = variables;
        String processId = "";
        // TODO 刘源兴工作流
//        // 如果是销售合同调用销售合同的流程，采购合同和其它合同还是用原来的流程
//        if (type.equals("0")) {
//            processId = flowEngineService.getProcessId("销售合同", "salesContract");
//        } else if (type.equals("4")) {
//            processId = flowEngineService.getProcessId("渠道合同", "channelContract");
//        } else {
//            processId = flowEngineService.getProcessId("合同管理", "contract");
//        }
        if ("".equals(processId)) {
            return R.fail(500, "流程不存在");
        }


        // TODO 刘源兴工作流
//        List<BladeFlow> bladeFlowList = flowEngineService.getFLowPrediction(processId, variable);
        List<BladeFlow> bladeFlowList = null;

        Map result = new HashMap();
        BladeUser user = AuthUtil.getUser();
//        List deptIds = Arrays.asList(user.getDeptId().split(","));
//        User applyUser = userService.getById(user.getUserId());
        Map map = new HashMap();
        map.put("type", type);
        map.put("userId", user.getUserId());
        ContractVO contractVO = selectDraft(map);
        if (contractVO != null) {
            if (contractVO.getUseChapterTypeIds() != null && (!contractVO.getUseChapterTypeIds().equals(""))) {
                //        用章类型转为数组类型
                List ids = Arrays.stream(contractVO.getUseChapterTypeIds().split(",")).map(s -> s.trim()).collect(Collectors.toList());
                List vals = Arrays.stream(contractVO.getUseChapterTypeVals().split(",")).map(s -> s.trim()).collect(Collectors.toList());
                List<ContractUseChapterType> contractUseChapterTypes = new ArrayList<>();
                for (int i = 0; i < ids.size(); i++) {
                    ContractUseChapterType c = new ContractUseChapterType();
                    c.setId(ids.get(i).toString());
                    c.setName(vals.get(i).toString());
                    contractUseChapterTypes.add(c);
                }
                contractVO.setUseChapterTypeList(contractUseChapterTypes);
            }
        }
        result.put("contract", contractVO);


        result.put("approvalList", bladeFlowList);
        return R.data(result);
    }

    @Override
    public List<ContractListResult> pageDetail(String contractId) {
        List<ContractListResult> listResults = baseMapper.pageDetail(contractId);
        for (ContractListResult result : listResults) {
            //        判断当前合同是否有归档数据,有归档数则全员三页，无归档数据则仅合同发起人有归档页
            ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(result.getCid());
            Contract contract = getById(result.getCid());
            if (archiveDetails != null) {
                result.setIsOwner(true);
            } else {
                if (contract.getCreateUser().longValue() == AuthUtil.getUserId().longValue()) {
                    result.setIsOwner(true);
                }
            }
        }
        return listResults;
    }

    @Override
    public List<Map> getKdContractEntry(Long projectId) {
        return baseMapper.getKdContractEntry(projectId);
    }

    @Override
    public List<Map> contractFilter(ContractListParam param) {
        param.setUserId(AuthUtil.getUserId());
        List<Map> maps = baseMapper.contractFilter(param);
//        for(Map m:maps){
//            if(m.get("contractStatus").toString().equals("8")){
//                m.put("contractStatus",3);
//            }
//            if(m.get("contractStatus").toString().equals("7")){
//                m.put("contractStatus",2);
//            }
//        }
        return maps;
    }

    @Override
    public ContractVO tidyUpContract(Contract contract) {
        ContractVO contractVO = ContractWrapper.build().entityVO(contract);
//     重新获取合同的业务类型（2024/07/31项目的分组和业务类型可以自己配置并修改，需要根据id进行联查）
        if (contractVO.getProjectId() != null && contractVO.getProjectId() != -1l) {
//        根据项目id获取业务类型id和业务类型名称(业务类型id/业务类型名称之前存的是金蝶项目分组id和名称，为了影响小，现在放的也是项目的金蝶分组id和名称)
            ProjectGroupDisposition group = projectBasicMapper.getGroupByProjectId(contractVO.getProjectId());
            if (group != null) {
                contractVO.setProjectTypeId(group.getKingdeeId());
                contractVO.setProjectTypeName(group.getName());
            }
        }
//         合同关联项目的大类（集成信息化1，安服2，其他3）
//        其他合同为其他
        if (contractVO.getContractType().equals("2")) {
            contractVO.setLargeType(3);
        }
//        销售合同项目类型区分集成信息项目/安服项目
//        if (contractVO.getContractType().equals("0")) {
//            contractVO.setLargeType(contractVO.getProjectTypeId().intValue());
//        }
        if (contractVO.getUseChapterTypeIds() != null && (!contractVO.getUseChapterTypeIds().equals(""))) {
            //        用章类型转为数组类型
            List ids = Arrays.stream(contractVO.getUseChapterTypeIds().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List vals = Arrays.stream(contractVO.getUseChapterTypeVals().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List<ContractUseChapterType> contractUseChapterTypes = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                ContractUseChapterType c = new ContractUseChapterType();
                c.setId(ids.get(i).toString());
                c.setName(vals.get(i).toString());
                contractUseChapterTypes.add(c);
            }
            contractVO.setUseChapterTypeList(contractUseChapterTypes);
        }
//        发起人姓名
        User createUser = userService.getUserByIdNotDelete(contractVO.getCreateUser());
//        判断当前合同是否有归档数据,有归档数则全员三页，无归档数据则仅合同发起人有归档页
        ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(contractVO.getId());
        if (archiveDetails != null) {
            contractVO.setIsOwner(true);
        } else {
            if (createUser.getId().longValue() == AuthUtil.getUserId().longValue()) {
                contractVO.setIsOwner(true);
            }
        }

        contractVO.setCreateUserName(createUser.getName());

        //未审批
        if (contractVO.getStatus() == ApprovedEnum.UNAPRROVED.getIndex()) {
            if (AuthUtil.getUserId().longValue() == contractVO.getCreateUser().longValue()) {
                contractVO.setWithdraw(true);
            } else {
                HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(contractVO.getId());
                if (historicProcess != null) {
                    if (historicProcess.getAssigneeId().contains(AuthUtil.getUserId().toString())) {
                        contractVO.setApproval(true);
                    }
                }
            }
        }
        //审批中
        if (contractVO.getStatus() == ApprovedEnum.APPROVING.getIndex()) {
            HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(contractVO.getId());
            if (historicProcess != null) {
                if (historicProcess.getAssigneeId().contains(AuthUtil.getUserId().toString())) {
                    contractVO.setApproval(true);
                }
            }
        }

        Map map = new HashMap();
        map.put("allowArchivingCondition", "true");
        map.put("contractId", contractVO.getId());
        Integer allowArchivingCount = contractChangeService.countChange(map);
        if (allowArchivingCount > 0) {
            contractVO.setAllowArchiving(true);
        }

        if (null != contractVO.getAnnex()) {
            contractVO.setAnnex(Base64Util.encode(AesUtil.encryptBase64(contractVO.getAnnex())));
        }
        if (contractVO.getStatus() == -1) {
            contractVO.setStatus(0);
        }
        //updaate 20250421 hz 回显产品类型
        List<OtherProjectGroupBO> bySourceId = otherProjectGroupMapper.getBySourceId(contract.getId());
        contractVO.setContractProjectGroupList(bySourceId);
        return contractVO;
    }

    @Override
    public Map saveDraft(ContractVO contract) {
        Map result = new HashMap();
        if (contract.getUseChapterTypeList() != null && !contract.getUseChapterTypeList().isEmpty()) {
            //        用章类型修改类型
            StringBuilder ids = new StringBuilder();
            StringBuilder vals = new StringBuilder();
            for (ContractUseChapterType c : contract.getUseChapterTypeList()) {
                ids.append(c.getId());
                ids.append(",");
                vals.append(c.getName());
                vals.append(",");
            }
            ids.deleteCharAt(ids.length() - 1);
            vals.deleteCharAt(vals.length() - 1);
            contract.setUseChapterTypeIds(ids.toString());
            contract.setUseChapterTypeVals(vals.toString());
        }
        boolean status;
        if (contract.getId() != null && StringUtil.isNotBlank(contract.getId().toString())) {
            status = updateById(contract);
        } else {
            status = save(contract);
        }
        if(status){
            saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
        }
        result.put("status", status);
        result.put("contractId", contract.getId());
        return result;
    }
    private void saveContractProjectGroup(Long contractId,List<OtherProjectGroupBO> groupBOList){
        otherProjectGroupMapper.removeBySourceId(contractId);
        for (OtherProjectGroupBO otherProjectGroupBO : groupBOList) {
            otherProjectGroupBO.setId(IdWorker.getId());
            otherProjectGroupBO.setOtherSourceId(contractId);
            otherProjectGroupBO.setOtherSourceTypeName(SourceTypeEnum.合同.getCode());
            otherProjectGroupBO.setCreateTime(LocalDateTime.now());
            otherProjectGroupBO.setCreateUser(AuthUtil.getUserId());
            otherProjectGroupMapper.insert(otherProjectGroupBO);
        }
    }

    @Override
    public Approval userToApproval(User user) {
        Approval approval = new Approval();
        approval.setId(user.getId().toString());
        approval.setAccount(user.getAccount());
        approval.setName(user.getName());
        approval.setPostId(user.getPostId());
        approval.setPost(user.getPost());
        approval.setAvatar(user.getAvatar());
        approval.setDeptId(user.getDeptId());
        approval.setParentId(String.valueOf(user.getParentId()));
        approval.setSort(user.getSort());
        return approval;

    }

    @Override
    public Map number(Long projectId) {
        ContractListParam param = new ContractListParam();
        param.setUserId(AuthUtil.getUserId());
        param.setPageType(1);
        param.setProjectId(projectId);
        String number1 = baseMapper.number(param);

        param.setPageType(2);
        String number2 = baseMapper.number(param);
        Map<String, String> objectObjectMap = new HashMap<>();
        objectObjectMap.put("number1", number1);
        objectObjectMap.put("number2", number2);
        return objectObjectMap;
    }

    /**
     * 根据合同名称校验合同的唯一性
     *
     * @param contractName
     * @return
     */
    @Override
    public List<ContractVO> checkContractNameSole(String contractName, Long contractId) {
        Map map = new HashMap();
        map.put("contractName", contractName);
        map.put("contractId", contractId);
        return baseMapper.checkContractNameSole(map);
    }

    /**
     * 合同变更时，选择合同时使用
     * 填写合同变更时要求合同列表展示：
     * 销售/其他合同展示待归档状态的合同【2023-10-11 销售/其他合同增加归档撤回、归档驳回两个状态】
     * 采购合同展示已归档项目
     *
     * @param contractName
     * @return
     */
    @Override
    public List<ContractVO> contractList(String contractName) {
        Map map = new HashMap();
        map.put("contractName", contractName);
        map.put("userId", AuthUtil.getUserId());
        return baseMapper.contractList(map);
    }

    // 从合同签订前的阶段跳转到合同签订，补充洽谈阶段详情数据
    private ProjectProgressSaleStage getDefaultSaleStageEntity() {
        ProjectProgressSaleStage projectProgressSaleStage = new ProjectProgressSaleStage();
        projectProgressSaleStage.setCreateUser(AuthUtil.getUserId());
        projectProgressSaleStage.setCreateTime(new Date());
        projectProgressSaleStage.setIsDeleted(BladeConstant.DB_NOT_DELETED);
        projectProgressSaleStage.setTenderFollowUp(2);
        projectProgressSaleStage.setServiceType("[]");
        return projectProgressSaleStage;
    }

    // 从合同签订前的阶段跳转到投标决策，补充投标决策详情数据
    private ProjectProgressBiddingDecision getDefaultBiddingDecisionEntity() {
        ProjectProgressBiddingDecision progressBiddingDecision = new ProjectProgressBiddingDecision();
        progressBiddingDecision.setCreateUser(AuthUtil.getUserId());
        progressBiddingDecision.setCreateTime(new Date());
        progressBiddingDecision.setIsDeleted(BladeConstant.DB_NOT_DELETED);
        return progressBiddingDecision;
    }

    /**
     * @param taskId 节点id
     * @param userId 要转给的人的id
     * @param type   1是无变更/0是有变更
     * @return 消息通知转办人，并更新本地表中当前审批人id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String transferTaskToUser(String taskId, String userId, int type) {
        try {
            // 验证任务存在
            if (taskService.createTaskQuery().taskId(taskId).count() == 0) {
                return "任务不存在";
            }

            // 连同任务附带的上下文信息一起转办给userId指定的用户
            taskService.setAssignee(taskId, userId);
            //无需记录转办原因
//        taskService.addComment(taskId, null, "转办原因: ");

            // 进一步的业务逻辑处理，如更新任务审批人记录、发送通知等
            // TODO: 2024/7/23 更新合同表办理人
            // 通过TaskService获取当前任务，通过任务去查找流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            // 通过任务对象获取流程实例ID
            String processInstanceId = task.getProcessInstanceId();
//        获取转办审批人信息
            User user = userService.getById(userId);
            //        根据合同流程实例id获取该实例关联的单据详情
            Map map = new HashMap();
            map.put("processInstanceId", processInstanceId);
            if (type == 1) {
                //        更新合同表当前审批人字段
                update(Wrappers.<Contract>update().lambda()
                        .set(Contract::getTaskUser, userId)
                        .eq(Contract::getProcessInstanceId, processInstanceId));

                ContractVO contractVO = selectDetail(map);
//        消息通知新的当前审批人
                BillMsgParams billMsgParams = new BillMsgParams();
                billMsgParams.setContractId(String.valueOf(contractVO.getId()));
                billMsgParams.setBillNo(contractVO.getName());
                billMsgParams.setType(DocumentType.合同.name());
                billMsgParams.setContractStatus(String.valueOf(contractVO.getStatus()));
                billMsgParams.setContractType(String.valueOf(contractVO.getContractType()));
                billMsgParams.setFlag(1);
                billMsgParams.setUserId(userId);
                billMsgParams.setUserName(user.getName());
                billMsgParams.setName(contractVO.getCreateUserName());
                billMsgParams.setStatus(ApprovedEnum.APPROVING.getIndex());
                sendBillMsgUtil.msg(billMsgParams);
            } else {
                contractChangeService.update(Wrappers.<ContractChange>update().lambda()
                        .set(ContractChange::getTaskUser, userId)
                        .eq(ContractChange::getProcessInstanceId, processInstanceId));
                ContractChangeVO contractChangeVO = contractChangeService.selectDetail(map);
//            根据合同id获取合同
                Contract contract = getById(contractChangeVO.getContractId());
                //                    获取下一级处理人信息
                BillMsgParams billMsgParams = new BillMsgParams();
                billMsgParams.setContractId(String.valueOf(contractChangeVO.getId()));
                billMsgParams.setBillNo(contractChangeVO.getName());
                if (contract.getContractType().equals("4")) {
                    billMsgParams.setType(DocumentType.渠道合同变更.name());
                } else {
                    billMsgParams.setType(DocumentType.变更.name());
                }
                billMsgParams.setContractType(contract.getContractType());
                billMsgParams.setType(DocumentType.变更.name());
                billMsgParams.setFlag(1);
                billMsgParams.setUserId(String.valueOf(user.getId()));
                billMsgParams.setUserName(user.getName());
                billMsgParams.setName(contractChangeVO.getCreateUserName());
                billMsgParams.setStatus(ApprovedEnum.APPROVING.getIndex());
                sendBillMsgUtil.msg(billMsgParams);
            }
            return "任务已成功转办给用户 " + user.getName();
        } catch (Exception e) {
            //            回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return "任务失败";
        }
    }

    @Override
    public String selectContractCount() {
        return baseMapper.selectContractCount();
    }

    @Override
    public List<Contract> contractListByBusinessId(Long businessId,List<Long> userIds) {
        return baseMapper.contractListByBusinessId(businessId,userIds);
    }

    @Override
    public List<Long> contractIdsByBusinessId(Long businessId,List<Long> userIds) {
        List<Contract> contracts = baseMapper.contractListByBusinessId(businessId, userIds);
        return contracts.stream().map(Contract::getId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getProjectIdsByContractIds(List<Long> contractIds) {
        return baseMapper.getProjectIdsByContractIds(contractIds);
    }

    @Override
    public List<Long> contractIdsForPaybackPanelBadDebt(Long businessId, List<Long> userIds) {
        return baseMapper.contractIdsForPaybackPanelBadDebt(businessId,userIds);
    }

    @Override
    public BigDecimal getTotalContractAmountByProjectId(Long projectId) {
        BigDecimal amount = baseMapper.getTotalContractAmountByProjectId(projectId,List.of(0,4));
        if (amount == null){
            amount = BigDecimal.ZERO;
        }
        return amount;
    }

    /**
     * 发起合同开启审批流程
     *
     * @param contract
     * @return
     */
    private boolean initCompleteTask(ContractVO contract) {
        // 启动流程
        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId())
                .set(FlowFormConstant.PROC_INSTANCE_FORM_NAME, FormNameConstant.CONTRACT)
                .set(FlowFormConstant.PROC_INSTANCE_FORM_DATA_ID, contract.getId());

        List<Long> assigneeList = userService.getALlParentByUserId(AuthUtil.getUserId());
        if (assigneeList != null && assigneeList.size() > 0) {
            variables.put("assigneeList", assigneeList);
        }

        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.CONTRACT_KEY);
        // TODO 刘源兴工作流
//        BladeFlow flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
        BladeFlow flow = null;
        if (Func.isNotEmpty(flow)) {
            contract.setProcessInstanceId(flow.getProcessInstanceId());
            update(Wrappers.<Contract>update().lambda()
                    .set(Contract::getProcessInstanceId, flow.getProcessInstanceId())
                    .set(Contract::getProcessDefinitionId, flow.getProcessDefinitionId())
                    .set(Contract::getStatus, ApprovedEnum.UNAPRROVED.getIndex())
                    .eq(Contract::getId, contract.getId()));

            HistoricProcessEntity historicProcessEntity = new HistoricProcessEntity();
            historicProcessEntity.setPid(contract.getId());
            historicProcessEntity.setBillNo(contract.getCode());
            historicProcessEntity.setBillName(contract.getName());
            historicProcessEntity.setMoney(contract.getContractAmount());
            historicProcessEntity.setType(FormNameConstant.CONTRACT);
            //合同类型（0销售合同/1采购合同/2其他合同/4渠道合同）
            if (contract.getContractType().equals("0")) {
                historicProcessEntity.setTypeName("销售合同");
            } else if (contract.getContractType().equals("1")) {
                historicProcessEntity.setTypeName("采购合同");
            } else {
                historicProcessEntity.setTypeName("其他合同");
            }
            historicProcessEntity.setBusinessType(BusinessTypeEnum.合同.getCode());
            historicProcessEntity.setBillsType(contract.getContractType());

            historicProcessEntity.setBillName(contract.getName());

            historicProcessService.saveHitoricProcessStart(historicProcessEntity, flow.getProcessInstanceId());
            return true;
        }
        return false;
    }

}
