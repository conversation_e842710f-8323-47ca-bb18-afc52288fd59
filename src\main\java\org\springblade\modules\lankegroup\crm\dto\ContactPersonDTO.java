/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 客户联系人表数据传输对象
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel(value = "ContactPersonDTO对象", description = "客户联系人表数据传输对象")
@EqualsAndHashCode(callSuper = false)
public class ContactPersonDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id", required = true)
	private Long id;

	/**
	 * 关联客户id
	 */
	@ApiModelProperty(value = "关联客户id")
	private Long customerContactId;

	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String contacts;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String contactsInformation;
	/**
	 * 微信号
	 */
	@ApiModelProperty(value = "微信号")
	private String wechat;
	/**
	 * 邮箱
	 */
	@ApiModelProperty(value = "邮箱")
	private String email;
	/**
	 * 是否决策者：0.否； 1.是；
	 */
	@ApiModelProperty(value = "是否决策者：0.否； 1.是；")
	private Integer decisionMakerStatus;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String province;
	/**
	 * 市
	 */
	@ApiModelProperty(value = "市")
	private String urbanArea;
	/**
	 * 县/区
	 */
	@ApiModelProperty(value = "县/区")
	private String city;
	/**
	 * 省份名称
	 */
	@ApiModelProperty(value = "省份名称")
	private String provinceName;
	/**
	 * 市名
	 */
	@ApiModelProperty(value = "市名")
	private String urbanAreaName;
	/**
	 * 县名/区名
	 */
	@ApiModelProperty(value = "县名/区名")
	private String cityName;
	/**
	 * 详细地址
	 */
	@ApiModelProperty(value = "详细地址")
	private String address;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
