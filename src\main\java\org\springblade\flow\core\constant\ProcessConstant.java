/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.core.constant;

/**
 * 流程常量.
 *
 * <AUTHOR>
 */
public interface ProcessConstant {

	//todo	这里只定义流程的常量，不应该把各业务常量放进来，以后优化

	/**
	 * 请假流程标识
	 */
	String LEAVE_KEY = "Leave";

	/**
	 * 报销流程标识
	 */
	String EXPENSE_KEY = "Expense";
	String PROJECTBASIC_KEY = "ProjectBasic";

	/**
	 * 合同审批
	 */
	String CONTRACT_KEY="Contract";
	/**
	 * mongo使用标识
	 * 采购申请单
	 */
	String CGSQ_KEY="CGSQ";
	/**
	 * mongo使用标识
	 * 油卡充值
	 */
	String YKCZ_KEY="YKCZ";
	/**
	 * mongo使用标识
	 * 付款单
	 */
	String FKD_KEY="FK";
	/**
	 * mongo使用标识
	 * 借款单
	 */
	String JKD_KEY="JK";

	/**
	 * 归档详情 标识
	 */
	String ARCHIVE_KEY = "Archive";
	/**
	 * 应收单 标识
	 */
	String RECEIVABLE_KEY = "Receivable";
	/**
	 * 其他应付单 标识
	 */
	String OTHER_PAYABLES_KEY="otherPayables";
	/**
	 * 回款单
	 */
	String PAYBACKRECEIPT_KEY="paybackReceipt";
	/**
	 * 出库单标识
	 */
	String 	OUTBOUND_KEY="outbound";
	/**
	 * 报销单
	 */
	String REIM_KEY="reim";
	/**
	 * 开票
	 */
	String INVOICE_KEY="invoice";
	/**
	 * 开票作废 标识
	 */
	String INVOICEVOID_KEY = "InvoiceVoid";

	/**
	 * 项目机会审批
	 */
	String PROJECTOPPORTUNITYASSISTANCE_KEY = "ProjectOpportunityAssistance";
	/**
	 * 开票看板项目流程审批
	 */
	String INVOICEKANBANPROJECTPROCESS_KEY = "InvoiceKanbanProjectProcess";
	/**
	 * 印章申请
	 */
	String SEALAPPLY_KEY="SealApply";
	/**
	 * 任务交付
	 */
	String TASKDELIVERY_KEY="Task";
	/**
	 * 渠道商流程 标识
	 */
	String CHANNEL_KEY = "ChannelSupplier";
	/**
	 * 同意标识
	 */
	String PASS_KEY = "pass";

	/**
	 * 同意代号
	 */
	String PASS_ALIAS = "ok";

	/**
	 * 同意默认批复
	 */
	String PASS_COMMENT = "同意";

	/**
	 * 驳回默认批复
	 */
	String NOT_PASS_COMMENT = "驳回";

	/**
	 * 创建人变量名
	 */
	String TASK_VARIABLE_CREATE_USER = "createUser";
	/**
	 * 创建人所属的一级部门变量名
	 */
	String TASK_VARIABLE_CREATE_USER_DEPT = "createUserDept";
	/**
	 * 创建人所属的直属部门变量名
	 */
	String TASK_VARIABLE_CREATE_USER_DIRECTLY_DEPT = "createUserDirectlyDept";


	/**
	 * 审批人
	 */
	String APPROVER_USER = "approver";
	/**
	 * 绩效评定
	 */
	String PERFORMANCE_EVALUATION = "performanceEvaluation";
	/**
	 * 付款单
	 */
	String PAYBILL_KEY="fk";

	/**
	 * 各级领导会签逐级审批列表流程字段名
	 */
	String TASK_VARIABLE_MULTI_INSTANCE_ASSIGNEE_LIST = "assigneeList";
	/**
	 * 财务法务会签
	 */
	String TASK_VARIABLE_MULTI_INSTANCE_FINANCIALLEGALASSIGNEE_LIST = "financialLegalAssigneeList";

	/**
	 * 任务分配岗位前缀
	 */
	String TASK_CANDIDATE_GROUP_POST = "POST_";

	/**
	 * 任务分配角色前缀
	 */
	String TASK_CANDIDATE_GROUP_ROLE = "ROLE_";

	/**
	 * 任务分配部门前缀
	 */
	String TASK_CANDIDATE_GROUP_DEPT = "DEPT_";

	/**
	 * 一级部门主管变量名
	 */
	String TASK_VARIABLE_DEPT_TOP_LEADER = "deptToplLeader";

	/**
	 * 用户直属领导变量名称
	 */
	String TASK_VARIABLE_USERSUPERIOR = "superior";
	/**
	 * 主管标志
	 */
	String TASK_VARIABLE_LEADER = "leader";


}
