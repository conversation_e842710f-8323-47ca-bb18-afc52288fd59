package org.springblade.common.cache.example;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.LocalCache;

/**
 * LocalCache使用示例
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class LocalCacheExample {
    
    /**
     * 用户缓存示例
     */
    private static final LocalCache<String> USER_CACHE = 
            new LocalCache<>("用户信息缓存", 10 * 60 * 1000L, 2); // 10分钟过期，2分钟清理
    
    /**
     * 配置缓存示例
     */
    private static final LocalCache<Object> CONFIG_CACHE = 
            new LocalCache<>("配置信息缓存"); // 使用默认配置
    
    /**
     * 示例：基本使用
     */
    public static void basicUsageExample() {
        // 缓存用户信息
        USER_CACHE.put("user123", "张三");
        
        // 获取用户信息
        String userName = USER_CACHE.get("user123");
        log.info("获取用户信息: {}", userName);
        
        // 检查是否存在
        boolean exists = USER_CACHE.contains("user123");
        log.info("用户信息是否存在: {}", exists);
        
        // 移除缓存
        USER_CACHE.remove("user123");
        
        // 获取统计信息
        String stats = USER_CACHE.getStats();
        log.info("缓存统计: {}", stats);
    }
    
    /**
     * 示例：使用自定义过期时间
     */
    public static void customExpireTimeExample() {
        // 缓存配置信息，指定5分钟过期
        CONFIG_CACHE.put("config.timeout", 30000, 5 * 60 * 1000L);
        
        // 获取配置信息
        Object timeout = CONFIG_CACHE.get("config.timeout");
        log.info("获取配置信息: {}", timeout);
    }
    
    /**
     * 示例：使用数据加载器
     */
    public static void dataLoaderExample() {
        // 获取数据，如果不存在则通过loader加载
        String userData = USER_CACHE.getOrLoad("user456", key -> {
            // 模拟从数据库加载用户数据
            log.info("从数据库加载用户数据: {}", key);
            return "用户数据-" + key;
        });
        
        log.info("获取用户数据: {}", userData);
        
        // 第二次获取，直接从缓存返回
        String cachedData = USER_CACHE.get("user456");
        log.info("从缓存获取用户数据: {}", cachedData);
    }
    
    /**
     * 示例：批量操作
     */
    public static void batchOperationExample() {
        // 批量缓存数据
        for (int i = 1; i <= 10; i++) {
            USER_CACHE.put("user" + i, "用户" + i);
        }
        
        // 获取缓存统计
        log.info("缓存统计: {}", USER_CACHE.getStats());
        log.info("缓存大小: {}", USER_CACHE.size());
        log.info("活跃缓存数: {}", USER_CACHE.getActiveCount());
        
        // 清理所有缓存
        USER_CACHE.clear();
        log.info("清理后缓存大小: {}", USER_CACHE.size());
    }
    
    /**
     * 示例：自定义对象缓存
     */
    public static void customObjectCacheExample() {
        // 创建自定义对象缓存
        LocalCache<UserInfo> userInfoCache = new LocalCache<>("用户详情缓存");
        
        // 缓存用户详情
        UserInfo userInfo = new UserInfo("123", "张三", "<EMAIL>");
        userInfoCache.put("user123", userInfo);
        
        // 获取用户详情
        UserInfo cachedUserInfo = userInfoCache.get("user123");
        log.info("获取用户详情: {}", cachedUserInfo);
        
        // 销毁缓存
        userInfoCache.destroy();
    }
    
    /**
     * 用户信息实体类示例
     */
    public static class UserInfo {
        private String id;
        private String name;
        private String email;
        
        public UserInfo(String id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }
        
        @Override
        public String toString() {
            return "UserInfo{id='" + id + "', name='" + name + "', email='" + email + "'}";
        }
    }
    
    /**
     * 主方法，运行示例
     */
    public static void main(String[] args) {
        log.info("=== LocalCache使用示例 ===");
        
        log.info("1. 基本使用示例");
        basicUsageExample();
        
        log.info("2. 自定义过期时间示例");
        customExpireTimeExample();
        
        log.info("3. 数据加载器示例");
        dataLoaderExample();
        
        log.info("4. 批量操作示例");
        batchOperationExample();
        
        log.info("5. 自定义对象缓存示例");
        customObjectCacheExample();
        
        // 销毁缓存
        USER_CACHE.destroy();
        CONFIG_CACHE.destroy();
        
        log.info("=== 示例执行完成 ===");
    }
} 