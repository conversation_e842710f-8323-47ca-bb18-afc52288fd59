/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import org.springblade.modules.lankegroup.contractManagement.controller.ContractUseChapterType;
import org.springblade.modules.lankegroup.contractManagement.entity.Approval;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetailsFiles;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.lankegroup.disposition.bo.OtherProjectGroupBO;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同管理表视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractVO extends Contract {
    private static final long serialVersionUID = 1L;
    /**
     * 发起人姓名
     */
    private String createUserName;
//    用章类型
    private List<ContractUseChapterType> useChapterTypeList=new ArrayList();
//    归档信息大类（集成信息化1，安服2，其他3）
    private Integer largeType=-1;
//    审批流程+抄送人
    private List approvalList=new ArrayList<>();
//    当前登录人是否可撤回
    private Boolean withdraw=false;
//    当前登录人是否可审批
    private Boolean approval=false;
/**
 * 有变更中/作废的合同不允许归档
 * 有变更中的/作废的合同true===》不允许归档
 */
    private Boolean allowArchiving=false;
    /**
     * 当前登录人是否为合同发起人
     * true为是
     * false为不是
     * 前端根据此字段控制合同归档是否要出现
     */
    private Boolean isOwner=false;
    /**
     * 签订方式描述（direct直签/channel渠道）（渠道合同）
     */
    private String signingModeName;
    //update 20250421 hz 关联产品类型
    private List<OtherProjectGroupBO> contractProjectGroupList=new ArrayList<>();
    //合同归档附件
    private ArchiveDetailsFiles archiveDetailsFiles = new ArchiveDetailsFiles();
}
