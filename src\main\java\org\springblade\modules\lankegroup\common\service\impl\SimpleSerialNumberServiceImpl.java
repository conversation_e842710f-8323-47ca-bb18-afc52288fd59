/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springblade.modules.lankegroup.common.entity.SimpleSerialNumber;
import org.springblade.modules.lankegroup.common.mapper.SimpleSerialNumberMapper;
import org.springblade.modules.lankegroup.common.service.ISimpleSerialNumberService;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 简单全局序列号表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@AllArgsConstructor
public class SimpleSerialNumberServiceImpl extends ServiceImpl<SimpleSerialNumberMapper, SimpleSerialNumber> implements ISimpleSerialNumberService {



    /**
     * 生成单据编号：流水号(3位)
     *
     * @param bizType 业务类型
     * @return
     */
    public String generateBillNumber(String bizType) {
        return generateBillNumber(bizType, 3);
    }
    /**
     * 生成单据编号：流水号(自定义位数位)
     *
     * @param bizType 业务类型
     * @return
     */
    public String generateBillNumber(String bizType, int numberOfDigits) {
        // 获取并递增序列号
        long sequence = getNextSequence(bizType);

        // 组合单据编号
        return String.format("%0" + numberOfDigits + "d", sequence);
    }

    // 使用乐观锁获取下一个序列号
    private long getNextSequence(String bizType) {
        int retryCount = 0;
        final int MAX_RETRY = 5;

        while (retryCount++ < MAX_RETRY) {
            // 获取当前序列记录
            SimpleSerialNumber serial = getOrCreateSerial(bizType);

            // 尝试更新序列值
            long newValue = serial.getCurrentValue() + 1;
            boolean update = update(Wrappers.lambdaUpdate(SimpleSerialNumber.class)
                    .eq(SimpleSerialNumber::getId, serial.getId())
                    .eq(SimpleSerialNumber::getCurrentValue, serial.getCurrentValue())
                    .eq(SimpleSerialNumber::getVersion, serial.getVersion())
                    .set(SimpleSerialNumber::getCurrentValue, newValue)
                    .set(SimpleSerialNumber::getVersion, serial.getVersion() + 1)
            );
            if (update) {
                return newValue;
            }
           /* int updateCount = baseMapper.incrementValue(
                    serial.getId(), serial.getCurrentValue(), newValue, serial.getVersion());

            if (updateCount > 0) {
                return newValue;
            }*/

            // 乐观锁冲突，等待后重试
            try {
                Thread.sleep(50 * (long) Math.pow(2, retryCount));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("序列号获取中断", e);
            }
        }
        throw new RuntimeException("无法获取序列号，请重试");
    }

    // 获取或创建序列记录
    private SimpleSerialNumber getOrCreateSerial(String bizType) {
        SimpleSerialNumber serial = baseMapper.selectByBizType(bizType);
        if (serial != null) {
            return serial;
        }

        // 创建新的序列记录
        serial = new SimpleSerialNumber();
        serial.setBizType(bizType);
        serial.setCurrentValue(1L);
        serial.setVersion(0);

        try {
            baseMapper.insert(serial);
            return serial;
        } catch (DuplicateKeyException e) {
            // 并发创建冲突，重新获取
            return baseMapper.selectByBizType(bizType);
        }
    }

}
