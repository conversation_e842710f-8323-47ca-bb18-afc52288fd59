package org.springblade.common.enums;

/**
 * 【兰科网络：109122】
 * 【北京实名宝：148638】
 * 【明鉴：149951】
 * 【随手拍：149954】
 * 【实名宝廊坊：163259】
 * 【实名宝成都：163525】
 * 【诺恒管理：163527】
 * 当前钉钉仅涉及兰科一个账簿
 */
public enum AccountBookEnum {
    LK("102", "109122");
//    SMB("101", "148638"),
//    MJ("106", "149951"),
//    SSP("107", "149954"),
//    LFSMB("108", "163259"),
//    CDSMB("109", "163525"),
//    NHGL("110", "163527");
    private String orgCode;
    private String bookId;

    AccountBookEnum(String orgCode, String bookId) {
        this.orgCode = orgCode;
        this.bookId = bookId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public String getBookId() {
        return bookId;
    }
}
