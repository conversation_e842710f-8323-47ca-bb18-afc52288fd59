<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ApproveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="approveResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.Approve">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="flag" property="flag"/>
        <result column="comment" property="comment"/>
    </resultMap>


    <select id="selectApprovePage" resultMap="approveResultMap">
        select * from blade_approve where is_deleted = 0
    </select>
    <select id="selectApprove" resultType="org.springblade.modules.lankegroup.contractManagement.entity.Approve">
        select * from blade_approve where is_deleted = 0 and process_instance_id = #{processInstanceId} ORDER BY create_time ASC
    </select>

</mapper>
