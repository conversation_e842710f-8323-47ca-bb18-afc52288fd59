/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.SupplierContact;
import org.springblade.modules.lankegroup.crm.vo.SupplierContactVO;
import org.springblade.modules.lankegroup.crm.service.ISupplierContactService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 供应商专职联系人表 控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/supplierContact/suppliercontact")
@Api(value = "供应商专职联系人表", tags = "供应商专职联系人表接口")
public class SupplierContactController extends BladeController {

	private final ISupplierContactService supplierContactService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入supplierContact")
	public R<SupplierContact> detail(SupplierContact supplierContact) {
		SupplierContact detail = supplierContactService.getOne(Condition.getQueryWrapper(supplierContact));
		return R.data(detail);
	}

	/**
	 * 分页 供应商专职联系人表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入supplierContact")
	public R<IPage<SupplierContact>> list(SupplierContact supplierContact, Query query) {
		IPage<SupplierContact> pages = supplierContactService.page(Condition.getPage(query), Condition.getQueryWrapper(supplierContact));
		return R.data(pages);
	}

	/**
	 * 自定义分页 供应商专职联系人表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入supplierContact")
	public R<IPage<SupplierContactVO>> page(SupplierContactVO supplierContact, Query query) {
		IPage<SupplierContactVO> pages = supplierContactService.selectSupplierContactPage(Condition.getPage(query), supplierContact);
		return R.data(pages);
	}

	/**
	 * 新增 供应商专职联系人表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入supplierContact")
	public R save(@Valid @RequestBody SupplierContact supplierContact) {
		return R.status(supplierContactService.save(supplierContact));
	}

	/**
	 * 修改 供应商专职联系人表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入supplierContact")
	public R update(@Valid @RequestBody SupplierContact supplierContact) {
		return R.status(supplierContactService.updateById(supplierContact));
	}

	/**
	 * 新增或修改 供应商专职联系人表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入supplierContact")
	public R submit(@Valid @RequestBody SupplierContact supplierContact) {
		return R.status(supplierContactService.saveOrUpdate(supplierContact));
	}

	
	/**
	 * 删除 供应商专职联系人表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierContactService.deleteLogic(Func.toLongList(ids)));
	}

	
}
