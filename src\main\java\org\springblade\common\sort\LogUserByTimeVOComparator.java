package org.springblade.common.sort;

import org.springblade.modules.lankegroup.customerVisit.vo.LogUserByTimeVO;

import java.util.Comparator;
import java.util.List;

public class LogUserByTimeVOComparator implements Comparator<List<LogUserByTimeVO>> {
    @Override
    public int compare(List<LogUserByTimeVO> list1, List<LogUserByTimeVO> list2) {
        LogUserByTimeVO lastItem1 = list1.get(list1.size() - 1);
        LogUserByTimeVO lastItem2 = list2.get(list2.size() - 1);
        return lastItem1.getTime().compareTo(lastItem2.getTime());
    }
}
