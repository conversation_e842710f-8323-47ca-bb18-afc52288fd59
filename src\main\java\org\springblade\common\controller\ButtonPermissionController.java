package org.springblade.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.config.ButtonPermissionConfig;
import org.springblade.common.dto.ButtonPermissionDTO;
import org.springblade.common.manager.ButtonPermissionManager;
import org.springblade.common.vo.ButtonPermissionVO;
import org.springblade.common.vo.ButtonPermissionDetailVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 按钮权限控制器
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 16:00
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/button-permission")
@Api(value = "按钮权限控制", tags = "按钮权限控制接口")
public class ButtonPermissionController extends BladeController {
    
    private final ButtonPermissionManager buttonPermissionManager;
    private final ButtonPermissionConfig buttonPermissionConfig;
    
    /**
     * 获取按钮权限
     */
    @PostMapping("/query")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查询按钮权限", notes = "根据页面业务标识和场景获取按钮权限配置")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R<Map<String, Map<String, ButtonPermissionDetailVO>>> queryButtonPermissions(@Valid @RequestBody @ApiParam(value = "按钮权限查询参数", required = true) ButtonPermissionDTO dto) {
        // 参数校验
        if (Func.isBlank(dto.getPageBusinessCode())) {
            return R.fail("页面业务标识不能为空");
        }
        
        try {
            ButtonPermissionVO result = buttonPermissionManager.getButtonPermissions(dto);
            // 直接返回permissions内容，不包装在外层对象中
            return R.data(result.getPermissions());
        } catch (Exception e) {
            // 记录完整的错误信息到日志
            log.error("查询按钮权限失败，请求参数: {}", dto, e);
            return R.fail("查询按钮权限失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理权限缓存
     */
    @PostMapping("/clear-cache")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "清理权限缓存", notes = "清理按钮权限的缓存数据")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功"),
        @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R<String> clearCache() {
        try {
            buttonPermissionManager.clearExpiredCache();
            return R.success("缓存清理成功");
        } catch (Exception e) {
            return R.fail("缓存清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新权限配置（开发环境使用）
     */
    @PostMapping("/refresh-config")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "刷新权限配置", notes = "手动刷新按钮权限配置，支持热重启")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功"),
        @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R<String> refreshConfig() {
        try {
            buttonPermissionConfig.refreshConfig();
            // 同时清理缓存，确保使用新配置
            buttonPermissionManager.clearExpiredCache();
            return R.success("配置刷新成功，缓存已清理");
        } catch (Exception e) {
            log.error("刷新权限配置失败", e);
            return R.fail("配置刷新失败: " + e.getMessage());
        }
    }
    
    /**
     * 快速查询按钮权限（GET方式）
     */
    @GetMapping("/quick-query")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "快速查询按钮权限", notes = "通过GET方式快速查询简单的按钮权限")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageBusinessCode", value = "页面业务标识", paramType = "query", dataType = "string", required = true),
        @ApiImplicitParam(name = "sceneCode", value = "场景标识", paramType = "query", dataType = "string"),
        @ApiImplicitParam(name = "buttonCode", value = "按钮标识", paramType = "query", dataType = "string"),
        @ApiImplicitParam(name = "businessDataId", value = "业务数据ID", paramType = "query", dataType = "long")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R<Map<String, Map<String, ButtonPermissionDetailVO>>> quickQueryButtonPermissions(
            @RequestParam String pageBusinessCode,
            @RequestParam(required = false) String sceneCode,
            @RequestParam(required = false) String buttonCode,
            @RequestParam(required = false) Long businessDataId) {
        
        // 参数校验
        if (Func.isBlank(pageBusinessCode)) {
            return R.fail("页面业务标识不能为空");
        }
        
        try {
            ButtonPermissionDTO dto = new ButtonPermissionDTO();
            dto.setPageBusinessCode(pageBusinessCode);
            
            if (Func.isNotBlank(sceneCode)) {
                dto.setSceneCodes(Func.ofImmutableList(sceneCode));
            }
            
            if (Func.isNotBlank(buttonCode)) {
                dto.setButtonCodes(Func.ofImmutableList(buttonCode));
            }
            
            if (businessDataId != null) {
                dto.setBusinessDataId(businessDataId);
            }
            
            ButtonPermissionVO result = buttonPermissionManager.getButtonPermissions(dto);
            // 直接返回permissions内容，不包装在外层对象中
            return R.data(result.getPermissions());
        } catch (Exception e) {
            // 记录完整的错误信息到日志
            log.error("快速查询按钮权限失败，请求参数: pageBusinessCode={}, sceneCode={}, buttonCode={}, businessDataId={}", 
                pageBusinessCode, sceneCode, buttonCode, businessDataId, e);
            return R.fail("查询按钮权限失败: " + e.getMessage());
        }
    }
} 