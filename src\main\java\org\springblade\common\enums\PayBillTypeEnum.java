package org.springblade.common.enums;


public enum PayBillTypeEnum {
    PREIM("费用报销付款单", "FKDLX04_SYS"),
    PBUY("采购付款单", "FKDLX01_SYS"),
    PMARGIN("保证金付款单", "FKDLX08_SYS"),
    OTHER("其他付款单", "FKDLX02_SYS"),
    LOAN("借款单","FKDLX10");


    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private PayBillTypeEnum(String name, String index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(String index) {
        for (PayBillTypeEnum c : PayBillTypeEnum.values()) {
            if (index.equalsIgnoreCase(c.getIndex())) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public String getIndex() {
        return index;
    }
}
