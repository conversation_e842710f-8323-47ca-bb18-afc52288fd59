/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.mapper;

import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;

import java.util.List;
import java.util.Map;

/**
 * 合同表变更记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
public interface ContractChangeMapper extends BaseMapper<ContractChange> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractChange
	 * @return
	 */
	List<ContractChangeVO> selectContractChangePage(IPage page, ContractChangeVO contractChange);
	/**
	 * 查询当前登录人，在当前合同类型是否存在草稿
	 *
	 * @param map
	 * @return
	 */
	ContractChangeVO selectDraft(Map map);
	/**
	 * 详情查询
	 *
	 * @param
	 * @return
	 */
	ContractChangeVO selectDetail(Map map);
	/**
	 * 流程id获取
	 *
	 * @param processInstanceId
	 * @return
	 */
	String selectTaskIdByBasic(String processInstanceId);
	String selectTaskIdByBasicAndUser(String processInstanceId, String assignee);

	//	查询流程实例关联的单据信息
	Long selectByProcessInstanceId(String processInstanceId);
	String selectByProcessInstanceIdString(String processInstanceId);
	List<Map> changeList(Long contractId);

	/**
	 * 查看合同是否有关联变更数据
	 * @param map
	 * @return
	 */
	Integer countChange(Map map);
}
