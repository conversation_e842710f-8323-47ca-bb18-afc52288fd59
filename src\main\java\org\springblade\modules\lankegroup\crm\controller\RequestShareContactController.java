/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.RequestShareContact;
import org.springblade.modules.lankegroup.crm.vo.RequestShareContactVO;
import org.springblade.modules.lankegroup.crm.service.IRequestShareContactService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏 控制器
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/share/requestsharecontact")
@Api(value = "客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏", tags = "客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏接口")
public class RequestShareContactController extends BladeController {

	private final IRequestShareContactService requestShareContactService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入requestShareContact")
	public R<RequestShareContact> detail(RequestShareContact requestShareContact) {
		RequestShareContact detail = requestShareContactService.getOne(Condition.getQueryWrapper(requestShareContact));
		return R.data(detail);
	}

	/**
	 * 分页 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入requestShareContact")
	public R<IPage<RequestShareContact>> list(RequestShareContact requestShareContact, Query query) {
		IPage<RequestShareContact> pages = requestShareContactService.page(Condition.getPage(query), Condition.getQueryWrapper(requestShareContact));
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 * 客户联系人申请共享联系人表
	 * 点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享
	 * 根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 *
	 * 仅负责人打开联系人详情有此列表
	 */
	@PostMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入requestShareContact")
	public R<IPage<RequestShareContactVO>> page(@RequestBody RequestShareContactVO requestShareContact,@RequestBody Query query) {
		IPage<RequestShareContactVO> pages = requestShareContactService.selectRequestShareContactPage(Condition.getPage(query), requestShareContact);
		return R.data(pages);
	}

	/**
	 * 新增 客户联系人申请共享联系人表
	 * 点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享
	 * 根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 */
	@PostMapping("/applyForSharing")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入requestShareContact")
	public R save(@Valid @RequestBody RequestShareContact requestShareContact) {
		if (requestShareContact == null ) {
			return R.fail("请选择客户");
		}
		try {
			return requestShareContactService.applyForSharing(requestShareContact);
		} catch (Exception e) {
			log.error("客户申请共享------>" + e.getMessage());
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 修改 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入requestShareContact")
	public R update(@Valid @RequestBody RequestShareContact requestShareContact) {
		return R.status(requestShareContactService.updateById(requestShareContact));
	}

	/**
	 * 新增或修改 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入requestShareContact")
	public R submit(@Valid @RequestBody RequestShareContact requestShareContact) {
		return R.status(requestShareContactService.saveOrUpdate(requestShareContact));
	}


	/**
	 * 删除 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(requestShareContactService.deleteLogic(Func.toLongList(ids)));
	}



	/**
	 * 客户联系人申请回应
	 * 状态（无回应0/1同意/2不同意/3添加联系人页面，提示库内有联系人，手机号已知的情况下，可以选择直接共享给自己）
	 * 由于考虑到客户的联系人可以转交，做出回应的可能不是最初申请的那个联系人，所以每次回应前先去取联系人的负责人，写道消息体里
	 * @param requestShareContact
	 * @return
	 */
	@PostMapping("/roundsForSharing")
	public R roundsForSharing(@RequestBody RequestShareContact requestShareContact){
		try {
			return requestShareContactService.roundsForSharing(requestShareContact);
		} catch (Exception e) {
			log.error("客户申请共享------>" + e.getMessage());
			return R.fail("系统繁忙，请稍后重试");
		}
	}


}
