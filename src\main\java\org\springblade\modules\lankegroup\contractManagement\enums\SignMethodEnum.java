package org.springblade.modules.lankegroup.contractManagement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 签约方式枚举
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum SignMethodEnum {

    /**
     * 客户签约
     */
    CUSTOMER_CONTACT_SIGN("customer", "客户签约"),

    /**
     * 机构签约
     */
    CUSTOMER_SIGN("organization", "机构签约");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

}
