package org.springblade.modules.lankegroup.clientrelation.dto;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;

import java.util.List;

/**
 * 客户看板入参
 */
@Data
public class CustomerKanBanDTO {

    /**
     * 年入参
     */
    private String dateYear;

    /**
     * 月入参
     */
    private String dateMonth;

    /**
     * 查询用户ID
     */
    private List<Long> selectUserList;

    /**
     * 当前登陆人权限内可查询用户Id
     */
    private List<Long> authUserList;

    /**
     * 是否添加时间过滤条件
     */
    private Integer type;

    /**
     * 查询数据类型（1、客户总数 2、拜访客户总数 3、立项客户总数 4、基本盘客户总数）
     */
    private Integer dataType;

    /**
     * 返回结果类型（1、List（客户ID） 2、Map（客户总数））
     */
    private Integer resultData;

    /**
     * 可查询区划信息
     */
    private List<AreaAndMarket> areaAndMarketList;

    /**
     * 可查询人员区划信息
     */
    private List<Long> areaAndMarketUserList;

    /**
     * 判断是否是高层（1、否 2是）
     */
    private Integer leader;

    /**
     * 区划市场ID
     */
    private String professionId;

    /**
     * 区划市场数组
     */
    private List<Long> professionIdList;

    /**
     * 行业Id
     */
    private String marketplaceId;

    /**
     * 行业Id
     */
    private List<String> marketplaceIdList;

    /**
     * 区划ID
     */
    private String areaId;

    /**
     * 区划ID
     */
    private List<String> areaIdList;


    /**
     * 行业名称
     */
    private String marketName;
    /**
     * 客户重要程度id
     */
    private Integer importanceKey;
    /**
     * 查询字段
     */
    private String searchBox;

    /**
     * 是否有数据(1有 2无)
     */
    private Integer isData;

    /**
     * 升序降序 默认降序
     */
    private boolean sort;
    /**
     * 项目类型Id
     */
    private String projectTypeId;

    /**
     * 是否是组长
     */
    private Integer groupManager;

    /**
     * 查询组别
     */
    private String deptId;

    /**
     * 查询人员
     */
    private String userId;

    /**
     * 开始时间
     * 2024-1-16
     * 客户看板时间筛选改为区间筛选，废弃年月筛选
     */
    private String startTime;

    /**
     * 2024-1-16
     * 客户看板时间筛选改为区间筛选，废弃年月筛选
     * 结束时间
     */
    private String endTime;

    /**
     * 是否需要查询其他
     */
    private String qiTqType;

    /**
     * 其他类型的区域市场ID
     */
    private String qiTqProfessionId;

    private List<String> customerList;
}
