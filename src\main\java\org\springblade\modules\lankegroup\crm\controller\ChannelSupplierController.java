package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.crm.entity.ChannelSupplierParams;
import org.springblade.modules.lankegroup.crm.entity.Org;
import org.springblade.modules.lankegroup.crm.service.IOrgService;
import org.springblade.modules.lankegroup.crm.vo.ChannelSupplierDetailVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.ChannelSupplier;
import org.springblade.modules.lankegroup.crm.vo.ChannelSupplierVO;
import org.springblade.modules.lankegroup.crm.service.IChannelSupplierService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;
import java.util.Map;

/**
 * 渠道商列表 控制器
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/channel/supplier")
@Api(value = "渠道商列表", tags = "渠道商列表接口")
public class ChannelSupplierController extends BladeController {

	private final IChannelSupplierService channelSupplierService;
	private final FlowEngineService flowEngineService;
	private final IOrgService orgService;

	/**
	 * 详情 流程需要
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入channelSupplier")
	public R<ChannelSupplierVO> detail(ChannelSupplier channelSupplier) {
		ChannelSupplier detail = channelSupplierService.getOne(Condition.getQueryWrapper(channelSupplier));
		return R.data(channelSupplierService.fillDetailInfo(detail));
	}

	@GetMapping("/getNumber")
	public R getNumber(){
		return R.data(channelSupplierService.operationsNumber());
	}
	/**
	 * 详情 渠道商详细信息：统计数据，关联项目，关联合同
	 */
	@GetMapping("/detailedInfo")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "渠道商详细信息", notes = "传入channelSupplier")
	public R<ChannelSupplierDetailVO> detailedInfo(ChannelSupplier channelSupplier){
		return R.data(channelSupplierService.detailedInfo(channelSupplier));
	}

	/**
	 * 前端没有使用
	 * @return
	 */
	@GetMapping("number")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "需要本人操作（待审批、已驳回）的数据", notes = "")
	public R operationsNumber(){
		return R.data(channelSupplierService.operationsNumber());
	}
	/**
	 * 分页 渠道商列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入channelSupplier")
	public R<IPage<ChannelSupplier>> list(ChannelSupplier channelSupplier, Query query) {
		QueryWrapper<ChannelSupplier> queryWrapper = new QueryWrapper<>();
		queryWrapper.like("channel_name", channelSupplier.getChannelName());
		IPage<ChannelSupplier> pages = channelSupplierService.page(Condition.getPage(query), queryWrapper);
		return R.data(pages);
	}
	/**
	 * 渠道商列表头部统计
	 */
	@GetMapping("headerStatistics")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "渠道商列表头部统计", notes = "传入channelSupplier")
	public R headerStatistics(ChannelSupplierParams channelSupplier){
		return R.data(channelSupplierService.headerStatistics(channelSupplier));
	}
	/**
	 * 自定义分页 渠道商列表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "渠道商列表分页", notes = "传入channelSupplier")
	public R<IPage<ChannelSupplierDetailVO>> page(ChannelSupplierParams channelSupplier, Query query) {
		IPage<ChannelSupplierDetailVO> pages = channelSupplierService.selectChannelSupplierPage(Condition.getPage(query), channelSupplier);
		return R.data(pages);
	}
	/**
	 * 自定义分页 审核列表
	 */
	@GetMapping("/auditPage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "审核列表分页", notes = "传入channelSupplier")
	public R<IPage<ChannelSupplierVO>> auditPage(ChannelSupplierParams channelSupplier, Query query) {
		IPage<ChannelSupplierVO> pages = channelSupplierService.selectChannelSupplierAuditPage(Condition.getPage(query), channelSupplier);
		return R.data(pages);
	}

	/**
	 * 新增 渠道商列表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入channelSupplier")
	public R save(@Valid @RequestBody ChannelSupplier channelSupplier) {
		return R.status(channelSupplierService.save(channelSupplier));
	}

	/**
	 * 修改 渠道商列表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入channelSupplier")
	public R update(@Valid @RequestBody ChannelSupplierVO channelSupplier) {
		return R.status(channelSupplierService.updateChannel(channelSupplier));
	}
	/**
	 * 判断渠道商是否已存在
	 * 根据纳税登记号判断
	 */
	@GetMapping("checkChannelIsExists")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "根据纳税登记号判断渠道商是否已存在", notes = "纳税登记号")
	public R checkChannelIsExists(String creditCode){
		Long channelCount = channelSupplierService.count(Wrappers.<ChannelSupplier>query().lambda()
				.eq(ChannelSupplier::getTaxRegistrationNo, creditCode));
		if(channelCount > 0){
			return R.data(false);
		}else{
			return R.data(true);
		}
	}

	/**
	 * 新增或修改 渠道商列表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入channelSupplier")
	public R submit(@Valid @RequestBody ChannelSupplierVO channelSupplier) {
		// 判断纳税登记号是否已存在
		Long channelCount;
		if(Func.isEmpty(channelSupplier.getId())){
			channelCount = channelSupplierService.count(Wrappers.<ChannelSupplier>query().lambda()
					.eq(ChannelSupplier::getTaxRegistrationNo, channelSupplier.getTaxRegistrationNo()));
		}else{
			channelCount = channelSupplierService.count(Wrappers.<ChannelSupplier>query().lambda()
					.eq(ChannelSupplier::getTaxRegistrationNo, channelSupplier.getTaxRegistrationNo())
					.ne(ChannelSupplier::getId, channelSupplier.getId()));
		}
		if(channelCount > 0){
			return R.fail(500,"该纳税登记号的渠道已创建，请勿重复创建");
		}
		// TODO 刘源兴工作流
//		// 判断流程是否存在，返回流程id
//		String processId = flowEngineService.getProcessId("渠道商审批流程", "channelSupplier");
//		if("".equals(processId)){
//			return R.fail(500, "流程不存在");
//		}
//		channelSupplier.setProcessDefinitionId(processId);
//		Map map = channelSupplierService.startProcess(channelSupplier);
//		if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().equals(""))) {
//			return R.data(map);
////			map = channelSupplierService.approvalInPerson(map);
////			if (map.get("status").toString().equals("true")) {
////				return R.data(map);
////			} else {
////				return R.fail(500, "流程无法提交，请联系管理员");
////			}
//		} else {
//			return R.fail(500, "流程保存失败");
//		}
		//return R.status(channelSupplierService.saveOrUpdate(channelSupplier));
			return R.fail(500, "流程保存失败");
	}


	/**
	 * 删除 渠道商列表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(channelSupplierService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 初始化渠道商添加页面
	 *
	 * @param
	 * @return  返回当前登陆人渠道商审批流程节点
	 */
	@GetMapping("/initChannelSupplier")
	public R initChannelSupplier() {
		return R.data(channelSupplierService.initChannelSupplier());
	}

	/**
	 * 启用/禁用
	 */
	@GetMapping("updateForbidStatus")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "启用/禁用", notes = "传入id和标识")
	public R updateForbidStatus(ChannelSupplier channelSupplier){
		return channelSupplierService.updateForbidStatus(channelSupplier);
	}

	/**
	 * 渠道商历史销售合同数据统计
	 * 用一次后就没有用了
	 */
	@GetMapping("salesContractCount")
	public R salesContractCount(){
		return channelSupplierService.oldContractCount();
	}
}
