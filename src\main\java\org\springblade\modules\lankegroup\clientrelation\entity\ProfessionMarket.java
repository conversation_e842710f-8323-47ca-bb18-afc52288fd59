package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 市场行业
 */
@Data
@TableName("blade_profession_market")
public class ProfessionMarket extends BaseEntity {

    private Long id;

    /**
     * 行业编码
     */
    private String marketCode;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 行业客户数量
     */
    private Integer num;

    /**
     * 归属市场ID
     */
    private String joinId;

}
