package org.springblade.common.enums;

public enum KingDeeFormEnum {
    挂账应付单("AP_OtherPayable","blade_other_payables"),
    出库单("STK_MisDelivery","blade_outbound");
    private String formId;
    private String formName;

    public String getFormId() {
        return formId;
    }

    public String getFormName() {
        return formName;
    }

    KingDeeFormEnum(String formId, String formName) {
        this.formId = formId;
        this.formName = formName;
    }
    public static String getformIdByFormName(String formName) {
        for (KingDeeFormEnum c : KingDeeFormEnum.values()) {
            if (c.getFormName().equals(formName)) {
                return c.formId;
            }
        }
        return null;
    }
}
