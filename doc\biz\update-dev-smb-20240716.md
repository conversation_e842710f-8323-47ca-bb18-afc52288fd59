

# 等保绩效 和 等保看板3.0 上线材料



## 等保看板3.0













## 等保绩效

### 新增角色数据

```sql
INSERT INTO `bladex_boot`.`blade_role`(`id`, `tenant_id`, `parent_id`, `role_name`, `sort`, `role_alias`, `is_deleted`) VALUES (1815585244880326657, '000000', 0, '等保绩效入口', 27, '等保绩效入口', 0);
INSERT INTO `bladex_boot`.`blade_role`(`id`, `tenant_id`, `parent_id`, `role_name`, `sort`, `role_alias`, `is_deleted`) VALUES (1815585244880326658, '000000', 0, '等保绩效导出功能', 28, '等保绩效导出功能', 0);

```



### 新增菜单数据

```sql
INSERT INTO `bladex_boot`.`blade_menu`(`id`, `parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1670741032977027075, 1670740112520237058, 'jxdb', '绩效待办', 'jxdb', '/secureServices/jxdb', '/iconImg/jxdb.png', 6, 2, 0, 1, '/secureServices/jxdb.vue', '', 0);
INSERT INTO `bladex_boot`.`blade_menu`(`id`, `parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1768080940196614150, 1670747264987156481, 'jxpd', '绩效评定', 'jxpd', '/secureServices/jxpd', '/iconImg/jxpd.png', 8, 2, 0, 1, '/secureServices/jxpd.vue', '', 0);

```



### 新增表

```sql
CREATE TABLE `blade_equal_protection_flow_copy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_id` bigint DEFAULT NULL COMMENT '业务id',
  `copy_time` datetime DEFAULT NULL COMMENT '抄送时间',
  `to_user_id` bigint DEFAULT NULL COMMENT '接收人userID',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除(0.未删除，1.已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_id` (`business_id`),
  KEY `to_user_id` (`to_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=169 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='等保看板流程抄送';

CREATE TABLE `blade_equal_protection_jxpd` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `djbt` int DEFAULT NULL COMMENT '单据标题 1.绩效单',
  `djbh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据编号,JX+发起单据日期+001（每天编号从001开始叠加）',
  `system_task_id` bigint DEFAULT NULL COMMENT '系统任务数据主键',
  `kd_project_fid` bigint DEFAULT NULL COMMENT '金蝶下推后正式项目id',
  `kd_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '金蝶编码',
  `project_id` bigint DEFAULT NULL COMMENT '项目id',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目名称',
  `archive_details_id` bigint DEFAULT NULL COMMENT '合同归档id',
  `archive_system_id` bigint DEFAULT NULL COMMENT '合同归档系统id',
  `archive_system_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统名称',
  `grade` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '等级 3.二级；4.三级',
  `cpcs` int DEFAULT NULL COMMENT '测评次数（0.首测/1.第一次复测/2.第二次复测/3.第三次复测/4.三次及以上）',
  `xtpsjg` int DEFAULT NULL COMMENT '系统评审结果（1.A等；2.B等；3.C等）',
  `jljs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '激励基数',
  `hte` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同额',
  `hke` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '回款额',
  `hkzt` int DEFAULT '0' COMMENT '回款状态（0.未回款；1.已回款）',
  `process_definition_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程定义主键',
  `process_instance_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程实例主键',
  `flow_start_time` datetime(3) DEFAULT NULL COMMENT '流程发起时间，每次发起人重新提交，都会更新该字段',
  `spsj` datetime(3) DEFAULT NULL COMMENT '审批时间，每次审批会更新该字段',
  `spr` bigint DEFAULT NULL COMMENT '当前节点审批人，每次审批会更新该字段；最终审核通过或者撤回时，为空',
  `bhly` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '驳回理由',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT NULL COMMENT '状态(0.审批中，1.已通过，2.已撤回，3.已驳回)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除(0.未删除，1.已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`),
  KEY `status` (`status`),
  KEY `process_instance_id` (`process_instance_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1816305424220659714 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='绩效评定表';


CREATE TABLE `blade_equal_protection_jxpd_map` (
  `id` bigint NOT NULL COMMENT '主键',
  `jxpdid` bigint DEFAULT NULL COMMENT '绩效评定id',
  `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名对应的account，组别（支撑组、渗透组）时为空',
  `xm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名或者组别（支撑组、渗透组）',
  `bl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '比例，最多输入两位小数',
  `jg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结果',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `jxpdid` (`jxpdid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='绩效评定--项目人员映射';
```



### 新增工作流

```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" xmlns:flowable="http://flowable.org/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:activiti="http://activiti.org/bpmn" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" targetNamespace="http://bpmn.io/schema/bpmn">
  <process id="performanceEvaluation" name="绩效评定" isExecutable="true">
    <startEvent id="start" name="开始">
      <outgoing>Flow_0066g95</outgoing>
    </startEvent>
    <userTask id="echnicalDirectorTask" name="技术部负责人" flowable:assignee="${applyUser}">
      <incoming>userPassFlow</incoming>
      <outgoing>echnicalDirectorPassFlow</outgoing>
      <outgoing>echnicalDirectorNotPassFlow</outgoing>
    </userTask>
    <sequenceFlow id="echnicalDirectorPassFlow" name="同意" sourceRef="echnicalDirectorTask" targetRef="adminStaffTask">
      <conditionExpression xsi:type="tFormalExpression">${pass}</conditionExpression>
    </sequenceFlow>
    <userTask id="financialStaffTask" name="财务会计" flowable:assignee="${applyUser}">
      <incoming>adminStaffPassFlow</incoming>
      <outgoing>financialStaffPassFlow</outgoing>
      <outgoing>financialStaffNotPassFlow</outgoing>
    </userTask>
    <endEvent id="end" name="结束">
      <incoming>financialStaffPassFlow</incoming>
    </endEvent>
    <sequenceFlow id="financialStaffPassFlow" name="同意" sourceRef="financialStaffTask" targetRef="end">
      <conditionExpression xsi:type="tFormalExpression">${pass}</conditionExpression>
    </sequenceFlow>
    <userTask id="adminStaffTask" name="行政" flowable:assignee="${applyUser}">
      <incoming>echnicalDirectorPassFlow</incoming>
      <outgoing>adminStaffPassFlow</outgoing>
      <outgoing>adminStaffNotPassFlow</outgoing>
    </userTask>
    <sequenceFlow id="adminStaffPassFlow" name="同意" sourceRef="adminStaffTask" targetRef="financialStaffTask">
      <conditionExpression xsi:type="tFormalExpression">${pass}</conditionExpression>
    </sequenceFlow>
    <userTask id="userTask" name="用户申请/调整" flowable:assignee="${applyUser}">
      <incoming>echnicalDirectorNotPassFlow</incoming>
      <incoming>Flow_0066g95</incoming>
      <incoming>adminStaffNotPassFlow</incoming>
      <incoming>financialStaffNotPassFlow</incoming>
      <outgoing>userPassFlow</outgoing>
    </userTask>
    <sequenceFlow id="userPassFlow" name="同意" sourceRef="userTask" targetRef="echnicalDirectorTask">
      <conditionExpression xsi:type="tFormalExpression">${pass}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="echnicalDirectorNotPassFlow" name="驳回" sourceRef="echnicalDirectorTask" targetRef="userTask">
      <conditionExpression xsi:type="tFormalExpression">${!pass}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0066g95" sourceRef="start" targetRef="userTask" />
    <sequenceFlow id="adminStaffNotPassFlow" name="驳回" sourceRef="adminStaffTask" targetRef="userTask">
      <conditionExpression xsi:type="tFormalExpression">${!pass}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="financialStaffNotPassFlow" name="驳回" sourceRef="financialStaffTask" targetRef="userTask">
      <conditionExpression xsi:type="tFormalExpression">${!pass}</conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="performanceEvaluation">
      <bpmndi:BPMNEdge id="Flow_07l4qye_di" bpmnElement="financialStaffNotPassFlow">
        <di:waypoint x="1061" y="-47" />
        <di:waypoint x="830" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="935" y="19" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04iyi5s_di" bpmnElement="adminStaffNotPassFlow">
        <di:waypoint x="830" y="-40" />
        <di:waypoint x="830" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="835" y="37" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0066g95_di" bpmnElement="Flow_0066g95">
        <di:waypoint x="830" y="312" />
        <di:waypoint x="830" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03rqvpw_di" bpmnElement="echnicalDirectorNotPassFlow">
        <di:waypoint x="634" y="-41" />
        <di:waypoint x="830" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="722" y="22" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ncx58r_di" bpmnElement="userPassFlow">
        <di:waypoint x="780" y="160" />
        <di:waypoint x="470" y="160" />
        <di:waypoint x="470" y="-80" />
        <di:waypoint x="540" y="-80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="606" y="173" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_099rn0j_di" bpmnElement="adminStaffPassFlow">
        <di:waypoint x="880" y="-80" />
        <di:waypoint x="1060" y="-80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="978" y="-68" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12elmm5_di" bpmnElement="financialStaffPassFlow">
        <di:waypoint x="1160" y="-80" />
        <di:waypoint x="1242" y="-80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1226" y="-100" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bplphe_di" bpmnElement="echnicalDirectorPassFlow">
        <di:waypoint x="640" y="-80" />
        <di:waypoint x="780" y="-80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="698" y="-68" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_startEvent_1" bpmnElement="start">
        <dc:Bounds x="812" y="312" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="819" y="373" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lx2yue_di" bpmnElement="echnicalDirectorTask">
        <dc:Bounds x="540" y="-120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1waqmqw_di" bpmnElement="financialStaffTask">
        <dc:Bounds x="1060" y="-120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_04u2o6x_di" bpmnElement="end">
        <dc:Bounds x="1242" y="-98" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1249" y="-37" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ivfcru_di" bpmnElement="adminStaffTask">
        <dc:Bounds x="780" y="-120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_196pdil_di" bpmnElement="userTask">
        <dc:Bounds x="780" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>

```



### 工作流部署

0.打开前端平台，进入流程管理-模型管理-创建按钮

1.导入上方工作流XML代码，完成工作流创建

2.系统字典“flow"下，新增子项”绩效评定“

3.工作流模型列表，进行部署





### 新增图标权限配置

0.打开前端平台，进入系统管理-用户管理-打开左侧树

1.选择待分配的人，依次进行权限分配【等保绩效入口】、【等保绩效导出功能】





### 保绩效导出模板处理

列表使用 {.fill1}........{.filln}

合计使用{heji1}.........{hejin}

处理完毕后，重命名文件为：【dbjx.xlsx】



### 上传minio等保绩效导出模板

处理后的模板，上传至正式环境minio;

桶名：wangdun

位置：template/upload/20240725/dbjx.xlsx
