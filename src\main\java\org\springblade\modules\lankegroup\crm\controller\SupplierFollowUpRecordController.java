/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.lankegroup.crm.entity.SupplierFollowUpRecord;
import org.springblade.modules.lankegroup.crm.service.ISupplierFollowUpRecordService;
import org.springblade.modules.lankegroup.crm.vo.SupplierFollowUpRecordVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 供应商--跟进记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/supplierFollowUpRecord/supplierfollowuprecord")
@Api(value = "供应商--跟进记录表", tags = "供应商--跟进记录表接口")
public class SupplierFollowUpRecordController extends BladeController {

    private final ISupplierFollowUpRecordService supplierFollowUpRecordService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入supplierFollowUpRecord")
    public R<SupplierFollowUpRecord> detail(SupplierFollowUpRecord supplierFollowUpRecord) {
        SupplierFollowUpRecord detail = supplierFollowUpRecordService.getOne(Condition.getQueryWrapper(supplierFollowUpRecord));
        return R.data(detail);
    }

    /**
     * 分页 供应商--跟进记录表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入supplierFollowUpRecord")
    public R<IPage<SupplierFollowUpRecord>> list(SupplierFollowUpRecord supplierFollowUpRecord, Query query) {
        IPage<SupplierFollowUpRecord> pages = supplierFollowUpRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(supplierFollowUpRecord));
        return R.data(pages);
    }

    /**
     * 自定义分页 供应商--跟进记录表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入supplierFollowUpRecord")
    public R<IPage<SupplierFollowUpRecordVO>> page(SupplierFollowUpRecordVO supplierFollowUpRecord, Query query) {
        if (supplierFollowUpRecord.getCustomerId() == null) {
            return R.fail("请选择供应商");
        }
        IPage<SupplierFollowUpRecordVO> pages = supplierFollowUpRecordService.selectSupplierFollowUpRecordPage(Condition.getPage(query), supplierFollowUpRecord);
        return R.data(pages);
    }

    /**
     * 新增 供应商--跟进记录表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入supplierFollowUpRecord")
    public R save(@Valid @RequestBody SupplierFollowUpRecord supplierFollowUpRecord) {
        if (supplierFollowUpRecord.getCustomerId() == null) {
            return R.fail("请添写供应商id");
        }
        if (StringUtil.isEmpty(supplierFollowUpRecord.getFollowUpContent())) {
            return R.fail("请添写跟进内容");
        }
        return R.status(supplierFollowUpRecordService.save(supplierFollowUpRecord));
    }

    /**
     * 修改 供应商--跟进记录表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入supplierFollowUpRecord")
    public R update(@Valid @RequestBody SupplierFollowUpRecord supplierFollowUpRecord) {
        return R.status(supplierFollowUpRecordService.updateById(supplierFollowUpRecord));
    }

    /**
     * 新增或修改 供应商--跟进记录表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入supplierFollowUpRecord")
    public R submit(@Valid @RequestBody SupplierFollowUpRecord supplierFollowUpRecord) {
        return R.status(supplierFollowUpRecordService.saveOrUpdate(supplierFollowUpRecord));
    }


    /**
     * 删除 供应商--跟进记录表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(supplierFollowUpRecordService.deleteLogic(Func.toLongList(ids)));
    }


}
