/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.NotifyRecord;
import org.springblade.modules.lankegroup.crm.vo.NotifyRecordVO;
import org.springblade.modules.lankegroup.crm.service.INotifyRecordService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户联系人 消息通知记录表 控制器
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/record/notifyrecord")
@Api(value = "客户联系人 消息通知记录表", tags = "客户联系人 消息通知记录表接口")
public class NotifyRecordController extends BladeController {

	private final INotifyRecordService notifyRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入notifyRecord")
	public R<NotifyRecord> detail(NotifyRecord notifyRecord) {
		NotifyRecord detail = notifyRecordService.getOne(Condition.getQueryWrapper(notifyRecord));
		return R.data(detail);
	}

	/**
	 * 分页 客户联系人 消息通知记录表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入notifyRecord")
	public R<IPage<NotifyRecord>> list(NotifyRecord notifyRecord, Query query) {
		IPage<NotifyRecord> pages = notifyRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(notifyRecord));
		return R.data(pages);
	}

	/**
	 * 自定义分页 客户联系人 消息通知记录表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入notifyRecord")
	public R<IPage<NotifyRecordVO>> page(NotifyRecordVO notifyRecord, Query query) {
		IPage<NotifyRecordVO> pages = notifyRecordService.selectNotifyRecordPage(Condition.getPage(query), notifyRecord);
		return R.data(pages);
	}

	/**
	 * 新增 客户联系人 消息通知记录表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入notifyRecord")
	public R save(@Valid @RequestBody NotifyRecord notifyRecord) {
		return R.status(notifyRecordService.save(notifyRecord));
	}

	/**
	 * 修改 客户联系人 消息通知记录表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入notifyRecord")
	public R update(@Valid @RequestBody NotifyRecord notifyRecord) {
		return R.status(notifyRecordService.updateById(notifyRecord));
	}

	/**
	 * 新增或修改 客户联系人 消息通知记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入notifyRecord")
	public R submit(@Valid @RequestBody NotifyRecord notifyRecord) {
		return R.status(notifyRecordService.saveOrUpdate(notifyRecord));
	}

	
	/**
	 * 删除 客户联系人 消息通知记录表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(notifyRecordService.deleteLogic(Func.toLongList(ids)));
	}

	
}
