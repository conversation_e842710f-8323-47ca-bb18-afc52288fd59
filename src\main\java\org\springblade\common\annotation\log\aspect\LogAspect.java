package org.springblade.common.annotation.log.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springblade.common.annotation.log.ControlsLog;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class LogAspect {

    private final ControlsLogService controlsLogService;

    @Pointcut("@annotation(org.springblade.common.annotation.log.ControlsLog)")
    private void LogAspect() {}

    @AfterReturning("LogAspect()")
    public void around(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        ControlsLog annotation = method.getAnnotation(ControlsLog.class);
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(args[0]));
        if(StringUtils.isNotBlank(annotation.byUserIdDesc())){
            jsonObject.put("byUserId",jsonObject.getLong(annotation.byUserIdDesc()));
        }
        jsonObject.put("userId",AuthUtil.getUserId());
        jsonObject.put("pid",jsonObject.getLong(annotation.pidDesc()));
        controlsLogService.saveLog(new LogModel(jsonObject, annotation));
    }
}