/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import co.elastic.clients.elasticsearch.nodes.Ingest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.MessageTiming;
import org.springblade.modules.lankegroup.crm.vo.MessageTimingVO;
import org.springblade.modules.lankegroup.crm.service.IMessageTimingService;
import org.springblade.core.boot.ctrl.BladeController;

import java.io.IOException;
import java.net.URISyntaxException;

/**
 * 收藏联系人--定时消息提醒 控制器
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/timing/messagetiming")
@Api(value = "收藏联系人--定时消息提醒", tags = "收藏联系人--定时消息提醒接口")
public class MessageTimingController extends BladeController {

	private final IMessageTimingService messageTimingService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入messageTiming")
	public R<MessageTiming> detail(MessageTiming messageTiming) {
		MessageTiming detail = messageTimingService.getOne(Condition.getQueryWrapper(messageTiming));
		return R.data(detail);
	}

	/**
	 * 分页 收藏联系人--定时消息提醒
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入messageTiming")
	public R<IPage<MessageTiming>> list(MessageTiming messageTiming, Query query) {
		IPage<MessageTiming> pages = messageTimingService.page(Condition.getPage(query), Condition.getQueryWrapper(messageTiming));
		return R.data(pages);
	}

	/**
	 * 自定义分页 收藏联系人--定时消息提醒
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入messageTiming")
	public R<IPage<MessageTimingVO>> page(MessageTimingVO messageTiming, Query query) {
		IPage<MessageTimingVO> pages = messageTimingService.selectMessageTimingPage(Condition.getPage(query), messageTiming);
		return R.data(pages);
	}

	/**
	 * 新增 收藏联系人--定时消息提醒
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入messageTiming")
	public R save(@Valid @RequestBody MessageTimingVO messageTimingVO) throws IOException, URISyntaxException {
		return messageTimingService.saveMessage(messageTimingVO);
	}

	/**
	 * 修改 收藏联系人--定时消息提醒
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入messageTiming")
	public R update(@Valid @RequestBody MessageTiming messageTiming) {
		return R.status(messageTimingService.updateById(messageTiming));
	}

	/**
	 * 新增或修改 收藏联系人--定时消息提醒
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入messageTiming")
	public R submit(@Valid @RequestBody MessageTiming messageTiming) {
		return R.status(messageTimingService.saveOrUpdate(messageTiming));
	}

	
	/**
	 * 删除 收藏联系人--定时消息提醒
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@RequestParam String ids) {
		return messageTimingService.delectById(ids);
	}

	/**
	 * 关闭 开启 收藏联系人--定时消息提醒
	 */
	@PostMapping("/start")
	public R start(@RequestParam Long id, Integer status) {
		return messageTimingService.start(id,status);
	}


	
}
