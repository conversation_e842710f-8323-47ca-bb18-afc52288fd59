/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FlowModeEnum {

	/**
	 * 通用流程
	 */
	COMMON("common", 1),

	/**
	 * 定制流程
	 */
	CUSTOM("custom", 2),
	;

	final String name;
	final int mode;

}
