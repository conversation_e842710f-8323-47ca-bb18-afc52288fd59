/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户关联的变更记录实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@TableName("blade_customer_connect_log")
@EqualsAndHashCode(callSuper = true)
public class CustomerConnectLog extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 客户id
	*/
		private Long customerId;
	/**
	* 变更表
	*/
		private String changeTable;
	/**
	 * 关连表单id
	 */
	private  Long contactId;
	/**
	* 大概操作（客户、客户联系人、拜访记录、项目、合同）
	*/
		private String aboutDescription;
	/**
	* 描述具体操作
	*/
		private String description;
	/**
	 * 操作人
	 */
	private Long chargeUser;

	/**
	 *	客户联系人id
	 */
	private Long customerContactId;

}
