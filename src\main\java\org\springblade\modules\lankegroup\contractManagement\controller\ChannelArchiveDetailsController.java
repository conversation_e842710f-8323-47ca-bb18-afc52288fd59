package org.springblade.modules.lankegroup.contractManagement.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.service.IChannelArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 渠道合同归档详情 控制器
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/archive/channelArchiveDetails")
@Api(value = "归档详情", tags = "归档详情接口")
public class ChannelArchiveDetailsController extends BladeController {
    private final IChannelArchiveDetailsService channelArchiveDetailsService;
    private final FlowEngineService flowEngineService;
    private final RedisTemplate redisTemplate;
    /**
     * 详情
     */
    @ApiLog("详情 归档详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入archiveDetails")
    public R<ArchiveDetailsVO> detail(Long contractId) {
        Map map = new HashMap();
        map.put("contractId", contractId);
        ArchiveDetailsVO archiveDetailsVO = channelArchiveDetailsService.selectArchiveDetails(map);
        return R.data(archiveDetailsVO);
    }

    /**
     * 新增 归档详情
     */
    @ApiLog("新增 归档详情")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入archiveDetails")
    public R save(@Valid @RequestBody ArchiveDetailsVO archiveDetailsVO) {
        if ( archiveDetailsVO.getName() == null || archiveDetailsVO.getName().length() > 50  || archiveDetailsVO.getName().isEmpty()) {
            return R.fail("合同名称为必填或名称过长");
        }
        if (archiveDetailsVO.getContractSigningDate() == null
                || archiveDetailsVO.getContractSigningDate().isEmpty()
                || archiveDetailsVO.getBid() == null) {
            return R.fail("请补全必填项");
        }
        /*Contract contract = channelContractService.getById(archiveDetailsVO.getContractId());
        if (contract.getSigningMode() != null
                && !contract.getSigningMode().isEmpty()
                && contract.getSigningMode().equals("direct")) {
            if (archiveDetailsVO.getContentChannelMerchants() == null || archiveDetailsVO.getContentChannelMerchants().isEmpty()) {
                return R.fail("渠道商分成额为必填");
            }
            if (archiveDetailsVO.getChannelSupplierName() == null || archiveDetailsVO.getChannelSupplierName().isEmpty()) {
                return R.fail("渠道商名称为必填");
            }
            if (archiveDetailsVO.getPerformanceContractAmount() == null) {
                return R.fail("业绩合同金额为必填");
            }
            Integer amountTotal = Integer.valueOf(archiveDetailsVO.getContentChannelMerchants()) + Integer.valueOf(archiveDetailsVO.getPerformanceContractAmount());
            Integer contractAmount = Integer.valueOf(contract.getContractAmount());
            if(contractAmount.intValue() != amountTotal.intValue()){
                return R.fail("渠道商分成额加上业绩合同金额需等于合同额");
            }
        }*/

        if (archiveDetailsVO.getNumber() == null || archiveDetailsVO.getNumber() <= 0) {
            return R.fail("留存份数为必填或不能小于0");
        }
        // 合同签订日期
        if (archiveDetailsVO.getContractSigningDate() != null){
            if(archiveDetailsVO.getContractSigningDate().isEmpty()){
                archiveDetailsVO.setContractSigningDate(null);
            }
        }
        // 服务起始日期
        if (archiveDetailsVO.getServiceStartDate() != null){
            if(archiveDetailsVO.getServiceStartDate().isEmpty()){
                archiveDetailsVO.setServiceStartDate(null);
            }
        }
        // 服务终止日期
        if (archiveDetailsVO.getServiceEndDate() != null){
            if(archiveDetailsVO.getServiceEndDate().isEmpty()){
                archiveDetailsVO.setServiceEndDate(null);
            }
        }
        // 开标日期
        if (archiveDetailsVO.getBidOpeningDate() != null){
            if(archiveDetailsVO.getBidOpeningDate().isEmpty()){
                archiveDetailsVO.setBidOpeningDate(null);
            }
        }
        // 中标日期
        if (archiveDetailsVO.getBidWinningDate() != null){
            if(archiveDetailsVO.getBidWinningDate().isEmpty()){
                archiveDetailsVO.setBidWinningDate(null);
            }
        }
        // 领取中标通知书日期
        if (archiveDetailsVO.getNoticeDate()!=null){
            if(archiveDetailsVO.getNoticeDate().isEmpty()){
                archiveDetailsVO.setNoticeDate(null);
            }
        }
        // 一次性预计回款 日期
        if (archiveDetailsVO.getOnceCollectionDate()!=null){
            if(archiveDetailsVO.getOnceCollectionDate().isEmpty()){
                archiveDetailsVO.setOnceCollectionDate(null);
            }
        }
        // 渠道商id
        if (archiveDetailsVO.getChannelSupplierId()!=null){
            if(archiveDetailsVO.getChannelSupplierId().isEmpty()){
                archiveDetailsVO.setChannelSupplierId(null);
            }
        }
//        String processId = flowEngineService.getProcessId("渠道合同归档审批", "channelFiling");
        // TODO 刘源兴工作流
//        String processId = flowEngineService.getProcessId("归档审批", "filing");
        String processId = null;
        archiveDetailsVO.setProcessDefinitionId(processId);
        List<ContractChange> contractChanges = channelArchiveDetailsService.contractChange(archiveDetailsVO.getContractId());
        if (contractChanges.size() != 0){
            for (ContractChange contractChange : contractChanges) {
              if (contractChange.getStatus().toString().equals("0")){
                  return R.fail(500,"合同处于变更中无法进行归档");
              }
            }
        }
        Map map = channelArchiveDetailsService.saveArchive(archiveDetailsVO);
        if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().equals(""))) {
            return R.data(map);
//            map = channelArchiveDetailsService.approvalInPersonArchiveDetails(map);
//            if (map.get("status").toString().equals("true")) {
//                return R.data(map);
//            } else {
//                return R.fail(500, "流程无法提交，请联系管理员");
//            }
        } else {
            return R.fail(500, "流程保存失败");
        }
    }

    /**
     * 修改 归档详情
     */
    @ApiLog("修改 归档详情")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入archiveDetails")
    public R update(@Valid @RequestBody ArchiveDetailsVO archiveDetailsVO) {
        if ( archiveDetailsVO.getName() == null || archiveDetailsVO.getName().length() > 50  || archiveDetailsVO.getName().isEmpty()) {
            return R.fail("合同名称为必填或名称过长");
        }
        if (archiveDetailsVO.getContractSigningDate() == null
                || archiveDetailsVO.getContractSigningDate().isEmpty()
                || archiveDetailsVO.getBid() == null) {
            return R.fail("请补全必填项");
        }
        /*Contract contract = channelContractService.getById(archiveDetailsVO.getContractId());
        if (contract.getSigningMode() != null
                && !contract.getSigningMode().isEmpty()
                && contract.getSigningMode().equals("direct")) {
            if (archiveDetailsVO.getContentChannelMerchants() == null || archiveDetailsVO.getContentChannelMerchants().isEmpty()) {
                return R.fail("渠道商分成额为必填");
            }
            if (archiveDetailsVO.getChannelSupplierName() == null || archiveDetailsVO.getChannelSupplierName().isEmpty()) {
                return R.fail("渠道商名称为必填");
            }
            if (archiveDetailsVO.getPerformanceContractAmount() == null) {
                return R.fail("业绩合同金额为必填");
            }
            Integer amountTotal = Integer.valueOf(archiveDetailsVO.getContentChannelMerchants()) + Integer.valueOf(archiveDetailsVO.getPerformanceContractAmount());
            if(!(Integer.valueOf(contract.getContractAmount()) == amountTotal)){
                return R.fail("渠道商分成额加上业绩合同金额需等于合同额");
            }

        }*/
        // 合同签订日期
        if (archiveDetailsVO.getContractSigningDate()!=null){
            if(archiveDetailsVO.getContractSigningDate().isEmpty()){
                archiveDetailsVO.setContractSigningDate(null);
            }
        }
        //      服务起始日期
        if (archiveDetailsVO.getServiceStartDate()!=null){
            if(archiveDetailsVO.getServiceStartDate().isEmpty()){
                archiveDetailsVO.setServiceStartDate(null);
            }
        }
        //     服务终止日期
        if (archiveDetailsVO.getServiceEndDate()!=null){
            if(archiveDetailsVO.getServiceEndDate().isEmpty()){
                archiveDetailsVO.setServiceEndDate(null);
            }
        }
        //        开标日期
        if (archiveDetailsVO.getBidOpeningDate()!=null){
            if(archiveDetailsVO.getBidOpeningDate().isEmpty()){
                archiveDetailsVO.setBidOpeningDate(null);
            }
        }
        //        中标日期
        if (archiveDetailsVO.getBidWinningDate()!=null){
            if(archiveDetailsVO.getBidWinningDate().isEmpty()){
                archiveDetailsVO.setBidWinningDate(null);
            }
        }
        //        领取中标通知书日期
        if (archiveDetailsVO.getNoticeDate()!=null){
            if(archiveDetailsVO.getNoticeDate().isEmpty()){
                archiveDetailsVO.setNoticeDate(null);
            }
        }
        //        渠道商id
        if (archiveDetailsVO.getChannelSupplierId()!=null){
            if(archiveDetailsVO.getChannelSupplierId().isEmpty()){
                archiveDetailsVO.setChannelSupplierId(null);
            }
        }
        //        一次性预计回款 日期
        if (archiveDetailsVO.getOnceCollectionDate()!=null){
            if(archiveDetailsVO.getOnceCollectionDate().isEmpty()){
                archiveDetailsVO.setOnceCollectionDate(null);
            }
        }
        boolean b = channelArchiveDetailsService.updateArchive(archiveDetailsVO);
        return R.status(b);
    }

    @ApiLog("完成任务 流程审批接口")
    @PostMapping("/completeTaskee")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "完成任务", notes = "传入流程信息")
    public R completeTaskee(@ApiParam("任务信息") @RequestBody BladeFlow flow) {
        //如果是 2 的话就是 走流程进行写入的
        Long userId = AuthUtil.getUserId();
        String vlaue = (userId.toString() + flow.getProcessInstanceId());
        Object key = redisTemplate.opsForValue().get(vlaue);
        if (key == null){
            try {
                redisTemplate.opsForValue().set(vlaue,vlaue,3, TimeUnit.HOURS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }else {
            return R.status(false);
        }
        return R.data(key);
    }
        /**
         * 初始化合同页面
         *
         * @return 返回初始化的流程的人员审批结构
         */
    @ApiLog("初始化合同页面")
    @GetMapping("/initContractArchiveDetails")
    public R initContractArchiveDetails() {
        return R.data(channelArchiveDetailsService.initContractArchiveDetails());
    }


    /**
     * Approval process
     *
     * @return 返回流程的人员审批结构
     */
    @ApiLog("返回流程的人员审批结构")
    @GetMapping("/approvalProcess")
    public R approvalProcess(String processInstanceId) {
        return R.data(channelArchiveDetailsService.approvalProcess(processInstanceId));
    }

}
