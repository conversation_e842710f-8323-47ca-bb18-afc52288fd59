package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionMarket;
import org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionMarketMapper;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionMarketService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@AllArgsConstructor
public class ProfessionMarketServiceImpl extends BaseServiceImpl<ProfessionMarketMapper, ProfessionMarket> implements ProfessionMarketService {

    @Override
    public void saveProfessionMarket(List<ProfessionMarket> profession) {
        saveBatch(profession);
    }

    @Override
    public void deleteProfessionMarket(String joinId) {
        QueryWrapper<ProfessionMarket> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("join_id",joinId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<Long> getProfessionMarketList(List<Long> ids) {
        return baseMapper.getProfessionMarketList(ids);
    }

    @Override
    public void deleteProfessionMarkets(List<Long> ids) {
        baseMapper.deleteProfessionMarkets(ids);
    }
}
