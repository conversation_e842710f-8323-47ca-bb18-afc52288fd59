package org.springblade.modules.lankegroup.contractManagement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.enums.DocumentType;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.service.impl.ContractServiceImpl;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.outbound.entity.Outbound;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/23 17:05
 */

@RestController
@AllArgsConstructor
@RequestMapping("/contract/con")
@Api(value = "任务转办处理", tags = "任务转办处理")
public class ContracTransferTaskController {
    private final TaskService taskService;
    private final IContractService contractService;
    private final IUserService userService;
    private final SendBillMsgUtil sendBillMsgUtil;


    /**
     * 合同项目分组负责人审批节点修改，进行任务转办
     *
     * @param taskId 任务id
     * @param userId 任务接收人的人员id（新的人的id）
     * @return
     */
    @GetMapping("/transfer_task/{taskId}/{userId}")
    public String transferTaskToUser(@PathVariable(value = "taskId") String taskId, @PathVariable(value = "userId") String userId) {
        // 验证任务存在
        if (taskService.createTaskQuery().taskId(taskId).count() == 0) {
            return "任务不存在";
        }

        // 连同任务附带的上下文信息一起转办给userId指定的用户
        taskService.setAssignee(taskId, userId);
        //无需转办原因 记录转办原因
//        taskService.addComment(taskId, null, "转办原因: ");

        // 进一步的业务逻辑处理，如更新任务审批人记录、发送通知等
        // TODO: 2024/7/23 更新合同表办理人
        // 通过TaskService获取当前任务，通过任务去查找流程实例id
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        // 通过任务对象获取流程实例ID
        String processInstanceId = task.getProcessInstanceId();
//        更新合同表当前审批人字段
        contractService.update(Wrappers.<Contract>update().lambda()
                .set(Contract::getTaskUser, userId)
                .eq(Contract::getProcessInstanceId,processInstanceId));

//        根据合同流程实例id获取该实例关联的单据详情
        Map map=new HashMap();
        map.put("processInstanceId",processInstanceId);
        ContractVO contractVO = contractService.selectDetail(map);
//        消息通知新的当前审批人
        User user=userService.getById(userId);
        BillMsgParams billMsgParams = new BillMsgParams();
        billMsgParams.setContractId(String.valueOf(contractVO.getId()));
        billMsgParams.setBillNo(contractVO.getName());
        billMsgParams.setType(DocumentType.合同.name());
        billMsgParams.setContractStatus(String.valueOf(contractVO.getStatus()));
        billMsgParams.setContractType(String.valueOf(contractVO.getContractType()));
        billMsgParams.setFlag(1);
        billMsgParams.setUserId(userId);
        billMsgParams.setUserName(user.getName());
        billMsgParams.setName(contractVO.getCreateUserName());
        billMsgParams.setStatus(ApprovedEnum.APPROVING.getIndex());
        sendBillMsgUtil.msg(billMsgParams);
        return "任务已成功转办给用户 " + user.getName();
    }
}
