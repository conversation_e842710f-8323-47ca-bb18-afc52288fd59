package org.springblade.modules.lankegroup.clientrelation.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;

import java.util.List;

public interface ProfessionJoinService extends BaseService<ProfessionJoin> {


    /**
     * 新增市场与区域行业关系
     * @param profession
     * @return
     */
    ProfessionJoin saveProfession(ProfessionJoin profession);

    /**
     * 根据市场ID获取关联关系ID
     * @param id
     * @return
     */
    List<Long> getJoinList(Long id);

    List<AreaAndMarket> getJoinListByObject(Long id,List<Long> prohibitList);

    /**
     * 删除关联信息
     * @param id
     */
    void deleteProfessionJoin(Long id);
}
