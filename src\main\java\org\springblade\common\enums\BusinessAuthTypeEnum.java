package org.springblade.common.enums;

/**
 * 业务权限枚举
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
public enum BusinessAuthTypeEnum {

    目标人及其所有上级(1),
    目标人及其所有下级(2),
    目标人及其直属上级(3),
    目标人及其直属下级(4),
    财务(5),
    <PERSON>层(6),
    部门领导(7),
    普通员工(8),
    ;

    private Integer dictKey;

    public Integer getFormId() {
        return dictKey;
    }

    BusinessAuthTypeEnum(Integer dictKey) {
        this.dictKey = dictKey;
    }
}
