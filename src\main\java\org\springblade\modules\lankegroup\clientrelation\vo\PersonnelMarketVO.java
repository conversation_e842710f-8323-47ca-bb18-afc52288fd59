package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;

import java.util.List;

@Data
public class PersonnelMarketVO {

    private String proId;

    /**
     * 区县编号
     */
    private String areaId;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 行业ID
     */
    private String marketId;

    /**
     * 行业名称
     */
    private String marketName;
}
