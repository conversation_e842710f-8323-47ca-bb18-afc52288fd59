# dev-smb-0422分支更新内容

## mysql

### blade_archive_system_name_map合同归档 --系统名称映射【新增字段】

```mysql
ALTER TABLE `blade_archive_system_name_map` 
ADD COLUMN `inventory_id` bigint DEFAULT NULL COMMENT '合同清单信息ID' AFTER `details_id`;
```


### blade_dict_biz业务字典表【新增数据】

```mysql
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839387, '000000', 0, 'equal_protection_system_stage', '-1', '等保阶段系统状态类型', 57, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839388, '000000', 1730136175165839387, 'equal_protection_system_stage', '1', '定级备案', 1, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839389, '000000', 1730136175165839387, 'equal_protection_system_stage', '2', '启动会议', 2, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839390, '000000', 1730136175165839387, 'equal_protection_system_stage', '3', '测评整改', 3, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839391, '000000', 1730136175165839387, 'equal_protection_system_stage', '4', '报告编制', 4, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839392, '000000', 1730136175165839387, 'equal_protection_system_stage', '5', '结项会议', 5, NULL, 0, 0);
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839393, '000000', 1730136175165839387, 'equal_protection_system_stage', '6', '系统完成', 6, NULL, 0, 0);

```

### 等保看板-项目任务表
```sql
CREATE TABLE `blade_equal_protection_project_task` (
  `id` bigint NOT NULL COMMENT 'ID',
  `project_id` bigint DEFAULT NULL COMMENT '项目id',
  `kd_project_fid` bigint DEFAULT NULL COMMENT '金蝶下推后正式项目id',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目名称',
  `archive_details_id` bigint DEFAULT NULL COMMENT '合同归档id',
  `secondary_service_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '等保二级系统个数',
  `allocated_secondary_service_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '已分配等保二级系统个数',
  `tertiary_service_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '等保三级系统个数',
  `allocated_tertiary_service_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '已分配等保三级系统个数',
  `project_task_order` int DEFAULT NULL COMMENT '项目任务次数，第n次，n>=1',
  `allocation_status` int DEFAULT NULL COMMENT '分配状态 1未分配2分配中3正常流程分配完成4手动跳过分配完成',
  `allocation_time` datetime DEFAULT NULL COMMENT '分配时间',
  `expect_money` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预期金额总数',
  `allocated_money` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '已分配金额总数',
  `expect_allocation_time` datetime DEFAULT NULL COMMENT '预计分配时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态(0.无效，1.正常)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='等保看板-项目任务表';
```

### 等保看板-系统任务表
```sql
CREATE TABLE `blade_equal_protection_system_task` (
  `id` bigint NOT NULL COMMENT 'ID',
  `project_task_id` bigint DEFAULT NULL COMMENT '项目任务表id',
  `project_id` bigint DEFAULT NULL COMMENT '项目id',
  `kd_project_fid` bigint DEFAULT NULL COMMENT '金蝶下推后正式项目id',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目名称',
  `archive_details_id` bigint DEFAULT NULL COMMENT '合同归档id',
  `archive_system_id` bigint DEFAULT NULL COMMENT '合同归档系统id',
  `system_task_order` int DEFAULT NULL COMMENT '系统次数，第n次，n>=1',
  `service_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '等保单价',
  `service_grade` int DEFAULT NULL COMMENT '等保级别。2是二级。3是三级。',
  `service_order` int DEFAULT NULL COMMENT '等保次数',
  `expected_end_time` date DEFAULT NULL COMMENT '预期结项时间',
  `login_status` int DEFAULT NULL COMMENT '客户是否登录小程序状态（0.未登录/1.已登录）',
  `stage_status` int DEFAULT NULL COMMENT '阶段状态（1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议/6.系统完成）',
  `closed_status` int DEFAULT NULL COMMENT '关闭状态（0未关闭/1成功关闭/2失败终止）',
  `termination_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '终止原因',
  `process_document_status` int DEFAULT NULL COMMENT '流程文档完成状态 0-未完成，1-已完成',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态(0.无效，1.正常)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='等保看板-系统任务表';
```

### 系统任务-客户表
```sql
CREATE TABLE `blade_equal_protection_system_task_customer` (
  `id` bigint NOT NULL COMMENT 'ID',
  `system_task_id` bigint DEFAULT NULL COMMENT '系统任务id',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `customer_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户电话',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态(0.无效，1.正常)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统任务-客户表';
```

### 系统任务-阶段表
```sql
CREATE TABLE `blade_equal_protection_system_task_stage` (
  `id` bigint NOT NULL COMMENT '主键',
  `system_task_id` bigint DEFAULT NULL COMMENT '系统任务id',
  `stage_status` int DEFAULT NULL COMMENT '阶段 （1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议/6.系统完成）',
  `stage_start_time` datetime DEFAULT NULL COMMENT '阶段开始时间',
  `stage_end_time` datetime DEFAULT NULL COMMENT '阶段结束时间',
  `expected_end_time` datetime DEFAULT NULL COMMENT '预期结项时间',
  `remark` json DEFAULT NULL COMMENT '备注json  [{i=用户id，t=时间, c=内容}]',
  `group_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '阶段负责人所在小组编码',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `equal_protection_id` (`system_task_id`) USING BTREE,
  KEY `group_code` (`group_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统任务-阶段表';
```

### 系统任务-阶段文件表
```sql
CREATE TABLE `blade_equal_protection_system_task_stage_file` (
  `id` bigint NOT NULL COMMENT '主键',
  `system_task_id` bigint DEFAULT NULL COMMENT '系统任务id',
  `stage_status` int DEFAULT NULL COMMENT '阶段 （1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议/6.系统完成）',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件类型',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件地址',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名称',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `system_task_id` (`system_task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统任务-阶段文件表';
```

### 系统任务-阶段成员表
```sql
CREATE TABLE `blade_equal_protection_system_task_stage_member` (
  `id` bigint NOT NULL COMMENT '主键',
  `system_task_id` bigint DEFAULT NULL COMMENT '系统任务id',
  `stage_status` int DEFAULT NULL COMMENT '阶段 （1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议）',
  `user_id` bigint DEFAULT NULL COMMENT '成员ID',
  `admin` int DEFAULT '0' COMMENT '是否是当前阶段负责人（0否，1是）',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `equal_protection_id` (`system_task_id`) USING BTREE,
  KEY `stage_status` (`stage_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统任务-阶段成员表';
```
