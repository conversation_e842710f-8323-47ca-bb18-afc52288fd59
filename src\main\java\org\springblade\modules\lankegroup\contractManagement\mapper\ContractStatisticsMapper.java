package org.springblade.modules.lankegroup.contractManagement.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractStatisticsDTO;
import org.springblade.modules.lankegroup.contractManagement.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ContractStatisticsMapper extends BaseMapper<ContractStatisticsVO> {

    /**
     * 状态统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsGroupVO> getContractStatus(@Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 情况统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsGroupVO> getContractStatistics(@Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 人员统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsVO> getContractStatisticsByUser(@Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 合同类型统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsProjectTypeVO> getContractStatisticsType(@Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 履行情况列表
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsListVO> getContractStatisticsList(IPage page, @Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 状态统计列表
     *
     * @param contractStatisticsDTO
     * @return
     */
    List<ContractStatisticsListVO> getContractStatisticsStatusList(IPage page, @Param("query") ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 合同详情
     *
     * @param id
     * @return
     */
    ContractStatisticsDetailVO getContractStatisticsDetail(Long id);

    /**
     * 合同归档详情
     *
     * @param id
     * @return
     */
    ContractStatisticsArchiveDetailVO getContractStatisticsArchiveDetail(Long id);

    /**
     * 查询超归档期限合同
     * @param contractStatisticsDTO
     * @return
     */
    Integer getContractHaveBeenFiled(@Param("query") ContractStatisticsDTO contractStatisticsDTO);
}
