/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractMainDTO;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractMainHeadVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractMainVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractReagentQuotationMaterialVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 合同主表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface IContractMainService extends BaseService<ContractMain> {

    /**
     * 跟进记录合同下拉框 自定义分页
     */
    IPage<ContractMainVO> selectContractMainPageByCustomerVisit(IPage<ContractMainVO> page, ContractMainDTO dto);

    /**
     * 自定义分页
     */
    IPage<ContractMainVO> selectContractMainPage(IPage<ContractMainVO> page, ContractMainDTO dto);

    /**
     * 自定义分页 头部统计
     */
    List<ContractMainHeadVO> pageHeadStatistics(IPage<ContractMainVO> page, ContractMainDTO dto);


    R<Kv> saveByDraft(@Valid ContractMainDTO dto);

    R<Kv> saveByNext(@Valid ContractMainDTO dto);

    boolean initiateAnApproval(@Valid ContractMainDTO dto);

//    boolean removeModel(Long id);

    ContractMainVO detailModel(ContractMain contractMain);

    void download(String url, String name, HttpServletResponse response);
}
