package org.springblade.common.controller.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UtilStoreReqVO {

    @ApiModelProperty(value = "原json", required = true)
    @NotEmpty(message = "原json数据不能为空")
    private String jsonStr;


    @ApiModelProperty(value = "口令", required = true)
    @NotEmpty(message = "口令不能为空")
    private String pwd;

    @ApiModelProperty(value = "有效期（天）", required = true)
    @NotNull(message = "有效期不能为空")
    private Integer expireTime;

}
