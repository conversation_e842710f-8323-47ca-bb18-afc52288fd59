package org.springblade.common.config;

/**
 * 钉钉开放接口网关常量
 */
public class UrlConstant {
    private static final String HOST = "https://oapi.dingtalk.com";

    /**
     * 获取access_token url
     */
    public static final String URL_GET_TOKEN = HOST + "/gettoken";

    /**
     * 获取jsapi_ticket url
     */
    public static final String URL_GET_JSTICKET = HOST + "/get_jsapi_ticket";

    /**
     * 通过免登授权码获取用户信息 url
     */
    public static final String URL_GET_USER_INFO = HOST + "/user/getuserinfo";

    /**
     * 根据用户id获取用户详情 url
     */
    public static final String URL_USER_GET = HOST + "/user/get";

    /**
     * 获取部门列表 url
     */
    public static final String URL_DEPARTMENT_LIST = HOST + "/department/list";

    /**
     * 获取部门用户 url
     */
    public static final String URL_USER_SIMPLELIST = HOST + "/user/simplelist";

    public static final String URL_NOTIFICATION = HOST + "/topapi/message/corpconversation/asyncsend_v2";

    /**
     * 钉钉消息通知上传文件
     */
    public static final String URL_UPLOAD_FILE = HOST + "/media/upload";


    /**
     * 获取部门列表
     */
    public static final String URL_DEPARTMENT = HOST + "/topapi/v2/department/listsub";

    /**
     * 获取用户列表
     */
    public static final String URL_USERLIST = HOST + "/topapi/user/listid";

    /**
     * 根据用户id获取用户详情
     */
    public static final String URL_USERDETAIL = HOST + "/topapi/v2/user/get";
    /**
     * 根据部门id获取部门详情
     */
    public static final String URL_DEPTDETAIL = HOST + "/topapi/v2/department/get";

}
