package org.springblade.modules.lankegroup.contractManagement.entity.kingdee;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.*;

@Data
public class PurConctract {
    //实体主键
    @JSONField(name = "FID", ordinal = 1)
    public String FID;
    //单据编号
    @JSONField(name = "FBillNo", ordinal = 2)
    public String FBillNo;
    @JSONField(name = "FBillTypeID", ordinal = 3)
    public Map FBillTypeID = new HashMap();
    //合同日期
    @JSONField(name = "FDate", ordinal = 3)
    public String FDate;
    //    起始日期
    @JSONField(name = "FBeginDate", ordinal = 4)
    public String FBeginDate;
    //截至日期
    @JSONField(name = "FEndDate", ordinal = 5)
    public String FEndDate;
    //项目档案
    @JSONField(name = "F_XM", ordinal = 6)
    public Map F_XM = new HashMap();
    //供应商
    @JSONField(name = "FSupplierId", ordinal = 7)
    public Map FSupplierId = new HashMap();
    //合同名称
    @JSONField(name = "FCONTRACTNAME", ordinal = 8)
    public String FCONTRACTNAME;
    //采购组织
    @JSONField(name = "FPurchaseOrgId", ordinal = 9)
    public Map FPurchaseOrgId = new HashMap();
    //    实际业务组织
    @JSONField(name = "F_SJYWZZ", ordinal = 10)
    public Map F_SJYWZZ = new HashMap();
    //采购员
    @JSONField(name = "FPurchaserId", ordinal = 11)
    public Map FPurchaserId = new HashMap();
//    录入明细
    @JSONField(name = "FISINPUTDETAIL", ordinal = 11)
    public Boolean FISINPUTDETAIL = true;
    //合同明细
    @JSONField(name = "FContractEntry", ordinal = 12)
    public List FContractEntry = new ArrayList();
}
