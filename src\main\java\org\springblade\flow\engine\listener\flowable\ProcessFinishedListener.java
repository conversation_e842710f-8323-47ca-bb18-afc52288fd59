package org.springblade.flow.engine.listener.flowable;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.core.constant.WorkFlowConstants;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.flow.engine.utils.ProcessMessageUtils;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;

/**
 * 流程实例监听器，在流程实例结束的时候触发
 */
@Slf4j
public class ProcessFinishedListener implements ExecutionListener {

	@Override
	public void notify(DelegateExecution execution) {
		if (!StrUtil.equals("end", execution.getEventName())) {
			return;
		}

		String processInstanceId = execution.getId();
		String formKey = execution.getVariable(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
//        // 更新流程状态
		String formDataId = execution.getVariable(PROC_INSTANCE_FORM_DATA_ID).toString();
		String processName = execution.getVariable(PROC_INSTANCE_NAME).toString();
		String initiator = execution.getVariable(PROC_INSTANCE_START_USER_NAME_VAR).toString();
		String formCode = Func.toStr(execution.getVariable(PROC_INSTANCE_FORM_CODE));

		// 更新流程实例数据,以及业务表单
		SpringContextUtils.getBean(IBusinessProcessService.class).businessProcessFinshed(formKey, formDataId, processInstanceId);


		SpringContextUtils.getBean(ProcessMessageUtils.class).sendMessageToInitiatorByFinish(initiator, formDataId, formKey, processName, formCode, ProcessStatusEnum.已完成.getName(), processInstanceId);

	}
}
