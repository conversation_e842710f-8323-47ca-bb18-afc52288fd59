package org.springblade.modules.lankegroup.contractManagement.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfoResult;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractUnionInfoMapper;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class ContractUnionInfoTask {
    private final ContractUnionInfoMapper contractUnionInfoMapper;
    private final ProjectBasicMapper projectBasicMapper;

//    @Scheduled(cron = "0 30 01 * * ?")
//    public void getContractUnoinInfo(){
//        contractUnionInfoMapper.deleteData();
//        // 先获取钉钉表项目对应的合同信息（归档完成的），保存
//        List<ContractUnionInfoResult> contractMap = contractUnionInfoMapper.getContractInfo();
//        List<ContractUnionInfo> contractUnionInfos = new ArrayList<>();
//        for(ContractUnionInfoResult result : contractMap){
//            ContractUnionInfo contractUnionInfo = BeanUtil.copy(result, ContractUnionInfo.class);
//            contractUnionInfo.setKdDate(result.getApproveDate());
//            contractUnionInfos.add(contractUnionInfo);
//        }
//        contractUnionInfoMapper.batchInsertContractInfo(contractUnionInfos);
//        // 再去金蝶查合同表信息（审批通过未禁用的），根据金蝶合同id判断，有就更新，没有就新增
//        List<ContractUnionInfoResult> kdContractInfos = contractUnionInfoMapper.getKdContractInfo();
//        List<ContractUnionInfo> kdContractList = new ArrayList<>();
//        for(ContractUnionInfoResult result : kdContractInfos){
//            ContractUnionInfo contractUnionInfo = contractUnionInfoMapper.selectOne(
//                    Wrappers.<ContractUnionInfo>query().lambda()
//                            .eq(ContractUnionInfo::getKdContractFid, result.getKdContractFid()));
//            // 合同信息不存在，即是在金蝶里走的合同，保存入库
//            if(contractUnionInfo == null){
//                ContractUnionInfo info = BeanUtil.copy(result, ContractUnionInfo.class);
//                ProjectBasic basic = projectBasicMapper.selectOne(
//                        Wrappers.<ProjectBasic>query().lambda()
//                                .eq(ProjectBasic::getKdProjectFid, result.getKdProjectFid()));
//                if(basic != null){
//                    info.setProjectId(basic.getId());
//                    kdContractList.add(info);
//                }
//            }else{
//                contractUnionInfo.setKdContractAmount(result.getKdContractAmount());
//                contractUnionInfo.setKdDate(result.getKdDate());
//                contractUnionInfo.setContractNumber(result.getContractNumber());
//                contractUnionInfoMapper.updateById(contractUnionInfo);
//            }
//        }
//        if(kdContractList != null && kdContractList.size() > 0){
//            contractUnionInfoMapper.batchInsertKdContractInfo(kdContractList);
//        }
//    }

    /**
     * 处理任务选项。
     * 该方法主要用于从钉钉合同表中获取数据，清洗数据后存入本地数据库。
     * 具体流程包括：
     * 1. 删除旧的合同数据。
     * 2. 查询钉钉合同表中的所有合同信息，并转换为本地数据库的合同实体。
     * 3. 将转换后的合同实体批量插入本地数据库。
     * 4. 查询钉钉合同表中已归档的快递合同信息。
     * 5. 对于每个项目，如果存在多个合同，处理这些合同，将处理结果保存到一个列表中。
     * 6. 如果处理结果不为空，则将这些结果保存到本地数据库。
     * 这个方法通过GET请求访问，路径为"optTask"。
     */
//    @Scheduled(cron = "0 30 01 * * ?")
    public void optTask() {
        // 删除旧的合同数据
        contractUnionInfoMapper.deleteData();

        // 从钉钉合同表中查询所有合同信息
//         先获取钉钉表项目对应的合同信息（归档完成的），保存
        List<ContractUnionInfoResult> contractMap = contractUnionInfoMapper.getContractInfo();
        List<ContractUnionInfo> contractUnionInfos = new ArrayList<>();
        // 将查询到的合同信息转换为本地数据库的实体，并设置相关的日期字段
        for(ContractUnionInfoResult result : contractMap){
            ContractUnionInfo contractUnionInfo = BeanUtil.copy(result, ContractUnionInfo.class);
            contractUnionInfo.setKdDate(result.getApproveDate());
            contractUnionInfos.add(contractUnionInfo);
        }
        // 批量插入转换后的合同信息到本地数据库
        contractUnionInfoMapper.batchInsertContractInfo(contractUnionInfos);

        try {
            // 从钉钉合同表中查询已归档的快递合同信息
            List<ContractUnionInfoResult> kdContractInfos = contractUnionInfoMapper.getKdContractInfo();
            List<ContractUnionInfoResult> optResults = new ArrayList<>();
            // 根据项目ID对合同信息进行分组
            Map<Long, List<ContractUnionInfoResult>> mapInfos = kdContractInfos.stream()
                    .collect(Collectors.groupingBy(ContractUnionInfoResult::getKdProjectFid));

            // 遍历每个项目的合同列表，处理多个合同的情况
            for (Map.Entry<Long, List<ContractUnionInfoResult>> entry : mapInfos.entrySet()) {
                List<ContractUnionInfoResult> values = entry.getValue();
                // 对合同按日期进行排序
                values.sort(Comparator.comparing(ContractUnionInfoResult::getKdDate));

                // 如果项目只有一个合同，则直接添加到处理结果列表
                if (values.size() == 1) {
                    optResults.add(values.get(0));
                    continue;
                }

                // 处理拥有多个合同的项目，将处理结果添加到处理结果列表
                processMultipleContracts(values, optResults);
            }

            // 如果处理结果不为空，则将其保存到本地数据库
            if (!optResults.isEmpty()) {
                saveContractUnionInfo(optResults);
            }
        } catch (Exception e) {
            // 如果在处理过程中发生异常，抛出运行时异常，并包含错误信息
//            log.error("Error in optTask: ", e);
            throw new RuntimeException("Error processing contracts", e);
        }

    }



    /**
     * 处理多个合同的信息，将合同分为负数金额合同和正数金额合同。
     * 对于负数金额合同，如果其满足特定条件（金额为负且标记为1），则将其金额累加到总负数金额中。
     * 如果总负数金额不为零，则将其从最后一个正数金额合同的金额中扣除。
     * 最后，将所有正数金额合同添加到优化结果列表中。
     *
     * @param contracts 合同信息的列表，包含所有需要处理的合同。
     * @param optResults 优化后的合同信息列表，仅包含正数金额合同。
     */
    private void processMultipleContracts(List<ContractUnionInfoResult> contracts, List<ContractUnionInfoResult> optResults) {

        //contracts 按kdDate中的年，分成一个Map<year,list<ContractUnionInfoResult>>
        Map<Integer, List<ContractUnionInfoResult>> contractMap = contracts.stream()
                .collect(Collectors.groupingBy(contract -> contract.getKdDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear()));
        //contractMap 按照year排序，year下的list里面还要按照kdDate排序
        TreeMap<Integer, List<ContractUnionInfoResult>> sortedContractMap = new TreeMap<>();
        contractMap.forEach((year, contractList) -> {
            contractList.sort(Comparator.comparing(ContractUnionInfoResult::getKdDate));
            sortedContractMap.put(year, contractList);
        });


        for (Map.Entry<Integer, List<ContractUnionInfoResult>> entry : sortedContractMap.entrySet()) {
            int year = entry.getKey();
            List<ContractUnionInfoResult> contractsInYear = entry.getValue();
            // 判断list中金额是否都为正数
            boolean allPositive = contractsInYear.stream()
                    .allMatch(contract -> contract.getKdContractAmount().compareTo(BigDecimal.ZERO) >= 0);
            if (allPositive) {
                optResults.addAll(contractsInYear);
            }else{
                // 判断list中金额是否都为负数
                boolean allNegative = contractsInYear.stream()
                        .allMatch(contract -> contract.getKdContractAmount().compareTo(BigDecimal.ZERO) < 0);
                if (allNegative) {
                    if (sortedContractMap.containsKey(year - 1)) {
                        boolean b = sortedContractMap.get(year - 1).stream().allMatch(contract -> contract.getKdContractAmount().compareTo(BigDecimal.ZERO) < 0);
                        if(b){
                            optResults.addAll(contractsInYear);
                        }else{
                            // 累计负数金额
                            BigDecimal yearNegativeAmount = contractsInYear.stream()
                                    .map(ContractUnionInfoResult::getKdContractAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            //造一条数据，时间是
                            ContractUnionInfoResult contractUnionInfo = new ContractUnionInfoResult();
                            contractUnionInfo.setKdProjectFid(contractsInYear.get(0).getKdProjectFid());
                            contractUnionInfo.setKdContractAmount(yearNegativeAmount);
                            contractUnionInfo.setInyear(year - 1);
                            optResults.add(contractUnionInfo);
                        }

                    }else{
                        optResults.addAll(contractsInYear);
                    }

                }else{
                    optResults.addAll(contractsInYear);
                }
            }
        }
/*        // 使用TreeMap按日期和合同ID排序
        TreeMap<DateIdKey, ContractUnionInfoResult> sortedContracts = new TreeMap<>();
        List<ContractUnionInfoResult> negativeContracts = new ArrayList<>();

        // 分离正负金额合同，并将正数合同放入TreeMap
        for (ContractUnionInfoResult contract : contracts) {
            if (contract.getKdContractAmount().compareTo(BigDecimal.ZERO) < 0 && contract.getMxs() == 1) {
                negativeContracts.add(contract);
            } else {
                sortedContracts.put(new DateIdKey(contract.getKdDate(), contract.getKdContractFid()), contract);
            }
        }

        // 处理负数合同
        for (ContractUnionInfoResult negativeContract : negativeContracts) {
            BigDecimal negativeAmount = negativeContract.getKdContractAmount();
            DateIdKey negativeKey = new DateIdKey(negativeContract.getKdDate(), negativeContract.getKdContractFid());

            // 先查找同一天的正数合同
            Map.Entry<DateIdKey, ContractUnionInfoResult> sameDay = sortedContracts.ceilingEntry(negativeKey);
            if (sameDay != null && sameDay.getKey().date.equals(negativeKey.date)) {
                // 存在同一天的正数合同，直接抵消
                applyNegativeAmount(sameDay.getValue(), negativeAmount);
            } else {
                // 不存在同一天的正数合同，找最近的前一个正数合同
                Map.Entry<DateIdKey, ContractUnionInfoResult> nearestPrevious = sortedContracts.lowerEntry(negativeKey);
                if (nearestPrevious != null) {
//                    if(nearestPrevious.getValue().getKdProjectFid().equals("174628") || "172697".equals(nearestPrevious.getValue().getKdProjectFid())){
//                        System.out.println(nearestPrevious.getValue());
//                    }
                    applyNegativeAmount(nearestPrevious.getValue(), negativeAmount);
                } else {
                    // 如果没有前一个正数合同，找第一个正数合同

                    Map.Entry<DateIdKey, ContractUnionInfoResult> firstPositive = sortedContracts.firstEntry();
                    if (firstPositive != null) {
//                        if(firstPositive.getValue().getKdProjectFid().equals("174628") || "172697".equals(nearestPrevious.getValue().getKdProjectFid())){
//                            System.out.println(nearestPrevious.getValue());
//                        }
                        applyNegativeAmount(firstPositive.getValue(), negativeAmount);
                    }
//                    else {
//                        //todo 处理没有正数合同的情况,预定
//                    }
                }
            }
        }

        // 将处理后的正数合同添加到结果列表
        optResults.addAll(sortedContracts.values());*/
    }

/*    private void applyNegativeAmount(ContractUnionInfoResult positiveContract, BigDecimal negativeAmount) {
        positiveContract.setKdContractAmount(positiveContract.getKdContractAmount()
                .add(negativeAmount).setScale(2, RoundingMode.HALF_UP));
    }

    // 用于TreeMap排序的键
    private static class DateIdKey implements Comparable<DateIdKey> {
        private final Date date;
        private final Long contractId;

        public DateIdKey(Date date, Long contractId) {
            this.date = date;
            this.contractId = contractId;
        }

        @Override
        public int compareTo(DateIdKey other) {
            int dateCompare = this.date.compareTo(other.date);
            if (dateCompare != 0) {
                return dateCompare;
            }
            return this.contractId.compareTo(other.contractId);
        }
    }*/



    /**
     * 保存合同联合信息。
     * 此方法用于处理批量保存合同联合信息的逻辑。它首先通过合同和项目ID列表检索已存在的合同和项目信息，
     * 然后根据这些信息决定是插入新合同还是更新已存在的合同。
     *
     * @param contractUnionInfos 合同联合信息的结果列表，这些信息需要被保存或更新。
     */
    private void saveContractUnionInfo(List<ContractUnionInfoResult> contractUnionInfos) {
        // 提取所有合同ID和项目ID，以便后续查询已存在的合同和项目信息
        List<Long> contractIds = contractUnionInfos.stream().map(ContractUnionInfoResult::getKdContractFid).collect(Collectors.toList());
        List<Long> projectIds = contractUnionInfos.stream().map(ContractUnionInfoResult::getKdProjectFid).collect(Collectors.toList());

        // 根据合同ID查询已存在的合同信息，并将其映射为一个ID到合同对象的映射
        Map<Long, ContractUnionInfo> existingContracts = contractUnionInfoMapper.selectList(Wrappers.<ContractUnionInfo>query().lambda().in(ContractUnionInfo::getKdContractFid, contractIds))
                .stream().collect(Collectors.toMap(ContractUnionInfo::getKdContractFid, Function.identity()));

        // 根据项目ID查询已存在的项目基本信息，并将其映射为一个ID到项目对象的映射
        Map<Long, ProjectBasic> projects = projectBasicMapper.selectList(Wrappers.<ProjectBasic>query().lambda().in(ProjectBasic::getKdProjectFid, projectIds))
                .stream().collect(Collectors.toMap(ProjectBasic::getKdProjectFid, Function.identity()));

        // 初始化列表，用于存储需要插入的新合同和需要更新的已存在合同
        List<ContractUnionInfo> contractsToInsert = new ArrayList<>();
        List<ContractUnionInfo> contractsToUpdate = new ArrayList<>();

        // 遍历输入的合同联合信息结果列表，处理每个合同信息
        for (ContractUnionInfoResult info : contractUnionInfos) {
            // 对合同金额进行四舍五入，保留两位小数
            info.setKdContractAmount(info.getKdContractAmount().setScale(2, RoundingMode.HALF_UP));
            // 检查当前合同是否已存在，如果不存在，则准备插入新合同；如果存在，则更新合同信息
            ContractUnionInfo existingContract = existingContracts.get(info.getKdContractFid());
            if (existingContract == null) {
                // 创建新合同对象，并设置相关信息
                ContractUnionInfo newContract = BeanUtil.copy(info, ContractUnionInfo.class);
                ProjectBasic project = projects.get(info.getKdProjectFid());
                if (project != null) {
                    newContract.setProjectId(project.getId());
                    contractsToInsert.add(newContract);
                }
            } else {
                // 更新已存在合同的信息
                existingContract.setKdContractAmount(info.getKdContractAmount());
                existingContract.setKdDate(info.getKdDate());
                existingContract.setContractNumber(info.getContractNumber());
                contractsToUpdate.add(existingContract);
            }
        }

        // 批量插入新合同
        if (!contractsToInsert.isEmpty()) {
            contractUnionInfoMapper.batchInsertKdContractInfo(contractsToInsert);
            System.out.println("kdContractList=============:::" + contractsToInsert.size());
        }

        // 批量更新已存在合同
        // 批量更新已存在的合同
        if (!contractsToUpdate.isEmpty()) {
            // 这里需要实现一个批量更新的方法，或者使用循环单个更新
            for (ContractUnionInfo contractUnionInfo : contractsToUpdate) {
                contractUnionInfoMapper.updateById(contractUnionInfo);
            }
            System.out.println("contractsToUpdate=============:::" + contractsToUpdate.size());
        }
    }
}
