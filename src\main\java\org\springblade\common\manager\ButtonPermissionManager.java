package org.springblade.common.manager;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.config.ButtonPermissionConfig;
import org.springblade.common.dto.ButtonPermissionDTO;
import org.springblade.common.enums.BusinessAuthTypeEnum;
import org.springblade.common.enums.PageBusinessTypeEnum;
import org.springblade.common.vo.ButtonPermissionDetailVO;
import org.springblade.common.vo.ButtonPermissionVO;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.Customer;
import org.springblade.modules.lankegroup.crm.entity.CustomerContact;
import org.springblade.modules.lankegroup.crm.mapper.ConcentUserMapper;
import org.springblade.modules.lankegroup.crm.mapper.CustomerContactMapper;
import org.springblade.modules.lankegroup.crm.mapper.CustomerMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 按钮权限管理器
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 16:00
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ButtonPermissionManager {

    private final ButtonPermissionConfig buttonPermissionConfig;
    private final BusinessAuthManager businessAuthManager;
    private final CustomerMapper customerMapper;
    private final CustomerContactMapper customerContactMapper;
    private final ConcentUserMapper concentUserMapper;

    /**
     * 本地缓存，避免重复计算（可以考虑使用Redis等分布式缓存）
     */
    private final Map<String, ButtonPermissionVO> localCache = new ConcurrentHashMap<>();

    /**
     * 获取按钮权限
     */
    public ButtonPermissionVO getButtonPermissions(ButtonPermissionDTO dto) {
        String cacheKey = generateCacheKey(dto);
        // 先从缓存获取
        ButtonPermissionVO cachedResult = localCache.get(cacheKey);
/*        if (cachedResult != null) {
            log.debug("从缓存获取按钮权限: {}", cacheKey);
            return cachedResult;
        }*/

        // 计算权限
        ButtonPermissionVO result = calculateButtonPermissions(dto);
        System.out.println("retulst---"+result);

        // 放入缓存（缓存5分钟）
        localCache.put(cacheKey, result);
        // 清理过期缓存的逻辑可以通过定时任务实现

        return result;
    }

    /**
     * 计算按钮权限
     */
    private ButtonPermissionVO calculateButtonPermissions(ButtonPermissionDTO dto) {
        Map<String, Map<String, ButtonPermissionDetailVO>> permissions = new HashMap<>();
        Long currentUserId = AuthUtil.getUserId();

        log.debug("开始计算按钮权限，用户ID: {}, 页面: {}, 场景: {}, 按钮: {}, 业务数据ID: {}",
            currentUserId, dto.getPageBusinessCode(), dto.getSceneCodes(), dto.getButtonCodes(), dto.getBusinessDataId());

        // 获取页面权限配置
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules =
            buttonPermissionConfig.getPagePermissionRules(dto.getPageBusinessCode());

        if (pageRules.isEmpty()) {
            log.warn("未找到页面权限配置: {}", dto.getPageBusinessCode());
            return new ButtonPermissionVO(permissions);
        }

        // 处理场景过滤
        Set<String> targetScenes = dto.getSceneCodes() != null && !dto.getSceneCodes().isEmpty()
            ? new HashSet<>(dto.getSceneCodes())
            : pageRules.keySet();

        // 处理按钮过滤
        Set<String> targetButtons = dto.getButtonCodes() != null && !dto.getButtonCodes().isEmpty()
            ? new HashSet<>(dto.getButtonCodes())
            : null;

        // 获取业务数据相关用户
        List<Long> businessUserIds = getBusinessUserIds(dto);
        log.debug("业务数据相关用户ID列表: {}", businessUserIds);

        // 遍历场景计算权限
        for (String sceneCode : targetScenes) {
            Map<String, List<BusinessAuthTypeEnum>> sceneRules = pageRules.get(sceneCode);
            if (sceneRules == null) {
                continue;
            }

            Map<String, ButtonPermissionDetailVO> scenePermissions = new HashMap<>();

            // 遍历按钮计算权限
            for (Map.Entry<String, List<BusinessAuthTypeEnum>> buttonRule : sceneRules.entrySet()) {
                String buttonCode = buttonRule.getKey();
                List<BusinessAuthTypeEnum> authTypes = buttonRule.getValue();

                // 如果指定了按钮过滤，且当前按钮不在过滤列表中，跳过
                if (targetButtons != null && !targetButtons.contains(buttonCode)) {
                    continue;
                }

                // 计算该按钮的权限
                ButtonPermissionDetailVO buttonPermission = calculateButtonPermission(
                    currentUserId, businessUserIds, authTypes, buttonCode);

                scenePermissions.put(buttonCode, buttonPermission);
            }

            if (!scenePermissions.isEmpty()) {
                permissions.put(sceneCode, scenePermissions);
            }
        }

        return new ButtonPermissionVO(permissions);
    }

    /**
     * 计算单个按钮的权限
     */
    private ButtonPermissionDetailVO calculateButtonPermission(Long currentUserId,
                                                             List<Long> businessUserIds,
                                                             List<BusinessAuthTypeEnum> authTypes,
                                                             String buttonCode) {
        // 如果没有配置权限规则，默认有权限
        if (CollectionUtil.isEmpty(authTypes)) {
            return createPermissionDetail(true, true, "");
        }

        // 使用现有的权限检查逻辑
        boolean hasAuth = businessAuthManager.chechAuth(currentUserId, businessUserIds, authTypes);

        if (hasAuth) {
            return createPermissionDetail(true, true, "");
        } else {
            return createPermissionDetail(false, false, "您没有权限执行此操作");
        }
    }

    /**
     * 获取业务数据相关的用户ID列表
     */
    private List<Long> getBusinessUserIds(ButtonPermissionDTO dto) {
        List<Long> businessUserIds = new ArrayList<>();

        // 如果没有传递任何业务数据ID，直接返回空列表
        if (dto.getBusinessDataId() == null && CollectionUtil.isEmpty(dto.getBusinessDataIds())) {
            return businessUserIds;
        }

        // 从DTO中获取业务数据ID
        if (dto.getBusinessDataId() != null) {
            // 这里需要根据具体业务逻辑查询业务数据的负责人和共享人
            // 例如：客户的负责人和共享人、联系人的负责人等
            businessUserIds.addAll(getBusinessDataRelatedUsers(dto.getBusinessDataId(), dto.getPageBusinessCode()));
        }

        if (CollectionUtil.isNotEmpty(dto.getBusinessDataIds())) {
            for (Long businessDataId : dto.getBusinessDataIds()) {
                businessUserIds.addAll(getBusinessDataRelatedUsers(businessDataId, dto.getPageBusinessCode()));
            }
        }

                // 去重
        return businessUserIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据业务数据ID和页面类型获取相关用户
     * 使用枚举来消除硬编码
     */
    private List<Long> getBusinessDataRelatedUsers(Long businessDataId, String pageBusinessCode) {
        List<Long> userIds = new ArrayList<>();

        // 根据不同的页面业务类型，查询不同的业务数据
        if (PageBusinessTypeEnum.CUSTOMER_PAGE.getCode().equals(pageBusinessCode) ||
            PageBusinessTypeEnum.CUSTOMER_DETAIL_PAGE.getCode().equals(pageBusinessCode)) {
            // 查询客户的负责人和共享人
            userIds.addAll(getCustomerRelatedUsers(businessDataId));
        } else if (PageBusinessTypeEnum.CONTACT_PAGE.getCode().equals(pageBusinessCode)) {
            // 查询联系人对应客户的负责人和共享人
            userIds.addAll(getContactRelatedUsers(businessDataId));
        } else if (PageBusinessTypeEnum.OPPORTUNITY_PAGE.getCode().equals(pageBusinessCode)) {
            // 查询商机的负责人和共享人
            userIds.addAll(getOpportunityRelatedUsers(businessDataId));
        } else if (PageBusinessTypeEnum.FOLLOW_PAGE.getCode().equals(pageBusinessCode)) {
            // 查询跟进记录相关的用户
            userIds.addAll(getFollowRelatedUsers(businessDataId));
        } else {
            log.warn("未支持的页面业务类型: {}", pageBusinessCode);
        }

        return userIds;
    }

    /**
     * 获取客户相关用户（负责人和共享人）
     */
    private List<Long> getCustomerRelatedUsers(Long customerId) {
        List<Long> userIds = new ArrayList<>();
        try {
            Customer customer = customerMapper.selectById(customerId);
            if (customer != null) {
                // 添加负责人
                if (customer.getChargeUser() != null) {
                    userIds.add(customer.getChargeUser());
                }
                System.out.println("shareUserId--"+customer);
                if(customer.getShareUserId()!=null){
                    userIds.add(customer.getShareUserId());
                }
                // 目前Customer实体没有看到共享用户字段
            }
        } catch (Exception e) {
            log.error("获取客户相关用户失败, customerId: {}", customerId, e);
        }
        return userIds;
    }

    /**
     * 获取联系人相关用户
     */
    private List<Long> getContactRelatedUsers(Long contactId) {
        List<Long> userIds = new ArrayList<>();
        try {
            CustomerContact customerContact = customerContactMapper.selectById(contactId);
            if (customerContact != null) {
                // 添加负责人
                if (customerContact.getPrincipal() != null) {
                    userIds.add(customerContact.getPrincipal());
                }
                // 添加共享人
                // 共享人列表
                Map map = new HashMap();
                map.put("contactsId", customerContact.getId());
                List<Long> sharUserIds = concentUserMapper.selectSharUserIds(map);
                if (CollectionUtil.isNotEmpty(sharUserIds)) {
                    userIds.addAll(sharUserIds);
                }
            }
        } catch (Exception e) {
            log.error("获取联系人相关用户失败, contactId: {}", contactId, e);
        }
        return userIds;
    }

    /**
     * 获取商机相关用户
     */
    private List<Long> getOpportunityRelatedUsers(Long opportunityId) {
        List<Long> userIds = new ArrayList<>();
        return userIds;
    }

    /**
     * 获取跟进记录相关用户
     */
    private List<Long> getFollowRelatedUsers(Long followId) {
        List<Long> userIds = new ArrayList<>();
        return userIds;
    }



    /**
     * 创建权限详情对象
     */
    private ButtonPermissionDetailVO createPermissionDetail(Boolean visible, Boolean enabled, String tooltip) {
        ButtonPermissionDetailVO detail = new ButtonPermissionDetailVO();
        detail.setVisible(visible);
        detail.setEnabled(enabled);
        detail.setTooltip(tooltip);
        detail.setExtraProperties(new HashMap<>());
        return detail;
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(ButtonPermissionDTO dto) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("btn_perm:")
            .append(AuthUtil.getUserId())
            .append(":")
            .append(dto.getPageBusinessCode())
            .append(":");

        if (CollectionUtil.isNotEmpty(dto.getSceneCodes())) {
            keyBuilder.append("scenes:").append(String.join(",", dto.getSceneCodes())).append(":");
        }

        if (CollectionUtil.isNotEmpty(dto.getButtonCodes())) {
            keyBuilder.append("buttons:").append(String.join(",", dto.getButtonCodes())).append(":");
        }

        if (dto.getBusinessDataId() != null) {
            keyBuilder.append("dataId:").append(dto.getBusinessDataId()).append(":");
        }

        if (CollectionUtil.isNotEmpty(dto.getBusinessDataIds())) {
            keyBuilder.append("dataIds:").append(dto.getBusinessDataIds().toString()).append(":");
        }

        return keyBuilder.toString();
    }

    /**
     * 清理过期缓存
     */
    public void clearExpiredCache() {
        // 这里可以实现清理逻辑，或者使用定时任务
        localCache.clear();
        log.info("清理按钮权限缓存");
    }
}
