/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractTemplate;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMainMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractTemplateMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractTemplateService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractTemplateVO;
import org.springblade.modules.system.mapper.UserMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 合同模板 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Service
@AllArgsConstructor
public class ContractTemplateServiceImpl extends BaseServiceImpl<ContractTemplateMapper, ContractTemplate> implements IContractTemplateService {

    private final UserMapper userMapper;
    private final ContractMainMapper contractMainMapper;


    @Override
    public List<ContractTemplateVO> selectContractTemplatePage(ContractTemplateVO contractTemplate) {
        List<ContractTemplateVO> contractTemplateVOS = baseMapper.selectContractTemplatePage(contractTemplate);
        if (contractTemplateVOS != null && contractTemplateVOS.size() > 0) {
            List<Map<String, Object>> draftCountMap = contractMainMapper.selectDraftCountByCategoryGroup(AuthUtil.getUserId());
            if (draftCountMap != null && draftCountMap.size() > 0) {
                for (ContractTemplateVO contractTemplateVO : contractTemplateVOS) {
                    draftCountMap.stream().filter(map -> contractTemplateVO.getType().equals(Func.toStr(map.get("category"))))
                            .findFirst()
                            .ifPresent(map -> contractTemplateVO.setDraftsCount(Func.toInt(map.get("draftCount"), 0)));
                }
            }
        }
        return contractTemplateVOS;
    }

}
