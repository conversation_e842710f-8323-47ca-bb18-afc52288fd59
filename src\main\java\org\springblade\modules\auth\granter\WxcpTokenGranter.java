package org.springblade.modules.auth.granter;

//import org.springblade.auth.granter.token.wxcp.WxcpAuthenticationToken;

import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.modules.auth.provider.ITokenGranter;
import org.springblade.modules.auth.provider.TokenParameter;
import org.springblade.modules.lankegroup.ding.dto.UserDTO;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信企业 TokenGranter
 */
@Component
@AllArgsConstructor
public class WxcpTokenGranter implements ITokenGranter {

	public static final String GRANT_TYPE = "wxcp";

	private final IUserService userService;


	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		// 获取访问用户身份
		HttpServletRequest request = WebUtil.getRequest();
		String code = request.getParameter("code");
		String tenantId = request.getParameter("tenantId");
		if (Func.isBlank(code)) {
			throw new ServiceException("未查询到用户code");
		}
		// 获取用户信息
		R<UserInfo> userInfoR = userService.userByAccountForWxcp(tenantId, code);
		// 该方法可本地测试时打开，方便获取token
		// R<UserInfo> userInfoR = userService.userInfo(tenantId, code);
		if (!userInfoR.isSuccess()) {
			throw new ServiceException(userInfoR.getMsg());
		}

		UserInfo userInfo = userInfoR.getData();
		UserDTO ddUser = new UserDTO();
		ddUser.setUserid(Func.toStr(userInfo.getUser().getId()));
		ddUser.setAcount(userInfo.getUser().getAccount());
		ddUser.setName(userInfo.getUser().getName());
		ddUser.setAvatar(userInfo.getUser().getAvatar());
		userInfo.setDdUser(ddUser);
		return userInfo;
	}
}
