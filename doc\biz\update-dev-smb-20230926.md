# dev-smb-0926分支更新内容

## mysql

### 项目新增下推时间字段（记得备份表）
> `blade_project_basic`表中无下推时间，新增一个下推时间字段。历史数据使用update_time字段同步到新字段，
> 新数据直接记录到新加的字段。
```mysql
ALTER TABLE `blade_project_basic` 
ADD COLUMN `push_down_time` datetime NULL COMMENT '下推时间' AFTER `apply_time`;
```
```mysql
update blade_project_basic set push_down_time = update_time where push_down = 1 and push_down_time is null;
```
### 项目关闭备注字段（记得备份表）
```mysql
ALTER TABLE `blade_project_progress_project_close` 
ADD COLUMN `remarks1` varchar(255) NULL COMMENT '备注1' AFTER `remarks`;
```

### 新增表：项目协作任务表-刘源兴
```mysql
CREATE TABLE `blade_project_collaboration_task`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint NULL DEFAULT NULL COMMENT '项目id',
  `task_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务描述',
  `principal_id` bigint NULL DEFAULT NULL COMMENT '负责人id（user表的人员id）(王芳、王汝成ID)',
  `start_date` date NULL DEFAULT NULL COMMENT '任务起始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '任务终止日期',
  `finish_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完成情况',
  `finish_file_path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '完成附件，多个以英文逗号分隔',
  `termination_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终止原因',
  `participant` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参与者（user表的人员id），多个以英文逗号分隔',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `status` int NULL DEFAULT NULL COMMENT '状态（0未开始/1进行中/2已超期/3已完成/4已终止）',
  `is_deleted` int NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1712666478627119106 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目协作任务表' ROW_FORMAT = Dynamic;
```

### 【blade_project_basic】新增字段：项目基础信息表-刘源兴
```mysql
ALTER TABLE `blade_project_basic` ADD `project_level` int DEFAULT NULL COMMENT '项目级别（1.个人,2.部门，3.公司）';
ALTER TABLE `blade_project_basic` ADD `sales_support` int DEFAULT NULL COMMENT '是否需要售前支撑（1是,2否）';
ALTER TABLE `blade_project_basic` ADD `task_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务描述';
ALTER TABLE `blade_project_basic` ADD `task_start_date` date DEFAULT NULL COMMENT '任务起始日期';
ALTER TABLE `blade_project_basic` ADD `task_end_date` date DEFAULT NULL COMMENT '任务终止日期';
```

### 【blade_project_progress_bidding_decision】表新增字段 
```mysql
ALTER TABLE `blade_project_progress_bidding_decision` ADD `bidding_type` int DEFAULT NULL COMMENT '招标类型（1.公开招标,2.内部招标，3.不需招标）';
ALTER TABLE `blade_project_progress_bidding_decision` ADD `bidding_website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '招标网址';
```

### 【blade_archive_details（归档详情）】表新增字段 
```mysql
ALTER TABLE `blade_archive_details` ADD `warranty_period` int DEFAULT NULL COMMENT '质保期';
```

### 【blade_archive_details（归档详情）】表新增字段 
```mysql
ALTER TABLE `blade_archive_details` 
ADD COLUMN `is_warranty` int(0) NULL COMMENT '是否包含质保，0否，1是' AFTER `warranty_period`;
```

### 【blade_receivable（应收单）】表新增字段 
```mysql
ALTER TABLE `blade_receivable` ADD `warranty_period` int DEFAULT NULL COMMENT '质保期';
ALTER TABLE `blade_receivable` ADD `warranty_end_time` date DEFAULT NULL COMMENT '质保结束时间';
```

### 【blade_receivable（应收单）】表新增字段 
```mysql
ALTER TABLE `blade_receivable` 
ADD COLUMN `is_warranty` int(0) NULL COMMENT '是否包含质保，0否，1是' AFTER `warranty_end_time`;
```
