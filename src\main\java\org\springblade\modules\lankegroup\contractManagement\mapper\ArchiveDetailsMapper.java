/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.mapper;

import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetailsFiles;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import java.util.Map;

/**
 * 归档详情 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface ArchiveDetailsMapper extends BaseMapper<ArchiveDetails> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param archiveDetails
	 * @return
	 */
	List<ArchiveDetailsVO> selectArchiveDetailsPage(IPage page, ArchiveDetailsVO archiveDetails);

	/**
	 * 流程id获取
	 *
	 * @param processInstanceId
	 * @return
	 */
	String selectTaskIdByarchiveDetails(String processInstanceId);

	//归档详情 详情归档信息查询
	ArchiveDetailsVO selectArchive(Map map);

	//查询合同详情的 甲方 code
	Map selectSignCompanyCode(Long id);

	//更新合同信息的 状态 和金蝶的id code
	boolean updateContractId(Contract contract);

	List<ContractChange> contractChange(Long contractId);

	/**
	 * 根据合同id查询当前合同是否有归档数据
	 * @param contractId 合同id
	 * @return
	 */
	ArchiveDetails queryByContractId(Long contractId);

	/**
	 * 查询全部的归档表的 等保二三级的信息
	 */
	List<ArchiveDetails> selectAll();

	/**
	 * 根据项目id查询第一次归档的id
	 */
	Long selectIdByProjectIdWithFirstTimeGuiDang(long projectId);

    int saveArchiveFiles(ArchiveDetailsFiles files);

	void deleteArchiveFilesByArchiveId(Long archiveId);

	ArchiveDetailsFiles countArchiveDetailsFiles(Long id);
}
