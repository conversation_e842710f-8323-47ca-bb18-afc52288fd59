package org.springblade.modules.lankegroup.clientrelation.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionArea;

import java.util.List;

public interface ProfessionAreaService extends BaseService<ProfessionArea> {


    /**
     * 新增市场与区域行业关系
     *
     * @param profession
     * @return
     */
    void saveProfessionArea(List<ProfessionArea> profession);

    /**
     * 删除区划列表
     *
     * @param joinId
     * @return
     */
    void deleteProfessionArea(String joinId);

    /**
     * 批量删除市场区县信息
     * @param ids
     */
    void deleteProfessionAreas(List<Long> ids);
}
