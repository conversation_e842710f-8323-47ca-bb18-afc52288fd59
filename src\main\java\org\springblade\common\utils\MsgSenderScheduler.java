//package org.springblade.common.utils;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springblade.common.config.AppConfig;
//import org.springblade.modules.lankegroup.crm.dingmsg.NotifyMessageCard;
//import org.springblade.modules.lankegroup.crm.dingmsg.NotifyMsgQueueCard;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.OrdinaryMessage;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.OrdinaryMessageQueue;
//import org.springblade.modules.lankegroup.pro_management.dingmsg.KDBillDingMessageSender;
//import org.springblade.modules.lankegroup.pro_management.dingmsg.KDBillSimpleMsgQueue;
//import org.springblade.modules.lankegroup.projectKanBan.dingmsg.BillDingMessageSender;
//import org.springblade.modules.lankegroup.projectKanBan.dingmsg.BillMsgBoss;
//import org.springblade.modules.lankegroup.projectKanBan.dingmsg.SimpleMsgBossQueue;
//import org.springblade.modules.lankegroup.projectKanBan.dingmsg.SimpleMsgQueue;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.LxDingMessageSender;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.LxMsgQueue;
//import org.springblade.modules.lankegroup.kingdeeproject.service.TokenService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//@Component
//public class MsgSenderScheduler {
//
//    private final static Logger log = LoggerFactory.getLogger(MsgSenderScheduler.class);
//
//    @Autowired
//    private TokenService tokenService;
//
//    @Autowired
//    private AppConfig appConfig;
//
////    @Scheduled(fixedDelay = 5000, initialDelay = 3000)
//    public void dingdingSendMessage() {
////        try {
////            KDBillDingMessageSender kdSender= KDBillSimpleMsgQueue.instance().poll();
////            BillDingMessageSender sender = SimpleMsgQueue.instance().poll();
////            BillMsgBoss boss= SimpleMsgBossQueue.instance().poll();
////            LxDingMessageSender lx= LxMsgQueue.instance().poll();
////            OrdinaryMessage om= OrdinaryMessageQueue.instance().poll();
////            NotifyMessageCard notify= NotifyMsgQueueCard.instance().poll();
////            if (kdSender != null) {
////                log.info("获取到钉钉消息队列中的消息: " + kdSender);
////                kdSender.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("KDBillDingMessageSender.sendMsg");
////            }
////            if (sender != null) {
////                log.info("获取到钉钉消息队列中的消息: " + sender);
////                sender.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("billDingMessageSender.sendMsg");
////            }
////            if(boss!=null){
////                log.info("获取到钉钉消息队列中的消息: " + boss);
////                boss.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("BillMsgBoss.sendMsg");
////            }
////            if (lx != null) {
////                log.info("获取到钉钉消息队列中的消息: " + sender);
////                lx.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("LxDingMessageSender.sendMsg");
////            }
////            if(om!=null){
////                log.info("获取到钉钉消息队列中的消息: " + sender);
////                om.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("OrdinaryMessage.sendMsg");
////            }
////            if(notify!=null){
////                log.info("获取到钉钉消息队列中的消息: " + sender);
////                notify.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
////                log.info("NotifyMessageCard.sendMsg");
////            }
////        } catch (Exception e) {
////            log.error("发送钉钉通知失败!");
////            e.printStackTrace();
////        }
//    }
//
//}
