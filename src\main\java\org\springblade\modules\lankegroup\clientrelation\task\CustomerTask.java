package org.springblade.modules.lankegroup.clientrelation.task;

import lombok.AllArgsConstructor;
import org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity;
import org.springblade.modules.lankegroup.clientrelation.mapper.CustomerTaskMapper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 统计客户相关数据
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CustomerTask {

    private final CustomerTaskMapper customerTaskMapper;

    /**
     * 统计所有客户信息
     */
//    @Scheduled(cron = "10 * * * * ?")
    public void getCustomerTask() {
        // 统计客户基础信息
        List<CustomerTaskEntity> customerData = customerTaskMapper.getCustomerData();
        if (null == customerData || customerData.size() < 1) {
            return;
        }
        // 删除客户基础信息
        customerTaskMapper.deleteCustomerData();
        // 保存客户基础信息
        customerTaskMapper.saveCustomerData(customerData);
        // 统计客户分组信息-----------------------------------------
        // 查询所有区域市场ID
        List<String> professionListId = customerTaskMapper.getProfessionListId();
        if (null == professionListId || professionListId.size() < 1) {
            return;
        }
        professionListId.stream().forEach(proByUser -> {
            // 查询当前区域市场下负责人ID
            List<String> professionByUserId = customerTaskMapper.getProfessionByUserId(proByUser);
            // 统计各区域市场总计应有实有客户
            List<CustomerTaskEntity> professionCustomerCount = customerTaskMapper.getProfessionCustomerCount(proByUser, professionByUserId);
            if (null != professionCustomerCount && professionCustomerCount.size() > 0) {
                customerTaskMapper.deleteProfessionCustomerCount(proByUser);
                customerTaskMapper.saveProfessionCustomerCount(professionCustomerCount);
            }
            // 查询当前区域市场所负责客户列表
            List<CustomerTaskEntity> professionByCustomerList = customerTaskMapper.getProfessionByCustomerList(proByUser, professionByUserId);
            if (null != professionByCustomerList && professionByCustomerList.size() > 0) {
                customerTaskMapper.deleteProfessionByCustomerList(proByUser);
                customerTaskMapper.saveProfessionByCustomerList(professionByCustomerList);
            }
            // 统计各组区域客户数-------------------------------
            List<CustomerTaskEntity> professionAreaGroup = customerTaskMapper.getProfessionAreaGroup(proByUser, professionByUserId);
            if (null != professionAreaGroup && professionAreaGroup.size() > 0) {
                customerTaskMapper.deleteProfessionAreaGroup(proByUser);
                customerTaskMapper.saveProfessionAreaGroup(professionAreaGroup);
                // 统计各组各市各区县客户数
                for (CustomerTaskEntity area : professionAreaGroup) {
                    List<CustomerTaskEntity> professionAreaCityGroup = customerTaskMapper.getProfessionAreaCityGroup(proByUser, professionByUserId, area.getAreaCode());
                    if (null != professionAreaCityGroup && professionAreaCityGroup.size() > 0) {
                        customerTaskMapper.deleteProfessionAreaCityGroup(proByUser, area.getAreaCode());
                        customerTaskMapper.saveProfessionAreaCityGroup(professionAreaCityGroup, proByUser, area.getAreaCode());
                    }
                }

            }
        });
        // 统计客户归属信息----------------------------
        // 查询所有负责人信息
        List<String> professionByUserId = customerTaskMapper.getProfessionByUserId(null);
        if (null == professionByUserId || professionByUserId.size() < 1) {
            return;
        }
        professionByUserId.stream().forEach(user -> {
            List<CustomerTaskEntity> userByCustomerList = customerTaskMapper.getUserByCustomerList(user);
            if (null != userByCustomerList && userByCustomerList.size() > 0) {
                customerTaskMapper.deleteUserByCustomerList(user);
                customerTaskMapper.saveUserByCustomerList(userByCustomerList);
            }
        });
        // 统计客户归属负责人信息-------------------------------
        // 查询所有负责人信息
        List<String> personnelList = customerTaskMapper.getPersonnelList();
        if (null == personnelList || personnelList.size() < 1) {
            return;
        }
        // 查询全部负责人应有实有客户
        List<CustomerTaskEntity> personnelCustomer = customerTaskMapper.getPersonnelCustomer();
        if (null == personnelCustomer || personnelCustomer.size() < 1) {
            return;
        }
        customerTaskMapper.deletePersonnelCustomer();
        customerTaskMapper.savePersonnelCustomer(personnelCustomer);
        personnelList.stream().forEach(personnel -> {
            // 查询各人员市区应有实有客户
            List<CustomerTaskEntity> personnelUrbanCustomer = customerTaskMapper.getPersonnelUrbanCustomer(personnel);
            if (null != personnelUrbanCustomer && personnelUrbanCustomer.size() > 0) {
                customerTaskMapper.deletePersonnelUrbanCustomer(personnel);
                customerTaskMapper.savePersonnelUrbanCustomer(personnelUrbanCustomer);
                personnelUrbanCustomer.stream().forEach(per -> {
                    List<CustomerTaskEntity> personnelCityCustomer = customerTaskMapper.getPersonnelCityCustomer(personnel, per.getAreaCode());
                    if (null != personnelCityCustomer && personnelCityCustomer.size() > 0) {
                        customerTaskMapper.deletePersonnelCityCustomer(personnel, per.getAreaCode());
                        customerTaskMapper.savePersonnelCityCustomer(personnelCityCustomer, personnel, per.getAreaCode());
                    }
                });
            }
            // 查询各人员区县应有实有客户
        });
    }
}
