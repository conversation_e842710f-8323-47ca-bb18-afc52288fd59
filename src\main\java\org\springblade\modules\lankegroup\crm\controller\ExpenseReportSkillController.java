/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.ExpenseReportSkill;
import org.springblade.modules.lankegroup.crm.vo.ExpenseReportSkillVO;
import org.springblade.modules.lankegroup.crm.vo.ExpenseReportSkillBaseVO;
import org.springblade.modules.lankegroup.crm.vo.ExpenseReportSkillCreateVO;
import org.springblade.modules.lankegroup.crm.vo.ExpenseReportSkillUpdateVO;
import org.springblade.modules.lankegroup.crm.dto.ExpenseReportSkillDTO;
import org.springblade.modules.lankegroup.crm.service.IExpenseReportSkillService;
import org.springblade.modules.lankegroup.common.service.IAttachmentService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 报账技巧 控制器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/crm/expensereportskill")
@Api(value = "报账技巧", tags = "报账技巧接口")
public class ExpenseReportSkillController extends BladeController {

	private final IExpenseReportSkillService expenseReportSkillService;

	/**
	 * 业务类型常量
	 */
	private static final String BUSINESS_TYPE = "expense_report_skill";

	/**
	 * 将实体转换为VO并加载附件
	 *
	 * @param entity 实体对象
	 * @return VO对象
	 */
	private ExpenseReportSkillVO convertToVO(ExpenseReportSkill entity) {
		return expenseReportSkillService.convertToVO(entity);
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功", response = ExpenseReportSkillVO.class)
	})
	public R<ExpenseReportSkillVO> detail(@ApiParam(value = "主键id", required = true) @RequestParam Long id) {
		ExpenseReportSkill detail = expenseReportSkillService.getById(id);
		ExpenseReportSkillVO vo = convertToVO(detail);
		return R.data(vo);
	}

	/**
	 * 分页查询 报账技巧
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页查询", notes = "传入查询条件")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功", response = ExpenseReportSkillVO.class)
	})
	public R<IPage<ExpenseReportSkillVO>> page(ExpenseReportSkill expenseReportSkill, Query query) {
		IPage<ExpenseReportSkill> pages = expenseReportSkillService.page(Condition.getPage(query), Condition.getQueryWrapper(expenseReportSkill));
		IPage<ExpenseReportSkillVO> voPages = pages.convert(this::convertToVO);
		return R.data(voPages);
	}

	/**
	 * 新增 报账技巧
	 */
	@PostMapping("/create")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入expenseReportSkillCreateVO")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R save(@Valid @RequestBody @ApiParam(value = "报账技巧创建对象", required = true) ExpenseReportSkillCreateVO expenseReportSkillCreateVO) {
		boolean success = expenseReportSkillService.createWithAttachments(expenseReportSkillCreateVO);
		return R.status(success);
	}

	/**
	 * 修改 报账技巧
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "修改", notes = "传入expenseReportSkillUpdateVO")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R update(@Valid @RequestBody @ApiParam(value = "报账技巧更新对象", required = true) ExpenseReportSkillUpdateVO expenseReportSkillUpdateVO) {
		boolean success = expenseReportSkillService.updateWithAttachments(expenseReportSkillUpdateVO);
		return R.status(success);
	}

	/**
	 * 删除 报账技巧
	 */
	@PostMapping("/delete")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiResponses({
		@ApiResponse(code = 200, message = "操作成功"),
		@ApiResponse(code = 400, message = "请求参数错误"),
		@ApiResponse(code = 500, message = "内部服务器错误")
	})
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		boolean success = expenseReportSkillService.deleteWithAttachments(Func.toLongList(ids));
		return R.status(success);
	}

}
