package org.springblade.flow.business.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import org.springblade.modules.lankegroup.payback.enums.DateRangeTypeEnum;

/**
 * 时间范围查询封装工具类
 */
public class DateRangeUtil {
	//时间范围查询处理
	public static void invoke(Object obj) {
		DateRangeTypeEnum[] vals = DateRangeTypeEnum.values();
		for (DateRangeTypeEnum val : vals) {
			String type = (String) ReflectUtil.getFieldValue(obj, val.getType());
			String stField = val.getStartTimeField();
			String etField = val.getEndTimeField();
			String stMethod = val.getStartTimeMethod();
			String etMethod = val.getEndTimeMethod();
			if (StrUtil.isNotBlank(type)) {
				switch (type) {
					case "today":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfDay(DateUtil.date()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfDay(DateUtil.date()).toString());
						break;
					case "yesterday":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfDay(DateUtil.yesterday()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfDay(DateUtil.yesterday()).toString());
						break;
					case "week":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfWeek(DateUtil.date()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfWeek(DateUtil.date()).toString());
						break;
					case "lastWeek":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfWeek(DateUtil.lastWeek()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfWeek(DateUtil.lastWeek()).toString());
						break;
					case "month":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfMonth(DateUtil.date()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfMonth(DateUtil.date()).toString());
						break;
					case "lastMonth":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfMonth(DateUtil.lastMonth()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfMonth(DateUtil.lastMonth()).toString());
						break;
					case "quarter":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfQuarter(DateUtil.date()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfQuarter(DateUtil.date()).toString());
						break;
					case "lastQuarter":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfQuarter(DateUtil.offsetMonth(DateUtil.date(), -3)).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfQuarter(DateUtil.offsetMonth(DateUtil.date(), -3)).toString());
						break;
					case "year":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfYear(DateUtil.date()).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfYear(DateUtil.date()).toString());
						break;
					case "lastYear":
						ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfYear(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1)).toString());
						ReflectUtil.invoke(obj, etMethod, DateUtil.endOfYear(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1)).toString());
						break;
				}
			} else {
				Object startTime = ReflectUtil.getFieldValue(obj, stField);
				if (startTime != null && StrUtil.isNotBlank((String) startTime)) {
					ReflectUtil.invoke(obj, stMethod, DateUtil.beginOfDay(DateUtil.parseDate((String) startTime)).toString());
				}
				Object endTime = ReflectUtil.getFieldValue(obj, etField);
				if (endTime != null && StrUtil.isNotBlank((String) endTime)) {
					ReflectUtil.invoke(obj, etMethod, DateUtil.endOfDay(DateUtil.parseDate((String) endTime)).toString());
				}
			}
		}
	}
}
