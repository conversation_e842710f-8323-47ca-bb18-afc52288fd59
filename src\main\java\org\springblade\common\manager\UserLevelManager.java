package org.springblade.common.manager;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.RoleIdConstant;
import org.springblade.common.enums.UserLevelEnum;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.User;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 业务权限
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 14:15
 **/
@Slf4j
@Service
@AllArgsConstructor
public class UserLevelManager {

    /**
     * 判断用户是否有对应的级别
     *
     * @param targetUserId 要判断的目标用户
     */
    public boolean hasTargetUserLevel(Long targetUserId, UserLevelEnum userLevelEnum) {
        if (Func.isEmpty(targetUserId)) {
            log.debug("权限检查失败：参数为空，targetUserId={}", targetUserId);
            return false;
        }
        boolean result = false;
        switch (userLevelEnum) {
            case 财务:
                result = Optional.ofNullable(UserCache.getUser(targetUserId)).map(User::getRoleId).map(roleId -> {
                    if (Func.isNotBlank(roleId) && Func.toLongList(roleId).contains(RoleIdConstant.FINANCE_ROLE_ID)) {
                        return true;
                    }
                    return false;
                }).orElse(false);
                log.debug("财务权限检查：结果={}", result);
                break;
            case 高层:
                result = Optional.ofNullable(UserCache.getUser(targetUserId)).map(User::getRoleId).map(roleId -> {
                    if (Func.isNotBlank(roleId) && Func.toLongList(roleId).contains(RoleIdConstant.MANAGER_ROLE_ID)) {
                        return true;
                    }
                    return false;
                }).orElse(false);
                log.debug("高层权限检查：结果={}", result);
                break;
            case 部门领导:
                result = Optional.ofNullable(UserCache.getUser(targetUserId)).map(User::getLeader).map(leader -> {
                    if (Func.isNotEmpty(leader) && leader == 1) {
                        return true;
                    }
                    return false;
                }).orElse(false);
                log.debug("部门领导权限检查：结果={}", result);
        }
        return result;
    }

    /**
     * 获取用户是对应的级别
     *
     * @param targetUserId 要判断的目标用户
     */
    public UserLevelEnum getUserLevel(Long targetUserId) {
        if (Func.isEmpty(targetUserId)) {
            log.debug("获取用户是对应的级别失败：参数为空，targetUserId={}", targetUserId);
            return UserLevelEnum.普通员工;
        }
        try {
            if (hasTargetUserLevel(targetUserId, UserLevelEnum.高层)) {
                return UserLevelEnum.高层;
            }
            if (hasTargetUserLevel(targetUserId, UserLevelEnum.财务)) {
                return UserLevelEnum.财务;
            }
            if (hasTargetUserLevel(targetUserId, UserLevelEnum.部门领导)) {
                return UserLevelEnum.部门领导;
            }
        } catch (Exception e) {
            log.debug("获取用户是对应的级别异常：e={}", e.getMessage());
            e.printStackTrace();
        }
        return UserLevelEnum.普通员工;
    }


}
