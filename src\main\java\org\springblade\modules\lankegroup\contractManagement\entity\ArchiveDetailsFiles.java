package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * update 20250414 hz
 *归档关联上传附件对象类
 */
@Data
@TableName("blade_archive_details_files")
public class ArchiveDetailsFiles implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_ID
    )
    private Long id;
    //归档信息id
    private Long archiveId;
    //文件名 结算
    private String jieSuanFileName;
    //文件路径
    private String jieSuanFileUrl;

    //文件名 验收
    private String yanShouFileName;
    //文件路径
    private String yanShouFileUrl;

    //文件名 订单
    private String dingFileName;
    //文件路径
    private String dingFileUrl;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime createTime;
    private Long createUser;
}
