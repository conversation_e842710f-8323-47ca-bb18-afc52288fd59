/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.cmmn.engine.impl.cmd.SaveTaskCmd;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.editor.language.json.converter.util.CollectionUtils;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.dynamic.DynamicUserTaskBuilder;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.impl.util.ProcessDefinitionUtil;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springblade.common.cache.UserCache;
import org.springblade.common.utils.JsonUtils;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.entity.BusinessProcessTransferEntity;
import org.springblade.flow.business.mapper.BusinessProcessMapper;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.business.service.IBusinessProcessTransferService;
import org.springblade.flow.business.util.CustomInjectTransferUserTaskInProcessInstanceCmd;
import org.springblade.flow.business.util.CustomInjectUserTaskInProcessInstanceCmd;
import org.springblade.flow.business.vo.AddHisCommentCmd;
import org.springblade.flow.business.vo.CommentVo;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.constant.WorkFlowConstants;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.constant.FlowEngineConstant;
import org.springblade.flow.engine.constant.FlowProcessConfigConstant;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.flow.engine.utils.FlowCache;
import org.springblade.flow.engine.utils.FlowElementPredictionUtils;
import org.springblade.flow.engine.utils.ProcessMessageUtils;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;

/**
 * 流程业务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class FlowBusinessServiceImpl implements FlowBusinessService {

	private final TaskService taskService;
	private final HistoryService historyService;
	private final RuntimeService runtimeService;
	private final FlowEngineService flowEngineService;
	private final IBusinessProcessService businessProcessService;
	private final BusinessProcessMapper businessProcessMapper;
	private final IBusinessProcessTransferService businessProcessTransferService;
	private final ManagementService managementService;
	private final RepositoryService repositoryService;




	@Override
	public IPage<BladeFlow> selectClaimPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		String taskGroup = TaskUtil.getCandidateGroup();
		List<BladeFlow> flowList = new LinkedList<>();

		// 个人等待签收的任务
		TaskQuery claimUserQuery = taskService.createTaskQuery().taskCandidateUser(taskUser)
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 定制流程等待签收的任务
		TaskQuery claimRoleWithTenantIdQuery = taskService.createTaskQuery().taskTenantId(AuthUtil.getTenantId()).taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 通用流程等待签收的任务
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, claimUserQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithoutTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);

		// 计算总数
		long count = claimUserQuery.count() + claimRoleWithTenantIdQuery.count() + claimRoleWithoutTenantIdQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectTodoPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		// 已签收的任务
		TaskQuery todoQuery = taskService.createTaskQuery().taskAssignee(taskUser).active()
			.includeProcessVariables().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, todoQuery, FlowEngineConstant.STATUS_TODO);

		// 计算总数
		long count = todoQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectSendPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricProcessInstanceQuery historyQuery = historyService.createHistoricProcessInstanceQuery().startedBy(taskUser).orderByProcessInstanceStartTime().desc();

		if (bladeFlow.getCategory() != null) {
			historyQuery.processDefinitionCategory(bladeFlow.getCategory());
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			historyQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			historyQuery.startedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			historyQuery.startedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricProcessInstance> historyList = historyQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));

		historyList.forEach(historicProcessInstance -> {
			BladeFlow flow = new BladeFlow();
			// historicProcessInstance
			flow.setCreateTime(historicProcessInstance.getStartTime());
			flow.setEndTime(historicProcessInstance.getEndTime());
			flow.setVariables(historicProcessInstance.getProcessVariables());
			String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
			if (businessKey.length > 1) {
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}
			flow.setHistoryActivityName(historicProcessInstance.getName());
			flow.setProcessInstanceId(historicProcessInstance.getId());
			flow.setHistoryProcessInstanceId(historicProcessInstance.getId());
			// ProcessDefinition
			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessInstanceId(historicProcessInstance.getId());
			// HistoricTaskInstance
			List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
			if (Func.isNotEmpty(historyTasks)) {
				HistoricTaskInstance historyTask = historyTasks.iterator().next();
				flow.setTaskId(historyTask.getId());
				flow.setTaskName(historyTask.getName());
				flow.setTaskDefinitionKey(historyTask.getTaskDefinitionKey());
			}
			// Status
			if (historicProcessInstance.getEndActivityId() != null) {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
			} else {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});

		// 计算总数
		long count = historyQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectDonePage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricTaskInstanceQuery doneQuery = historyService.createHistoricTaskInstanceQuery().taskAssignee(taskUser).finished()
			.includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc();

		if (bladeFlow.getCategory() != null) {
			doneQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			doneQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			doneQuery.taskCompletedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			doneQuery.taskCompletedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricTaskInstance> doneList = doneQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));
		doneList.forEach(historicTaskInstance -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(historicTaskInstance.getId());
			flow.setTaskDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
			flow.setTaskName(historicTaskInstance.getName());
			flow.setAssignee(historicTaskInstance.getAssignee());
			flow.setCreateTime(historicTaskInstance.getCreateTime());
			flow.setExecutionId(historicTaskInstance.getExecutionId());
			flow.setHistoryTaskEndTime(historicTaskInstance.getEndTime());
			flow.setVariables(historicTaskInstance.getProcessVariables());

			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicTaskInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));

			flow.setProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			flow.setHistoryProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance((historicTaskInstance.getProcessInstanceId()));
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
				if (historicProcessInstance.getEndActivityId() != null) {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
				} else {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
				}
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});
		// 计算总数
		long count = doneQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public boolean completeTask(BladeFlow flow) {
		if (Func.isEmpty(flow.getProcessInstanceId())) {
			return false;
		}
		Task task = taskService.createTaskQuery().processInstanceId(flow.getProcessInstanceId())
			.taskAssignee(String.valueOf(AuthUtil.getUserId()))
			.singleResult();
//		Task task1 = taskService.createTaskQuery().taskId(flow.getTaskId()).singleResult();
		if (Objects.isNull(task)) {
			throw new ServiceException("任务不存在");
		}
		String taskId = flow.getTaskId();
		String processInstanceId = flow.getProcessInstanceId();
		String comment = Func.toStr(flow.getComment(), ProcessConstant.PASS_COMMENT);
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, FlowCommentEnum.APPROVE.getType(), comment);
		}
		// 流程变量
		Map<String, Object> variables = flow.getVariables();
//		variables.put(ProcessConstant.PASS_KEY, flow.isPass());
		if (variables == null) {
			//完成审批
			taskService.complete(taskId);
		} else {
			//完成审批
			taskService.complete(taskId, variables);
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R approveTask(WorkFlowDTO flow) {
		if (Func.isEmpty(flow.getProcessInstanceId())) {
			return R.fail("流程实例ID不能为空");
		}
		if (Func.isEmpty(flow.getTaskId())) {
			return R.fail("任务ID不能为空");
		}
		TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(flow.getTaskId()).singleResult();
		if (ObjectUtil.isNull(task)) {
			return R.fail("任务不存在");
		}
		if (!Func.isEmpty(task.getAssignee()) && !String.valueOf(AuthUtil.getUserId()).equals(task.getAssignee())) {
			return R.fail("不是任务审批人");
		}
		String taskId = flow.getTaskId();
		String processInstanceId = flow.getProcessInstanceId();
		// 增加评论 null会报错，先置城空字符串
		if(flow.getComment()==null){
			flow.setComment("");
		}
		taskService.addComment(taskId, processInstanceId, FlowCommentEnum.APPROVE.getType(), flow.getComment());
		// 任务处理人
		// (因为新销客流程都是通过候选人进行设置的，所有待办任务都通过候选任务表进行查询，
		// 所以候选处理人可以任务进行任务处理人，因此这里直接给任务办理人进行赋值)
		taskService.setAssignee(taskId, String.valueOf(AuthUtil.getUserId()));
		// 流程变量
		Map<String, Object> variables = new HashMap<>();
		variables.put(ProcessConstant.PASS_KEY, true);

		// 处理记录
		handleRecordTask(task);

		//完成审批
		taskService.complete(taskId, variables);

		return R.status(true);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
	public R rejectTask(WorkFlowDTO flow) {
		if (Func.isEmpty(flow.getProcessInstanceId())) {
			return R.fail("流程实例ID不能为空");
		}
		if (!FlowProcessConfigConstant.VIRTUAL_APPROVER.equals(flow.getApprover())) {
			Task currentTask = taskService.createTaskQuery().taskId(flow.getTaskId()).singleResult();
			if (ObjectUtil.isNull(currentTask)) {
				return R.fail("任务不存在");
			}
			if (!Func.isEmpty(currentTask.getAssignee()) && !String.valueOf(AuthUtil.getUserId()).equals(currentTask.getAssignee()) && !FlowProcessConfigConstant.VIRTUAL_APPROVER.equals(currentTask.getAssignee())) {
				return R.fail("不是任务审批人");
			}
		}

		// 获取所有历史接口
		List<HistoricTaskInstance> taskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(flow.getProcessInstanceId()).finished().orderByHistoricTaskInstanceEndTime().asc().list();
		if (taskInstanceList != null && taskInstanceList.size() > 0) {
			//获取第一个任务节点
			HistoricTaskInstance target = taskInstanceList.get(0);

			// 获取所有激活的任务节点，找到需要撤回的任务
			List<Task> activateTaskList = taskService.createTaskQuery().processInstanceId(flow.getProcessInstanceId()).list();
			List<String> revokeExecutionIds = new ArrayList<>();
			for (Task task : activateTaskList) {
				// 添加撤回审批信息
				taskService.setAssignee(task.getId(), String.valueOf(AuthUtil.getUserId()));
				taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.REBACK.getType(), flow.getComment());
				revokeExecutionIds.add(task.getExecutionId());
			}
			try {
				runtimeService.createChangeActivityStateBuilder()
						.processInstanceId(flow.getProcessInstanceId())
						.moveExecutionsToSingleActivityId(revokeExecutionIds, target.getTaskDefinitionKey())
						.changeState();
			} catch (FlowableObjectNotFoundException e) {
				return R.fail("未找到流程实例，流程可能已发生变化");
			} catch (FlowableException e) {
				return R.fail("执行退回操作失败");
			}
		} else {
			taskService.addComment(flow.getTaskId(), flow.getProcessInstanceId(), FlowCommentEnum.REBACK.getType(), flow.getComment());

			BpmnModel bpmnModel = repositoryService.getBpmnModel(flow.getDelegateTask().getProcessDefinitionId());
			String firstNodeId = FlowElementPredictionUtils.findFirstUserTaskId(bpmnModel);
			runtimeService.createChangeActivityStateBuilder()
					.processInstanceId(flow.getProcessInstanceId())
					.moveExecutionsToSingleActivityId(Collections.singletonList(flow.getDelegateTask().getExecutionId()), firstNodeId)
					.changeState();
		}
		//moveExecutionsToSingleActivityId 方法用于将执行实例移动到指定的活动节点，直接使用该方法不会触发目标活动节点上配置的，也就是不会触发用户任务的监听器。
		// 所以这里需要单独处理业务数据状态
		Map<String, Object> variables = runtimeService.getVariables(flow.getProcessInstanceId());
		String formKey = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		String formDataId = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_DATA_ID).toString();
		businessProcessService.updateBusinessProcessRejectTask(formKey, formDataId, flow.getProcessInstanceId());
		String processName = variables.get(PROC_INSTANCE_NAME).toString();
		String initiator = variables.get(PROC_INSTANCE_START_USER_NAME_VAR).toString();
		String formCode = Func.toStr(variables.get(PROC_INSTANCE_FORM_CODE));
		SpringContextUtils.getBean(ProcessMessageUtils.class).sendMessageToInitiatorByReject(initiator, formDataId, formKey, processName, formCode, ProcessStatusEnum.已驳回.getName(), flow.getComment(), AuthUtil.getUser(), flow.getProcessInstanceId());

		return R.status(true);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public R revokeTask(WorkFlowDTO flow) {
		String processInstanceId = flow.getProcessInstanceId();
		// 校验流程是否结束
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
			.processInstanceId(processInstanceId)
			.active()
			.singleResult();
		if (Objects.isNull(processInstance)) {
			return R.fail("流程已结束或已挂起，无法执行撤回操作");
		}
		/*Task currentTask = taskService.createTaskQuery().taskId(flow.getTaskId()).singleResult();
		if (ObjectUtil.isNull(currentTask)) {
			return R.fail("任务不存在");
		}*/

		List<Task> taskList = taskService.createTaskQuery()
				.processInstanceId(flow.getProcessInstanceId())
				.taskAssignee(AuthUtil.getUserId().toString())
				.orderByTaskCreateTime() // 按任务创建时间排序
				.desc() // 倒序
				.listPage(0, 1);

		if (Func.isNotEmpty(taskList) && StrUtil.equals(taskList.get(0).getName(), "发起人")) {
			return R.fail("当前用户没有可撤回的任务");
		}

		// 获取所有历史接口
		List<HistoricTaskInstance> taskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).finished().orderByHistoricTaskInstanceEndTime().asc().list();
		//获取第一个任务节点
		HistoricTaskInstance target = taskInstanceList.get(0);

		// 获取所有激活的任务节点，找到需要撤回的任务
		List<Task> activateTaskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
		List<String> revokeExecutionIds = new ArrayList<>();
//		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		for (Task task : activateTaskList) {
			// 添加撤回审批信息
			taskService.setAssignee(task.getId(), String.valueOf(AuthUtil.getUserId()));
			taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.REVOKE.getType(), AuthUtil.getUserName() + "撤回流程审批" + (Func.isNotBlank(flow.getComment()) ? "，撤回理由：" + flow.getComment() : StringPool.EMPTY));
			revokeExecutionIds.add(task.getExecutionId());

			// 处理记录
			handleRecordTask(task);
		}
		try {
			//退回到发起人节点
			runtimeService.createChangeActivityStateBuilder()
				.processInstanceId(processInstanceId)
				.moveExecutionsToSingleActivityId(revokeExecutionIds, target.getTaskDefinitionKey())
				.changeState();
		} catch (FlowableObjectNotFoundException e) {
			return R.fail("未找到流程实例，流程可能已发生变化");
		} catch (FlowableException e) {
			return R.fail("执行撤回操作失败");
		}

		//moveExecutionsToSingleActivityId 方法用于将执行实例移动到指定的活动节点，直接使用该方法不会触发目标活动节点上配置的，也就是不会触发用户任务的监听器。
		// 所以这里需要单独处理业务数据状态
		Map<String, Object> variables = runtimeService.getVariables(flow.getProcessInstanceId());
		String formKey = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		String formDataId = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_DATA_ID).toString();
		businessProcessService.updateBusinessProcessRevokeTask(formKey, formDataId, flow.getProcessInstanceId());

		return R.status(true);
	}


	/**
	 * 构建流程
	 *
	 * @param bladeFlow 流程通用类
	 * @param flowList  流程列表
	 * @param taskQuery 任务查询类
	 * @param status    状态
	 */
	private void buildFlowTaskList(BladeFlow bladeFlow, List<BladeFlow> flowList, TaskQuery taskQuery, String status) {
		if (bladeFlow.getCategory() != null) {
			taskQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			taskQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			taskQuery.taskCreatedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			taskQuery.taskCreatedBefore(bladeFlow.getEndDate());
		}
		taskQuery.list().forEach(task -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(task.getId());
			flow.setTaskDefinitionKey(task.getTaskDefinitionKey());
			flow.setTaskName(task.getName());
			flow.setAssignee(task.getAssignee());
			flow.setCreateTime(task.getCreateTime());
			flow.setClaimTime(task.getClaimTime());
			flow.setExecutionId(task.getExecutionId());
			flow.setVariables(task.getProcessVariables());

			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(task.getProcessInstanceId());
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}

			FlowProcess processDefinition = FlowCache.getProcessDefinition(task.getProcessDefinitionId());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessInstanceId(task.getProcessInstanceId());
			flow.setStatus(status);
			flowList.add(flow);
		});
	}

	/**
	 * 获取历史流程
	 *
	 * @param processInstanceId 流程实例id
	 * @return HistoricProcessInstance
	 */
	private HistoricProcessInstance getHistoricProcessInstance(String processInstanceId) {
		return historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
	}

	@Override
	public R selectFlowModelKey(String formKey) {
		FlowModel flowModel = flowEngineService.getOne(Wrappers.lambdaQuery(FlowModel.class)
			.eq(FlowModel::getFormKey, formKey)
			.eq(FlowModel::getStatus, 0).last("limit 1"));
		if (Func.isEmpty(flowModel)) {
			R.fail("未找到流程模型");
		}
		if (flowModel.getStatus() == 1) {
			R.fail("流程模型已禁用");
		}
		return R.data(flowModel);
	}

	@Override
	public R transferTask(String taskId, String userId) {
		taskService.setAssignee(taskId, userId);
		businessProcessTransferService.update(
			Wrappers.lambdaUpdate(BusinessProcessTransferEntity.class)
				.set(BusinessProcessTransferEntity::getTragetUser, userId)
				.set(BusinessProcessTransferEntity::getUpdateUser, AuthUtil.getUserId())
				.set(BusinessProcessTransferEntity::getUpdateTime,new Date())
				.set(BusinessProcessTransferEntity::getIsDeleted, 1)
				.eq(BusinessProcessTransferEntity::getTaskId, taskId));
		return R.success("转交任务成功");
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public R transferTaskV2(String taskId, String userId, String reason) {
		// 当前用户（转交操作人）不能为转交用户
		Long currentUserId = AuthUtil.getUserId();
		if (Func.isNotEmpty(currentUserId) && currentUserId.equals(Long.valueOf(userId))) {
			return R.fail("被转交人不能为当前操作人");
		}
		// 当前节点信息
		Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (ObjectUtil.isNull(currentTask)) {
			return R.fail("任务不存在");
		}
		if (!Func.isEmpty(currentTask.getAssignee()) && !String.valueOf(currentUserId).equals(currentTask.getAssignee())) {
			return R.fail("不是任务审批人");
		}
		// 是否是多人节点
		boolean instanceTaskFlag = isMultiInstanceTask(currentTask, repositoryService);

		if (instanceTaskFlag) {
			// 判断当前多人是否存在转交人
			Task task = taskService.createTaskQuery().processInstanceId(currentTask.getProcessInstanceId()).taskAssignee(userId).singleResult();
			if (task != null) {
				return R.fail("被转交人已经是审批人");
			}
		}


		// 参数KEY前缀
		String parameKeyPrefix = instanceTaskFlag ? currentTask.getTaskDefinitionKey() : taskId;


		// 转交操作人
		User currentUser = UserCache.getUser(currentUserId);
		// 转交人
		User targetUser = UserCache.getUser(Long.valueOf(userId));
		// 转交列表
		List<WorkFlow> transferTaskList = new ArrayList<>();
		Object variable = runtimeService.getVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_before");
		if (variable != null) {
			transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
		}
		Date currentTime = null;
		Object variableTime = runtimeService.getVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_currentTime");
		if (variableTime != null) {
			currentTime = DateUtil.parse((String) variableTime, "yyyy-MM-dd HH:mm:ss");
		}
		// 新加数据
		WorkFlow workFlow = new WorkFlow();
		workFlow.setTaskId(UUID.randomUUID().toString());
		workFlow.setTaskName("审核人");
		workFlow.setAssignee(String.valueOf(currentUser.getId()));
		workFlow.setAssigneeName(StrUtil.format("{}（已转交给{}）", currentUser.getRealName(), targetUser.getRealName()));
		workFlow.setAssigneeAvatar(currentUser.getAvatar());
		workFlow.setAssigneeIsDeleted(0);
		workFlow.setCreateTime(currentTime == null ? currentTask.getCreateTime() : currentTime);
		workFlow.setEndTime(new Date());
		workFlow.setProcessDefinitionId(currentTask.getProcessDefinitionId());
		workFlow.setStatus("approve");
		workFlow.setComment(StrUtil.format("转交理由：{}",reason));
		workFlow.setRecordType(0);
		transferTaskList.add(workFlow);
		runtimeService.setVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_before", JsonUtils.objToJson(transferTaskList));
		runtimeService.setVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_currentTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
		taskService.setAssignee(taskId, userId);


		businessProcessTransferService.update(
				Wrappers.lambdaUpdate(BusinessProcessTransferEntity.class)
						.set(BusinessProcessTransferEntity::getTragetUser, userId)
						.set(BusinessProcessTransferEntity::getUpdateUser, currentUserId)
						.set(BusinessProcessTransferEntity::getUpdateTime,new Date())
						.set(BusinessProcessTransferEntity::getIsDeleted, 1)
						.eq(BusinessProcessTransferEntity::getTaskId, taskId));

		Map<String, Object> variables = runtimeService.getVariables(currentTask.getProcessInstanceId());
		String formKey = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		String formDataId = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_DATA_ID).toString();
		String initiator = variables.get(PROC_INSTANCE_START_USER_NAME_VAR).toString();
		String formCode = Func.toStr(variables.get(PROC_INSTANCE_FORM_CODE));
		SpringContextUtils.getBean(ProcessMessageUtils.class).sendMessageToInitiatorByTransferTaskV2(initiator, userId, formDataId, formKey, formCode, currentTask.getProcessInstanceId());

		return R.success("转交任务成功");
	}

	/**
	 * 撤回、驳回、同意时，处理多人节点的记录
	 */
	private void handleRecordTask(Task task) {
		boolean instanceTaskFlag = isMultiInstanceTask(task, repositoryService);
		if (instanceTaskFlag) {
			// 将多人节点的操作记录，存放在当前节点，并重置对应参数值
			String parameKeyPrefix = task.getTaskDefinitionKey();

			Object variable = runtimeService.getVariable(task.getProcessInstanceId(), parameKeyPrefix + "_before");
			if (variable != null) {
				runtimeService.setVariable(task.getProcessInstanceId(), task.getId() + "_before", variable);
			}
			Object variableTime = runtimeService.getVariable(task.getProcessInstanceId(), parameKeyPrefix + "_currentTime");
			if (variableTime != null) {
				runtimeService.setVariable(task.getProcessInstanceId(), task.getId() + "_currentTime", variableTime);
			}

			runtimeService.setVariable(task.getProcessInstanceId(), parameKeyPrefix + "_before", null);
			runtimeService.setVariable(task.getProcessInstanceId(), parameKeyPrefix + "_currentTime", null);
		}
	}

	/**
	 * 是否是多人节点
	 * true-是；false-否
	 */
	public boolean isMultiInstanceTask(Task task, RepositoryService repositoryService) {
		try {
			String taskDefinitionKey = task.getTaskDefinitionKey();
			ProcessDefinition processDefinition = repositoryService.getProcessDefinition(task.getProcessDefinitionId());
			BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
			FlowElement flowElement = bpmnModel.getFlowElement(taskDefinitionKey);
			if (flowElement instanceof UserTask) {
				UserTask userTask = (UserTask) flowElement;
				MultiInstanceLoopCharacteristics multiInstance = userTask.getLoopCharacteristics();
				if (multiInstance != null) {
					return true;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}


	@Override
	public R signTask(String taskId, String userIds, Integer signType, Integer approvalType, String reason) {
		// TODO
		// 当前用户（当前操作人）不能为加签审批人
		Long currentUserId = AuthUtil.getUserId();
		if (Func.isNotEmpty(currentUserId) && Func.toStrList(userIds).contains(String.valueOf(currentUserId))) {
			return R.fail("加签审批人列表不能包含当前操作人");
		}
		// 当前节点信息
		TaskEntityImpl currentTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
		if (ObjectUtil.isNull(currentTask)) {
			return R.fail("任务不存在");
		}
		if (!Func.isEmpty(currentTask.getAssignee()) && !String.valueOf(currentUserId).equals(currentTask.getAssignee())) {
			return R.fail("不是任务审批人");
		}
		// 是否是多人节点
		boolean instanceTaskFlag = isMultiInstanceTask(currentTask, repositoryService);
		// signType 加签方式 1.前加签(true)；2.后加签(false)
		boolean isBeforeSignUp = (signType == 1);
		// approvalType 审批方式 1.会签；2.或签

		// 参数KEY前缀
		String parameKeyPrefix = instanceTaskFlag ? currentTask.getTaskDefinitionKey() : taskId;
		// 加签操作人
		User currentUser = UserCache.getUser(currentUserId);
		// 加签人
		List<User> signUserList = new ArrayList<>();
		Func.toStrList(userIds).forEach(userId -> {
			User targetUser = UserCache.getUser(Long.valueOf(userId));
			if (targetUser != null && targetUser.getId() != null) {
				signUserList.add(targetUser);
			}
		});
		if (signUserList == null || signUserList.isEmpty()) {
			throw new ServiceException("获取加签人信息失败");
		}



		// 转交列表（加签列表也在此存放）
		List<WorkFlow> transferTaskList = new ArrayList<>();
		Object variable = runtimeService.getVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_before");
		if (variable != null) {
			transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
		}
		List<WorkFlow> afterTaskList = new ArrayList<>();
		Object variable2 = runtimeService.getVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_after");
		if (variable2 != null) {
			afterTaskList = JsonUtils.jsonToList((String) variable2, WorkFlow.class);
		}
		Date currentTime = null;
		Object variableTime = runtimeService.getVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_currentTime");
		if (variableTime != null) {
			currentTime = DateUtil.parse((String) variableTime, "yyyy-MM-dd HH:mm:ss");
		}

		// 加签操作人记录
		// 新加数据
		WorkFlow workFlow = new WorkFlow();
		workFlow.setTaskId(UUID.randomUUID().toString());
		workFlow.setTaskName("审核人");
		workFlow.setAssignee(String.valueOf(currentUser.getId()));
		workFlow.setAssigneeName(StrUtil.format("{}（加签）", currentUser.getRealName()));
		workFlow.setAssigneeAvatar(currentUser.getAvatar());
		workFlow.setAssigneeIsDeleted(0);
		workFlow.setCreateTime(currentTime == null ? currentTask.getCreateTime() : currentTime);
		workFlow.setEndTime(new Date());
		workFlow.setProcessDefinitionId(currentTask.getProcessDefinitionId());
		workFlow.setStatus("approve");
		workFlow.setComment(StrUtil.format("加签理由：{}",reason));
		workFlow.setRecordType(0);
		// 记录加签人姓名集合
		String signUserNames = signUserList.stream().map(User::getRealName).collect(Collectors.joining(","));
		workFlow.setRemark(signUserNames);
		transferTaskList.add(workFlow);



		//1.把当前的节点设置为空
//		if (currentTask != null) {
			//如果是加签再加签
			String parentTaskId = currentTask.getParentTaskId();
			if (StringUtils.isBlank(parentTaskId)) {
				currentTask.setOwner(currentTask.getAssignee());
				currentTask.setAssignee(null);
				currentTask.setCountEnabled(true);
				// signType 加签方式 1.前加签；2.后加签
				if (isBeforeSignUp) {
					currentTask.setScopeType("before");
				} else {
					currentTask.setScopeType("after");
					//1.2 设置任务为空执行者
//					taskService.saveTask(currentTask);
				}
			}
			//2.添加加签数据
			List<TaskEntity> signSubTasks = this.createSignSubTasks(userIds, currentTask, isBeforeSignUp);
			if (CollectionUtil.isEmpty(signSubTasks)) {
				throw new ServiceException("创建加签任务失败");
			}

			// 加签节点记录
			// 新加数据
			if (signSubTasks.size() > 1) {
				List<WorkFlow.Approver> approverList = new ArrayList<>();
				WorkFlow workFlow2 = new WorkFlow();
				workFlow2.setTaskId(signSubTasks.get(0).getId());
				workFlow2.setTaskName("审核人（前加签）");
				workFlow2.setAssignee(signUserList.stream().map(User::getId).map(String::valueOf).collect(Collectors.joining(",")));
				workFlow2.setAssigneeName(null);
				workFlow2.setAssigneeAvatar(null);
				workFlow2.setAssigneeIsDeleted(0);
				workFlow2.setCreateTime(currentTime == null ? currentTask.getCreateTime() : currentTime);
				workFlow2.setEndTime(null);
				workFlow2.setProcessDefinitionId(currentTask.getProcessDefinitionId());
				workFlow2.setStatus("todo");
				workFlow2.setComment(null);
				workFlow2.setRecordType(isBeforeSignUp ? 1 : 2);
				for (User user : signUserList) {
					WorkFlow.Approver approver = new WorkFlow.Approver();
					approver.setAssignee(Func.toStr(user.getId()));
					approver.setAssigneeName(user.getRealName());
					approver.setAssigneeAvatar(user.getAvatar());
					approver.setAssigneeIsDeleted(user.getIsDeleted());
					approverList.add(approver);
				}
				workFlow2.setApproverList(approverList);
				transferTaskList.add(workFlow2);
			} else {
				User user = signUserList.get(0);
				WorkFlow workFlow2 = new WorkFlow();
				workFlow2.setTaskId(signSubTasks.get(0).getId());
				workFlow2.setTaskName("审核人（前加签）");
				workFlow2.setAssignee(String.valueOf(user.getId()));
				workFlow2.setAssigneeName(user.getRealName());
				workFlow2.setAssigneeAvatar(user.getAvatar());
				workFlow2.setAssigneeIsDeleted(0);
				workFlow2.setCreateTime(currentTime == null ? currentTask.getCreateTime() : currentTime);
				workFlow2.setEndTime(new Date());
				workFlow2.setProcessDefinitionId(currentTask.getProcessDefinitionId());
				workFlow2.setStatus("todo");
				workFlow2.setComment(null);
				workFlow2.setRecordType(isBeforeSignUp ? 1 : 2);
				transferTaskList.add(workFlow2);
			}


			runtimeService.setVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_before", JsonUtils.objToJson(transferTaskList));
			runtimeService.setVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_after", JsonUtils.objToJson(afterTaskList));
			runtimeService.setVariable(currentTask.getProcessInstanceId(), parameKeyPrefix + "_currentTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));



//			String parentTaskId = currentTask.getParentTaskId();
			if (StringUtils.isBlank(parentTaskId)) {
				parentTaskId = currentTask.getId();
			}
			if (StringUtils.isBlank(currentTask.getParentTaskId())) {
				//2.创建加签人的任务并执行完毕
				Task task = this.createSubTask(currentTask, parentTaskId, String.valueOf(currentUserId));
				taskId = task.getId();
			}
			if (isBeforeSignUp) {
				Task taskInfo = taskService.createTaskQuery().taskId(taskId).singleResult();
				if (null != taskInfo) {
					taskService.complete(taskId);
				}
			}


			//3.添加审批意见
			String type = isBeforeSignUp ? "前加签" : "后加签";
			this.addComment(taskId, String.valueOf(currentUserId), currentTask.getProcessInstanceId(),
					type, reason);
			String message =  isBeforeSignUp ? "后加签成功" : "前加签成功";
//		}

		return R.success("加签任务成功");
	}

	@Override
	public R deleteTask(String processInstanceId, Long formDataId) {
		// 删除审批待办 和 业务所以这里需要单独处理业务数据状态
		Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
		System.out.println(variables);
		String formKey = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		businessProcessService.updateBusinessProcessDeleteFlow(formKey, String.valueOf(formDataId), processInstanceId);

		// 删除工作流
		if (Func.isNotBlank(processInstanceId)) {
			try {
				runtimeService.deleteProcessInstance(processInstanceId, "流程实例删除");
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
		return R.success("操作成功");
	}

	/**
	 * 添加审批意见
	 *
	 * @param taskId            任务id
	 * @param userCode          处理人工号
	 * @param processInstanceId 流程实例id
	 * @param type              审批类型
	 * @param message           审批意见
	 */
	protected void addComment(String taskId, String userCode, String processInstanceId, String type, String message) {
		//1.添加备注
		CommentVo commentVo = new CommentVo(taskId, userCode, processInstanceId, type, message);
		managementService.executeCommand(new AddHisCommentCmd(commentVo.getTaskId(), commentVo.getUserId(), commentVo.getProcessInstanceId(),
				commentVo.getType(), commentVo.getMessage()));
	}


	/**
	 * 创建加签子任务
	 *
	 * @param userIds     加签人ID
	 * @param taskEntity 父任务
	 * @param isBeforeSignUp true.前加签(true)；false.后加签(false)
	 */
	private List<TaskEntity> createSignSubTasks(String userIds, TaskEntity taskEntity, boolean isBeforeSignUp) {
		List<TaskEntity> resultList = new ArrayList<>();
		Long currentUserId = AuthUtil.getUserId();
		List<String> userIdList = Func.toStrList(userIds);
		if (CollectionUtils.isNotEmpty(userIdList)) {
			String parentTaskId = taskEntity.getParentTaskId();
			if (StringUtils.isBlank(parentTaskId)) {
				parentTaskId = taskEntity.getId();
			}
			String finalParentTaskId = parentTaskId;
			//1.创建被加签人的任务列表
			userIdList.forEach(userId -> {
				if (StringUtils.isNotBlank(userId)) {
					TaskEntity subTask = this.createSubTask(taskEntity, finalParentTaskId, userId);
					resultList.add(subTask);
				}
			});
//			String taskId = taskEntity.getId();
//			if (StringUtils.isBlank(taskEntity.getParentTaskId())) {
//				//2.创建加签人的任务并执行完毕
//				Task task = this.createSubTask(taskEntity, finalParentTaskId, String.valueOf(currentUserId));
//				taskId = task.getId();
//			}
//			if (isBeforeSignUp) {
//				Task taskInfo = taskService.createTaskQuery().taskId(taskId).singleResult();
//				if (null != taskInfo) {
//					taskService.complete(taskId);
//				}
//			}
//			//如果是候选人，需要删除运行时候选表种的数据。
//			long candidateCount = taskService.createTaskQuery().taskId(parentTaskId).taskCandidateUser(String.valueOf(currentUserId)).count();
//			if (candidateCount > 0) {
//				taskService.deleteCandidateUser(parentTaskId, String.valueOf(currentUserId));
//			}
		}
		return resultList;
	}
	/**
	 * 创建子任务
	 *
	 * @param ptask    创建子任务
	 * @param assignee 子任务的执行人
	 * @return
	 */
	protected TaskEntity createSubTask(TaskEntity ptask, String ptaskId, String assignee) {
		TaskEntity task = null;
		if (ptask != null) {
			//1.生成子任务
			task = (TaskEntity) taskService.newTask(UUID.randomUUID().toString());
			task.setCategory(ptask.getCategory());
			task.setDescription(ptask.getDescription());
			task.setTenantId(ptask.getTenantId());
			task.setAssignee(assignee);
			task.setName(ptask.getName());
			task.setParentTaskId(ptaskId);
			task.setProcessDefinitionId(ptask.getProcessDefinitionId());
			task.setProcessInstanceId(ptask.getProcessInstanceId());
			task.setTaskDefinitionKey(ptask.getTaskDefinitionKey());
			task.setTaskDefinitionId(ptask.getTaskDefinitionId());
			task.setPriority(ptask.getPriority());
			task.setCreateTime(new Date());
			taskService.saveTask(task);
		}
		return task;
	}

//	/**
//	 * 前加签
//	 */
//	private void addBeforeSign(Task currentTask, String newAssignee) {
//		// 创建一个新的任务
////		TaskEntity newTask = (TaskEntity)taskService.newTask();
////		newTask.setName("加签任务");
////		newTask.setAssignee(newAssignee);
////		newTask.setParentTaskId(currentTask.getId()); // 关联到原任务
////		newTask.setProcessDefinitionId(currentTask.getProcessDefinitionId());
////		newTask.setProcessInstanceId(currentTask.getProcessInstanceId());
////		taskEntity.setAssignee(null);
////		taskEntity.setCountEnabled(true);
//
//
//	}
}
