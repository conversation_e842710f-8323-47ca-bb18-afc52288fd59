package org.springblade.flow.engine.utils;

import com.github.xiaoymin.knife4j.core.util.StrUtil;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.impl.de.odysseus.el.ExpressionFactoryImpl;
import org.flowable.common.engine.impl.de.odysseus.el.util.SimpleContext;
import org.flowable.common.engine.impl.javax.el.ExpressionFactory;
import org.flowable.common.engine.impl.javax.el.ValueExpression;
import org.flowable.common.engine.impl.util.io.StringStreamSource;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.flow.core.enums.MultiInstanceType;

import java.util.*;

/**
 * 审批流预测
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 11:16
 */
public class FlowElementPredictionUtils {

	private static final BpmnXMLConverter bpmnXMLConverter = new BpmnXMLConverter();

	/**
	 * xml转bpmnModel对象
	 *
	 * @param xml xml
	 * @return bpmnModel对象
	 */
	public static BpmnModel getBpmnModel(String xml) {
		return bpmnXMLConverter.convertToBpmnModel(new StringStreamSource(xml), false, false);
	}

//    /**
//     * bpmnModel转xml字符串
//     *
//     * @deprecated 存在会丢失 bpmn 连线问题
//     * @param bpmnModel bpmnModel对象
//     * @return xml字符串
//     */
//    @Deprecated
//    public static String getBpmnXmlStr(BpmnModel bpmnModel) {
//        return StrUtil.utf8Str(getBpmnXml(bpmnModel));
//    }

	/**
	 * bpmnModel转xml对象
	 *
	 * @param bpmnModel bpmnModel对象
	 * @return xml
	 * @deprecated 存在丢失 bpmn 连线问题
	 */
	@Deprecated
	public static byte[] getBpmnXml(BpmnModel bpmnModel) {
		return bpmnXMLConverter.convertToXML(bpmnModel);
	}

	/**
	 * 根据节点，获取入口连线
	 *
	 * @param source 起始节点
	 * @return 入口连线列表
	 */
	public static List<SequenceFlow> getElementIncomingFlows(FlowElement source) {
		List<SequenceFlow> sequenceFlows = new ArrayList<>();
		if (source instanceof FlowNode) {
			sequenceFlows = ((FlowNode) source).getIncomingFlows();
		}
		return sequenceFlows;
	}


	/**
	 * 根据节点，获取出口连线
	 *
	 * @param source 起始节点
	 * @return 出口连线列表
	 */
	public static List<SequenceFlow> getElementOutgoingFlows(FlowElement source) {
		List<SequenceFlow> sequenceFlows = new ArrayList<>();
		if (source instanceof FlowNode) {
			sequenceFlows = ((FlowNode) source).getOutgoingFlows();
		}
		return sequenceFlows;
	}

	/**
	 * 获取开始节点
	 *
	 * @param model bpmnModel对象
	 * @return 开始节点（未找到开始节点，返回null）
	 */
	public static StartEvent getStartEvent(BpmnModel model) {
		Process process = model.getMainProcess();
		FlowElement startElement = process.getInitialFlowElement();
		if (startElement instanceof StartEvent) {
			return (StartEvent) startElement;
		}
		return getStartEvent(process.getFlowElements());
	}

	/**
	 * 获取开始节点
	 *
	 * @param flowElements 流程元素集合
	 * @return 开始节点（未找到开始节点，返回null）
	 */
	public static StartEvent getStartEvent(Collection<FlowElement> flowElements) {
		for (FlowElement flowElement : flowElements) {
			if (flowElement instanceof StartEvent) {
				return (StartEvent) flowElement;
			}
		}
		return null;
	}

	/**
	 * 获取结束节点
	 *
	 * @param model bpmnModel对象
	 * @return 结束节点（未找到开始节点，返回null）
	 */
	public static EndEvent getEndEvent(BpmnModel model) {
		Process process = model.getMainProcess();
		return getEndEvent(process.getFlowElements());
	}

	/**
	 * 获取结束节点
	 *
	 * @param flowElements 流程元素集合
	 * @return 结束节点（未找到开始节点，返回null）
	 */
	public static EndEvent getEndEvent(Collection<FlowElement> flowElements) {
		for (FlowElement flowElement : flowElements) {
			if (flowElement instanceof EndEvent) {
				return (EndEvent) flowElement;
			}
		}
		return null;
	}

	public static UserTask getUserTaskByKey(BpmnModel model, String taskKey) {
		Process process = model.getMainProcess();
		FlowElement flowElement = process.getFlowElement(taskKey);
		if (flowElement instanceof UserTask) {
			return (UserTask) flowElement;
		}
		return null;
	}

	/**
	 * 获取流程元素信息
	 *
	 * @param model         bpmnModel对象
	 * @param flowElementId 元素ID
	 * @return 元素信息
	 */
	public static FlowElement getFlowElementById(BpmnModel model, String flowElementId) {
		Process process = model.getMainProcess();
		return process.getFlowElement(flowElementId);
	}

	/**
	 * 获取元素表单Key（限开始节点和用户节点可用）
	 *
	 * @param flowElement 元素
	 * @return 表单Key
	 */
	public static String getFormKey(FlowElement flowElement) {
		if (flowElement != null) {
			if (flowElement instanceof StartEvent) {
				return ((StartEvent) flowElement).getFormKey();
			} else if (flowElement instanceof UserTask) {
				return ((UserTask) flowElement).getFormKey();
			}
		}
		return null;
	}

	/**
	 * 获取开始节点属性值
	 *
	 * @param model bpmnModel对象
	 * @param name  属性名
	 * @return 属性值
	 */
	public static String getStartEventAttributeValue(BpmnModel model, String name) {
		StartEvent startEvent = getStartEvent(model);
		return getElementAttributeValue(startEvent, name);
	}

	/**
	 * 获取结束节点属性值
	 *
	 * @param model bpmnModel对象
	 * @param name  属性名
	 * @return 属性值
	 */
	public static String getEndEventAttributeValue(BpmnModel model, String name) {
		EndEvent endEvent = getEndEvent(model);
		return getElementAttributeValue(endEvent, name);
	}

	/**
	 * 获取用户任务节点属性值
	 *
	 * @param model   bpmnModel对象
	 * @param taskKey 任务Key
	 * @param name    属性名
	 * @return 属性值
	 */
	public static String getUserTaskAttributeValue(BpmnModel model, String taskKey, String name) {
		UserTask userTask = getUserTaskByKey(model, taskKey);
		return getElementAttributeValue(userTask, name);
	}

	/**
	 * 获取元素属性值
	 *
	 * @param baseElement 流程元素
	 * @param name        属性名
	 * @return 属性值
	 */
	public static String getElementAttributeValue(BaseElement baseElement, String name) {
		if (baseElement != null) {
			List<ExtensionAttribute> attributes = baseElement.getAttributes().get(name);
			if (attributes != null && !attributes.isEmpty()) {
				attributes.iterator().next().getValue();
				Iterator<ExtensionAttribute> attrIterator = attributes.iterator();
				if (attrIterator.hasNext()) {
					ExtensionAttribute attribute = attrIterator.next();
					return attribute.getValue();
				}
			}
		}
		return null;
	}

	public static boolean isMultiInstance(BpmnModel model, String taskKey) {
		UserTask userTask = getUserTaskByKey(model, taskKey);
		if (ObjectUtil.isNotEmpty(userTask)) {
			return userTask.hasMultiInstanceLoopCharacteristics();
		}
		return false;
	}

	/**
	 * 获取所有用户任务节点（包括用户任务节点和抄送节点）
	 *
	 * @param model bpmnModel对象
	 * @return 用户任务节点列表
	 */
	public static Collection<FlowElement> getAllUserTaskEvent(BpmnModel model) {
		Process process = model.getMainProcess();
		Collection<FlowElement> flowElements = process.getFlowElements();
		return getAllUserTaskEvent(flowElements, null);
	}

	/**
	 * 获取所有用户任务节点
	 *
	 * @param flowElements 流程元素集合
	 * @param allElements  所有流程元素集合
	 * @return 用户任务节点列表
	 */
	public static Collection<FlowElement> getAllUserTaskEvent(Collection<FlowElement> flowElements, Collection<FlowElement> allElements) {
		allElements = allElements == null ? new ArrayList<>() : allElements;
		for (FlowElement flowElement : flowElements) {
			if (flowElement instanceof UserTask) {
				allElements.add(flowElement);
			}
			if (flowElement instanceof SubProcess) {
				// 继续深入子流程，进一步获取子流程
				allElements = getAllUserTaskEvent(((SubProcess) flowElement).getFlowElements(), allElements);
			}
			if (flowElement instanceof ServiceTask) {
				allElements.add(flowElement);
			}
		}
		return allElements;
	}

	/**
	 * 查找起始节点下一个用户任务列表列表
	 *
	 * @param source 起始节点
	 * @return 结果
	 */
	public static List<UserTask> findNextUserTasks(FlowElement source) {
		return findNextUserTasks(source, null, null);
	}

	/**
	 * 查找起始节点下一个用户任务列表列表
	 *
	 * @param source          起始节点
	 * @param hasSequenceFlow 已经经过的连线的 ID，用于判断线路是否重复
	 * @param userTaskList    用户任务列表
	 * @return 结果
	 */
	public static List<UserTask> findNextUserTasks(FlowElement source, Set<String> hasSequenceFlow, List<UserTask> userTaskList) {
		hasSequenceFlow = Optional.ofNullable(hasSequenceFlow).orElse(new HashSet<>());
		userTaskList = Optional.ofNullable(userTaskList).orElse(new ArrayList<>());
		// 获取出口连线
		List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
		if (!sequenceFlows.isEmpty()) {
			for (SequenceFlow sequenceFlow : sequenceFlows) {
				// 如果发现连线重复，说明循环了，跳过这个循环
				if (hasSequenceFlow.contains(sequenceFlow.getId())) {
					continue;
				}
				// 添加已经走过的连线
				hasSequenceFlow.add(sequenceFlow.getId());
				FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();
				if (targetFlowElement instanceof UserTask) {
					// 若节点为用户任务，加入到结果列表中
					userTaskList.add((UserTask) targetFlowElement);
				} else {
					// 若节点非用户任务，继续递归查找下一个节点
					findNextUserTasks(targetFlowElement, hasSequenceFlow, userTaskList);
				}
			}
		}
		return userTaskList;
	}

	/**
	 * 迭代从后向前扫描，判断目标节点相对于当前节点是否是串行
	 * 不存在直接回退到子流程中的情况，但存在从子流程出去到父流程情况
	 *
	 * @param source          起始节点
	 * @param target          目标节点
	 * @param visitedElements 已经经过的连线的 ID，用于判断线路是否重复
	 * @return 结果
	 */
	public static boolean isSequentialReachable(FlowElement source, FlowElement target, Set<String> visitedElements) {
		visitedElements = visitedElements == null ? new HashSet<>() : visitedElements;
		if (source instanceof StartEvent && isInEventSubprocess(source)) {
			return false;
		}

		// 根据类型，获取入口连线
		List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);
		if (sequenceFlows != null && sequenceFlows.size() > 0) {
			// 循环找到目标元素
			for (SequenceFlow sequenceFlow : sequenceFlows) {
				// 如果发现连线重复，说明循环了，跳过这个循环
				if (visitedElements.contains(sequenceFlow.getId())) {
					continue;
				}
				// 添加已经走过的连线
				visitedElements.add(sequenceFlow.getId());
				FlowElement sourceFlowElement = sequenceFlow.getSourceFlowElement();
				// 这条线路存在目标节点，这条线路完成，进入下个线路
				if (target.getId().equals(sourceFlowElement.getId())) {
					continue;
				}
				// 如果目标节点为并行网关，则不继续
				if (sourceFlowElement instanceof ParallelGateway) {
					return false;
				}
				// 否则就继续迭代
				boolean isSequential = isSequentialReachable(sourceFlowElement, target, visitedElements);
				if (!isSequential) {
					return false;
				}
			}
		}
		return true;
	}

	protected static boolean isInEventSubprocess(FlowElement flowElement) {
		FlowElementsContainer flowElementsContainer = flowElement.getParentContainer();
		while (flowElementsContainer != null) {
			if (flowElementsContainer instanceof EventSubProcess) {
				return true;
			}

			if (flowElementsContainer instanceof FlowElement) {
				flowElementsContainer = ((FlowElement) flowElementsContainer).getParentContainer();
			} else {
				flowElementsContainer = null;
			}
		}
		return false;
	}

	/**
	 * 查询当前节点是否是会签节点
	 *
	 * @param processDefinitionId
	 * @param taskDefinitionKey
	 * @return
	 */
	public static boolean isSequentialMultiInstance(RepositoryService repositoryService, String processDefinitionId, String taskDefinitionKey) {
		boolean res = false;
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskDefinitionKey);
		// 判断是否是并行会签节点 （或签） ParallelMultiInstanceBehavior
		if (flowNode.getBehavior() instanceof SequentialMultiInstanceBehavior) {
			res = true;
		}
		return res;
	}

	/**
	 * 根据当前用户任务获取所有待审批用户
	 *
	 * @param currentNode
	 * @param variables
	 * @return
	 */
	public static List<FlowElement> getToDoUserTaskEvent(FlowElement currentNode, Map<String, Object> variables) {
		List<FlowElement> todo = new ArrayList<>();

		FlowElement next = currentNode;
		boolean end = false;
		while (!end) {
			next = findTargetNode(next, variables);
			if (next instanceof Gateway) {
				continue;
			}
			if (next instanceof EndEvent) {
				end = true;
				continue;
			}
			if (next == null) {
				end = true;
			} else {
				todo.add(next);
			}
		}

		return todo;
	}

	/**
	 * 查找节点在工作流中的下一个节点。如遇到网关有多个出口路径，则只搜索条件表达式包含‘pass’的路径。
	 *
	 * @param node
	 * @return
	 */
	private static FlowElement findTargetNode(FlowElement node, Map<String, Object> variables) {
		if (node instanceof Gateway) {
			// 下个节点为排它网关时
			ExclusiveGateway exclusiveGateway = (ExclusiveGateway) node;
			List<SequenceFlow> outgoingFlows = exclusiveGateway.getOutgoingFlows();
			// 遍历网关的出线得到下个activity
			FlowElement targetFlowElement = getNextTargetFlowElement(variables, outgoingFlows);
			return targetFlowElement;
		}
		if (node instanceof EndEvent) {
			return node;
		}
		if (node instanceof ServiceTask) {
			return ((ServiceTask) node).getOutgoingFlows().get(0).getTargetFlowElement();
		}
		if (node instanceof StartEvent) {
			SequenceFlow passWay = ((StartEvent) node).getOutgoingFlows().stream()
				.filter(flow -> flow.getConditionExpression() == null || flow.getConditionExpression().contains("${pass}"))
				.findFirst()
				.orElseThrow(() -> new ServiceException("网关未正确配置流转条件"));
			return passWay.getTargetFlowElement();
		}
		if (node == null) {
			return null;
		}

		final UserTask userTask = (UserTask) node;


		SequenceFlow passWay = userTask.getOutgoingFlows().stream()
			.filter(flow -> flow.getConditionExpression() == null || flow.getConditionExpression().contains("${pass}"))
			.findFirst()
			.orElseThrow(() -> new ServiceException("网关未正确配置流转条件"));
		return passWay.getTargetFlowElement();
	}

	/**
	 * 根据el表达式取得满足条件的下一个activityId
	 *
	 * @param variables
	 * @param outgoingFlows
	 * @return
	 */
	private static FlowElement getNextTargetFlowElement(Map<String, Object> variables, List<SequenceFlow> outgoingFlows) {
		FlowElement targetFlowElement = null;
		// 遍历出线
		for (SequenceFlow outgoingFlow : outgoingFlows) {
			// 取得线上的条件
			String conditionExpression = outgoingFlow.getConditionExpression();
			if (StrUtil.isBlank(conditionExpression)) {
				// 取得目标节点
				targetFlowElement = outgoingFlow.getTargetFlowElement();

				break;
			}

			String variableName = "";
			// 判断网关条件里是否包含变量名
			for (String s : variables.keySet()) {
				if (StrUtil.isNotBlank(conditionExpression) && conditionExpression.contains(s)) {
					// 找到网关条件里的变量名
					variableName = s;
				}
			}
			String conditionVal = getVariableValue(variableName, variables);
			// 判断el表达式是否成立
			if (isCondition(variableName, conditionExpression, conditionVal)) {
				// 取得目标节点
				targetFlowElement = outgoingFlow.getTargetFlowElement();
				break;
			}
		}
		return targetFlowElement;
	}

	private static String getVariableValue(String variableName, Map<String, Object> variables) {
		Object object = variables.get(variableName);
		return object == null ? "" : object.toString();
	}

	/**
	 * 根据key和value判断el表达式是否通过
	 *
	 * @param key   el表达式key
	 * @param el    el表达式
	 * @param value el表达式传入值
	 * @return
	 */
	private static boolean isCondition(String key, String el, String value) {
		ExpressionFactory factory = new ExpressionFactoryImpl();
		SimpleContext context = new SimpleContext();
//        2024/03/27正则表达式增加负数可能，其他应付单（挂账应付单）允许走负数流程
		if (value.matches("-?\\d+\\.\\d+")) {
			Number number = Float.parseFloat(value);
			context.setVariable(key, factory.createValueExpression(number, Number.class));
		} else {
			context.setVariable(key, factory.createValueExpression(value, String.class));
		}

		ValueExpression e = factory.createValueExpression(context, el, boolean.class);

		return (Boolean) e.getValue(context);
	}

	/**
	 * 判断 HistoricActivityInstance 是否是会签节点，并返回其类型
	 *
	 * @param historicActivityInstance 历史活动实例
	 * @param repositoryService        Flowable RepositoryService
	 * @return MultiInstanceType 枚举：NONE, PARALLEL, SEQUENTIAL, OR_GATEWAY
	 */
	public static Integer getMultiInstanceType(HistoricActivityInstance historicActivityInstance, RepositoryService repositoryService) {
		String processDefinitionId = historicActivityInstance.getProcessDefinitionId();
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		Process process = bpmnModel.getMainProcess();

		FlowElement flowElement = process.getFlowElement(historicActivityInstance.getActivityId());
		if (flowElement == null) {
			return null;
		}

		if (flowElement instanceof UserTask) {
			UserTask userTask = (UserTask) flowElement;
			if (userTask.hasMultiInstanceLoopCharacteristics()) {
				MultiInstanceLoopCharacteristics loopCharacteristics = userTask.getLoopCharacteristics();
				if (loopCharacteristics.isSequential()) {
					return MultiInstanceType.SEQUENTIAL.getKey();
				} else {
					if (loopCharacteristics.getCompletionCondition().equals("${OneSignMultiInstance.accessCondition(execution)}")) {
						return MultiInstanceType.OR_GATEWAY.getKey();
					}
					return MultiInstanceType.PARALLEL.getKey();
				}
			}
		}
		return null;
	}

	/**
	 * 获取流程定义的第一个用户任务节点的ID
	 *
	 * @param bpmnModel BPMN模型
	 * @return 用户任务节点ID
	 */
	public static String findFirstUserTaskId(BpmnModel bpmnModel) {
		return bpmnModel.getMainProcess().getFlowElements().stream()
				.filter(e -> e instanceof UserTask)
				.map(FlowElement::getId)
				.findFirst()
				.orElseThrow();
	}
}
