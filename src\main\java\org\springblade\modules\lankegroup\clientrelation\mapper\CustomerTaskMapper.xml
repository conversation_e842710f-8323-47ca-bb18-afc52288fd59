<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.CustomerTaskMapper">
    <insert id="saveCustomerData">
        insert into customer_all_data
        (customerId,customerName,province,urbanArea,city,marketId,userId,importanceKey,createTime)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.customerId},#{item.customerName},#{item.province},#{item.urbanArea},#{item.city},#{item.marketId},#{item.userId},#{item.importanceKey},#{item.createTime})
        </foreach>
    </insert>
    <insert id="saveProfessionByCustomerList">
        insert into customer_all_profession (professionId,city,marketId,customerId,createTime)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.professionId},#{item.city},#{item.marketId},#{item.customerId},#{item.createTime})
        </foreach>
    </insert>
    <insert id="saveUserByCustomerList">
        insert into customer_all_user (userId,city,marketId,customerId)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.userId},#{item.city},#{item.marketId},#{item.customerId})
        </foreach>
    </insert>
    <insert id="saveProfessionAreaGroup">
        insert into customer_all_profession_area_group(professionId,areaCode,areaName,oughtClient,actualClient)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.professionId},#{item.areaCode},#{item.areaName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <insert id="saveProfessionAreaCityGroup">
        insert into customer_all_profession_area_city_group
        (professionId,cityCode,areaCode,areaName,oughtClient,actualClient)
        <foreach collection="list" item="item" separator="," index="index">
            (#{professionId},#{areaCode},#{item.areaCode},#{item.areaName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <insert id="saveProfessionCustomerCount">
        insert into profession_all_customer (professionId,professionName,oughtClient,actualClient)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.professionId},#{item.professionName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <insert id="savePersonnelCustomer">
        insert into personnel_all_customer(personnelId,userId,userName,oughtClient,actualClient)
        <foreach collection="list" index="index" separator="," item="item">
            (#{item.personnelId},#{item.userId},#{item.userName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <insert id="savePersonnelUrbanCustomer">
        insert into customer_all_personnel_area_group(personnelId,areaCode,areaName,oughtClient,actualClient)
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.personnelId},#{item.areaCode},#{item.areaName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <insert id="savePersonnelCityCustomer">
        insert into customer_all_personnel_area_city_group(personnelId,cityCode,areaCode,areaName,oughtClient,actualClient)
        <foreach collection="list" index="index" separator="," item="item">
            (#{personnelId},#{areaCode},#{item.areaCode},#{item.areaName},#{item.oughtClient},#{item.actualClient})
        </foreach>
    </insert>
    <delete id="deleteCustomerData">
        delete
        from customer_all_data
    </delete>
    <delete id="deleteProfessionByCustomerList">
        delete
        from customer_all_profession
        where professionId = #{professionId}
    </delete>
    <delete id="deleteUserByCustomerList">
        delete
        from customer_all_user
        where userId = #{userId}
    </delete>
    <delete id="deleteProfessionAreaGroup">
        delete
        from customer_all_profession_area_group
        where professionId = #{professionId}
    </delete>
    <delete id="deleteProfessionAreaCityGroup">
        delete
        from customer_all_profession_area_city_group
        where professionId = #{professionId}
          and cityCode = #{areaCode}
    </delete>
    <delete id="deleteProfessionCustomerCount">
        delete
        from profession_all_customer
        where professionId = #{professionId}
    </delete>
    <delete id="deletePersonnelCustomer">
        delete
        from personnel_all_customer
    </delete>
    <delete id="deletePersonnelUrbanCustomer">
        delete
        from customer_all_personnel_area_group where personnelId = #{personnelId}
    </delete>
    <delete id="deletePersonnelCityCustomer">
        delete
        from customer_all_personnel_area_city_group where personnelId = #{personnelId} and cityCode = #{areaCode}
    </delete>
    <select id="getCustomerData"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select DISTINCT cust.id              customerId,
                        cust.customer_name   customerName,
                        cust.province,
                        cust.urban_area      urbanArea,
                        cust.city,
                        industry.industry_id marketId,
                        charge.user_id       userId,
                        cust.importance_key  importanceKey,
                        cust.create_time     createTime
        from blade_customer cust
                 left join blade_customer_charge_user charge
                           on charge.customer_id = cust.id
                 left join blade_customer_industry_involved industry
                           on industry.customer_id = cust.id and industry.is_deleted = 0
        where cust.forbid_status = 'A'
          and cust.is_deleted = 0
    </select>
    <select id="getProfessionListId" resultType="java.lang.String">
        select id
        from blade_profession
        where is_deleted = 0
    </select>
    <select id="getProfessionByUserId" resultType="java.lang.String">
        select user_id
        from blade_personnel
        where is_deleted = 0
        <if test="professionId != null">
            and profession_id = #{professionId}
        </if>
    </select>
    <select id="getProfessionByCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select DISTINCT #{professionId} professionId,city,marketId,customerId,createTime
        from (
        select DISTINCT area.counties_code city,market.market_code marketId,cust.id customerId,cust.create_time
        createTime
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        join blade_customer cust
        on cust.city = area.counties_code and cust.forbid_status = 'A' and cust.is_deleted = 0
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.industry_id = market.market_code and industry.is_deleted = 0
        where pro.id = #{professionId}
        <if test="userList != null and userList.size() > 0">
            union all
            select DISTINCT city,marketId,customerId,createTime
            from customer_all_data where userId in (
            <foreach collection="userList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        ) t
    </select>
    <select id="getUserByCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select DISTINCT #{userId} userId, city, marketId, customerId
        from (
                 select DISTINCT market.area_id city, market.market_id marketId, cust.id customerId
                 from blade_personnel per
                          join blade_personnel_market market
                               on per.id = market.personnel_id and market.is_deleted = 0
                          join blade_customer cust
                               on cust.city = market.area_id and cust.forbid_status = 'A' and cust.is_deleted = 0
                          join blade_customer_industry_involved industry
                               on industry.industry_id = market.market_id and industry.customer_id = cust.id and
                                  industry.is_deleted = 0
                 where per.user_id = 0
                 union all
                 select DISTINCT city, marketId, customerId
                 from customer_all_data
                 where userId = #{userId}
             ) t
    </select>
    <select id="getProfessionAreaGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select #{professionId} professionId,area.code areaCode, area.name areaName, sum(oughtClient) oughtClient,
        sum(actualClient) actualClient
        from blade_area area
        join (
        select areaCode, areaName, oughtClient, actualClient
        from (
        select areaCode, areaName, oughtClient, count(allpro.customerId) actualClient
        from (
        select areaCode, areaName, oughtClient, max(type)
        from (
        select area.counties_code areaCode,
        area.counties_name areaName,
        sum(market.num) oughtClient,
        1 type
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}
        group by area.counties_code
        <if test="userList != null and userList.size() > 0">
            union all
            select area.code areaCode, area.name areaName, 0 oughtClient, 0 type
            from customer_all_data alldata
            join blade_area area
            on area.code = alldata.city
            where userId in (
            <foreach collection="userList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        ) t
        group by areaCode) t1
        left join customer_all_profession allpro
        on allpro.city = t1.areaCode and allpro.professionId = #{professionId}
        group by t1.areaCode
        ) areaData
        ) areaData
        on area.code = left (areaData.areaCode, 4)
        group by area.code
    </select>
    <select id="getProfessionAreaCityGroup"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select #{professionId} professionId,areaCode, areaName, oughtClient, count(allpro.customerId) actualClient
        from (
        select areaCode, areaName, oughtClient, max(type)
        from (
        select area.counties_code areaCode,
        area.counties_name areaName,
        sum(market.num) oughtClient,
        1 type
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}
        group by area.counties_code
        <if test="userList != null and userList.size() > 0">
            union all
            select area.code areaCode, area.name areaName, 0 oughtClient, 0 type
            from customer_all_data alldata
            join blade_area area
            on area.code = alldata.city
            where userId in (
            <foreach collection="userList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        ) t
        group by areaCode) t1
        left join customer_all_profession allpro
        on allpro.city = t1.areaCode and allpro.professionId = #{professionId}
        where t1.areaCode like concat(#{areaCode},'%')
        group by t1.areaCode
        order by areaCode
    </select>
    <select id="getProfessionCustomerCount"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select t1.professionId,t1.professionName,t2.oughtClient,t1.actualClient from
        (select profession.id professionId,profession.profession_name professionName,pro.oughtClient,pro.actualClient
        from blade_profession profession join (
        select #{professionId} professionId,areaCode,areaName,oughtClient,count(allpro.customerId) actualClient from (
        select areaCode,areaName,sum(oughtClient) oughtClient,max(type) from (
        select area.counties_code areaCode ,area.counties_name areaName ,sum(market.num) oughtClient, 1 type
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}
        group by area.counties_code
        <if test="userList != null and userList.size() > 0">
            union all
            select area.code areaCode, area.name areaName, 0 oughtClient, 0 type
            from customer_all_data alldata
            join blade_area area
            on area.code = alldata.city
            where userId in (
            <foreach collection="userList" index="index" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        ) t group by areaCode) t1
        left join customer_all_profession allpro
        on allpro.city = t1.areaCode and allpro.professionId = #{professionId}) pro
        on pro.professionId = profession.id ) t1
        join
        (select pro.id ,sum(market.num) oughtClient
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}) t2
        on t1.professionId = t2.id
    </select>
    <select id="getPersonnelList" resultType="java.lang.String">
        select id
        from blade_personnel per
        where per.is_deleted = 0
    </select>
    <select id="getPersonnelCustomer"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select t1.personnelId, t1.userId, t1.userName, t2.oughtClient, t1.actualClient
        from (
                 select per.id                    personnelId,
                        per.user_id               userId,
                        per.user_name             userName,
                        count(alluser.customerId) actualClient
                 from blade_personnel per
                          join customer_all_user alluser
                               on alluser.userId = per.user_id
                 where per.is_deleted = 0
                 group by per.user_id
             ) t1
                 join
             (select t2.userId, t2.userName, t2.oughtClient
              from (
                       select per.id          personnelId,
                              per.user_id     userId,
                              per.user_name   userName,
                              sum(market.num) oughtClient,
                              1               type
                       from blade_personnel per
                                join blade_personnel_market market
                                     on market.personnel_id = per.id
                       where per.is_deleted = 0
                       group by per.id
                   ) t2) t2
             on t1.userId = t2.userId
    </select>
    <select id="getPersonnelUrbanCustomer"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select t1.personnelId,
               t1.areaId areaCode,
               t1.areaName,
               t1.oughtClient                                                 ,
               case when t2.actualClient is null then 0 else actualClient end actualClient
        from (
                 select personnelId, areaId, areaName, oughtClient, max(type)
                 from (
                          select per.id          personnelId,
                                 area.code       areaId,
                                 area.name       areaName,
                                 sum(market.num) oughtClient,
                                 1               type
                          from blade_personnel per
                                   join blade_personnel_market market
                                        on market.personnel_id = per.id and market.is_deleted = 0
                                   join blade_area area
                                        on area.code = left (market.area_id, 4)
                 where per.id = #{personnelId}
                   and per.is_deleted = 0
                 group by area.code
                 union all
                 select per.id personnelId, area.code areaId, area.name areaName, 0 oughtClient, 0 type
                 from customer_all_data alldata
                          join blade_area area
                               on area.code = alldata.urbanArea
                          join blade_personnel per
                               on per.user_id = alldata.userId
                 where per.id = #{personnelId}
                 group by area.code
             ) t1
        group by areaId) t1
left join
(select area.code,area.name,count(alluser.customerId) actualClient
from customer_all_user alluser
join blade_personnel per
on per.user_id = alluser.userId and per.is_deleted = 0
join blade_area area
on area.code = left(alluser.city,4)
where per.id = #{personnelId}
            group by area.code) t2
        on t1.areaId = t2.code
    </select>
    <select id="getPersonnelCityCustomer"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity">
        select t1.personnelId,
               t1.areaId areaCode,
               t1.areaName,
               t1.oughtClient                                                 ,
               case when t2.actualClient is null then 0 else actualClient end actualClient
        from (
                 select personnelId, areaId, areaName, oughtClient, max(type)
                 from (
                          select per.id          personnelId,
                                 area.code       areaId,
                                 area.name       areaName,
                                 sum(market.num) oughtClient,
                                 1               type
                          from blade_personnel per
                                   join blade_personnel_market market
                                        on market.personnel_id = per.id and market.is_deleted = 0
                                   join blade_area area
                                        on area.code = market.area_id
                          where per.id = #{personnelId}
                            and per.is_deleted = 0
                          group by area.code
                          union all
                          select per.id personnelId, area.code areaId, area.name areaName, 0 oughtClient, 0 type
                          from customer_all_data alldata
                                   join blade_area area
                                        on area.code = alldata.city
                                   join blade_personnel per
                                        on per.user_id = alldata.userId
                          where per.id = #{personnelId}
                          group by area.code
                      ) t1
                 where left (areaId, 4) = #{areaCode}
                 group by areaId) t1
                 left join
             (select area.code, area.name, count(alluser.customerId) actualClient
              from customer_all_user alluser
                       join blade_personnel per
                            on per.user_id = alluser.userId and per.is_deleted = 0
                       join blade_area area
                            on area.code = alluser.city
              where per.id = #{personnelId}
                and left (area.code
                  , 4) = #{areaCode}
              group by area.code) t2
             on t1.areaId = t2.code;
    </select>
</mapper>
