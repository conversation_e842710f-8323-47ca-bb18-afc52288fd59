/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractMainDTO;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;
import org.springblade.modules.lankegroup.contractManagement.service.IContractMainService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractMainHeadVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractMainVO;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 合同主表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cm/contractmain")
@Api(value = "合同主表【晶莱】", tags = "合同主表接口【晶莱】")
public class ContractMainController extends BladeController {

    private final IContractMainService contractMainService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true)
    })
    public R<ContractMainVO> detail(ContractMain contractMain) {
        if (Func.isEmpty(contractMain.getId())) {
            return R.fail("参数不完整");
        }
        ContractMainVO vo = contractMainService.detailModel(contractMain);
        return R.data(vo);
    }

    /**
     * 跟进记录合同下拉框 合同主表
     */
    @GetMapping("/pageByCustomerVisit")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "跟进记录合同下拉框", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "每页数量", required = true),
            @ApiImplicitParam(name = "current", value = "当前页", required = true),
            @ApiImplicitParam(name = "name", value = "合同名称", required = true),
    })
    public R<IPage<ContractMainVO>> pageByCustomerVisit(ContractMainDTO dto, Query query) {
        IPage<ContractMainVO> pages = contractMainService.selectContractMainPageByCustomerVisit(Condition.getPage(query), dto);
        return R.data(pages);
    }

    /**
     * 自定义分页 合同主表
     */
    @PostMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "每页数量", required = true),
            @ApiImplicitParam(name = "current", value = "当前页", required = true),
            @ApiImplicitParam(name = "timeType", value = "时间类型 1.创建时间；2.签约时间", required = true),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true),
            @ApiImplicitParam(name = "createUserParam", value = "创建人搜索条件 直接选择ID或者我的（myself）或者下属的（subordinate）", required = true),
            @ApiImplicitParam(name = "name", value = "合同名称", required = true),
            @ApiImplicitParam(name = "status", value = "审批状态(0:待审批,1:审批中,2:已驳回,3:已撤回,4:已通过)", required = true),
            @ApiImplicitParam(name = "draftStatus", value = "草稿状态(0:草稿,1:非草稿)", required = true),
    })
    public R<IPage<ContractMainVO>> page(@RequestBody ContractMainDTO dto, @RequestBody Query query) {
        IPage<ContractMainVO> pages = contractMainService.selectContractMainPage(Condition.getPage(query), dto);
        return R.data(pages);
    }

    /**
     * 自定义分页的头部统计 合同主表
     */
    @PostMapping("/pageHeadStatistics")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页头部统计", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间类型 1.创建时间；2.签约时间", required = true),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true),
            @ApiImplicitParam(name = "createUserParam", value = "创建人搜索条件 直接选择ID或者我的（myself）或者下属的（subordinate）", required = true),
            @ApiImplicitParam(name = "name", value = "合同名称", required = true),
            @ApiImplicitParam(name = "status", value = "审批状态(0:待审批,1:审批中,2:已驳回,3:已撤回,4:已通过)", required = true),
    })
    public R<List<ContractMainHeadVO>> pageHeadStatistics(@RequestBody ContractMainDTO dto, @RequestBody Query query) {
        List<ContractMainHeadVO> pages = contractMainService.pageHeadStatistics(Condition.getPage(query), dto);
        return R.data(pages);
    }

    /**
     * 新增（保存草稿） 合同主表
     */
    @PostMapping("/saveByDraft")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增（保存草稿） ", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "category", value = "合同类型(1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)", required = true),
            @ApiImplicitParam(name = "name", value = "合同名称", required = true),
            @ApiImplicitParam(name = "businessOpportunityId", value = "商机ID", required = true),
            @ApiImplicitParam(name = "businessOpportunityName", value = "商机名称", required = true),
            @ApiImplicitParam(name = "businessType", value = "业务类型", required = true),
            @ApiImplicitParam(name = "signMethod", value = "签约方式 客户签约，机构签约", required = true),
            @ApiImplicitParam(name = "signDate", value = "签约日期", required = true),
            @ApiImplicitParam(name = "amount", value = "合同总金额", required = true),
            @ApiImplicitParam(name = "amountText", value = "合同金额大写", required = true),
            @ApiImplicitParam(name = "endDate", value = "截止日期、交货日期", required = true),
            @ApiImplicitParam(name = "customerContactId", value = "客户ID", required = true),
            @ApiImplicitParam(name = "customerContactName", value = "客户名称", required = true),
            @ApiImplicitParam(name = "customerContactPhone", value = "客户电话", required = true),
            @ApiImplicitParam(name = "customerId", value = "机构ID", required = true),
            @ApiImplicitParam(name = "customerName", value = "机构名称", required = true),
            @ApiImplicitParam(name = "customerAddress", value = "机构通讯地址", required = true),
            @ApiImplicitParam(name = "partyBId", value = "乙方ID", required = true),
            @ApiImplicitParam(name = "partyBName", value = "乙方名称", required = true),
            @ApiImplicitParam(name = "partyBAddress", value = "乙方通讯地址", required = true),
            @ApiImplicitParam(name = "partyBPerson", value = "乙方联系人", required = true),
            @ApiImplicitParam(name = "partyBPhone", value = "乙方电话", required = true),
            @ApiImplicitParam(name = "sealType", value = "用章类型：合同章、公章、法人章、财务章", required = true)
    })
    public R<Kv> saveByDraft(@Valid @RequestBody ContractMainDTO dto) {
        return contractMainService.saveByDraft(dto);
    }

    /**
     * 新增（下一步） 合同主表
     */
    @PostMapping("/saveByNext")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增（下一步） ", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "category", value = "合同类型(1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)", required = true),
            @ApiImplicitParam(name = "name", value = "合同名称", required = true),
            @ApiImplicitParam(name = "businessOpportunityId", value = "商机ID", required = true),
            @ApiImplicitParam(name = "businessOpportunityName", value = "商机名称", required = true),
            @ApiImplicitParam(name = "businessType", value = "业务类型", required = true),
            @ApiImplicitParam(name = "signMethod", value = "签约方式 客户签约，机构签约", required = true),
            @ApiImplicitParam(name = "signDate", value = "签约日期", required = true),
            @ApiImplicitParam(name = "amount", value = "合同总金额", required = true),
            @ApiImplicitParam(name = "amountText", value = "合同金额大写", required = true),
            @ApiImplicitParam(name = "endDate", value = "截止日期、交货日期", required = true),
            @ApiImplicitParam(name = "customerContactId", value = "客户ID", required = true),
            @ApiImplicitParam(name = "customerContactName", value = "客户名称", required = true),
            @ApiImplicitParam(name = "customerContactPhone", value = "客户电话", required = true),
            @ApiImplicitParam(name = "customerId", value = "机构ID", required = true),
            @ApiImplicitParam(name = "customerName", value = "机构名称", required = true),
            @ApiImplicitParam(name = "customerAddress", value = "机构通讯地址", required = true),
            @ApiImplicitParam(name = "partyBId", value = "乙方ID", required = true),
            @ApiImplicitParam(name = "partyBName", value = "乙方名称", required = true),
            @ApiImplicitParam(name = "partyBAddress", value = "乙方通讯地址", required = true),
            @ApiImplicitParam(name = "partyBPerson", value = "乙方联系人", required = true),
            @ApiImplicitParam(name = "partyBPhone", value = "乙方电话", required = true),
            @ApiImplicitParam(name = "sealType", value = "用章类型：合同章、公章、法人章、财务章", required = true)
    })
    public R<Kv> saveByNext(@Valid @RequestBody ContractMainDTO dto) {
        return contractMainService.saveByNext(dto);
    }

    /**
     * 保存并发起审批 合同主表
     */
    @PostMapping("/initiateAnApproval")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "保存并发起审批", notes = "传入contractMain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "合同ID", required = true)
    })
    public R initiateAnApproval(@Valid @RequestBody ContractMainDTO dto) {
        return R.status(contractMainService.initiateAnApproval(dto));
    }


//    /**
//     * 删除 合同主表
//     */
//    @PostMapping("/remove")
//    @ApiOperationSupport(order = 7)
//    @ApiOperation(value = "逻辑删除", notes = "传入ids")
//    public R remove(@ApiParam(value = "合同ID", required = true) @RequestParam Long id) {
//        return R.status(contractMainService.removeModel(id));
//    }

    /**
     * 下载
     */
    @GetMapping("download")
    public void download(@RequestParam String url, @RequestParam String name, HttpServletResponse response) {
        contractMainService.download(url, name, response);
    }


}
