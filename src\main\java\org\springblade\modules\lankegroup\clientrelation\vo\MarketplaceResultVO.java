package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

@Data
public class MarketplaceResultVO {

    /**
     * 区划编码
     */
    private String areaCode;

    /**
     * 区划名称
     */
    private String areaName;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 行业编码
     */
    private String marketCode;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 是否多个
     */
    private boolean isNum;

    /**
     * 是否有数据
     */
    private boolean ywData;

    /**
     * 区域+行业唯一编码
     */
    private String areaAddMarket;

}
