//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class QueryRequestBodyObject extends RequestBodyObject {
    String beginmethod = "BeginQueryImpl";
    String querymethod = "QueryAsyncResult";

    QueryRequestBodyObject(Object[] parameters) {
        super(parameters);
    }

    public String getBeginmethod() {
        return this.beginmethod;
    }

    public void setBeginmethod(String beginmethod) {
        this.beginmethod = beginmethod;
    }

    public String getQuerymethod() {
        return this.querymethod;
    }

    public void setQuerymethod(String querymethod) {
        this.querymethod = querymethod;
    }
}
