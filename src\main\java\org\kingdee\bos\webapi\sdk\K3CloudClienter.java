//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class K3CloudClienter extends WebApiClient {

    private final static Logger log = LoggerFactory.getLogger(K3CloudClienter.class);

    boolean inited = false;
    Context ctx;

    K3CloudClienter() {
    }


    K3CloudClienter(String serverUrl) {
        super(serverUrl);
    }


    public void initIdentify(String serverUrl, String dcID, String userName, String appId, String appSecret) {
        this.identify.setServerUrl(serverUrl);
        this.identify.setdCID(dcID);
        this.identify.setUserName(userName);
        this.identify.setAppId(appId);
        this.identify.setAppSecret(appSecret);
    }


    protected void onPreExecute(String serviceMethod) {
    }

    CommonConfig getCommonConfig() {
        return (CommonConfig)this.execute("Kingdee.BOS.ServiceFacade.ServicesStub.ClienterService.GetServerCommonConfig", (Object[])null, CommonConfig.class);
    }

    List<DataCenter> getDataCenters() {
        Type tp = (new TypeToken<ArrayList<DataCenter>>() {
        }).getType();
        return this.execute("Kingdee.BOS.ServiceFacade.ServicesStub.Account.AccountService.GetDataCenterList", (Object[])null, tp);
    }

    public boolean loginByAppSecret() {
        System.out.println(this.identify.getAppId());
        System.out.println(this.identify.getUserName());
        Object[] loginInfo = new Object[]{this.identify.getdCID(), this.identify.getUserName(), this.identify.getAppId(), this.identify.getAppSecret(), this.identify.getlCID()};
        LoginResult ret = (LoginResult)this.execute("Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginByAppSecret", loginInfo, LoginResult.class);
        log.info("loginByAppSecret = " + JSON.toJSONString(ret));
        this.ctx = ret.getContext();
        if (ret.getIsSuccessByAPI()) {
            System.out.println("Login successfully!");
            return ret.getIsSuccessByAPI();
        } else {
            System.out.println(ret.getMessage());
            return false;
        }
    }
}
