package org.springblade.flow.core.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 自定义工作流实体，仿钉钉样式显示工作流
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/8 11:37
 */
@Data
public class WorkFlow implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 任务编号
	 */
	@ApiModelProperty(value = "任务编号")
	private String taskId;
	/**
	 * 任务名称
	 */
	@ApiModelProperty(value = "任务名称")
	private String taskName;
	/**
	 * 任务执行人编号
	 */
	@ApiModelProperty(value = "任务执行人编号")
	private String assignee;
	/**
	 * 任务执行人名称
	 */
	@ApiModelProperty(value = "任务执行人名称")
	private String assigneeName;
	/**
	 * 任务执行人头像
	 */
	@ApiModelProperty(value = "任务执行人头像")
	private String assigneeAvatar;
	/**
	 * 任务执行人在职状态
	 */
	@ApiModelProperty(value = "任务执行人在职状态")
	private Integer assigneeIsDeleted;
	/**
	 * 禁用状态
	 */
	@ApiModelProperty(value = "任务执行人禁用状态")
	private Integer assigneeStatus;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date endTime;
	/**
	 * 签收时间
	 */
	@ApiModelProperty(value = "签收时间")
	private Date claimTime;

	@ApiModelProperty(value = "历史活动id")
	private String historyActivityId;

	@ApiModelProperty(value = "历史节点类型")
	private String historyActivityType;
//	/**
//	 * 历史任务结束时间
//	 */
//	@ApiModelProperty(value = "历史任务结束时间")
//	private Date historyTaskEndTime;
	/**
	 * 执行ID
	 */
	@ApiModelProperty(value = "执行ID")
	private String executionId;
//	/**
//	 * 流程实例ID
//	 */
//	@ApiModelProperty(value = "流程实例ID")
//	private String processInstanceId;
	/**
	 * 流程ID
	 */
	@ApiModelProperty(value = "流程ID")
	private String processDefinitionId;
//	/**
//	 * 流程标识
//	 */
//	@ApiModelProperty(value = "流程标识")
//	private String processDefinitionKey;
//	/**
//	 * 流程名
//	 */
//	@ApiModelProperty(value = "流程名")
//	private String processDefinitionName;
	/**
	 * 任务状态
	 */
	@ApiModelProperty(value = "任务节点状态对应FlowCommentEnum枚举")
	private String status;
	/**
	 * 任务意见
	 */
	@ApiModelProperty(value = "任务意见")
	private String comment;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "并行审批人列表/抄送人列表")
	private List<Approver> approverList;
	/**
	 * 是否多实例审批(0串行会签 1 并行会签 2 或签)
	 */
	@ApiModelProperty(value = "0串行会签 1 并行会签 2 或签")
	private Integer multipleApprovalType;
	/**
	 * 记录类型 转交、加签的信息 0.转交；1.前加签；2.后加签
	 */
	@ApiModelProperty(value = "记录类型 转交、加签的信息 0.转交；1.前加签；2.后加签")
	private Integer recordType;

	/**
	 * 单选审批人
	 */
	private Boolean singleSelectedUser;

	/**
     * 发起人可选择的用户列表列表
     */
	private List<ApproverUser> sponsorUserList;


	@Data
	public static class Approver {
		private String taskId;
		/**
		 * 任务执行人编号
		 */
		private String assignee;
		/**
		 * 任务执行人名称
		 */
		private String assigneeName;
		/**
		 * 任务执行人头像
		 */
		private String assigneeAvatar;

		private Integer assigneeIsDeleted;

		private Integer assigneeStatus;
	}

	//抄送人前端组件兼容性改的名字
	@Data
	public static class ApproverUser {
		/**
		 * 任务执行人编号
		 */
		private String id;
		/**
		 * 任务执行人名称
		 */
		private String name;
		/**
		 * 任务执行人头像
		 */
		private String avatar;
	}

}
