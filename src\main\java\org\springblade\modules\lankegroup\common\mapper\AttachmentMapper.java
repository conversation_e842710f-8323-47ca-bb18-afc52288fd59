/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.mapper;

import org.springblade.modules.lankegroup.common.entity.Attachment;
import org.springblade.modules.lankegroup.common.vo.AttachmentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 通用附件 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface AttachmentMapper extends BaseMapper<Attachment> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attachment
	 * @return
	 */
	List<AttachmentVO> selectAttachmentPage(IPage page, @Param("query") AttachmentVO attachment);

}
 