//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class WebApiClient {
    protected IdentifyInfo identify;
    K3CloudCookieStore cookier;
    int connectTimeout;
    int requestTimeout;
    int stockTimeout;

    public IdentifyInfo getIdentify() {
        return identify;
    }

    public WebApiClient() {
        this("");
    }

    public WebApiClient(String serverUrl) {
        this((new IdentifyInfo()).setServerUrl(serverUrl));
    }

    public WebApiClient(String serverUrl, int timeout) {
        this((new IdentifyInfo()).setServerUrl(serverUrl));
        this.requestTimeout = timeout;
    }

    public WebApiClient(IdentifyInfo identify) {
        this.connectTimeout = 120;
        this.requestTimeout = 120;
        this.stockTimeout = 180;
        if (identify != null) {
            this.identify = identify;
        } else {
            this.identify = new IdentifyInfo();
        }

        AppCfg cfg = CfgUtil.getAppDefaultCfg();
        if (cfg != null) {
            this.identify.setAppId(cfg.getAppId());
            this.identify.setAppSecret(cfg.getAppSecret());
            this.identify.setdCID(cfg.getdCID());
            this.identify.setLcid(cfg.getlCID());
            this.identify.setOrgNum(cfg.getOrgNum());
            this.identify.setUserName(cfg.getUserName());
            if (this.identify.getServerUrl() == null || this.identify.getServerUrl().length() == 0) {
                this.identify.setServerUrl(cfg.getServerUrl());
                if (this.identify.getServerUrl() == null || this.identify.getServerUrl().length() == 0) {
                    this.identify.setServerUrl("https://api.kingdee.com/galaxyapi/");
                }
            }

            if (cfg.getConnectTimeout() > 0) {
                this.connectTimeout = cfg.getConnectTimeout();
            }

            if (cfg.getRequestTimeout() > 0) {
                this.requestTimeout = cfg.getRequestTimeout();
            }

            if (cfg.getStockTimeout() > 0) {
                this.stockTimeout = cfg.getStockTimeout();
            }
        }

    }

    protected void onPreExecute(String serviceMethod) {
    }

    protected void preExecute() {
        if (this.cookier == null) {
            this.cookier = new K3CloudCookieStore();
        }

    }

    protected void afterExecute(String json, K3CloudCookieStore cookier) {
        if (cookier != null) {
            this.cookier = cookier;
        }

    }

    Gson builderGson() {
        GsonBuilder builder = (new GsonBuilder()).setDateFormat("yyyy-MM-dd HH:mm:ss");
        return builder.create();
    }

    RequestBodyObject genrateBody(Object[] parameters, InvokeMode type) {
        RequestBodyObject json = null;
        if (type == InvokeMode.Query) {
            json = new QueryRequestBodyObject(parameters);
        } else {
            json = new RequestBodyObject(parameters);
        }

        return (RequestBodyObject)json;
    }

    public String execute(String serviceName, Object[] parameters) {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                } else {
                    throw new Exception(ex.getMessage());
                }
            } else {
                return json;
            }
        } catch (Exception var5) {
            var5.getStackTrace();
            return null;
        }
    }

    public <T> List<T> execute(String serviceName, Object[] parameters, Type type) {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            System.out.println("json = "+ json);
            Gson gson = this.builderGson();
            return (List)gson.fromJson(json, type);
        } catch (Exception var6) {
            var6.getStackTrace();
            return null;
        }
    }

    public <T> T execute(String serviceName, Object[] parameters, Class<T> cls) {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                } else {
                    throw new Exception(ex.getMessage());
                }
            } else {
                Gson gson = new Gson();
                System.out.println(json);
                return gson.fromJson(json, cls);
            }
        } catch (Exception var6) {
            var6.getStackTrace();
            return null;
        }
    }

    String executeByQuery(String serviceName, Object[] parameters) throws Exception {
        try {
            String json = this.doExecuteJson(serviceName, parameters, InvokeMode.Query);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                } else {
                    throw new Exception(ex.getMessage());
                }
            } else {
                Gson gson = new Gson();
                QueryResultInfo taskInfo = (QueryResultInfo)gson.fromJson(json, QueryResultInfo.class);
                return taskInfo.getStatus() == 2 ? gson.toJson(taskInfo.getResult()) : gson.toJson(this.queryTaskResult(serviceName, new QueryTaskParam(taskInfo.getTaskId(), false), 5));
            }
        } catch (Exception var6) {
            var6.getStackTrace();
            throw var6;
        }
    }

    Object queryTaskResult(String serviceName, QueryTaskParam param, int failTime) throws Exception {
        try {
            Thread.sleep(1000L);
            int index = serviceName.lastIndexOf(".");
            String qService = serviceName.substring(0, index) + "." + "QueryAsyncResult";
            String json = this.doExecuteJson(qService, new Object[]{param}, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                if (failTime > 0) {
                    return this.queryTaskResult(serviceName, param, failTime - 1);
                } else {
                    KDException ex = KDException.parse(json);
                    if (ex == null) {
                        throw new Exception(json);
                    } else {
                        throw new Exception(ex.getMessage());
                    }
                }
            } else {
                Gson gson = new Gson();
                QueryResultInfo taskInfo = (QueryResultInfo)gson.fromJson(json, QueryResultInfo.class);
                return taskInfo.getStatus() == 2 ? taskInfo.getResult() : this.queryTaskResult(serviceName, param, 5);
            }
        } catch (Exception var9) {
            if (failTime > 0) {
                return this.queryTaskResult(serviceName, param, failTime - 1);
            } else {
                throw var9;
            }
        }
    }

    public String executeJson(String serviceName, Object[] parameters, InvokeMode type) throws Exception {
        if (type == InvokeMode.Query) {
            return this.executeByQuery(serviceName, parameters);
        } else if (type == InvokeMode.Syn) {
            return this.doExecuteJson(serviceName, parameters, type);
        } else {
            throw new Exception("Not suppoer yet,for InvokeMode:" + type.toString());
        }
    }

    String doExecuteJson(String serviceName, Object[] parameters, InvokeMode type) throws Exception {
        this.preExecute();
        this.onPreExecute(serviceName);
        String url = this.identify.getServerUrl();
        if (!url.endsWith("/")) {
            url = url + "/";
        }

        url = url + serviceName + ".common.kdsvc";
        System.out.println("url = " + url);
        ApiRequester req = null;
        if (type == InvokeMode.Query) {
            req = new ApiQueryRequester(url);
        } else {
            req = new ApiRequester(url);
        }

        ((ApiRequester)req).setConnectTimeout(this.connectTimeout);
        ((ApiRequester)req).setConnectionRequrestTimeout(this.requestTimeout);
        ((ApiRequester)req).setSocketTimeout(this.stockTimeout);
        ((ApiRequester)req).setIdentify(this.identify);
        ((ApiRequester)req).setCookieStore(this.cookier);

        try {
            String json = ((ApiRequester)req).postJson(this.genrateBody(parameters, type));
            this.afterExecute(json, ((ApiRequester)req).getCookieStore());
            return json;
        } catch (Exception var7) {
            this.afterExecute((String)null, ((ApiRequester)req).getCookieStore());
            var7.getStackTrace();
            throw var7;
        }
    }

    protected List<Map> loadDataList(String fieldKeys, String json) {
        List<Map> rets = new ArrayList();
        Gson gson = new Gson();
        String[] fields = fieldKeys.split(",");
        List<ArrayList<Object>> rows = (List)gson.fromJson(json, rets.getClass());
        for( int i = 0; i < rows.size(); i++ ) {
            ArrayList<Object> row = rows.get(i);
            Map<String,Object> rowMap = new HashMap<>();
            for ( int j = 0; j < row.size(); j++) {
                String key = StringUtils.trim(fields[j]);
                Object value = row.get(j);
                System.out.println("key " + key + ", value = " + value + ", type = " + value.getClass().getName());
                if (key.indexOf(":") > -1) {
                    String[] arr = key.split(":");
                    key = arr[0];
                    if ("int".equals(arr[1]) && "java.lang.Double".equals(value.getClass().getName())) {
                        value = ((Double) value).intValue();
                    }else if ("int".equals(arr[1]) && "java.lang.String".equals(value.getClass().getName())) {
                        value = Integer.parseInt((String) value);
                    } else if ("date".equals(arr[1])) {
                        String dateStr = ((String)value).replace("T", " ");
                    }
                }
                rowMap.put(key, value);

            }
            rets.add(rowMap);
        }

        return rets;
    }

    protected <T> List<T> loadDataList(String fieldKeys, Class type, String json) throws InstantiationException, IllegalAccessException, InvocationTargetException {
        List<T> rets = new ArrayList();
        Gson gson = new Gson();
        List<ArrayList<Object>> rows = (List)gson.fromJson(json, rets.getClass());
        Method[] pes = type.getMethods();
        String[] fields = fieldKeys.split(",");
        Method[] setPes = this.getMethodsByFields(pes, "set", fields);
        Method[] getPes = this.getMethodsByFields(pes, "get", fields);
        Iterator var12 = rows.iterator();

        while(var12.hasNext()) {
            List<Object> darray = (List)var12.next();
            T ret = (T) type.newInstance();

            for(int i = 0; i < fields.length; ++i) {
                if (setPes[i] != null && getPes[i] != null) {
                    Object v = this.convertToDest(getPes[i].getReturnType(), darray.get(i));
                    if (v != null) {
                        setPes[i].invoke(ret, v);
                    }
                }
            }

            rets.add(ret);
        }

        return rets;
    }

    String loadErrorMsg(String json) {
        if (json.startsWith("[[") && json.endsWith("]]")) {
            Gson gson = new Gson();
            return ((RepoError)((RepoRet)gson.fromJson(json.substring(2, json.length() - 2), RepoRet.class)).getResult().getResponseStatus().getErrors().get(0)).getMessage();
        } else {
            return String.format("fail for ExecuteBillQuery:%s", json);
        }
    }

    Method[] getMethodsByFields(Method[] pes, String pre, String[] fields) {
        Method[] rets = new Method[fields.length];

        for(int i = 0; i < fields.length; ++i) {
            for(int j = 0; j < pes.length; ++j) {
                if (pes[j].getName().toLowerCase().equals(pre + fields[i].toLowerCase())) {
                    rets[i] = pes[j];
                }
            }
        }

        return rets;
    }

    Method getMethodFromT(Method[] pes, String pname) {
        for(int i = 0; i < pes.length; ++i) {
            if (pes[i].getName().toLowerCase().equals(pname.toLowerCase())) {
                return pes[i];
            }
        }

        return null;
    }

    Object convertToDest(Type type, Object val) {
        if (val == null) {
            return null;
        } else if (type.getTypeName().equals(String.class.getTypeName())) {
            return val.toString();
        } else if (!type.getTypeName().equals(Integer.TYPE.getTypeName()) && !type.getTypeName().equals(Short.TYPE.getTypeName()) && !type.getTypeName().equals(Long.TYPE.getTypeName())) {
            if (type.getTypeName().equals(BigDecimal.class.getTypeName())) {
                return new BigDecimal(val.toString());
            } else if (type.getTypeName().equals(Date.class.getTypeName())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

                try {
                    return sdf.parse(val.toString());
                } catch (ParseException var5) {
                    var5.printStackTrace();
                    return null;
                }
            } else {
                return val;
            }
        } else {
            String v = val.toString();
            if (v.toString().indexOf(".") > -1) {
                v = v.substring(0, v.toString().indexOf("."));
            }

            return new Integer(v.toString());
        }
    }
}
