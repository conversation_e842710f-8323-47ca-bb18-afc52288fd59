/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同表变更记录日志实体类
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@TableName("blade_contract_change_log")
@EqualsAndHashCode(callSuper = true)
public class ContractChangeLog extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 描述原因
	*/
		private String description;
	/**
	 * 合同id
	 */
	private Long contractId;
	/**
	 * 合同变更/合同作废操作id
	 */
	private Long contractChangeId;


}
