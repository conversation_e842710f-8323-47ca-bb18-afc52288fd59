package org.springblade.flow.engine.listener.flowable;

import lombok.AllArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 或签完成的条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 17:37
 */
@Component("OneSignMultiInstance")
@AllArgsConstructor
public class OneSignMultiInstanceCompleteTask implements Serializable {

    private final HistoryService historyService;

    /**
     * 评估结果判定条件
     *
     * @param execution 分配执行实例
     */
    public boolean accessCondition(DelegateExecution execution) {
        // 获取流程变量时增加空值检查
        Object passObj = execution.getVariable("pass");
        if (passObj == null) {
            return false;
        }
        // 确保变量类型正确
        if (!(passObj instanceof Boolean)) {
            throw new IllegalArgumentException("Expected Boolean type for 'pass' variable, but got " + passObj.getClass());
        }
        return (Bo<PERSON>an) passObj;
    }

}
