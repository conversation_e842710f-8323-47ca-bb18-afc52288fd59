package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 市场
 */
@Data
@TableName("blade_profession")
public class Profession extends BaseEntity {

    private Long id;

    /**
     * 市场名称
     */
    private String professionName;

    /**
     * 负责人ID
     */
    private String chargeId;

    /**
     * 负责人姓名
     */
    private String chargeName;

    /**
     * 负责部门ID
     */
    private String chargeDeptId;

    /**
     * 负责部门名称
     */
    private String chargeDeptName;

    /**
     * 应有客户
     */
    private Integer oughtClient;

    /**
     * 实有客户
     */
    private Integer actualClient;

}
