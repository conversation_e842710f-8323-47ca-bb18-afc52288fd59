package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

/**
 * 产值分析
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 16:15
 */
@Data
public class OutputListVO {
    private Long id;

    /**
     * 行业名称
     */
    private String marketplacName;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 产值(项目合同额)
     */
    private Double contractAmount;

    /**
     * 项目类型
     */
    private String projectTypeId;
    private String projectType;


    /**
     * 排序
     */
    private Integer sort;

    private String projectId;

    private String marketId;

    private String kdProjectId;

    /**
     * 用于业务逻辑当中的分组
     */
    private String groupServiceId;
}
