package org.springblade.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springblade.core.tool.support.Kv;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import java.net.URL;
import java.time.Duration;

/**
 * http
 */
@Slf4j
public class HttpUtil {

    /**
     * POST
     * @param url
     * @param header
     * @param body
     * @return
     */
    public static JSONObject sendHttpRequestByPost(String url, Kv header, Kv body) {
        log.info("POST请求路径==============》" + url);
        log.info("POST请求体================》" + body);
        try {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(Duration.ofSeconds(60));
            builder.readTimeout(Duration.ofSeconds(60));
            builder.writeTimeout(Duration.ofSeconds(60));
            OkHttpClient client = builder.build();
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONObject.toJSONString(body));
            Request.Builder requestBuilder = new Request.Builder().url(url).post(requestBody);
            if (null != header) {
                header.forEach((k,v) -> requestBuilder.addHeader(k, v.toString()));
            }
            Request request = requestBuilder.build();
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            log.info("POST请求返回===========》" + jsonObject);
            if (200 == jsonObject.getInteger("code")) {
                return jsonObject;
            }
            throw new BadCredentialsException(String.valueOf(jsonObject.get("msg")));
        } catch (Exception e) {
            throw new AccessDeniedException(e.getMessage());
        }
    }

    /**
     * GET
     * @param url
     * @param header
     * @param body
     * @return
     */
    public static JSONObject sendHttpRequestByGet(String url, Kv header, Kv body) {
        log.info("GET请求路径==============》" + url);
        log.info("GET请求体================》" + body);
        try {
            URL uri = new URL(url);
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(Duration.ofSeconds(10));
            builder.readTimeout(Duration.ofSeconds(10));
            builder.writeTimeout(Duration.ofSeconds(10));
            OkHttpClient client = builder.build();
            Request.Builder request = new Request.Builder().url(uri);
            if (null != header) {
                header.forEach((k,v) -> request.addHeader(k, v.toString()));
            }
            Response response = client.newCall(request.build()).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            log.info("GET请求返回===========》" + jsonObject);
            if (200 == jsonObject.getInteger("code")) {
                return jsonObject;
            }
            throw new BadCredentialsException(String.valueOf(jsonObject.get("msg")));
        } catch (Exception e) {
            throw new AccessDeniedException(e.getMessage());
        }
    }

    /**
     * PUT
     * @param url
     * @param header
     * @param body
     * @return
     */
    public static JSONObject sendHttpRequestByPut(String url, Kv header, Kv body) {
        try {
            log.info("POST请求路径==============》" + url);
            log.info("POST请求体================》" + body);
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(Duration.ofSeconds(10));
            builder.readTimeout(Duration.ofSeconds(10));
            builder.writeTimeout(Duration.ofSeconds(10));
            OkHttpClient client = builder.build();
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONObject.toJSONString(body));
            Request.Builder requestBuilder = new Request.Builder().url(url).put(requestBody);
            if (null != header) {
                header.forEach((k,v) -> requestBuilder.addHeader(k, v.toString()));
            }
            Request request = requestBuilder.build();
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            log.info("PUT请求返回===========》" + jsonObject);
            if (200 == jsonObject.getInteger("code")) {
                return jsonObject;
            }
            throw new BadCredentialsException(String.valueOf(jsonObject.get("msg")));
        } catch (Exception e) {
            throw new AccessDeniedException(e.getMessage());
        }
    }
}
