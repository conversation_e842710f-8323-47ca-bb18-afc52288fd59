/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.CustomerCare;
import org.springblade.modules.lankegroup.crm.vo.CustomerCareVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.ConcentUser;
import org.springblade.modules.lankegroup.crm.vo.ConcentUserVO;
import org.springblade.modules.lankegroup.crm.wrapper.ConcentUserWrapper;
import org.springblade.modules.lankegroup.crm.service.IConcentUserService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 客户联系人共享关联表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/concent/concentuser")
@Api(value = "客户联系人共享关联表", tags = "客户联系人共享关联表接口")
public class ConcentUserController extends BladeController {

	private final IConcentUserService concentUserService;

	/**
	 * 新增 客户联系人关联人员
	 */
	@ApiLog("新增客户联系人关联人员")
	@PostMapping("/insert")
	public R save(@Valid @RequestBody ConcentUserVO concentUser){
		return R.status(concentUserService.insertRepeatable(concentUser));
	}


	/**
	 * 查询  客户联系人关联人员
	 */
	@ApiLog("查询客户联系人关联人员")
	@GetMapping("/selectAll")
	public R<List<ConcentUserVO>> detail(Long contactsId,Long principalId){
		return R.data(concentUserService.selectAllByContactsId(contactsId, principalId));
	}
}
