package org.springblade.common.enums;

/**
 * UNAPRROVED:未审批 <br>
 * APPROVING:审批中  <br>
 * PASSED:已通过     <br>
 * REFUSED:已拒绝    <br>
 * WITHDRAWED:已撤销
 */
public enum ApprovedEnum {

    UNAPRROVED("未审批", -1),
    APPROVING("审批中", 0),
    PASSED("已审批", 1),
    REFUSED("已驳回", 2),
    ABORT("作废", 3),
    WITHDRAWED("已撤回", 4);


    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private ApprovedEnum(String name, int index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(int index) {
        for (ApprovedEnum c : ApprovedEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public int getIndex() {
        return index;
    }
}
