/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.AesUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * 销售合同
 * 采购合同
 * 合同管理表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/contract/con")
@Api(value = "合同管理表", tags = "合同管理表接口")
public class ContractController extends BladeController {

    private final IContractService contractService;
    private final ICustomerConnectLogService customerConnectLogService;
    private final FlowEngineService flowEngineService;
    private final ProjectBasicMapper projectBasicMapper;

    private final ContractMapper contractMapper;

    /**
     * 详情
     * 对查出的数据进行数据整理后，返回
     * id:合同id
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contract")
    public R<ContractVO> detail(Contract contract) {
        Contract detail = contractService.getOne(Condition.getQueryWrapper(contract));
        if (null != detail) {
            return R.data(contractService.tidyUpContract(detail));
        } else {
            return R.fail("合同已删除或撤回");
        }

    }

    @GetMapping("getFileIo")
    public ResponseEntity<byte[]> getFileIo(String httpUrl, String fileUrl) {
        try {
            String file = AesUtil.decryptBase64(Base64Util.decode(fileUrl));
            String urlAddr = httpUrl + file;
            URL url = new URL(urlAddr);
            URLConnection connection = url.openConnection();
            // 设置Blob的MIME类型
            String contentType = connection.getContentType();

            // 获取文件的二进制数据
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try (InputStream inputStream = connection.getInputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                }
            }
            byte[] blobData = byteArrayOutputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(file, StandardCharsets.UTF_8));
            headers.setContentType(MediaType.parseMediaType(contentType));

            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .body(blobData);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 分页 合同管理表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入contract")
    public R<IPage<Contract>> list(Contract contract, Query query) {
        IPage<Contract> pages = contractService.page(Condition.getPage(query), Condition.getQueryWrapper(contract));
        return R.data(pages);
    }

    /**
     * 自定义分页 合同管理表
     * 合同审批列表中包含：合同审批、合同归档审批、合同变更审批
     * 审批状态必传
     * pageType:  页面类型（待审批1、我发起的2、已审批3、我收到的4）
     * contractType:  合同类型（销售合同/采购合同/其他合同/渠道合同）
     * contractStatus:   合同状态
     * projectType:    项目类型
     * startTime:    合同筛选开始时间
     * endTime:    合同筛选结束时间
     * deptOrPerson:    人员/部门id
     * searchBox:   搜索框
     * personnel:   员/部门名称
     */
    @PostMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入contract")
    public R<IPage<ContractListResult>> page(@RequestBody ContractListParam param, @RequestBody Query query) {
//        IPage<Map> pages = contractService.selectContractPage(Condition.getPage(query), param);
        return R.data(contractService.selectContractPage(Condition.getPage(query), param));
    }

    /**
     * 新增 合同管理表
     * 新增合同表单时，需要注意，合同类型contractType（0销售合同/1采购合同/2其他合同）必须传递
     * 当为草稿时，合同的状态status需要定值6传递，存储完成后会返回草稿id，意为当前页再次提交或者存为草稿时将id传入避免数据重复
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入contract")
    public R save(@Valid @RequestBody ContractVO contract) {
        try {
            contract.setAnnex(AesUtil.decryptBase64(Base64Util.decode(contract.getAnnex())));
        } catch (Exception e) {

        }
//		先判断是否要发起流程，即是否要存为草稿
        if (contract.getStatus() != null && contract.getStatus() == 6) {
            Map map = contractService.saveDraft(contract);
            if (map.get("status").toString().equals("true")) {
                return R.data(map);
            } else {
                return R.fail(500, "草稿保存失败");
            }

        } else {
            //        先判断合同名称是否重复
            //20240604需求:销售合同/采购合同/其他合同/渠道合同，合同名称唯一校验去掉
//            if(StringUtil.isNotBlank(contract.getName())){
//                List<ContractVO> contractVOS =contractService.checkContractNameSole(contract.getName(),contract.getId());
//                if(contractVOS!=null&&contractVOS.size()>0){
//                    return R.fail(500,"合同名称已存在，请核对");
//                }
//            }
//            ProjectBasic projectBasic = projectBasicMapper.selectById(contract.getProjectId());
//            if ("0".equals(contract.getContractType()) && !(projectBasic.getContractingUnitId().longValue() == contract.getSignCompanyId().longValue())) {
//                return R.fail("甲方单位与项目客户不一致，请重新选择项目");
//            }
            if (contract.getSignCompanyManId() != null && !contract.getSignCompanyManId().isEmpty()) {
                String selectByContactId = contractMapper.selectByContactId(contract.getSignCompanyManId());
                if (selectByContactId == null || selectByContactId.equals("")) {
                    return R.fail("甲方联系人已删除或修改，请重新选择甲方联系人");
                }
                if (!(Long.valueOf(selectByContactId).longValue() == contract.getSignCompanyId().longValue())) {
                    return R.fail("甲方联系人不属于此甲方单位，请重新选择甲方联系人");
                }
            }
//            保存时的时间为提交时间
            contract.setCreateTime(new Date());
            String processId = "";
            // TODO 刘源兴工作流
//            // 如果是销售合同调用销售合同的流程，采购合同和其它合同还是用原来的流程
//            if (contract.getContractType().equals("0")) {
//                processId = flowEngineService.getProcessId("销售合同", "salesContract");
//            } else {
//                processId = flowEngineService.getProcessId("合同管理", "contract");
//            }
            if ("".equals(processId)) {
                return R.fail(500, "流程不存在");
            }

            contract.setProcessDefinitionId(processId);
            Map map = contractService.startProcess(contract);

            if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().isEmpty())) {
                return R.data(map);
            } else {
                return R.fail(500, "流程保存失败");
            }
        }
    }

    /**
     * 修改 合同管理表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入contract")
    public R update(@Valid @RequestBody Contract contract) {
        return R.status(contractService.updateById(contract));
    }

    /**
     * 新增或修改 合同管理表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入contract")
    public R submit(@Valid @RequestBody Contract contract) {
        return R.status(contractService.saveOrUpdate(contract));
    }


    /**
     * 删除 合同管理表
     * 合同仅允许删除被驳回、已撤回的单据
     * 删除时将操作写入动态表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        Contract contract = contractService.getById(ids);
        if (contract.getStatus() == 3 || contract.getStatus() == 2) {
            //                                    写入记录日志表
            CustomerConnectLog contractChangeLog = new CustomerConnectLog();
            switch (contract.getContractType()) {
                case "0":
                    contractChangeLog.setCustomerId(Long.valueOf(contract.getSignCompanyId()));
                    contractChangeLog.setAboutDescription("删除了销售合同");
                    contractChangeLog.setDescription("删除了销售合同");
                    break;
                case "2":
                    contractChangeLog.setCustomerId(Long.valueOf(contract.getSignCompanyId()));
                    contractChangeLog.setAboutDescription("删除了其他合同");
                    contractChangeLog.setDescription("删除了其他合同");
                    break;
                case "1":
                    contractChangeLog.setCustomerId(Long.valueOf(contract.getSupplierId()));
                    contractChangeLog.setAboutDescription("删除了采购合同");
                    contractChangeLog.setDescription("删除了采购合同");
                    break;
                case "3":
                    contractChangeLog.setCustomerId(Long.valueOf(contract.getSignCompanyId()));
                    contractChangeLog.setAboutDescription("删除了渠道合同");
                    contractChangeLog.setDescription("删除了渠道合同");
                    break;
            }
            contractChangeLog.setChangeTable(TableIdEnum.合同.getTableId());
            contractChangeLog.setContactId(contract.getId());
            contractChangeLog.setChargeUser(AuthUtil.getUserId());
            customerConnectLogService.save(contractChangeLog);

            return R.status(contractService.deleteLogic(Func.toLongList(ids)));
        } else {
            return R.fail(500, "只有驳回或撤回状态的合同可删除");
        }
    }

    /**
     * 初始化合同页面
     *
     * @param type 合同类型（0销售合同、1采购合同、2其他合同）
     * @param
     * @return 返回当前登陆人最近一次保存的草稿，合同的审批流程节点
     */
    @GetMapping("/initContract")
    public R initContract(String type) {
        return contractService.initContract(type);
    }

    /**
     * 合同筛选页面及列表
     *
     * @param param pageType:  页面类型（待审批1、我发起的2、已审批3、我收到的4）
     *              contractType:  合同类型（销售合同/采购合同/其他合同）
     *              status:   合同状态
     *              projectType:    项目类型
     *              startTime:    合同筛选开始时间
     *              endTime:    合同筛选结束时间
     *              deptOrPerson:    人员/部门
     * @return 符合条件的数据返回
     */
    @PostMapping("/screenList")
    public R screenList(@Valid @RequestBody ContractListParam param) {
        return R.data(contractService.contractFilter(param));
    }


    /**
     * 列表数量
     * 展示审批流程页面待审批统计、我发起的仅统计（已驳回、待归档的数据）
     */
    @GetMapping("/number")
    public R number(Long projectId) {
        return R.data(contractService.number(projectId));
    }

    /**
     * 合同列表
     * 根据合同名称模糊查询
     * 填写合同变更时要求合同列表展示：
     * 销售/其他合同展示待归档状态的合同【2023-10-11 销售/其他合同增加归档撤回、归档驳回两个状态】
     * 采购合同展示已归档项目
     *
     * @return
     */
    @GetMapping("/contractList")
    public R contractList(String contractName) {
        return R.data(contractService.contractList(contractName));
    }


    /**
     * 根据合同id查询单个合同
     * 列表详情（前端使用）
     *
     * @return
     */
    @GetMapping("/pageDetail")
    @ApiOperation(value = "合同页面详情", notes = "传入单据id")
    public R pageDetail(String contractId) {
        return R.data(contractService.pageDetail(contractId));
    }

}
