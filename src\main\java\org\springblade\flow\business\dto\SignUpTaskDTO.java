package org.springblade.flow.business.dto;

import lombok.Data;

/**
 * 晶莱加签 接参
 *
 * <AUTHOR>
 * @since 2025年06月18日 17:13
 **/
@Data
public class SignUpTaskDTO {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 加签用户，多个英文逗号分隔
     */
    private String userIds;
    /**
     * 加签理由
     */
    private String reason;
    /**
     * 加签方式 1.前加签；2.后加签
     */
    private Integer signType;
    /**
     * 审批方式 1.会签；2.或签
     */
    private Integer approvalType;


}
