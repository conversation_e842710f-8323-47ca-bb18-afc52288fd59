package org.springblade.modules.lankegroup.contractManagement.annotation;

import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.lankegroup.contractManagement.constant.ContractToWordConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 导出word自定义注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldToWord {

    /**
     * 类型 field(默认)、split、enums、date、book
     */
    String type() default ContractToWordConstant.FIELD;

    /**
     * ENUMS 下的 枚举类 默认值
     */
    Class<? extends Enum> enumClass() default Enum.class;

    /**
     * ENUMS 下的 附件标签名称，
     */
    String[] fieldName() default {};

    /**
     * ENUMS 下的 名称，使用{{dwmc}}、{{xtmc}}标签以备后续替换
     */
    String[] attachName() default {};

    /**
     * SPLIT 下的 分隔符，默认不填，按单个字符分隔；如abc->a,b,c
     */
    String split() default "";

    /**
     * SPLIT 下的 分隔成几份
     */
    int splitSum() default 0;

    /**
     * LOCALDATE 下的 日期格式化样式，默认为yyyy-MM-dd
     */
    String format() default "yyyy-MM-dd";

    /**
     * 默认值
     */
    String value() default StringPool.EMPTY;

}
