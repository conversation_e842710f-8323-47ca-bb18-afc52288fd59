/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 合同主表统计数据视图对象
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@ApiModel(value = "合同统计数据", description = "合同主表统计数据视图对象")
public class ContractMainStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计类型：业务类型
     */
    @ApiModelProperty(value = "统计类型")
    private String statisticsType;

    /**
     * 统计键
     */
    @ApiModelProperty(value = "统计键")
    private String statisticsKey;

    /**
     * 统计名称
     */
    @ApiModelProperty(value = "统计名称")
    private String statisticsName;

    /**
     * 统计数量
     */
    @ApiModelProperty(value = "统计数量")
    private Integer count;

    /**
     * 统计金额
     */
    @ApiModelProperty(value = "统计金额")
    private BigDecimal amount;

    /**
     * 统计数量显示，包含单位"个"
     */
    @ApiModelProperty(value = "统计数量显示")
    private String countDisplay;

    /**
     * 统计金额显示，包含单位"万"，保留2位小数
     */
    @ApiModelProperty(value = "统计金额显示")
    private String amountDisplay;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    private String icon;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;

    /**
     * 获取统计数量显示
     */
    @JsonProperty
    public String getCountDisplay() {
        return (count == null ? 0 : count) + "个";
    }

    /**
     * 获取统计金额显示
     */
    @JsonProperty
    public String getAmountDisplay() {
        // 转为万为单位，并保留2位小数
        BigDecimal amountInTenThousand = amount == null ?
                BigDecimal.ZERO :
                amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
        return amountInTenThousand.toString();
    }
}
