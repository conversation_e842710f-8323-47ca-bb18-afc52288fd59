/*
 Navicat Premium Data Transfer

 Source Server         : oracle_localhost
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : 127.0.0.1:1521
 Source Schema         : BLADEX

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 20/03/2022 22:00:00
*/


-- ----------------------------
-- Table structure for ACT_APP_APPDEF
-- ----------------------------
-- DROP TABLE "ACT_APP_APPDEF";
CREATE TABLE "ACT_APP_APPDEF" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "KEY_" VARCHAR2(255 BYTE) NOT NULL ,
  "VERSION_" NUMBER NOT NULL ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_NAME_" VARCHAR2(4000 BYTE) ,
  "DESCRIPTION_" VARCHAR2(4000 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_APP_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_APP_DATABASECHANGELOG";
CREATE TABLE "ACT_APP_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_APP_DATABASECHANGELOG" VALUES ('1', 'flowable', 'org/flowable/app/db/liquibase/flowable-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:02.893666', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:496fc778bdf2ab13f2e1926d0e63e0a2', 'createTable tableName=ACT_APP_DEPLOYMENT; createTable tableName=ACT_APP_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_APP_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_APP_RSRC_DPL, referencedTableName=ACT_APP_DEPLOYMENT; createIndex...', NULL, NULL, '3.5.3', NULL, NULL, '9500486764');
INSERT INTO "ACT_APP_DATABASECHANGELOG" VALUES ('3', 'flowable', 'org/flowable/app/db/liquibase/flowable-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:03.090284', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:f1f8aff320aade831944ebad24355f3d', 'createIndex indexName=ACT_IDX_APP_DEF_UNIQ, tableName=ACT_APP_APPDEF', NULL, NULL, '3.5.3', NULL, NULL, '9500486764');
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_APP_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_APP_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_APP_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_APP_DEPLOYMENT";
CREATE TABLE "ACT_APP_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "KEY_" VARCHAR2(255 BYTE) ,
  "DEPLOY_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_APP_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_APP_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_BYTES_" BLOB
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_CASEDEF
-- ----------------------------
-- DROP TABLE "ACT_CMMN_CASEDEF";
CREATE TABLE "ACT_CMMN_CASEDEF" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "KEY_" VARCHAR2(255 BYTE) NOT NULL ,
  "VERSION_" NUMBER NOT NULL ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_NAME_" VARCHAR2(4000 BYTE) ,
  "DESCRIPTION_" VARCHAR2(4000 BYTE) ,
  "HAS_GRAPHICAL_NOTATION_" NUMBER(1) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '' ,
  "DGRM_RESOURCE_NAME_" VARCHAR2(4000 BYTE) ,
  "HAS_START_FORM_KEY_" NUMBER(1)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DATABASECHANGELOG";
CREATE TABLE "ACT_CMMN_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('1', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:05.572529', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:8b4b922d90b05ff27483abefc9597aa6', 'createTable tableName=ACT_CMMN_DEPLOYMENT; createTable tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_CMMN_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_CMMN_RSRC_DPL, referencedTableName=ACT_CMMN_DEPLOYMENT; create...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('2', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.062159', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:65e39b3d385706bb261cbeffe7533cbe', 'addColumn tableName=ACT_CMMN_CASEDEF; addColumn tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('3', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.474729', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '8:c01f6e802b49436b4489040da3012359', 'addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_CASE_INST; createIndex indexName=ACT_IDX_PLAN_ITEM_STAGE_INST, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableNam...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('4', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.633601', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '8:e40d29cb79345b7fb5afd38a7f0ba8fc', 'createTable tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_MIL_INST; addColumn tableName=ACT_CMMN_HI_MIL_INST', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('6', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.752638', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '5', 'EXECUTED', '8:10e82e26a7fee94c32a92099c059c18c', 'createIndex indexName=ACT_IDX_CASE_DEF_UNIQ, tableName=ACT_CMMN_CASEDEF', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" VALUES ('7', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:07.919259', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '6', 'EXECUTED', '8:530bc81a1e30618ccf4a2da1f7c6c043', 'renameColumn newColumnName=CREATE_TIME_, oldColumnName=START_TIME_, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; renameColumn newColumnName=CREATE_TIME_, oldColumnName=CREATED_TIME_, tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_P...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_CMMN_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DEPLOYMENT";
CREATE TABLE "ACT_CMMN_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "KEY_" VARCHAR2(255 BYTE) ,
  "DEPLOY_TIME_" TIMESTAMP(6) ,
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_BYTES_" BLOB ,
  "GENERATED_" NUMBER(1)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_CASE_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_CASE_INST";
CREATE TABLE "ACT_CMMN_HI_CASE_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "BUSINESS_KEY_" VARCHAR2(255 BYTE) ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "PARENT_ID_" VARCHAR2(255 BYTE) ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) ,
  "STATE_" VARCHAR2(255 BYTE) ,
  "START_TIME_" TIMESTAMP(6) ,
  "END_TIME_" TIMESTAMP(6) ,
  "START_USER_ID_" VARCHAR2(255 BYTE) ,
  "CALLBACK_ID_" VARCHAR2(255 BYTE) ,
  "CALLBACK_TYPE_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_MIL_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_MIL_INST";
CREATE TABLE "ACT_CMMN_HI_MIL_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL ,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL ,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_PLAN_ITEM_INST";
CREATE TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "STATE_" VARCHAR2(255 BYTE) ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) ,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) ,
  "STAGE_INST_ID_" VARCHAR2(255 BYTE) ,
  "IS_STAGE_" NUMBER(1) ,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) ,
  "ITEM_DEFINITION_ID_" VARCHAR2(255 BYTE) ,
  "ITEM_DEFINITION_TYPE_" VARCHAR2(255 BYTE) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "LAST_AVAILABLE_TIME_" TIMESTAMP(6) ,
  "LAST_ENABLED_TIME_" TIMESTAMP(6) ,
  "LAST_DISABLED_TIME_" TIMESTAMP(6) ,
  "LAST_STARTED_TIME_" TIMESTAMP(6) ,
  "LAST_SUSPENDED_TIME_" TIMESTAMP(6) ,
  "COMPLETED_TIME_" TIMESTAMP(6) ,
  "OCCURRED_TIME_" TIMESTAMP(6) ,
  "TERMINATED_TIME_" TIMESTAMP(6) ,
  "EXIT_TIME_" TIMESTAMP(6) ,
  "ENDED_TIME_" TIMESTAMP(6) ,
  "LAST_UPDATED_TIME_" TIMESTAMP(6) ,
  "START_USER_ID_" VARCHAR2(255 BYTE) ,
  "REFERENCE_ID_" VARCHAR2(255 BYTE) ,
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '' ,
  "ENTRY_CRITERION_ID_" VARCHAR2(255 BYTE) ,
  "EXIT_CRITERION_ID_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_CASE_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_CASE_INST";
CREATE TABLE "ACT_CMMN_RU_CASE_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "BUSINESS_KEY_" VARCHAR2(255 BYTE) ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "PARENT_ID_" VARCHAR2(255 BYTE) ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) ,
  "STATE_" VARCHAR2(255 BYTE) ,
  "START_TIME_" TIMESTAMP(6) ,
  "START_USER_ID_" VARCHAR2(255 BYTE) ,
  "CALLBACK_ID_" VARCHAR2(255 BYTE) ,
  "CALLBACK_TYPE_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '' ,
  "LOCK_TIME_" TIMESTAMP(6) ,
  "IS_COMPLETEABLE_" NUMBER(1)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_MIL_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_MIL_INST";
CREATE TABLE "ACT_CMMN_RU_MIL_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL ,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL ,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_PLAN_ITEM_INST";
CREATE TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) ,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) ,
  "STAGE_INST_ID_" VARCHAR2(255 BYTE) ,
  "IS_STAGE_" NUMBER(1) ,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "STATE_" VARCHAR2(255 BYTE) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "START_USER_ID_" VARCHAR2(255 BYTE) ,
  "REFERENCE_ID_" VARCHAR2(255 BYTE) ,
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '' ,
  "ITEM_DEFINITION_ID_" VARCHAR2(255 BYTE) ,
  "ITEM_DEFINITION_TYPE_" VARCHAR2(255 BYTE) ,
  "IS_COMPLETEABLE_" NUMBER(1) ,
  "IS_COUNT_ENABLED_" NUMBER(1) ,
  "VAR_COUNT_" NUMBER ,
  "SENTRY_PART_INST_COUNT_" NUMBER ,
  "LAST_AVAILABLE_TIME_" TIMESTAMP(3) ,
  "LAST_ENABLED_TIME_" TIMESTAMP(3) ,
  "LAST_DISABLED_TIME_" TIMESTAMP(3) ,
  "LAST_STARTED_TIME_" TIMESTAMP(3) ,
  "LAST_SUSPENDED_TIME_" TIMESTAMP(3) ,
  "COMPLETED_TIME_" TIMESTAMP(3) ,
  "OCCURRED_TIME_" TIMESTAMP(3) ,
  "TERMINATED_TIME_" TIMESTAMP(3) ,
  "EXIT_TIME_" TIMESTAMP(3) ,
  "ENDED_TIME_" TIMESTAMP(3) ,
  "ENTRY_CRITERION_ID_" VARCHAR2(255 BYTE) ,
  "EXIT_CRITERION_ID_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_SENTRY_PART_INST";
CREATE TABLE "ACT_CMMN_RU_SENTRY_PART_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "REV_" NUMBER NOT NULL ,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) ,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) ,
  "PLAN_ITEM_INST_ID_" VARCHAR2(255 BYTE) ,
  "ON_PART_ID_" VARCHAR2(255 BYTE) ,
  "IF_PART_ID_" VARCHAR2(255 BYTE) ,
  "TIME_STAMP_" TIMESTAMP(6)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CO_CONTENT_ITEM
-- ----------------------------
-- DROP TABLE "ACT_CO_CONTENT_ITEM";
CREATE TABLE "ACT_CO_CONTENT_ITEM" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL ,
  "MIME_TYPE_" VARCHAR2(255 BYTE) ,
  "TASK_ID_" VARCHAR2(255 BYTE) ,
  "PROC_INST_ID_" VARCHAR2(255 BYTE) ,
  "CONTENT_STORE_ID_" VARCHAR2(255 BYTE) ,
  "CONTENT_STORE_NAME_" VARCHAR2(255 BYTE) ,
  "FIELD_" VARCHAR2(400 BYTE) ,
  "CONTENT_AVAILABLE_" NUMBER(1) DEFAULT 0 ,
  "CREATED_" TIMESTAMP(6) ,
  "CREATED_BY_" VARCHAR2(255 BYTE) ,
  "LAST_MODIFIED_" TIMESTAMP(6) ,
  "LAST_MODIFIED_BY_" VARCHAR2(255 BYTE) ,
  "CONTENT_SIZE_" NUMBER(38) DEFAULT 0 ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "SCOPE_ID_" VARCHAR2(255 BYTE) ,
  "SCOPE_TYPE_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_CO_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_CO_DATABASECHANGELOG";
CREATE TABLE "ACT_CO_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CO_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_CO_DATABASECHANGELOG" VALUES ('1', 'activiti', 'org/flowable/content/db/liquibase/flowable-content-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:15.412866', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:7644d7165cfe799200a2abdd3419e8b6', 'createTable tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_taskid, tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_procid, tableName=ACT_CO_CONTENT_ITEM', NULL, NULL, '3.5.3', NULL, NULL, '9500492548');
INSERT INTO "ACT_CO_DATABASECHANGELOG" VALUES ('2', 'flowable', 'org/flowable/content/db/liquibase/flowable-content-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:59.614707', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:fe7b11ac7dbbf9c43006b23bbab60bab', 'addColumn tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_scope, tableName=ACT_CO_CONTENT_ITEM', NULL, NULL, '3.5.3', NULL, NULL, '9500492548');
COMMIT;

-- ----------------------------
-- Table structure for ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_CO_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_CO_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_CO_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_DE_DATABASECHANGELOG";
CREATE TABLE "ACT_DE_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_DE_DATABASECHANGELOG" VALUES ('1', 'flowable', 'META-INF/liquibase/flowable-modeler-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 02:21:28.919681', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:e70d1d9d3899a734296b2514ccc71501', 'createTable tableName=ACT_DE_MODEL; createIndex indexName=idx_proc_mod_created, tableName=ACT_DE_MODEL; createTable tableName=ACT_DE_MODEL_HISTORY; createIndex indexName=idx_proc_mod_history_proc, tableName=ACT_DE_MODEL_HISTORY; createTable tableN...', NULL, NULL, '3.6.3', NULL, NULL, '4626087832');
INSERT INTO "ACT_DE_DATABASECHANGELOG" VALUES ('3', 'flowable', 'META-INF/liquibase/flowable-modeler-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 02:21:29.414387', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:3a9143bef2e45f2316231cc1369138b6', 'addColumn tableName=ACT_DE_MODEL; addColumn tableName=ACT_DE_MODEL_HISTORY', NULL, NULL, '3.6.3', NULL, NULL, '4626087832');
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_DE_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_DE_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_DE_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_MODEL
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL";
CREATE TABLE "ACT_DE_MODEL" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME" VARCHAR2(400 BYTE) NOT NULL ,
  "MODEL_KEY" VARCHAR2(400 BYTE) NOT NULL ,
  "DESCRIPTION" VARCHAR2(4000 BYTE) ,
  "MODEL_COMMENT" VARCHAR2(4000 BYTE) ,
  "CREATED" TIMESTAMP(6) ,
  "CREATED_BY" VARCHAR2(255 BYTE) ,
  "LAST_UPDATED" TIMESTAMP(6) ,
  "LAST_UPDATED_BY" VARCHAR2(255 BYTE) ,
  "VERSION" NUMBER ,
  "MODEL_EDITOR_JSON" CLOB ,
  "MODEL_EDITOR_XML" CLOB ,
  "THUMBNAIL" BLOB ,
  "MODEL_TYPE" NUMBER ,
  "TENANT_ID" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DE_MODEL_HISTORY
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL_HISTORY";
CREATE TABLE "ACT_DE_MODEL_HISTORY" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME" VARCHAR2(400 BYTE) NOT NULL ,
  "MODEL_KEY" VARCHAR2(400 BYTE) NOT NULL ,
  "DESCRIPTION" VARCHAR2(4000 BYTE) ,
  "MODEL_COMMENT" VARCHAR2(4000 BYTE) ,
  "CREATED" TIMESTAMP(6) ,
  "CREATED_BY" VARCHAR2(255 BYTE) ,
  "LAST_UPDATED" TIMESTAMP(6) ,
  "LAST_UPDATED_BY" VARCHAR2(255 BYTE) ,
  "REMOVAL_DATE" TIMESTAMP(6) ,
  "VERSION" NUMBER ,
  "MODEL_EDITOR_JSON" CLOB ,
  "MODEL_ID" VARCHAR2(255 BYTE) NOT NULL ,
  "MODEL_TYPE" NUMBER ,
  "TENANT_ID" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DE_MODEL_RELATION
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL_RELATION";
CREATE TABLE "ACT_DE_MODEL_RELATION" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "PARENT_MODEL_ID" VARCHAR2(255 BYTE) ,
  "MODEL_ID" VARCHAR2(255 BYTE) ,
  "RELATION_TYPE" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DMN_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_DMN_DATABASECHANGELOG";
CREATE TABLE "ACT_DMN_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_DMN_DATABASECHANGELOG" VALUES ('1', 'activiti', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.445285', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:c8701f1c71018b55029f450b2e9a10a1', 'createTable tableName=ACT_DMN_DEPLOYMENT; createTable tableName=ACT_DMN_DEPLOYMENT_RESOURCE; createTable tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" VALUES ('2', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.548377', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:47f94b27feb7df8a30d4e338c7bd5fb8', 'createTable tableName=ACT_DMN_HI_DECISION_EXECUTION', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" VALUES ('3', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.617030', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '8:ac17eae89fbdccb6e08daf3c7797b579', 'addColumn tableName=ACT_DMN_HI_DECISION_EXECUTION', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" VALUES ('4', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.086703', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '8:f73aabc4529e7292c2942073d1cff6f9', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" VALUES ('6', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.165065', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '5', 'EXECUTED', '8:646c6a061e0b6e8a62e69844ff96abb0', 'createIndex indexName=ACT_IDX_DEC_TBL_UNIQ, tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_DMN_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_DMN_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_DMN_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DECISION_TABLE
-- ----------------------------
-- DROP TABLE "ACT_DMN_DECISION_TABLE";
CREATE TABLE "ACT_DMN_DECISION_TABLE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "VERSION_" NUMBER ,
  "KEY_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_NAME_" VARCHAR2(255 BYTE) ,
  "DESCRIPTION_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DMN_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_DMN_DEPLOYMENT";
CREATE TABLE "ACT_DMN_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOY_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_DMN_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_BYTES_" BLOB
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
-- DROP TABLE "ACT_DMN_HI_DECISION_EXECUTION";
CREATE TABLE "ACT_DMN_HI_DECISION_EXECUTION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "DECISION_DEFINITION_ID_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "START_TIME_" TIMESTAMP(6) ,
  "END_TIME_" TIMESTAMP(6) ,
  "INSTANCE_ID_" VARCHAR2(255 BYTE) ,
  "EXECUTION_ID_" VARCHAR2(255 BYTE) ,
  "ACTIVITY_ID_" VARCHAR2(255 BYTE) ,
  "FAILED_" NUMBER(1) DEFAULT 0 ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "EXECUTION_JSON_" CLOB ,
  "SCOPE_TYPE_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_EVT_LOG
-- ----------------------------
-- DROP TABLE "ACT_EVT_LOG";
CREATE TABLE "ACT_EVT_LOG" (
  "LOG_NR_" NUMBER(19) NOT NULL ,
  "TYPE_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL ,
  "USER_ID_" NVARCHAR2(255) ,
  "DATA_" BLOB ,
  "LOCK_OWNER_" NVARCHAR2(255) ,
  "LOCK_TIME_" TIMESTAMP(6) ,
  "IS_PROCESSED_" NUMBER(3) DEFAULT 0
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_FO_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_FO_DATABASECHANGELOG";
CREATE TABLE "ACT_FO_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL ,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL ,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL ,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL ,
  "ORDEREXECUTED" NUMBER NOT NULL ,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL ,
  "MD5SUM" VARCHAR2(35 BYTE) ,
  "DESCRIPTION" VARCHAR2(255 BYTE) ,
  "COMMENTS" VARCHAR2(255 BYTE) ,
  "TAG" VARCHAR2(255 BYTE) ,
  "LIQUIBASE" VARCHAR2(20 BYTE) ,
  "CONTEXTS" VARCHAR2(255 BYTE) ,
  "LABELS" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_FO_DATABASECHANGELOG" VALUES ('1', 'activiti', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.875144', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:033ebf9380889aed7c453927ecc3250d', 'createTable tableName=ACT_FO_FORM_DEPLOYMENT; createTable tableName=ACT_FO_FORM_RESOURCE; createTable tableName=ACT_FO_FORM_DEFINITION; createTable tableName=ACT_FO_FORM_INSTANCE', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" VALUES ('2', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:11.176570', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:986365ceb40445ce3b27a8e6b40f159b', 'addColumn tableName=ACT_FO_FORM_INSTANCE', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" VALUES ('3', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:13.571387', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '8:abf482518ceb09830ef674e52c06bf15', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_FO_FORM_DEFINITION', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" VALUES ('5', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:13.675232', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '8:b4be732b89e5ca028bdd520c6ad4d446', 'createIndex indexName=ACT_IDX_FORM_DEF_UNIQ, tableName=ACT_FO_FORM_DEFINITION', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_FO_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_FO_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL ,
  "LOCKED" NUMBER(1) NOT NULL ,
  "LOCKGRANTED" TIMESTAMP(6) ,
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_FO_DATABASECHANGELOGLOCK" VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_FORM_DEFINITION
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_DEFINITION";
CREATE TABLE "ACT_FO_FORM_DEFINITION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "VERSION_" NUMBER ,
  "KEY_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_NAME_" VARCHAR2(255 BYTE) ,
  "DESCRIPTION_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_DEPLOYMENT";
CREATE TABLE "ACT_FO_FORM_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "CATEGORY_" VARCHAR2(255 BYTE) ,
  "DEPLOY_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_FO_FORM_INSTANCE
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_INSTANCE";
CREATE TABLE "ACT_FO_FORM_INSTANCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "FORM_DEFINITION_ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "TASK_ID_" VARCHAR2(255 BYTE) ,
  "PROC_INST_ID_" VARCHAR2(255 BYTE) ,
  "PROC_DEF_ID_" VARCHAR2(255 BYTE) ,
  "SUBMITTED_DATE_" TIMESTAMP(6) ,
  "SUBMITTED_BY_" VARCHAR2(255 BYTE) ,
  "FORM_VALUES_ID_" VARCHAR2(255 BYTE) ,
  "TENANT_ID_" VARCHAR2(255 BYTE) ,
  "SCOPE_ID_" VARCHAR2(255 BYTE) ,
  "SCOPE_TYPE_" VARCHAR2(255 BYTE) ,
  "SCOPE_DEFINITION_ID_" VARCHAR2(255 BYTE)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_FO_FORM_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_RESOURCE";
CREATE TABLE "ACT_FO_FORM_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME_" VARCHAR2(255 BYTE) ,
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE) ,
  "RESOURCE_BYTES_" BLOB
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_GE_BYTEARRAY
-- ----------------------------
-- DROP TABLE "ACT_GE_BYTEARRAY";
CREATE TABLE "ACT_GE_BYTEARRAY" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "NAME_" NVARCHAR2(255) ,
  "DEPLOYMENT_ID_" NVARCHAR2(64) ,
  "BYTES_" BLOB ,
  "GENERATED_" NUMBER(1)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_GE_PROPERTY
-- ----------------------------
-- DROP TABLE "ACT_GE_PROPERTY";
CREATE TABLE "ACT_GE_PROPERTY" (
  "NAME_" NVARCHAR2(64) NOT NULL ,
  "VALUE_" NVARCHAR2(300) ,
  "REV_" NUMBER
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_GE_PROPERTY
-- ----------------------------
INSERT INTO "ACT_GE_PROPERTY" VALUES ('common.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('next.dbid', '1', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('entitylink.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('identitylink.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('job.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('task.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('variable.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('eventsubscription.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('schema.history', 'create(*******)', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('cfg.execution-related-entities-count', 'true', '1');
INSERT INTO "ACT_GE_PROPERTY" VALUES ('cfg.task-related-entities-count', 'true', '1');
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_ACTINST
-- ----------------------------
-- DROP TABLE "ACT_HI_ACTINST";
CREATE TABLE "ACT_HI_ACTINST" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER DEFAULT 1 ,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL ,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL ,
  "EXECUTION_ID_" NVARCHAR2(64) NOT NULL ,
  "ACT_ID_" NVARCHAR2(255) NOT NULL ,
  "TASK_ID_" NVARCHAR2(64) ,
  "CALL_PROC_INST_ID_" NVARCHAR2(64) ,
  "ACT_NAME_" NVARCHAR2(255) ,
  "ACT_TYPE_" NVARCHAR2(255) NOT NULL ,
  "ASSIGNEE_" NVARCHAR2(255) ,
  "START_TIME_" TIMESTAMP(6) NOT NULL ,
  "END_TIME_" TIMESTAMP(6) ,
  "DURATION_" NUMBER(19) ,
  "DELETE_REASON_" NVARCHAR2(2000) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_ATTACHMENT
-- ----------------------------
-- DROP TABLE "ACT_HI_ATTACHMENT";
CREATE TABLE "ACT_HI_ATTACHMENT" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "USER_ID_" NVARCHAR2(255) ,
  "NAME_" NVARCHAR2(255) ,
  "DESCRIPTION_" NVARCHAR2(2000) ,
  "TYPE_" NVARCHAR2(255) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "URL_" NVARCHAR2(2000) ,
  "CONTENT_ID_" NVARCHAR2(64) ,
  "TIME_" TIMESTAMP(6)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_COMMENT
-- ----------------------------
-- DROP TABLE "ACT_HI_COMMENT";
CREATE TABLE "ACT_HI_COMMENT" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "TYPE_" NVARCHAR2(255) ,
  "TIME_" TIMESTAMP(6) NOT NULL ,
  "USER_ID_" NVARCHAR2(255) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "ACTION_" NVARCHAR2(255) ,
  "MESSAGE_" NVARCHAR2(2000) ,
  "FULL_MSG_" BLOB
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_DETAIL
-- ----------------------------
-- DROP TABLE "ACT_HI_DETAIL";
CREATE TABLE "ACT_HI_DETAIL" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "ACT_INST_ID_" NVARCHAR2(64) ,
  "NAME_" NVARCHAR2(255) NOT NULL ,
  "VAR_TYPE_" NVARCHAR2(64) ,
  "REV_" NUMBER ,
  "TIME_" TIMESTAMP(6) NOT NULL ,
  "BYTEARRAY_ID_" NVARCHAR2(64) ,
  "DOUBLE_" NUMBER ,
  "LONG_" NUMBER(19) ,
  "TEXT_" NVARCHAR2(2000) ,
  "TEXT2_" NVARCHAR2(2000)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_ENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_HI_ENTITYLINK";
CREATE TABLE "ACT_HI_ENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "LINK_TYPE_" NVARCHAR2(255) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "REF_SCOPE_ID_" NVARCHAR2(255) ,
  "REF_SCOPE_TYPE_" NVARCHAR2(255) ,
  "REF_SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "HIERARCHY_TYPE_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_IDENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_HI_IDENTITYLINK";
CREATE TABLE "ACT_HI_IDENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "GROUP_ID_" NVARCHAR2(255) ,
  "TYPE_" NVARCHAR2(255) ,
  "USER_ID_" NVARCHAR2(255) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_PROCINST
-- ----------------------------
-- DROP TABLE "ACT_HI_PROCINST";
CREATE TABLE "ACT_HI_PROCINST" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER DEFAULT 1 ,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL ,
  "BUSINESS_KEY_" NVARCHAR2(255) ,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL ,
  "START_TIME_" TIMESTAMP(6) NOT NULL ,
  "END_TIME_" TIMESTAMP(6) ,
  "DURATION_" NUMBER(19) ,
  "START_USER_ID_" NVARCHAR2(255) ,
  "START_ACT_ID_" NVARCHAR2(255) ,
  "END_ACT_ID_" NVARCHAR2(255) ,
  "SUPER_PROCESS_INSTANCE_ID_" NVARCHAR2(64) ,
  "DELETE_REASON_" NVARCHAR2(2000) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "NAME_" NVARCHAR2(255) ,
  "CALLBACK_ID_" NVARCHAR2(255) ,
  "CALLBACK_TYPE_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_TASKINST
-- ----------------------------
-- DROP TABLE "ACT_HI_TASKINST";
CREATE TABLE "ACT_HI_TASKINST" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER DEFAULT 1 ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "TASK_DEF_ID_" NVARCHAR2(64) ,
  "TASK_DEF_KEY_" NVARCHAR2(255) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "PARENT_TASK_ID_" NVARCHAR2(64) ,
  "NAME_" NVARCHAR2(255) ,
  "DESCRIPTION_" NVARCHAR2(2000) ,
  "OWNER_" NVARCHAR2(255) ,
  "ASSIGNEE_" NVARCHAR2(255) ,
  "START_TIME_" TIMESTAMP(6) NOT NULL ,
  "CLAIM_TIME_" TIMESTAMP(6) ,
  "END_TIME_" TIMESTAMP(6) ,
  "DURATION_" NUMBER(19) ,
  "DELETE_REASON_" NVARCHAR2(2000) ,
  "PRIORITY_" NUMBER ,
  "DUE_DATE_" TIMESTAMP(6) ,
  "FORM_KEY_" NVARCHAR2(255) ,
  "CATEGORY_" NVARCHAR2(255) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "LAST_UPDATED_TIME_" TIMESTAMP(6)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_TSK_LOG
-- ----------------------------
-- DROP TABLE "ACT_HI_TSK_LOG";
CREATE TABLE "ACT_HI_TSK_LOG" (
  "ID_" NUMBER(19) NOT NULL ,
  "TYPE_" NVARCHAR2(64) ,
  "TASK_ID_" NVARCHAR2(64) NOT NULL ,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL ,
  "USER_ID_" NVARCHAR2(255) ,
  "DATA_" NVARCHAR2(2000) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_HI_VARINST
-- ----------------------------
-- DROP TABLE "ACT_HI_VARINST";
CREATE TABLE "ACT_HI_VARINST" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER DEFAULT 1 ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "NAME_" NVARCHAR2(255) NOT NULL ,
  "VAR_TYPE_" NVARCHAR2(100) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "BYTEARRAY_ID_" NVARCHAR2(64) ,
  "DOUBLE_" NUMBER ,
  "LONG_" NUMBER(19) ,
  "TEXT_" NVARCHAR2(2000) ,
  "TEXT2_" NVARCHAR2(2000) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "LAST_UPDATED_TIME_" TIMESTAMP(6)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_BYTEARRAY
-- ----------------------------
-- DROP TABLE "ACT_ID_BYTEARRAY";
CREATE TABLE "ACT_ID_BYTEARRAY" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "NAME_" NVARCHAR2(255) ,
  "BYTES_" BLOB
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_GROUP
-- ----------------------------
-- DROP TABLE "ACT_ID_GROUP";
CREATE TABLE "ACT_ID_GROUP" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "NAME_" NVARCHAR2(255) ,
  "TYPE_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_INFO
-- ----------------------------
-- DROP TABLE "ACT_ID_INFO";
CREATE TABLE "ACT_ID_INFO" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "USER_ID_" NVARCHAR2(64) ,
  "TYPE_" NVARCHAR2(64) ,
  "KEY_" NVARCHAR2(255) ,
  "VALUE_" NVARCHAR2(255) ,
  "PASSWORD_" BLOB ,
  "PARENT_ID_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_MEMBERSHIP
-- ----------------------------
-- DROP TABLE "ACT_ID_MEMBERSHIP";
CREATE TABLE "ACT_ID_MEMBERSHIP" (
  "USER_ID_" NVARCHAR2(64) NOT NULL ,
  "GROUP_ID_" NVARCHAR2(64) NOT NULL
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_PRIV
-- ----------------------------
-- DROP TABLE "ACT_ID_PRIV";
CREATE TABLE "ACT_ID_PRIV" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "NAME_" NVARCHAR2(255) NOT NULL
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_PRIV_MAPPING
-- ----------------------------
-- DROP TABLE "ACT_ID_PRIV_MAPPING";
CREATE TABLE "ACT_ID_PRIV_MAPPING" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "PRIV_ID_" NVARCHAR2(64) NOT NULL ,
  "USER_ID_" NVARCHAR2(255) ,
  "GROUP_ID_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_PROPERTY
-- ----------------------------
-- DROP TABLE "ACT_ID_PROPERTY";
CREATE TABLE "ACT_ID_PROPERTY" (
  "NAME_" NVARCHAR2(64) NOT NULL ,
  "VALUE_" NVARCHAR2(300) ,
  "REV_" NUMBER
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_PROPERTY
-- ----------------------------
INSERT INTO "ACT_ID_PROPERTY" VALUES ('schema.version', '*******', '1');
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_TOKEN
-- ----------------------------
-- DROP TABLE "ACT_ID_TOKEN";
CREATE TABLE "ACT_ID_TOKEN" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TOKEN_VALUE_" NVARCHAR2(255) ,
  "TOKEN_DATE_" TIMESTAMP(6) ,
  "IP_ADDRESS_" NVARCHAR2(255) ,
  "USER_AGENT_" NVARCHAR2(255) ,
  "USER_ID_" NVARCHAR2(255) ,
  "TOKEN_DATA_" NVARCHAR2(2000)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_ID_USER
-- ----------------------------
-- DROP TABLE "ACT_ID_USER";
CREATE TABLE "ACT_ID_USER" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "FIRST_" NVARCHAR2(255) ,
  "LAST_" NVARCHAR2(255) ,
  "DISPLAY_NAME_" NVARCHAR2(255) ,
  "EMAIL_" NVARCHAR2(255) ,
  "PWD_" NVARCHAR2(255) ,
  "PICTURE_ID_" NVARCHAR2(64) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_PROCDEF_INFO
-- ----------------------------
-- DROP TABLE "ACT_PROCDEF_INFO";
CREATE TABLE "ACT_PROCDEF_INFO" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "INFO_JSON_ID_" NVARCHAR2(64)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RE_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_RE_DEPLOYMENT";
CREATE TABLE "ACT_RE_DEPLOYMENT" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "NAME_" NVARCHAR2(255) ,
  "CATEGORY_" NVARCHAR2(255) ,
  "KEY_" NVARCHAR2(255) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "DEPLOY_TIME_" TIMESTAMP(6) ,
  "DERIVED_FROM_" NVARCHAR2(64) ,
  "DERIVED_FROM_ROOT_" NVARCHAR2(64) ,
  "PARENT_DEPLOYMENT_ID_" NVARCHAR2(255) ,
  "ENGINE_VERSION_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RE_MODEL
-- ----------------------------
-- DROP TABLE "ACT_RE_MODEL";
CREATE TABLE "ACT_RE_MODEL" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "NAME_" NVARCHAR2(255) ,
  "KEY_" NVARCHAR2(255) ,
  "CATEGORY_" NVARCHAR2(255) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "LAST_UPDATE_TIME_" TIMESTAMP(6) ,
  "VERSION_" NUMBER ,
  "META_INFO_" NVARCHAR2(2000) ,
  "DEPLOYMENT_ID_" NVARCHAR2(64) ,
  "EDITOR_SOURCE_VALUE_ID_" NVARCHAR2(64) ,
  "EDITOR_SOURCE_EXTRA_VALUE_ID_" NVARCHAR2(64) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RE_PROCDEF
-- ----------------------------
-- DROP TABLE "ACT_RE_PROCDEF";
CREATE TABLE "ACT_RE_PROCDEF" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "CATEGORY_" NVARCHAR2(255) ,
  "NAME_" NVARCHAR2(255) ,
  "KEY_" NVARCHAR2(255) NOT NULL ,
  "VERSION_" NUMBER NOT NULL ,
  "DEPLOYMENT_ID_" NVARCHAR2(64) ,
  "RESOURCE_NAME_" NVARCHAR2(2000) ,
  "DGRM_RESOURCE_NAME_" VARCHAR2(4000 BYTE) ,
  "DESCRIPTION_" NVARCHAR2(2000) ,
  "HAS_START_FORM_KEY_" NUMBER(1) ,
  "HAS_GRAPHICAL_NOTATION_" NUMBER(1) ,
  "SUSPENSION_STATE_" NUMBER ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "DERIVED_FROM_" NVARCHAR2(64) ,
  "DERIVED_FROM_ROOT_" NVARCHAR2(64) ,
  "DERIVED_VERSION_" NUMBER DEFAULT 0  NOT NULL ,
  "ENGINE_VERSION_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_ACTINST
-- ----------------------------
-- DROP TABLE "ACT_RU_ACTINST";
CREATE TABLE "ACT_RU_ACTINST" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER DEFAULT 1 ,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL ,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL ,
  "EXECUTION_ID_" NVARCHAR2(64) NOT NULL ,
  "ACT_ID_" NVARCHAR2(255) NOT NULL ,
  "TASK_ID_" NVARCHAR2(64) ,
  "CALL_PROC_INST_ID_" NVARCHAR2(64) ,
  "ACT_NAME_" NVARCHAR2(255) ,
  "ACT_TYPE_" NVARCHAR2(255) NOT NULL ,
  "ASSIGNEE_" NVARCHAR2(255) ,
  "START_TIME_" TIMESTAMP(6) NOT NULL ,
  "END_TIME_" TIMESTAMP(6) ,
  "DURATION_" NUMBER(19) ,
  "DELETE_REASON_" NVARCHAR2(2000) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_DEADLETTER_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_DEADLETTER_JOB";
CREATE TABLE "ACT_RU_DEADLETTER_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "EXCLUSIVE_" NUMBER(1) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "ELEMENT_ID_" NVARCHAR2(255) ,
  "ELEMENT_NAME_" NVARCHAR2(255) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64) ,
  "EXCEPTION_MSG_" NVARCHAR2(2000) ,
  "DUEDATE_" TIMESTAMP(6) ,
  "REPEAT_" NVARCHAR2(255) ,
  "HANDLER_TYPE_" NVARCHAR2(255) ,
  "HANDLER_CFG_" NVARCHAR2(2000) ,
  "CUSTOM_VALUES_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_ENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_RU_ENTITYLINK";
CREATE TABLE "ACT_RU_ENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "LINK_TYPE_" NVARCHAR2(255) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "REF_SCOPE_ID_" NVARCHAR2(255) ,
  "REF_SCOPE_TYPE_" NVARCHAR2(255) ,
  "REF_SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "HIERARCHY_TYPE_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_EVENT_SUBSCR
-- ----------------------------
-- DROP TABLE "ACT_RU_EVENT_SUBSCR";
CREATE TABLE "ACT_RU_EVENT_SUBSCR" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "EVENT_TYPE_" NVARCHAR2(255) NOT NULL ,
  "EVENT_NAME_" NVARCHAR2(255) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "ACTIVITY_ID_" NVARCHAR2(64) ,
  "CONFIGURATION_" NVARCHAR2(255) ,
  "CREATED_" TIMESTAMP(6) NOT NULL ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "SUB_SCOPE_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(64) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(64) ,
  "SCOPE_TYPE_" NVARCHAR2(64) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_EXECUTION
-- ----------------------------
-- DROP TABLE "ACT_RU_EXECUTION";
CREATE TABLE "ACT_RU_EXECUTION" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "BUSINESS_KEY_" NVARCHAR2(255) ,
  "PARENT_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "SUPER_EXEC_" NVARCHAR2(64) ,
  "ROOT_PROC_INST_ID_" NVARCHAR2(64) ,
  "ACT_ID_" NVARCHAR2(255) ,
  "IS_ACTIVE_" NUMBER(1) ,
  "IS_CONCURRENT_" NUMBER(1) ,
  "IS_SCOPE_" NUMBER(1) ,
  "IS_EVENT_SCOPE_" NUMBER(1) ,
  "IS_MI_ROOT_" NUMBER(1) ,
  "SUSPENSION_STATE_" NUMBER ,
  "CACHED_ENT_STATE_" NUMBER ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "NAME_" NVARCHAR2(255) ,
  "START_ACT_ID_" NVARCHAR2(255) ,
  "START_TIME_" TIMESTAMP(6) ,
  "START_USER_ID_" NVARCHAR2(255) ,
  "LOCK_TIME_" TIMESTAMP(6) ,
  "IS_COUNT_ENABLED_" NUMBER(1) ,
  "EVT_SUBSCR_COUNT_" NUMBER ,
  "TASK_COUNT_" NUMBER ,
  "JOB_COUNT_" NUMBER ,
  "TIMER_JOB_COUNT_" NUMBER ,
  "SUSP_JOB_COUNT_" NUMBER ,
  "DEADLETTER_JOB_COUNT_" NUMBER ,
  "VAR_COUNT_" NUMBER ,
  "ID_LINK_COUNT_" NUMBER ,
  "CALLBACK_ID_" NVARCHAR2(255) ,
  "CALLBACK_TYPE_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_HISTORY_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_HISTORY_JOB";
CREATE TABLE "ACT_RU_HISTORY_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "LOCK_EXP_TIME_" TIMESTAMP(6) ,
  "LOCK_OWNER_" NVARCHAR2(255) ,
  "RETRIES_" NUMBER ,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64) ,
  "EXCEPTION_MSG_" NVARCHAR2(2000) ,
  "HANDLER_TYPE_" NVARCHAR2(255) ,
  "HANDLER_CFG_" NVARCHAR2(2000) ,
  "CUSTOM_VALUES_ID_" NVARCHAR2(64) ,
  "ADV_HANDLER_CFG_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_IDENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_RU_IDENTITYLINK";
CREATE TABLE "ACT_RU_IDENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "GROUP_ID_" NVARCHAR2(255) ,
  "TYPE_" NVARCHAR2(255) ,
  "USER_ID_" NVARCHAR2(255) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_JOB";
CREATE TABLE "ACT_RU_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "LOCK_EXP_TIME_" TIMESTAMP(6) ,
  "LOCK_OWNER_" NVARCHAR2(255) ,
  "EXCLUSIVE_" NUMBER(1) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "ELEMENT_ID_" NVARCHAR2(255) ,
  "ELEMENT_NAME_" NVARCHAR2(255) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "RETRIES_" NUMBER ,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64) ,
  "EXCEPTION_MSG_" NVARCHAR2(2000) ,
  "DUEDATE_" TIMESTAMP(6) ,
  "REPEAT_" NVARCHAR2(255) ,
  "HANDLER_TYPE_" NVARCHAR2(255) ,
  "HANDLER_CFG_" NVARCHAR2(2000) ,
  "CUSTOM_VALUES_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_SUSPENDED_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_SUSPENDED_JOB";
CREATE TABLE "ACT_RU_SUSPENDED_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "EXCLUSIVE_" NUMBER(1) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "ELEMENT_ID_" NVARCHAR2(255) ,
  "ELEMENT_NAME_" NVARCHAR2(255) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "RETRIES_" NUMBER ,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64) ,
  "EXCEPTION_MSG_" NVARCHAR2(2000) ,
  "DUEDATE_" TIMESTAMP(6) ,
  "REPEAT_" NVARCHAR2(255) ,
  "HANDLER_TYPE_" NVARCHAR2(255) ,
  "HANDLER_CFG_" NVARCHAR2(2000) ,
  "CUSTOM_VALUES_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_TASK
-- ----------------------------
-- DROP TABLE "ACT_RU_TASK";
CREATE TABLE "ACT_RU_TASK" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "TASK_DEF_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "NAME_" NVARCHAR2(255) ,
  "PARENT_TASK_ID_" NVARCHAR2(64) ,
  "DESCRIPTION_" NVARCHAR2(2000) ,
  "TASK_DEF_KEY_" NVARCHAR2(255) ,
  "OWNER_" NVARCHAR2(255) ,
  "ASSIGNEE_" NVARCHAR2(255) ,
  "DELEGATION_" NVARCHAR2(64) ,
  "PRIORITY_" NUMBER ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "DUE_DATE_" TIMESTAMP(6) ,
  "CATEGORY_" NVARCHAR2(255) ,
  "SUSPENSION_STATE_" NUMBER ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '' ,
  "FORM_KEY_" NVARCHAR2(255) ,
  "CLAIM_TIME_" TIMESTAMP(6) ,
  "IS_COUNT_ENABLED_" NUMBER(1) ,
  "VAR_COUNT_" NUMBER ,
  "ID_LINK_COUNT_" NUMBER ,
  "SUB_TASK_COUNT_" NUMBER
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_TIMER_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_TIMER_JOB";
CREATE TABLE "ACT_RU_TIMER_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "LOCK_EXP_TIME_" TIMESTAMP(6) ,
  "LOCK_OWNER_" NVARCHAR2(255) ,
  "EXCLUSIVE_" NUMBER(1) ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64) ,
  "PROC_DEF_ID_" NVARCHAR2(64) ,
  "ELEMENT_ID_" NVARCHAR2(255) ,
  "ELEMENT_NAME_" NVARCHAR2(255) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255) ,
  "RETRIES_" NUMBER ,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64) ,
  "EXCEPTION_MSG_" NVARCHAR2(2000) ,
  "DUEDATE_" TIMESTAMP(6) ,
  "REPEAT_" NVARCHAR2(255) ,
  "HANDLER_TYPE_" NVARCHAR2(255) ,
  "HANDLER_CFG_" NVARCHAR2(2000) ,
  "CUSTOM_VALUES_ID_" NVARCHAR2(64) ,
  "CREATE_TIME_" TIMESTAMP(6) ,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Table structure for ACT_RU_VARIABLE
-- ----------------------------
-- DROP TABLE "ACT_RU_VARIABLE";
CREATE TABLE "ACT_RU_VARIABLE" (
  "ID_" NVARCHAR2(64) NOT NULL ,
  "REV_" NUMBER ,
  "TYPE_" NVARCHAR2(255) NOT NULL ,
  "NAME_" NVARCHAR2(255) NOT NULL ,
  "EXECUTION_ID_" NVARCHAR2(64) ,
  "PROC_INST_ID_" NVARCHAR2(64) ,
  "TASK_ID_" NVARCHAR2(64) ,
  "SCOPE_ID_" NVARCHAR2(255) ,
  "SUB_SCOPE_ID_" NVARCHAR2(255) ,
  "SCOPE_TYPE_" NVARCHAR2(255) ,
  "BYTEARRAY_ID_" NVARCHAR2(64) ,
  "DOUBLE_" NUMBER ,
  "LONG_" NUMBER(19) ,
  "TEXT_" NVARCHAR2(2000) ,
  "TEXT2_" NVARCHAR2(2000)
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Sequence structure for ACT_EVT_LOG_SEQ
-- ----------------------------
DROP SEQUENCE "ACT_EVT_LOG_SEQ";
CREATE SEQUENCE "ACT_EVT_LOG_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;

-- ----------------------------
-- Sequence structure for ACT_HI_TASK_EVT_LOG_SEQ
-- ----------------------------
DROP SEQUENCE "ACT_HI_TASK_EVT_LOG_SEQ";
CREATE SEQUENCE "ACT_HI_TASK_EVT_LOG_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;

-- ----------------------------
-- Primary Key structure for table ACT_APP_APPDEF
-- ----------------------------
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "PK_ACT_APP_APPDEF" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_APPDEF
-- ----------------------------
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015409" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015410" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015411" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015412" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_APP_APPDEF
-- ----------------------------
CREATE INDEX "ACT_IDX_APP_DEF_DPLY"
  ON "ACT_APP_APPDEF" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE UNIQUE INDEX "ACT_IDX_APP_DEF_UNIQ"
  ON "ACT_APP_APPDEF" ("VERSION_" ASC, "TENANT_ID_" ASC, "KEY_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_APP_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015398" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015399" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015400" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015401" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015402" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015403" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_APP_DATABASECHANGELOGLO" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015395" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015396" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_APP_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015404" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_APP_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015406" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
CREATE INDEX "ACT_IDX_APP_RSRC_DPL"
  ON "ACT_APP_DEPLOYMENT_RESOURCE" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_CASEDEF
-- ----------------------------
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "PK_ACT_CMMN_CASEDEF" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_CASEDEF
-- ----------------------------
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015429" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015430" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015431" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015432" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_CASEDEF
-- ----------------------------
CREATE INDEX "ACT_IDX_CASE_DEF_DPLY"
  ON "ACT_CMMN_CASEDEF" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE UNIQUE INDEX "ACT_IDX_CASE_DEF_UNIQ"
  ON "ACT_CMMN_CASEDEF" ("TENANT_ID_" ASC, "KEY_" ASC, "VERSION_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015418" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015419" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015420" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015421" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015422" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015423" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_CMMN_DATABASECHANGELOGL" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015415" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015416" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_CMMN_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015424" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_CMMN_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015426" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
CREATE INDEX "ACT_IDX_CMMN_RSRC_DPL"
  ON "ACT_CMMN_DEPLOYMENT_RESOURCE" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_CASE_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0015459" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0015460" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_MIL_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015462" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015463" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015464" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015465" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015466" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015467" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015468" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_PLAN_ITEM_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015470" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015471" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "PK_ACT_CMMN_RU_CASE_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0015435" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0015436" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_CASE_INST_CASE_DEF"
  ON "ACT_CMMN_RU_CASE_INST" ("CASE_DEF_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_CASE_INST_PARENT"
  ON "ACT_CMMN_RU_CASE_INST" ("PARENT_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "PK_ACT_CMMN_RU_MIL_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015450" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015451" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015452" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015453" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015454" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015455" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_MIL_CASE_DEF"
  ON "ACT_CMMN_RU_MIL_INST" ("CASE_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MIL_CASE_INST"
  ON "ACT_CMMN_RU_MIL_INST" ("CASE_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "PK_CMMN_PLAN_ITEM_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015439" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015440" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_DEF"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("CASE_DEF_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_INST"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("CASE_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PLAN_ITEM_STAGE_INST"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("STAGE_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "PK_CMMN_SENTRY_PART_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0015444" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0015445" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_SENTRY_CASE_DEF"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("CASE_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SENTRY_CASE_INST"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("CASE_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SENTRY_PLAN_ITEM"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("PLAN_ITEM_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "PK_ACT_CO_CONTENT_ITEM" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0015517" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0015518" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
CREATE INDEX "IDX_CONTITEM_PROCID"
  ON "ACT_CO_CONTENT_ITEM" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "IDX_CONTITEM_SCOPE"
  ON "ACT_CO_CONTENT_ITEM" ("SCOPE_TYPE_" ASC, "SCOPE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "IDX_CONTITEM_TASKID"
  ON "ACT_CO_CONTENT_ITEM" ("TASK_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_CO_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015511" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015512" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015513" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015514" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015515" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015516" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_CO_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015508" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015509" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_DE_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015523" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015524" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015525" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015526" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015527" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015528" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_DE_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015520" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015521" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "PK_ACT_DE_MODEL" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015529" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015530" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015531" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DE_MODEL
-- ----------------------------
CREATE INDEX "IDX_PROC_MOD_CREATED"
  ON "ACT_DE_MODEL" ("CREATED_BY" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "PK_ACT_DE_MODEL_HISTORY" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015533" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015534" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015535" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015536" CHECK ("MODEL_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
CREATE INDEX "IDX_PROC_MOD_HISTORY_PROC"
  ON "ACT_DE_MODEL_HISTORY" ("MODEL_ID" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "PK_ACT_DE_MODEL_RELATION" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "SYS_C0015538" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_DMN_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015476" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015477" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015478" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015479" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015480" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015481" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_DMN_DATABASECHANGELOGLO" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015473" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015474" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DECISION_TABLE
-- ----------------------------
ALTER TABLE "ACT_DMN_DECISION_TABLE" ADD CONSTRAINT "PK_ACT_DMN_DECISION_TABLE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DECISION_TABLE
-- ----------------------------
ALTER TABLE "ACT_DMN_DECISION_TABLE" ADD CONSTRAINT "SYS_C0015486" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DMN_DECISION_TABLE
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_DEC_TBL_UNIQ"
  ON "ACT_DMN_DECISION_TABLE" ("KEY_" ASC, "TENANT_ID_" ASC, "VERSION_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_DMN_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015482" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_ACT_DMN_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015484" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT "PK_ACT_DMN_HI_DECISION_EXECUTI" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT "SYS_C0015488" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_EVT_LOG
-- ----------------------------
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0015308" PRIMARY KEY ("LOG_NR_");

-- ----------------------------
-- Checks structure for table ACT_EVT_LOG
-- ----------------------------
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0015307" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_FO_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015493" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015494" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015495" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015496" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015497" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015498" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_FO_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015490" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015491" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "PK_ACT_FO_FORM_DEFINITION" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "SYS_C0015503" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_FORM_DEF_UNIQ"
  ON "ACT_FO_FORM_DEFINITION" ("KEY_" ASC, "TENANT_ID_" ASC, "VERSION_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_FO_FORM_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015499" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_INSTANCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "PK_ACT_FO_FORM_INSTANCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_INSTANCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0015505" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0015506" CHECK ("FORM_DEFINITION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_RESOURCE" ADD CONSTRAINT "PK_ACT_FO_FORM_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_RESOURCE" ADD CONSTRAINT "SYS_C0015501" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_GE_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "SYS_C0015239" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_GE_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "SYS_C0015238" CHECK (GENERATED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_GE_BYTEARRAY
-- ----------------------------
CREATE INDEX "ACT_IDX_BYTEAR_DEPL"
  ON "ACT_GE_BYTEARRAY" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_GE_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_GE_PROPERTY" ADD CONSTRAINT "SYS_C0015237" PRIMARY KEY ("NAME_");

-- ----------------------------
-- Primary Key structure for table ACT_HI_ACTINST
-- ----------------------------
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015366" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_ACTINST
-- ----------------------------
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015359" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015360" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015361" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015362" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015363" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015364" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015365" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_ACTINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_ACT_INST_END"
  ON "ACT_HI_ACTINST" ("END_TIME_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_EXEC"
  ON "ACT_HI_ACTINST" ("EXECUTION_ID_" ASC, "ACT_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_PROCINST"
  ON "ACT_HI_ACTINST" ("PROC_INST_ID_" ASC, "ACT_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_START"
  ON "ACT_HI_ACTINST" ("START_TIME_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_ATTACHMENT
-- ----------------------------
ALTER TABLE "ACT_HI_ATTACHMENT" ADD CONSTRAINT "SYS_C0015376" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_ATTACHMENT
-- ----------------------------
ALTER TABLE "ACT_HI_ATTACHMENT" ADD CONSTRAINT "SYS_C0015375" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_COMMENT
-- ----------------------------
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015374" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_COMMENT
-- ----------------------------
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015372" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015373" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_DETAIL
-- ----------------------------
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015371" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_DETAIL
-- ----------------------------
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015367" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015368" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015369" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015370" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_DETAIL
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_DETAIL_ACT_INST"
  ON "ACT_HI_DETAIL" ("ACT_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_NAME"
  ON "ACT_HI_DETAIL" ("NAME_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_PROC_INST"
  ON "ACT_HI_DETAIL" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_TASK_ID"
  ON "ACT_HI_DETAIL" ("TASK_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_TIME"
  ON "ACT_HI_DETAIL" ("TIME_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_ENTITYLINK" ADD CONSTRAINT "SYS_C0015241" PRIMARY KEY ("ID_");

-- ----------------------------
-- Indexes structure for table ACT_HI_ENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE"
  ON "ACT_HI_ENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE_DEF"
  ON "ACT_HI_ENTITYLINK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_IDENTITYLINK" ADD CONSTRAINT "SYS_C0015243" PRIMARY KEY ("ID_");

-- ----------------------------
-- Indexes structure for table ACT_HI_IDENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_PROCINST"
  ON "ACT_HI_IDENTITYLINK" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE"
  ON "ACT_HI_IDENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE_DEF"
  ON "ACT_HI_IDENTITYLINK" ("SCOPE_TYPE_" ASC, "SCOPE_DEFINITION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_TASK"
  ON "ACT_HI_IDENTITYLINK" ("TASK_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_USER"
  ON "ACT_HI_IDENTITYLINK" ("USER_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015357" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015358" UNIQUE ("PROC_INST_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015353" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015354" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015355" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015356" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_PROCINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_PRO_INST_END"
  ON "ACT_HI_PROCINST" ("END_TIME_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PRO_I_BUSKEY"
  ON "ACT_HI_PROCINST" ("BUSINESS_KEY_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_TASKINST
-- ----------------------------
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015274" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_TASKINST
-- ----------------------------
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015272" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015273" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_TASKINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_TASK_INST_PROCINST"
  ON "ACT_HI_TASKINST" ("PROC_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SCOPE"
  ON "ACT_HI_TASKINST" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SCOPE_DEF"
  ON "ACT_HI_TASKINST" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SUB_SCOPE"
  ON "ACT_HI_TASKINST" ("SCOPE_TYPE_" ASC, "SUB_SCOPE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_TSK_LOG
-- ----------------------------
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015277" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_TSK_LOG
-- ----------------------------
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015275" CHECK ("TASK_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015276" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_VARINST
-- ----------------------------
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015285" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_VARINST
-- ----------------------------
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015283" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015284" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_VARINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_PROCVAR_EXE"
  ON "ACT_HI_VARINST" ("EXECUTION_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_NAME_TYPE"
  ON "ACT_HI_VARINST" ("VAR_TYPE_" ASC, "NAME_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_PROC_INST"
  ON "ACT_HI_VARINST" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_TASK_ID"
  ON "ACT_HI_VARINST" ("TASK_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_VAR_SCOPE_ID_TYPE"
  ON "ACT_HI_VARINST" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_VAR_SUB_ID_TYPE"
  ON "ACT_HI_VARINST" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_ID_BYTEARRAY" ADD CONSTRAINT "SYS_C0015378" PRIMARY KEY ("ID_");

-- ----------------------------
-- Primary Key structure for table ACT_ID_GROUP
-- ----------------------------
ALTER TABLE "ACT_ID_GROUP" ADD CONSTRAINT "SYS_C0015379" PRIMARY KEY ("ID_");

-- ----------------------------
-- Primary Key structure for table ACT_ID_INFO
-- ----------------------------
ALTER TABLE "ACT_ID_INFO" ADD CONSTRAINT "SYS_C0015382" PRIMARY KEY ("ID_");

-- ----------------------------
-- Primary Key structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "SYS_C0015380" PRIMARY KEY ("USER_ID_", "GROUP_ID_");

-- ----------------------------
-- Indexes structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
CREATE INDEX "ACT_IDX_MEMB_GROUP"
  ON "ACT_ID_MEMBERSHIP" ("GROUP_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MEMB_USER"
  ON "ACT_ID_MEMBERSHIP" ("USER_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015387" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "ACT_UNIQ_PRIV_NAME" UNIQUE ("NAME_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015385" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015386" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015390" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015388" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015389" CHECK ("PRIV_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
CREATE INDEX "ACT_IDX_PRIV_GROUP"
  ON "ACT_ID_PRIV_MAPPING" ("GROUP_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PRIV_MAPPING"
  ON "ACT_ID_PRIV_MAPPING" ("PRIV_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PRIV_USER"
  ON "ACT_ID_PRIV_MAPPING" ("USER_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_ID_PROPERTY" ADD CONSTRAINT "SYS_C0015377" PRIMARY KEY ("NAME_");

-- ----------------------------
-- Primary Key structure for table ACT_ID_TOKEN
-- ----------------------------
ALTER TABLE "ACT_ID_TOKEN" ADD CONSTRAINT "SYS_C0015384" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_TOKEN
-- ----------------------------
ALTER TABLE "ACT_ID_TOKEN" ADD CONSTRAINT "SYS_C0015383" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_USER
-- ----------------------------
ALTER TABLE "ACT_ID_USER" ADD CONSTRAINT "SYS_C0015381" PRIMARY KEY ("ID_");

-- ----------------------------
-- Primary Key structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015311" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_UNIQ_INFO_PROCDEF" UNIQUE ("PROC_DEF_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015309" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015310" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_PROCDEF_INFO
-- ----------------------------
CREATE INDEX "ACT_IDX_PROCDEF_INFO_JSON"
  ON "ACT_PROCDEF_INFO" ("INFO_JSON_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);


-- ----------------------------
-- Primary Key structure for table ACT_RE_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_RE_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015290" PRIMARY KEY ("ID_");

-- ----------------------------
-- Primary Key structure for table ACT_RE_MODEL
-- ----------------------------
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "SYS_C0015292" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RE_MODEL
-- ----------------------------
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "SYS_C0015291" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RE_MODEL
-- ----------------------------
CREATE INDEX "ACT_IDX_MODEL_DEPLOYMENT"
  ON "ACT_RE_MODEL" ("DEPLOYMENT_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MODEL_SOURCE"
  ON "ACT_RE_MODEL" ("EDITOR_SOURCE_VALUE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MODEL_SOURCE_EXTRA"
  ON "ACT_RE_MODEL" ("EDITOR_SOURCE_EXTRA_VALUE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015306" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "ACT_UNIQ_PROCDEF" UNIQUE ("KEY_", "VERSION_", "DERIVED_VERSION_", "TENANT_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015300" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015301" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015302" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015303" CHECK ("DERIVED_VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015304" CHECK (HAS_START_FORM_KEY_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015305" CHECK (HAS_GRAPHICAL_NOTATION_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_RU_ACTINST
-- ----------------------------
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015319" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_ACTINST
-- ----------------------------
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015312" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015313" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015314" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015315" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015316" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015317" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015318" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_ACTINST
-- ----------------------------
CREATE INDEX "ACT_IDX_RU_ACTI_END"
  ON "ACT_RU_ACTINST" ("END_TIME_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_EXEC"
  ON "ACT_RU_ACTINST" ("EXECUTION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_EXEC_ACT"
  ON "ACT_RU_ACTINST" ("ACT_ID_" ASC, "EXECUTION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_PROC"
  ON "ACT_RU_ACTINST" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_PROC_ACT"
  ON "ACT_RU_ACTINST" ("ACT_ID_" ASC, "PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_START"
  ON "ACT_RU_ACTINST" ("START_TIME_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015259" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015256" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015257" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015258" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_DJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("CUSTOM_VALUES_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_EXCEPTION"
  ON "ACT_RU_DEADLETTER_JOB" ("EXCEPTION_STACK_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_EXECUTION_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("EXECUTION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_PROC_DEF_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("PROC_DEF_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_PROC_INST_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("PROCESS_INSTANCE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SCOPE"
  ON "ACT_RU_DEADLETTER_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SCOPE_DEF"
  ON "ACT_RU_DEADLETTER_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SUB_SCOPE"
  ON "ACT_RU_DEADLETTER_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_ENTITYLINK" ADD CONSTRAINT "SYS_C0015240" PRIMARY KEY ("ID_");

-- ----------------------------
-- Indexes structure for table ACT_RU_ENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE"
  ON "ACT_RU_ENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE_DEF"
  ON "ACT_RU_ENTITYLINK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015289" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015286" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015287" CHECK ("EVENT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015288" CHECK ("CREATED_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
CREATE INDEX "ACT_IDX_EVENT_SUBSCR"
  ON "ACT_RU_EVENT_SUBSCR" ("EXECUTION_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EVENT_SUBSCR_CONFIG_"
  ON "ACT_RU_EVENT_SUBSCR" ("CONFIGURATION_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015299" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015293" CHECK (IS_ACTIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015294" CHECK (IS_CONCURRENT_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015295" CHECK (IS_SCOPE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015296" CHECK (IS_EVENT_SCOPE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015297" CHECK (IS_MI_ROOT_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015298" CHECK (IS_COUNT_ENABLED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_EXECUTION
-- ----------------------------
CREATE INDEX "ACT_IDX_EXEC_BUSKEY"
  ON "ACT_RU_EXECUTION" ("BUSINESS_KEY_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXEC_ROOT"
  ON "ACT_RU_EXECUTION" ("ROOT_PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PARENT"
  ON "ACT_RU_EXECUTION" ("PARENT_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PROCDEF"
  ON "ACT_RU_EXECUTION" ("PROC_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PROCINST"
  ON "ACT_RU_EXECUTION" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_SUPER"
  ON "ACT_RU_EXECUTION" ("SUPER_EXEC_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_HISTORY_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_HISTORY_JOB" ADD CONSTRAINT "SYS_C0015261" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_HISTORY_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_HISTORY_JOB" ADD CONSTRAINT "SYS_C0015260" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "SYS_C0015242" PRIMARY KEY ("ID_");

-- ----------------------------
-- Indexes structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_ATHRZ_PROCEDEF"
  ON "ACT_RU_IDENTITYLINK" ("PROC_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_GROUP"
  ON "ACT_RU_IDENTITYLINK" ("GROUP_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE"
  ON "ACT_RU_IDENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE_DEF"
  ON "ACT_RU_IDENTITYLINK" ("SCOPE_TYPE_" ASC, "SCOPE_DEFINITION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_USER"
  ON "ACT_RU_IDENTITYLINK" ("USER_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDL_PROCINST"
  ON "ACT_RU_IDENTITYLINK" ("PROC_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TSKASS_TASK"
  ON "ACT_RU_IDENTITYLINK" ("TASK_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015247" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015244" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015245" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015246" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_JOB_CUSTOM_VAL_ID"
  ON "ACT_RU_JOB" ("CUSTOM_VALUES_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_EXCEPTION"
  ON "ACT_RU_JOB" ("EXCEPTION_STACK_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_EXECUTION_ID"
  ON "ACT_RU_JOB" ("EXECUTION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_PROC_DEF_ID"
  ON "ACT_RU_JOB" ("PROC_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_PROC_INST_ID"
  ON "ACT_RU_JOB" ("PROCESS_INSTANCE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SCOPE"
  ON "ACT_RU_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SCOPE_DEF"
  ON "ACT_RU_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SUB_SCOPE"
  ON "ACT_RU_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015255" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015252" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015253" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015254" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_SJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("CUSTOM_VALUES_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_EXCEPTION"
  ON "ACT_RU_SUSPENDED_JOB" ("EXCEPTION_STACK_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_EXECUTION_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("EXECUTION_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_PROC_DEF_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("PROC_DEF_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_PROC_INST_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("PROCESS_INSTANCE_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SCOPE"
  ON "ACT_RU_SUSPENDED_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SCOPE_DEF"
  ON "ACT_RU_SUSPENDED_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SUB_SCOPE"
  ON "ACT_RU_SUSPENDED_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_TASK
-- ----------------------------
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "SYS_C0015271" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_TASK
-- ----------------------------
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "SYS_C0015270" CHECK (IS_COUNT_ENABLED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_TASK
-- ----------------------------
CREATE INDEX "ACT_IDX_TASK_CREATE"
  ON "ACT_RU_TASK" ("CREATE_TIME_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_EXEC"
  ON "ACT_RU_TASK" ("EXECUTION_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_PROCDEF"
  ON "ACT_RU_TASK" ("PROC_DEF_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_PROCINST"
  ON "ACT_RU_TASK" ("PROC_INST_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SCOPE"
  ON "ACT_RU_TASK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SCOPE_DEF"
  ON "ACT_RU_TASK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SUB_SCOPE"
  ON "ACT_RU_TASK" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_TIMER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015251" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_TIMER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015248" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015249" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015250" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_TIMER_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_TJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_TIMER_JOB" ("CUSTOM_VALUES_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_EXCEPTION"
  ON "ACT_RU_TIMER_JOB" ("EXCEPTION_STACK_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_EXECUTION_ID"
  ON "ACT_RU_TIMER_JOB" ("EXECUTION_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_PROC_DEF_ID"
  ON "ACT_RU_TIMER_JOB" ("PROC_DEF_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_PROC_INST_ID"
  ON "ACT_RU_TIMER_JOB" ("PROCESS_INSTANCE_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SCOPE"
  ON "ACT_RU_TIMER_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SCOPE_DEF"
  ON "ACT_RU_TIMER_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SUB_SCOPE"
  ON "ACT_RU_TIMER_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_VARIABLE
-- ----------------------------
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015281" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_VARIABLE
-- ----------------------------
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015278" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015279" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015280" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_VARIABLE
-- ----------------------------
CREATE INDEX "ACT_IDX_RU_VAR_SCOPE_ID_TYPE"
  ON "ACT_RU_VARIABLE" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_VAR_SUB_ID_TYPE"
  ON "ACT_RU_VARIABLE" ("SCOPE_TYPE_" ASC, "SUB_SCOPE_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VARIABLE_TASK_ID"
  ON "ACT_RU_VARIABLE" ("TASK_ID_" ASC)
  LOGGING
  ONLINE
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_BYTEARRAY"
  ON "ACT_RU_VARIABLE" ("BYTEARRAY_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_EXE"
  ON "ACT_RU_VARIABLE" ("EXECUTION_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_PROCINST"
  ON "ACT_RU_VARIABLE" ("PROC_INST_ID_" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Foreign Keys structure for table ACT_APP_APPDEF
-- ----------------------------
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "ACT_FK_APP_DEF_DPLY" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_APP_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "ACT_FK_APP_RSRC_DPL" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_APP_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_CASEDEF
-- ----------------------------
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "ACT_FK_CASE_DEF_DPLY" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_CMMN_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "ACT_FK_CMMN_RSRC_DPL" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_CMMN_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "ACT_FK_CASE_INST_CASE_DEF" FOREIGN KEY ("CASE_DEF_ID_") REFERENCES "ACT_CMMN_CASEDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "ACT_FK_MIL_CASE_DEF" FOREIGN KEY ("CASE_DEF_ID_") REFERENCES "ACT_CMMN_CASEDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "ACT_FK_MIL_CASE_INST" FOREIGN KEY ("CASE_INST_ID_") REFERENCES "ACT_CMMN_RU_CASE_INST" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "ACT_FK_PLAN_ITEM_CASE_DEF" FOREIGN KEY ("CASE_DEF_ID_") REFERENCES "ACT_CMMN_CASEDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "ACT_FK_PLAN_ITEM_CASE_INST" FOREIGN KEY ("CASE_INST_ID_") REFERENCES "ACT_CMMN_RU_CASE_INST" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_CASE_DEF" FOREIGN KEY ("CASE_DEF_ID_") REFERENCES "ACT_CMMN_CASEDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_CASE_INST" FOREIGN KEY ("CASE_INST_ID_") REFERENCES "ACT_CMMN_RU_CASE_INST" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_PLAN_ITEM" FOREIGN KEY ("PLAN_ITEM_INST_ID_") REFERENCES "ACT_CMMN_RU_PLAN_ITEM_INST" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_CHILD" FOREIGN KEY ("MODEL_ID") REFERENCES "ACT_DE_MODEL" ("ID") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_PARENT" FOREIGN KEY ("PARENT_MODEL_ID") REFERENCES "ACT_DE_MODEL" ("ID") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_GE_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "ACT_FK_BYTEARR_DEPL" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_RE_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "ACT_FK_MEMB_GROUP" FOREIGN KEY ("GROUP_ID_") REFERENCES "ACT_ID_GROUP" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "ACT_FK_MEMB_USER" FOREIGN KEY ("USER_ID_") REFERENCES "ACT_ID_USER" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "ACT_FK_PRIV_MAPPING" FOREIGN KEY ("PRIV_ID_") REFERENCES "ACT_ID_PRIV" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_FK_INFO_JSON_BA" FOREIGN KEY ("INFO_JSON_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_FK_INFO_PROCDEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RE_MODEL
-- ----------------------------
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_DEPLOYMENT" FOREIGN KEY ("DEPLOYMENT_ID_") REFERENCES "ACT_RE_DEPLOYMENT" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_SOURCE" FOREIGN KEY ("EDITOR_SOURCE_VALUE_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_SOURCE_EXTRA" FOREIGN KEY ("EDITOR_SOURCE_EXTRA_VALUE_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DJOB_CUSTOM_VAL" FOREIGN KEY ("CUSTOM_VALUES_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DJOB_EXCEPTION" FOREIGN KEY ("EXCEPTION_STACK_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DJOB_EXECUTION" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DJOB_PROCESS_INSTANCE" FOREIGN KEY ("PROCESS_INSTANCE_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DJOB_PROC_DEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "ACT_FK_EVENT_EXEC" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PARENT" FOREIGN KEY ("PARENT_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PROCDEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PROCINST" FOREIGN KEY ("PROC_INST_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_SUPER" FOREIGN KEY ("SUPER_EXEC_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_ATHRZ_PROCEDEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_IDL_PROCINST" FOREIGN KEY ("PROC_INST_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_TSKASS_TASK" FOREIGN KEY ("TASK_ID_") REFERENCES "ACT_RU_TASK" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_CUSTOM_VAL" FOREIGN KEY ("CUSTOM_VALUES_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_EXCEPTION" FOREIGN KEY ("EXCEPTION_STACK_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_EXECUTION" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_PROCESS_INSTANCE" FOREIGN KEY ("PROCESS_INSTANCE_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_PROC_DEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SJOB_CUSTOM_VAL" FOREIGN KEY ("CUSTOM_VALUES_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SJOB_EXCEPTION" FOREIGN KEY ("EXCEPTION_STACK_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SJOB_EXECUTION" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SJOB_PROCESS_INSTANCE" FOREIGN KEY ("PROCESS_INSTANCE_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SJOB_PROC_DEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_TASK
-- ----------------------------
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_EXE" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_PROCDEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_PROCINST" FOREIGN KEY ("PROC_INST_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_TIMER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TJOB_CUSTOM_VAL" FOREIGN KEY ("CUSTOM_VALUES_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TJOB_EXCEPTION" FOREIGN KEY ("EXCEPTION_STACK_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TJOB_EXECUTION" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TJOB_PROCESS_INSTANCE" FOREIGN KEY ("PROCESS_INSTANCE_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TJOB_PROC_DEF" FOREIGN KEY ("PROC_DEF_ID_") REFERENCES "ACT_RE_PROCDEF" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_VARIABLE
-- ----------------------------
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_BYTEARRAY" FOREIGN KEY ("BYTEARRAY_ID_") REFERENCES "ACT_GE_BYTEARRAY" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_EXE" FOREIGN KEY ("EXECUTION_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_PROCINST" FOREIGN KEY ("PROC_INST_ID_") REFERENCES "ACT_RU_EXECUTION" ("ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
