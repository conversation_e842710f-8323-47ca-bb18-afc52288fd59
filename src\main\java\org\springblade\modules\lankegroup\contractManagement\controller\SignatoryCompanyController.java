/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.contractManagement.entity.SignatoryCompany;
import org.springblade.modules.lankegroup.contractManagement.service.ISignatoryCompanyService;
import org.springblade.modules.lankegroup.contractManagement.vo.SignatoryCompanyVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 签约公司表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/cm/signatorycompany")
@Api(value = "签约公司表【晶莱】", tags = "签约公司表接口【晶莱】")
public class SignatoryCompanyController extends BladeController {

    private final ISignatoryCompanyService signatoryCompanyService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入signatoryCompany")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true)
    })
    public R<SignatoryCompany> detail(SignatoryCompany signatoryCompany) {
        if (Func.isEmpty(signatoryCompany.getId())) {
            return R.fail("参数不完整");
        }
        SignatoryCompany detail = signatoryCompanyService.getOne(Condition.getQueryWrapper(signatoryCompany));
        return R.data(detail);
    }

    /**
     * 自定义分页 签约公司表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入signatoryCompany; 销售合同选择签约公司时，传参size/current/status，其中status=1")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "每页数量", required = true),
            @ApiImplicitParam(name = "current", value = "当前页", required = true),
            @ApiImplicitParam(name = "name", value = "名称", required = true),
            @ApiImplicitParam(name = "status", value = "状态(0:禁用 1:启用)", required = true)
    })
    public R<IPage<SignatoryCompanyVO>> page(SignatoryCompanyVO signatoryCompany, Query query) {
        IPage<SignatoryCompanyVO> pages = signatoryCompanyService.selectSignatoryCompanyPage(Condition.getPage(query), signatoryCompany);
        return R.data(pages);
    }

    /**
     * 新增 签约公司表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入signatoryCompany")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称", required = true),
            @ApiImplicitParam(name = "taxNumber", value = "纳税登记号", required = true),
            @ApiImplicitParam(name = "address", value = "通讯地址", required = true),
            @ApiImplicitParam(name = "phone", value = "联系电话", required = true),
            @ApiImplicitParam(name = "accountName", value = "开户行名称", required = true),
            @ApiImplicitParam(name = "bankCode", value = "银行卡号", required = true),
            @ApiImplicitParam(name = "sortOrder", value = "排序序号", required = true)
    })
    public R save(@Valid @RequestBody SignatoryCompany signatoryCompany) {
        return R.status(signatoryCompanyService.saveModel(signatoryCompany));
    }

    /**
     * 修改 签约公司表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入signatoryCompany")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键id", required = true),
            @ApiImplicitParam(name = "name", value = "名称", required = true),
            @ApiImplicitParam(name = "taxNumber", value = "纳税登记号", required = true),
            @ApiImplicitParam(name = "address", value = "通讯地址", required = true),
            @ApiImplicitParam(name = "phone", value = "联系电话", required = true),
            @ApiImplicitParam(name = "accountName", value = "开户行名称", required = true),
            @ApiImplicitParam(name = "bankCode", value = "银行卡号", required = true),
            @ApiImplicitParam(name = "sortOrder", value = "排序序号", required = true)
    })
    public R update(@Valid @RequestBody SignatoryCompany signatoryCompany) {
        if (Func.isEmpty(signatoryCompany.getId())) {
            return R.fail("参数不完整");
        }
        return R.status(signatoryCompanyService.updateModel(signatoryCompany));
    }

    /**
     * 禁用启用 签约公司表
     */
    @PostMapping("/disableEnablement")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "禁用启用", notes = "传入signatoryCompany")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "id", value = "主键id", required = true)
    )
    public R disableEnablement(@Valid @RequestBody SignatoryCompany signatoryCompany) {
        if (Func.isEmpty(signatoryCompany.getId())) {
            return R.fail("参数不完整");
        }
        return R.status(signatoryCompanyService.disableEnablement(signatoryCompany));
    }

}
