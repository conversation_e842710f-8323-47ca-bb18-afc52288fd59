/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.wrapper;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.vo.BusinessProcessVO;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.UserCache;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.modules.system.entity.User;

import java.util.List;
import java.util.Objects;

/**
 * 自定义流程审批中的所有业务单据任务 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public class BusinessProcessWrapper extends BaseEntityWrapper<BusinessProcessEntity, BusinessProcessVO> {

	public static BusinessProcessWrapper build() {
		return new BusinessProcessWrapper();
	}

	@Override
	public BusinessProcessVO entityVO(BusinessProcessEntity businessProcessTask) {
		BusinessProcessVO businessProcessTaskVO = Objects.requireNonNull(BeanUtil.copy(businessProcessTask, BusinessProcessVO.class));
		businessProcessTaskVO.setCreateUserName(UserCache.getUser(businessProcessTask.getCreateUser()).getName());
		return businessProcessTaskVO;
	}

	public BusinessProcessVO entityVO(BusinessProcessVO businessProcessTask) {
		BusinessProcessVO businessProcessVO = Objects.requireNonNull(BeanUtil.copy(businessProcessTask, BusinessProcessVO.class));

		// businessProcessVO.setCreateUserName(UserCache.getUser(businessProcessTask.getCreateUser()).getName());
		if (Func.isNotEmpty(businessProcessTask.getAssigneeId())) {
			List<String> assigneeList = StrUtil.split(businessProcessTask.getAssigneeId(), StrUtil.COMMA);
			StringBuilder assigneeName = new StringBuilder();
			assigneeList.forEach(assigneeId -> {
				User user = UserCache.getUser(Long.valueOf(assigneeId));
				if (user != null) {
					assigneeName.append(user.getName() + ",");
				}
			});
			if (assigneeName.toString().endsWith(",")) {
				assigneeName.deleteCharAt(assigneeName.length() - 1);
			}
			businessProcessVO.setAssigneeName(assigneeName.toString());
		}
//		if (Func.isNotEmpty(businessProcessTask.getTaskId()) && Func.isNotEmpty(businessProcessTask.getAssigneeList())) {
//			JSONObject jsonObj = new JSONObject(businessProcessTask.getAssigneeList());
//			String taskId = jsonObj.getStr(String.valueOf(AuthUtil.getUserId())); // 根据审批人ID获取taskId
//			businessProcessVO.setTaskId(taskId == null ? businessProcessTask.getTaskId() : taskId);
//		}
		businessProcessVO.setFormName(DictCache.getValue("flow", businessProcessTask.getFormKey()));
		if (Func.isNotBlank(businessProcessTask.getBusinessField())) {
            try {
				businessProcessVO.setBusinessFieldJson(JsonUtil.readList(businessProcessTask.getBusinessField(), Kv.class));
            } catch (Exception e) {
            }
        }
		// businessProcessVO.setCreateUserAvatar(Optional.ofNullable(UserCache.getUser(businessProcessTask.getCreateUser())).map(User::getAvatar).orElse(null));
		businessProcessVO.setStatusName(ProcessStatusEnum.getProcessStatusEnumName(businessProcessTask.getStatus()));
		return businessProcessVO;
	}

}
