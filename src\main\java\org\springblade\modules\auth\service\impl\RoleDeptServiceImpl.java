/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.service.impl;

import org.springblade.modules.auth.entity.RoleDept;
import org.springblade.modules.auth.vo.RoleDeptVO;
import org.springblade.modules.auth.mapper.RoleDeptMapper;
import org.springblade.modules.auth.service.IRoleDeptService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 角色与部门的对照表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class RoleDeptServiceImpl extends BaseServiceImpl<RoleDeptMapper, RoleDept> implements IRoleDeptService {

	@Override
	public IPage<RoleDeptVO> selectRoleDeptPage(IPage<RoleDeptVO> page, RoleDeptVO roleDept) {
		return page.setRecords(baseMapper.selectRoleDeptPage(page, roleDept));
	}

	@Override
	public List<Long> roleSelectByDeptId(Map map) {
		List<Long> result = new ArrayList<>();
//查询部门对应的权限
		List<Long> longs = baseMapper.roleSelectByDeptId(map);
		if(longs!=null&&longs.size()>0){
			result.addAll(longs);
		}
//		所有人员都要有普通员工的权限
		result.add(1552895891959816194l);
		return result;
	}

}
