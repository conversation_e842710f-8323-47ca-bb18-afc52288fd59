# dev-smb-0409分支更新内容

## mysql

### blade_project_basic表新增字段

```mysql
ALTER TABLE `blade_project_basic` 
ADD COLUMN `public_project` int DEFAULT '0' COMMENT '项目立项时选择是否公共项目（1是/0不是）' AFTER `is_public`;
```
### 处理blade_project_basic表历史数据
```sql
update `blade_project_basic`
set public_project = 1
where id in (
109120,
1712296767162359809,
1712295847238246401,
104859,
104863,
104878,
107452
)
```


### 项目交付数据处理
> 备份项目id
> 1701916557292285954,1710471710073040897,1732598610845126657,1748229204983558145,1730498554558087170,1749237905244778497,1744254556478582786,1753326373453340674,1770080765258895362,1751438668020789250,1747648555423772674,1748605653829693442
```sql
select GROUP_CONCAT(project_id) from blade_project_progress where is_deleted = 0 and current_progress_key = 5 and stage_leader_id = '106260541137537332' and project_id in (select id from blade_project_basic where group_id in (147132));

update blade_project_progress set stage_leader_id = 114023467283552999, stage_leader_name = '赵夏堇' where is_deleted = 0 and current_progress_key = 5 and stage_leader_id = '106260541137537332' and project_id in (select id from blade_project_basic where group_id in (147132));
```

> 备份项目id
> 1701916557292285954,1710471710073040897,1732598610845126657,1748229204983558145,1730498554558087170,1749237905244778497,1744254556478582786,1753326373453340674,1770080765258895362,1751438668020789250,1747648555423772674,1748605653829693442
```sql
select GROUP_CONCAT(project_id) from blade_project_team where node_id = 5 and admin = 1 and is_deleted = 0 and user_id = 106260541137537332 and project_id in (select id from blade_project_basic where group_id in (147132));

update blade_project_team set user_id = 114023467283552999 where node_id = 5 and admin = 1 and is_deleted = 0 and user_id = 106260541137537332 and project_id in (select id from blade_project_basic where group_id in (147132));
```
