package org.springblade.common.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UtilStoreGetRespVO {


    @ApiModelProperty(value = "ID", required = true)
    @NotEmpty(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "口令", required = true)
    @NotEmpty(message = "口令不能为空")
    private String password;

}
