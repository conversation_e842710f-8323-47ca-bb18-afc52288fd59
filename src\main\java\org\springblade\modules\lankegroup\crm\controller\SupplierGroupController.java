/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.SupplierGroup;
import org.springblade.modules.lankegroup.crm.service.ISupplierGroupService;
import org.springblade.modules.lankegroup.crm.vo.SupplierGroupVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;

import org.springblade.core.boot.ctrl.BladeController;

/**
 * 供应商分组表 控制器
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/supplier/suppliergroup")
@Api(value = "供应商分组表", tags = "供应商分组表接口")
public class SupplierGroupController extends BladeController {

	private final ISupplierGroupService supplierGroupService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入supplierGroup")
	public R<SupplierGroup> detail(SupplierGroup supplierGroup) {
		SupplierGroup detail = supplierGroupService.getOne(Condition.getQueryWrapper(supplierGroup));
		return R.data(detail);
	}

	/**
	 * 分页 供应商分组表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入supplierGroup")
	public R<IPage<SupplierGroup>> list(SupplierGroup supplierGroup, Query query) {
		query.setSize(50);
		QueryWrapper<SupplierGroup> queryWrapper = Condition.getQueryWrapper(supplierGroup);
		queryWrapper.ne("id", 103080);
		IPage<SupplierGroup> pages = supplierGroupService.page(Condition.getPage(query), queryWrapper);
		return R.data(pages);
	}

	/**
	 * 自定义分页 供应商分组表
	 * 每页默认条数改为100条
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入supplierGroup")
	public R<IPage<SupplierGroupVO>> page(SupplierGroupVO supplierGroup, Query query) {
		query.setSize(100);
		IPage<SupplierGroupVO> pages = supplierGroupService.selectSupplierGroupPage(Condition.getPage(query), supplierGroup);
		return R.data(pages);
	}

	/**
	 * 新增 供应商分组表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入supplierGroup")
	public R save(@Valid @RequestBody SupplierGroup supplierGroup) {
		return R.status(supplierGroupService.save(supplierGroup));
	}

	/**
	 * 修改 供应商分组表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入supplierGroup")
	public R update(@Valid @RequestBody SupplierGroup supplierGroup) {
		return R.status(supplierGroupService.updateById(supplierGroup));
	}

	/**
	 * 新增或修改 供应商分组表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入supplierGroup")
	public R submit(@Valid @RequestBody SupplierGroup supplierGroup) {
		return R.status(supplierGroupService.saveOrUpdate(supplierGroup));
	}

	
	/**
	 * 删除 供应商分组表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierGroupService.deleteLogic(Func.toLongList(ids)));
	}

	
}
