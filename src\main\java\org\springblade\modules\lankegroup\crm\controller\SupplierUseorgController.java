/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.BatchAllocateParam;
import org.springblade.modules.lankegroup.crm.entity.CustomerUseOrgResult;
import org.springblade.modules.lankegroup.crm.entity.CustomerUseorg;
import org.springblade.modules.lankegroup.crm.entity.SupplierUseorg;
import org.springblade.modules.lankegroup.crm.service.ISupplierUseorgService;
import org.springblade.modules.lankegroup.crm.vo.SupplierUseorgVO;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 供应商分配使用组织关联表 控制器
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/supplier/supplieruseorg")
@Api(value = "供应商分配使用组织关联表", tags = "供应商分配使用组织关联表接口")
public class SupplierUseorgController extends BladeController {

	private final ISupplierUseorgService supplierUseorgService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入supplierUseorg")
	public R<SupplierUseorg> detail(SupplierUseorg supplierUseorg) {
		SupplierUseorg detail = supplierUseorgService.getOne(Condition.getQueryWrapper(supplierUseorg));
		return R.data(detail);
	}

	/**
	 * 分页 供应商分配使用组织关联表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入supplierUseorg")
	public R<IPage<SupplierUseorg>> list(SupplierUseorg supplierUseorg, Query query) {
		IPage<SupplierUseorg> pages = supplierUseorgService.page(Condition.getPage(query), Condition.getQueryWrapper(supplierUseorg));
		return R.data(pages);
	}

	/**
	 * 自定义分页 供应商分配使用组织关联表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入supplierUseorg")
	public R<IPage<SupplierUseorgVO>> page(SupplierUseorgVO supplierUseorg, Query query) {
		IPage<SupplierUseorgVO> pages = supplierUseorgService.selectSupplierUseorgPage(Condition.getPage(query), supplierUseorg);
		return R.data(pages);
	}

	/**
	 * 新增 供应商分配使用组织关联表
	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "新增", notes = "传入supplierUseorg")
//	public R save(@Valid @RequestBody SupplierUseorg supplierUseorg) throws Exception {
//		R r = supplierUseorgService.checkDistribution(supplierUseorg);
//		if(r.isSuccess()) {
//			Boolean kd = supplierUseorgService.KDAllocate(supplierUseorg);
//			if(kd){
//					return R.success("供应商分配成功！");
//				}
//				return R.fail(500,"金蝶供应商分配失败！");
//		}
//		return R.fail(500,r.getMsg());
//	}
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入supplierUseorg")
	public R save(@Valid @RequestBody BatchAllocateParam supplierUserorgs) throws Exception {

		if(supplierUserorgs.getSupplierOrgIds()!=null && supplierUserorgs.getSupplierOrgIds().size()>0) {
			for (SupplierUseorg org : supplierUserorgs.getSupplierOrgIds()) {
				org.setSupplierId(supplierUserorgs.getSupplierId());
			}
			CustomerUseOrgResult result = supplierUseorgService.checkDistribution(supplierUserorgs);
			List<SupplierUseorg> orgs = result.getSupplierUseorgs();
			if (orgs != null && orgs.size() > 0) {
				Boolean kd = supplierUseorgService.KDAllocate(orgs);
				if (kd) {
					if (result.getMsg() != null && (!result.getMsg().equals(""))) {
						return R.success(result.getMsg());
					}
					return R.success("供应商分配成功");
				}else{
					return R.fail(500,"供应商分配失败，请联系相关开发人员");
				}
			}else{
				return R.fail(500,result.getMsg());
			}
		}else{
			return R.fail(500,"请选择分配组织");
		}





//		R r = supplierUseorgService.checkDistribution(supplierUseorg);
//		if(r.isSuccess()) {
//			Boolean kd = supplierUseorgService.KDAllocate(supplierUseorg);
//			if(kd){
//				return R.success("供应商分配成功！");
//			}
//			return R.fail(500,"金蝶供应商分配失败！");
//		}
//		return R.fail(500,r.getMsg());
	}
	/**
	 * 修改 供应商分配使用组织关联表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入supplierUseorg")
	public R update(@Valid @RequestBody SupplierUseorg supplierUseorg) {
		return R.status(supplierUseorgService.updateById(supplierUseorg));
	}

	/**
	 * 新增或修改 供应商分配使用组织关联表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入supplierUseorg")
	public R submit(@Valid @RequestBody SupplierUseorg supplierUseorg) {
		return R.status(supplierUseorgService.saveOrUpdate(supplierUseorg));
	}

	
	/**
	 * 删除 供应商分配使用组织关联表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierUseorgService.deleteLogic(Func.toLongList(ids)));
	}

	
}
