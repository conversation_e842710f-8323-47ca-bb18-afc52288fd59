/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.develop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.develop.entity.Code;
import org.springblade.modules.develop.entity.Datasource;
import org.springblade.modules.develop.service.ICodeService;
import org.springblade.modules.develop.service.IDatasourceService;
import org.springblade.common.utils.DatabaseMetadataUtil;

import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

// 添加Velocity模板引擎相关导入
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Properties;
import java.util.List;
import java.util.ArrayList;



/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_DEVELOP_NAME + "/code")
@Api(value = "代码生成", tags = "代码生成")
@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
public class CodeController extends BladeController {

	private final ICodeService codeService;
	private final IDatasourceService datasourceService;
	private final DatabaseMetadataUtil databaseMetadataUtil;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入code")
	public R<Code> detail(Code code) {
		Code detail = codeService.getOne(Condition.getQueryWrapper(code));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "codeName", value = "模块名", paramType = "query", dataType = "string"),
			@ApiImplicitParam(name = "tableName", value = "表名", paramType = "query", dataType = "string"),
			@ApiImplicitParam(name = "modelName", value = "实体名", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入code")
	public R<IPage<Code>> list(@ApiIgnore @RequestParam Map<String, Object> code, Query query) {
		IPage<Code> pages = codeService.page(Condition.getPage(query), Condition.getQueryWrapper(code, Code.class));
		return R.data(pages);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入code")
	public R submit(@Valid @RequestBody Code code) {
		return R.status(codeService.submit(code));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(codeService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 复制
	 */
	@PostMapping("/copy")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "复制", notes = "传入id")
	public R copy(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		Code code = codeService.getById(id);
		code.setId(null);
		code.setCodeName(code.getCodeName() + "-copy");
		return R.status(codeService.save(code));
	}

	/**
	 * 代码生成
	 */
	@PostMapping("/gen-code")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "代码生成", notes = "传入ids")
	public R genCode(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, @RequestParam(defaultValue = "sword") String system) {
		Collection<Code> codes = codeService.listByIds(Func.toLongList(ids));
		codes.forEach(code -> {
			// 使用原有的BladeCodeGenerator，不再使用自定义版本
			org.springblade.develop.support.BladeCodeGenerator generator = new org.springblade.develop.support.BladeCodeGenerator();
			// 设置数据源
			Datasource datasource = datasourceService.getById(code.getDatasourceId());
			generator.setDriverName(datasource.getDriverClass());
			generator.setUrl(datasource.getUrl());
			generator.setUsername(datasource.getUsername());
			generator.setPassword(datasource.getPassword());
			// 设置基础配置
			generator.setSystemName(system);
			generator.setCodeName(code.getCodeName());
			generator.setServiceName(code.getServiceName());
			generator.setPackageName(code.getPackageName());
			generator.setPackageDir(code.getApiPath());
			generator.setPackageWebDir(code.getWebPath());
			generator.setTablePrefix(Func.toStrArray(code.getTablePrefix()));
			generator.setIncludeTables(Func.toStrArray(code.getTableName()));
			// 设置是否继承基础业务字段
			generator.setHasSuperEntity(code.getBaseMode() == 2);
			// 设置是否开启包装器模式
			generator.setHasWrapper(code.getWrapMode() == 2);
			// 设置控制器服务名前缀
			generator.setHasServiceName(Boolean.TRUE);

			// 运行标准代码生成
			generator.run();

			// 生成自定义VO文件 - 读取已生成的entity文件来获取正确的字段类型
			generateCustomVOFiles(code, system);
		});
		return R.success("代码生成成功");
	}

	/**
	 * 生成自定义VO文件 (BaseVO, CreateVO, UpdateVO, RespVO,pageReqVO)
	 * 读取已生成的entity文件来获取正确的字段类型信息
	 */
	private void generateCustomVOFiles(Code code, String system) {
		try {
			// 打印调试信息
			System.out.println("=== 开始生成自定义VO文件 ===");
			System.out.println("ApiPath: " + code.getApiPath());
			System.out.println("PackageName: " + code.getPackageName());
			System.out.println("TableName: " + code.getTableName());
			System.out.println("TablePrefix: " + code.getTablePrefix());

			// 配置Velocity引擎
			VelocityEngine velocityEngine = new VelocityEngine();
			Properties props = new Properties();
			props.put(RuntimeConstants.RESOURCE_LOADER, "classpath");
			props.put("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
			velocityEngine.init(props);

			// 处理每个表
			String[] tableNames = Func.toStrArray(code.getTableName());
			for (String tableName : tableNames) {
				String entityName = convertToCamelCase(tableName, code.getTablePrefix());
				System.out.println("表名: " + tableName + " -> 实体名: " + entityName);

				// 从已生成的entity文件中读取字段信息
				List<Map<String, Object>> tableFields = getFieldsFromEntityFile(code, entityName, tableName);

				// 准备模板变量
				VelocityContext context = setupVelocityContext(code, entityName, tableName, tableFields);

				// 生成四个VO文件
				generateVOFile(velocityEngine, context, "baseVO.java.vm", entityName + "BaseVO", code);
				generateVOFile(velocityEngine, context, "createVO.java.vm", entityName + "CreateVO", code);
				generateVOFile(velocityEngine, context, "updateVO.java.vm", entityName + "UpdateVO", code);
				generateVOFile(velocityEngine, context, "respVO.java.vm", entityName + "RespVO", code);
				generateVOFile(velocityEngine, context, "pageReqVO.java.vm", entityName + "PageReqVO", code);
			}

			System.out.println("自定义VO文件生成完成: BaseVO, CreateVO, UpdateVO, RespVO");

		} catch (Exception e) {
			System.err.println("生成自定义VO文件失败: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 从已生成的entity文件中读取字段信息
	 */
	private List<Map<String, Object>> getFieldsFromEntityFile(Code code, String entityName, String tableName) {
		List<Map<String, Object>> fields = new ArrayList<>();

		try {
			// 构建entity文件路径
			String apiPath = code.getApiPath();
			String packagePath = code.getPackageName().replace(".", File.separator);
			String entityDir = apiPath + File.separator + "src" + File.separator + "main" + File.separator + "java" + File.separator + packagePath + File.separator + "entity";
			String entityFilePath = entityDir + File.separator + entityName + ".java";

			System.out.println("读取Entity文件: " + entityFilePath);

			// 读取文件内容
			File entityFile = new File(entityFilePath);
			if (!entityFile.exists()) {
				System.err.println("Entity文件不存在: " + entityFilePath);
				return fields;
			}

			try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(entityFile))) {
				String line;
				String currentComment = "";

				while ((line = reader.readLine()) != null) {
					line = line.trim();

					// 读取注释
					if (line.startsWith("/**") || line.startsWith("/*")) {
						currentComment = "";
						continue;
					} else if (line.startsWith("*") && !line.equals("*/")) {
						// 提取注释内容
						String comment = line.substring(1).trim();
						if (!comment.isEmpty() && !comment.startsWith("@")) {
							currentComment = comment;
						}
						continue;
					} else if (line.equals("*/")) {
						continue;
					}

					// 解析字段声明：private Type fieldName;
					if (line.startsWith("private ") && line.endsWith(";")) {
						String fieldDeclaration = line.substring(8, line.length() - 1).trim(); // 去掉 "private " 和 ";"
						String[] parts = fieldDeclaration.split("\\s+");

						System.out.println("解析行: " + line + " -> 声明: " + fieldDeclaration);

						if (parts.length >= 2) {
							String propertyType = parts[0];
							String propertyName = parts[1];

							System.out.println("  找到字段候选: " + propertyName + ", 类型: " + propertyType);

							// 跳过BaseEntity中的字段
							if (isBaseEntityField(propertyName)) {
								System.out.println("  跳过BaseEntity字段: " + propertyName);
								currentComment = "";
								continue;
							}

							// 跳过其他不该包含的字段
							if (shouldSkipField(propertyName, propertyType)) {
								System.out.println("  跳过特殊字段: " + propertyName);
								currentComment = "";
								continue;
							}

							// 判断是否必填（根据数据库约束）
							boolean isRequired = isFieldRequired(tableName, propertyName);

							Map<String, Object> field = new HashMap<>();
							field.put("propertyName", propertyName);
							field.put("propertyType", propertyType);
							field.put("comment", Func.isBlank(currentComment) ? propertyName : currentComment);
							field.put("isRequired", isRequired);

							fields.add(field);
							System.out.println("  ✓ 包含字段: " + propertyName + ", 类型: " + propertyType + ", 注释: " + currentComment + ", 必填: " + isRequired);
						}

						currentComment = "";
					}
				}
			}

			System.out.println("从Entity文件共解析到 " + fields.size() + " 个字段");

		} catch (Exception e) {
			System.err.println("读取Entity文件失败: " + e.getMessage());
			e.printStackTrace();
		}

		return fields;
	}

	/**
	 * 判断是否为BaseEntity中的字段
	 */
	private boolean isBaseEntityField(String propertyName) {
		return "id".equals(propertyName) ||
				"createTime".equals(propertyName) ||
				"createUser".equals(propertyName) ||
				"createDept".equals(propertyName) ||
				"updateTime".equals(propertyName) ||
				"updateUser".equals(propertyName) ||
				"status".equals(propertyName) ||
				"isDeleted".equals(propertyName) ||
				"tenantId".equals(propertyName);
	}

	/**
	 * 判断是否应该跳过某个字段
	 */
	private boolean shouldSkipField(String propertyName, String propertyType) {
		// 跳过序列化版本号
		if ("serialVersionUID".equals(propertyName)) {
			return true;
		}

		// 跳过常量字段（通常以大写字母开头或包含下划线）
		if (propertyName.toUpperCase().equals(propertyName) || propertyName.contains("_")) {
			return true;
		}

		// 跳过可能的内部字段
		if (propertyName.startsWith("$") || propertyName.startsWith("_")) {
			return true;
		}

		return false;
	}

	/**
	 * 根据数据库约束判断字段是否必填
	 */
	private boolean isFieldRequired(String tableName, String propertyName) {
		try {
			// 先尝试从数据库查询字段约束
			Boolean required = databaseMetadataUtil.isFieldRequired(tableName, propertyName);
			if (required != null) {
				System.out.println("  数据库约束查询成功: " + propertyName + " 必填=" + required);
				return required;
			}
		} catch (Exception e) {
			System.out.println("  ⚠️  查询数据库约束失败，使用字段名判断: " + e.getMessage());
			System.out.println("     表名: " + tableName + ", 字段名: " + propertyName);
		}

		// 如果数据库查询失败，使用基于字段名的fallback逻辑
		boolean fallbackResult = isFieldRequiredByName(propertyName);
		System.out.println("  使用fallback逻辑判断: " + propertyName + " 必填=" + fallbackResult);
		return fallbackResult;
	}

	/**
	 * 基于字段名判断是否必填（fallback逻辑）
	 */
	private boolean isFieldRequiredByName(String propertyName) {
		// 常见的必填字段名
		return "name".equals(propertyName) ||
				"title".equals(propertyName) ||
				"code".equals(propertyName) ||
				propertyName.endsWith("Name") ||
				propertyName.endsWith("Code") ||
				propertyName.endsWith("Type");
	}



	/**
	 * 设置Velocity模板上下文
	 */
	private VelocityContext setupVelocityContext(Code code, String entityName, String tableName, List<Map<String, Object>> tableFields) {
		VelocityContext context = new VelocityContext();

		// 包信息
		Map<String, String> packageInfo = new HashMap<>();
		packageInfo.put("Entity", code.getPackageName() + ".entity");
		context.put("package", packageInfo);

		// 实体信息
		context.put("entity", entityName);

		// 表信息
		Map<String, Object> table = new HashMap<>();
		table.put("comment", tableName + "表");
		table.put("fields", tableFields);  // 重要：设置字段列表
		context.put("table", table);

		// 其他信息
		context.put("author", "Blade");
		context.put("date", java.time.LocalDate.now().toString());

		return context;
	}

	/**
	 * 使用Velocity引擎生成VO文件
	 */
	private void generateVOFile(VelocityEngine velocityEngine, VelocityContext context, String templateName, String fileName, Code code) {
		try {
			// 使用Velocity引擎处理模板
			StringWriter sw = new StringWriter();
			velocityEngine.mergeTemplate("/templates/" + templateName, "UTF-8", context, sw);

			// 构建输出路径，使用与BladeCodeGenerator相同的根路径
			String apiPath = code.getApiPath();
			String packagePath = code.getPackageName().replace(".", File.separator);
			String voDir = apiPath + File.separator + "src" + File.separator + "main" + File.separator + "java" + File.separator + packagePath + File.separator + "vo";

			System.out.println("使用ApiPath: " + apiPath);
			System.out.println("最终VO目录: " + voDir);

			// 创建目录
			File dir = new File(voDir);
			if (!dir.exists()) {
				boolean created = dir.mkdirs();
				System.out.println("创建目录: " + voDir + (created ? " 成功" : " 失败"));
			}

			// 写入文件
			String filePath = voDir + File.separator + fileName + ".java";
			try (FileWriter fileWriter = new FileWriter(filePath)) {
				fileWriter.write(sw.toString());
			}

			System.out.println("生成文件: " + filePath);

		} catch (Exception e) {
			System.err.println("生成文件失败: " + fileName + ", 错误: " + e.getMessage());
			e.printStackTrace();
		}
	}



	/**
	 * 将表名转换为驼峰命名的实体名
	 */
	private String convertToCamelCase(String tableName, String tablePrefix) {
		// 移除表前缀
		if (Func.isNotBlank(tablePrefix)) {
			String[] prefixes = tablePrefix.split(",");
			for (String prefix : prefixes) {
				if (tableName.startsWith(prefix.trim())) {
					tableName = tableName.substring(prefix.trim().length());
					break;
				}
			}
		}

		// 转换为驼峰命名
		String[] parts = tableName.split("_");
		StringBuilder entityName = new StringBuilder();
		for (String part : parts) {
			if (part.length() > 0) {
				entityName.append(Character.toUpperCase(part.charAt(0)));
				if (part.length() > 1) {
					entityName.append(part.substring(1).toLowerCase());
				}
			}
		}

		return entityName.toString();
	}

}

