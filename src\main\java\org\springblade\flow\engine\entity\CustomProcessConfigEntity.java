package org.springblade.flow.engine.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 9:30
 * @desc 自定义流程配置实体
 */
@Data
@TableName("custom_process_config")
public class CustomProcessConfigEntity {
	@ApiModelProperty(value = "主键")
	private Long id;

	@ApiModelProperty(value = "关联流程定义的id")
	private String processDefinitionId;

	@ApiModelProperty(value = "无审批人（离职/找不到人）对应系统字典表（1转交管理员；2自动通过；3自动拒绝）")
	private String noApprover;

	@ApiModelProperty(value = "是否自动去重(1去重，0不去重)")
	private int deduplication;
}
