package org.springblade.modules.lankegroup.clientrelation.controller;

import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.service.AreaService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 区域
 */
@AllArgsConstructor
@RestController
@RequestMapping("area")
public class AreaController {

    private final AreaService areaService;

    @GetMapping
    public R getAreaList(String parentCode){
        return areaService.getAreaList(parentCode);
    }

}
