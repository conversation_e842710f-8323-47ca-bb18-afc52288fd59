/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerCareMaterial;
import org.springblade.modules.lankegroup.crm.vo.CustomerCareMaterialVO;
import org.springblade.modules.lankegroup.crm.wrapper.CustomerCareMaterialWrapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerCareMaterialService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户关怀物料表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/care/customercarematerial")
@Api(value = "客户关怀物料表", tags = "客户关怀物料表接口")
public class CustomerCareMaterialController extends BladeController {

	private final ICustomerCareMaterialService customerCareMaterialService;

	/**
	 * 详情
	 */
	@ApiLog("详情")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerCareMaterial")
	public R<CustomerCareMaterialVO> detail(CustomerCareMaterial customerCareMaterial) {
		CustomerCareMaterial detail = customerCareMaterialService.getOne(Condition.getQueryWrapper(customerCareMaterial));
		return R.data(CustomerCareMaterialWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 客户关怀物料表
	 */
	@ApiLog("分页 客户关怀物料表")
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerCareMaterial")
	public R<IPage<CustomerCareMaterialVO>> list(CustomerCareMaterial customerCareMaterial, Query query) {
		IPage<CustomerCareMaterial> pages = customerCareMaterialService.page(Condition.getPage(query), Condition.getQueryWrapper(customerCareMaterial));
		return R.data(CustomerCareMaterialWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 客户关怀物料表
	 */
	@ApiLog("客户关怀物料表")
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerCareMaterial")
	public R<IPage<CustomerCareMaterialVO>> page(CustomerCareMaterialVO customerCareMaterial, Query query) {
		IPage<CustomerCareMaterialVO> pages = customerCareMaterialService.selectCustomerCareMaterialPage(Condition.getPage(query), customerCareMaterial);
		return R.data(pages);
	}

	/**
	 * 新增 客户关怀物料表
	 */
	@ApiLog("新增 客户关怀物料表")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerCareMaterial")
	public R save(@Valid @RequestBody CustomerCareMaterial customerCareMaterial) {
		return R.status(customerCareMaterialService.save(customerCareMaterial));
	}

	/**
	 * 修改 客户关怀物料表
	 */
	@ApiLog("修改 客户关怀物料表")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerCareMaterial")
	public R update(@Valid @RequestBody CustomerCareMaterial customerCareMaterial) {
		return R.status(customerCareMaterialService.updateById(customerCareMaterial));
	}

	/**
	 * 新增或修改 客户关怀物料表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerCareMaterial")
	public R submit(@Valid @RequestBody CustomerCareMaterial customerCareMaterial) {
		return R.status(customerCareMaterialService.saveOrUpdate(customerCareMaterial));
	}

	
	/**
	 * 删除 客户关怀物料表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerCareMaterialService.deleteLogic(Func.toLongList(ids)));
	}

	
}
