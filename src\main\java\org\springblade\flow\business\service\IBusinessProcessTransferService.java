/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.mp.base.BaseService;
import org.springblade.flow.business.entity.BusinessProcessTransferEntity;

/**
 * 自定义流程审批中的所有业务单据任务 服务类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IBusinessProcessTransferService extends IService<BusinessProcessTransferEntity> {

	/**
	 * 添加转交任务
	 * @param processInstanceId
	 * @param remark
	 */
	void saveTransferTask(String processInstanceId,String taskId,  String remark);
}
