package org.springblade.common.enums;

public enum FinanceTypeEnum {
    //    取科目分组【PRE10的科目：6601/6602/6603         PRE12的科目：6711          PRE11的科目：6051/6301        PRE08的科目：6001        PRE09的科目：6401 】
    科目6001("6001","PRE08"),
    科目6401("6401","PRE09"),
    科目6601("6601","PRE10"),
    科目6602("6602","PRE10"),
    科目6603("6603","PRE10"),
    科目6051("6051","PRE11"),
    科目6301("6301","PRE11"),
    科目6711("6711","PRE12");
    private String code;
    private String typeCode;

    FinanceTypeEnum(String code, String typeCode) {
        this.code = code;
        this.typeCode = typeCode;
    }

    public String getCode() {
        return code;
    }

    public String getTypeCode() {
        return typeCode;
    }
    public static String getTypeCodeByCode(String code) {
        for (FinanceTypeEnum c : FinanceTypeEnum.values()) {
            if (c.getCode().startsWith(code)) {
                return c.getTypeCode();
            }
        }
        return null;
    }
}
