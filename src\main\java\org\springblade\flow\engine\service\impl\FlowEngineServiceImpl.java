/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.engine.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import liquibase.pro.packaged.D;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.impl.util.IoUtil;
import org.flowable.common.engine.impl.util.io.StringStreamSource;
import org.flowable.editor.language.json.converter.BpmnJsonConverter;
import org.flowable.editor.language.json.converter.util.CollectionUtils;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springblade.common.utils.JsonUtils;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.flow.core.enums.FlowModeEnum;
import org.springblade.flow.core.enums.ProcessApprovalConfigEunm;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.builder.ApproverTypeEnum;
import org.springblade.flow.engine.builder.BpmnBuilder;
import org.springblade.flow.engine.builder.entity.ApproverFlowNode;
import org.springblade.flow.engine.cache.FlowProcessConfigCache;
import org.springblade.flow.engine.constant.FlowEngineConstant;
import org.springblade.flow.engine.constant.FlowProcessConfigConstant;
import org.springblade.flow.engine.entity.CustomProcessConfigEntity;
import org.springblade.flow.engine.entity.FlowExecution;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.mapper.CustomProcessConfigMapper;
import org.springblade.flow.engine.mapper.FlowMapper;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.flow.engine.utils.FlowApproverVariablesUtils;
import org.springblade.flow.engine.utils.FlowCache;
import org.springblade.flow.engine.utils.FlowElementPredictionUtils;
import org.springblade.flow.engine.utils.UserTaskButtonUtils;
import org.springblade.flow.engine.vo.FlowModelDeployVO;
import org.springblade.flow.engine.vo.WorkFlowVO;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.FLOW_CACHE;
import static org.springblade.flow.core.constant.WorkFlowConstants.*;
import static org.springblade.flow.engine.constant.FlowEngineConstant.*;

/**
 * 工作流服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FlowEngineServiceImpl extends ServiceImpl<FlowMapper, FlowModel> implements FlowEngineService {
	private static final String ALREADY_IN_STATE = "already in state";
	private static final String IMAGE_NAME = "image";
	private static final String XML_NAME = "xml";
	private static final Integer INT_1024 = 1024;
	private static final BpmnJsonConverter BPMN_JSON_CONVERTER = new BpmnJsonConverter();
	private static final BpmnXMLConverter BPMN_XML_CONVERTER = new BpmnXMLConverter();
	private final ObjectMapper objectMapper;
	private final RepositoryService repositoryService;
	private final RuntimeService runtimeService;
	private final HistoryService historyService;
	private final TaskService taskService;
	private final ProcessEngine processEngine;
	private final CustomProcessConfigMapper customProcessConfigMapper;
	private final IBusinessProcessService businessProcessService;


	@Override
	public IPage<FlowModel> selectFlowPage(IPage<FlowModel> page, FlowModel flowModel) {
		return page.setRecords(baseMapper.selectFlowPage(page, flowModel));
	}

	@Override
	public IPage<FlowProcess> selectProcessPage(IPage<FlowProcess> page, String category, Integer mode) {
		ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery().latestVersion().orderByProcessDefinitionKey().asc();
		// 通用流程
		if (mode == FlowModeEnum.COMMON.getMode()) {
			processDefinitionQuery.processDefinitionWithoutTenantId();
		}
		// 定制流程
		else if (!AuthUtil.isAdministrator()) {
			processDefinitionQuery.processDefinitionTenantId(AuthUtil.getTenantId());
		}
		if (StringUtils.isNotEmpty(category)) {
			processDefinitionQuery.processDefinitionCategory(category);
		}
		List<ProcessDefinition> processDefinitionList = processDefinitionQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));
		List<FlowProcess> flowProcessList = new ArrayList<>();
		processDefinitionList.forEach(processDefinition -> {
			String deploymentId = processDefinition.getDeploymentId();
			Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
			FlowProcess flowProcess = new FlowProcess((ProcessDefinitionEntityImpl) processDefinition);
			flowProcess.setDeploymentTime(deployment.getDeploymentTime());
			flowProcessList.add(flowProcess);
		});
		page.setTotal(processDefinitionQuery.count());
		page.setRecords(flowProcessList);
		return page;
	}

	@Override
	public IPage<FlowExecution> selectFollowPage(IPage<FlowExecution> page, String processInstanceId, String processDefinitionKey) {
		ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();
		if (StringUtil.isNotBlank(processInstanceId)) {
			processInstanceQuery.processInstanceId(processInstanceId);
		}
		if (StringUtil.isNotBlank(processDefinitionKey)) {
			processInstanceQuery.processDefinitionKey(processDefinitionKey);
		}
		List<FlowExecution> flowList = new ArrayList<>();
		List<ProcessInstance> procInsList = processInstanceQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));
		procInsList.forEach(processInstance -> {
			ExecutionEntityImpl execution = (ExecutionEntityImpl) processInstance;
			FlowExecution flowExecution = new FlowExecution();
			flowExecution.setId(execution.getId());
			flowExecution.setName(execution.getName());
			flowExecution.setStartUserId(execution.getStartUserId());
			User taskUser = UserCache.getUserByTaskUser(execution.getStartUserId());
			if (taskUser != null) {
				flowExecution.setStartUser(taskUser.getName());
			}
			flowExecution.setStartTime(execution.getStartTime());
			flowExecution.setExecutionId(execution.getId());
			flowExecution.setProcessInstanceId(execution.getProcessInstanceId());
			flowExecution.setProcessDefinitionId(execution.getProcessDefinitionId());
			flowExecution.setProcessDefinitionKey(execution.getProcessDefinitionKey());
			flowExecution.setSuspensionState(execution.getSuspensionState());
			FlowProcess processDefinition = FlowCache.getProcessDefinition(execution.getProcessDefinitionId());
			flowExecution.setCategory(processDefinition.getCategory());
			flowExecution.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flowList.add(flowExecution);
		});
		page.setTotal(processInstanceQuery.count());
		page.setRecords(flowList);
		return page;
	}

	@Override
	public List<BladeFlow> historyFlowList(String processInstanceId, String startActivityId, String endActivityId) {
		List<BladeFlow> flowList = new LinkedList<>();
		List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().orderByHistoricActivityInstanceEndTime().asc().list();
		boolean start = false;
		Map<String, Integer> activityMap = new HashMap<>(16);
		for (int i = 0; i < historicActivityInstanceList.size(); i++) {
			HistoricActivityInstance historicActivityInstance = historicActivityInstanceList.get(i);
			// 过滤开始节点前的节点
			if (StringUtil.isNotBlank(startActivityId) && startActivityId.equals(historicActivityInstance.getActivityId())) {
				start = true;
			}
			if (StringUtil.isNotBlank(startActivityId) && !start) {
				continue;
			}
			// 显示开始节点和结束节点，并且执行人不为空的任务
			if (StringUtils.equals(USR_TASK, historicActivityInstance.getActivityType())
				|| FlowEngineConstant.START_EVENT.equals(historicActivityInstance.getActivityType())
				|| END_EVENT.equals(historicActivityInstance.getActivityType())) {
				// 给节点增加序号
				Integer activityNum = activityMap.get(historicActivityInstance.getActivityId());
				if (activityNum == null) {
					activityMap.put(historicActivityInstance.getActivityId(), activityMap.size());
				}
				BladeFlow flow = new BladeFlow();
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flow.setHistoryActivityName(historicActivityInstance.getActivityName());
				flow.setCreateTime(historicActivityInstance.getStartTime());
				flow.setEndTime(historicActivityInstance.getEndTime());
				String durationTime = DateUtil.secondToTime(Func.toLong(historicActivityInstance.getDurationInMillis(), 0L) / 1000);
				flow.setHistoryActivityDurationTime(durationTime);
				// 获取流程发起人名称
				if (FlowEngineConstant.START_EVENT.equals(historicActivityInstance.getActivityType())) {
					List<HistoricProcessInstance> processInstanceList = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).orderByProcessInstanceStartTime().asc().list();
					if (processInstanceList.size() > 0) {
						if (StringUtil.isNotBlank(processInstanceList.get(0).getStartUserId())) {
							String taskUser = processInstanceList.get(0).getStartUserId();
							User user = UserCache.getUser(TaskUtil.getUserId(taskUser));
							if (user != null) {
								flow.setAssignee(historicActivityInstance.getAssignee());
								flow.setAssigneeName(user.getName());
							}
						}
					}
				}
				// 获取任务执行人名称
				if (StringUtil.isNotBlank(historicActivityInstance.getAssignee())) {
					User user = UserCache.getUser(TaskUtil.getUserId(historicActivityInstance.getAssignee()));
					if (user != null) {
						flow.setAssignee(historicActivityInstance.getAssignee());
						flow.setAssigneeName(user.getName());
					}
				}
				// 获取意见评论内容
				if (StringUtil.isNotBlank(historicActivityInstance.getTaskId())) {
					List<Comment> commentList = taskService.getTaskComments(historicActivityInstance.getTaskId());
					if (commentList.size() > 0) {
						flow.setComment(commentList.get(0).getFullMessage());
					}
				}
				flowList.add(flow);
			}
			// 过滤结束节点后的节点
			if (StringUtils.isNotBlank(endActivityId) && endActivityId.equals(historicActivityInstance.getActivityId())) {
				boolean temp = false;
				Integer activityNum = activityMap.get(historicActivityInstance.getActivityId());
				// 该活动节点，后续节点是否在结束节点之前，在后续节点中是否存在
				for (int j = i + 1; j < historicActivityInstanceList.size(); j++) {
					HistoricActivityInstance hi = historicActivityInstanceList.get(j);
					Integer activityNumA = activityMap.get(hi.getActivityId());
					boolean numberTemp = activityNumA != null && activityNumA < activityNum;
					boolean equalsTemp = StringUtils.equals(hi.getActivityId(), historicActivityInstance.getActivityId());
					if (numberTemp || equalsTemp) {
						temp = true;
					}
				}
				if (!temp) {
					break;
				}
			}
		}
		return flowList;
	}

	@Override
	public String changeState(String state, String processId) {
		try {
			if (state.equals(FlowEngineConstant.ACTIVE)) {
				repositoryService.activateProcessDefinitionById(processId, true, null);
				return StringUtil.format("激活ID为 [{}] 的流程成功", processId);
			} else if (state.equals(FlowEngineConstant.SUSPEND)) {
				repositoryService.suspendProcessDefinitionById(processId, true, null);
				return StringUtil.format("挂起ID为 [{}] 的流程成功", processId);
			} else {
				return "暂无流程变更";
			}
		} catch (Exception e) {
			if (e.getMessage().contains(ALREADY_IN_STATE)) {
				return StringUtil.format("ID为 [{}] 的流程已是此状态，无需操作", processId);
			}
			return e.getMessage();
		}
	}

	@Override
	public boolean deleteDeployment(String deploymentIds) {
		Func.toStrList(deploymentIds).forEach(deploymentId -> repositoryService.deleteDeployment(deploymentId, true));
		return true;
	}

	@Override
	public boolean deployUpload(List<MultipartFile> files, String category, List<String> tenantIdList) {
		files.forEach(file -> {
			try {
				String fileName = file.getOriginalFilename();
				InputStream fileInputStream = file.getInputStream();
				byte[] bytes = FileUtil.copyToByteArray(fileInputStream);
				if (Func.isNotEmpty(tenantIdList)) {
					tenantIdList.forEach(tenantId -> {
						Deployment deployment = repositoryService.createDeployment().addBytes(fileName, bytes).tenantId(tenantId).deploy();
						deploy(deployment, category);
					});
				} else {
					Deployment deployment = repositoryService.createDeployment().addBytes(fileName, bytes).deploy();
					deploy(deployment, category);
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		});
		return true;
	}

	@Override
	public boolean deployModel(String modelId, String category, List<String> tenantIdList) {
		FlowModel model = this.getById(modelId);
		if (model == null) {
			throw new ServiceException("未找到模型 id: " + modelId);
		}
		byte[] bytes = getBpmnXML(model);
		String processName = model.getName();
		if (!StringUtil.endsWithIgnoreCase(processName, FlowEngineConstant.SUFFIX)) {
			processName += FlowEngineConstant.SUFFIX;
		}
		String finalProcessName = processName;
		if (Func.isNotEmpty(tenantIdList)) {
			tenantIdList.forEach(tenantId -> {
				Deployment deployment = repositoryService.createDeployment().addBytes(finalProcessName, bytes).name(model.getName()).key(model.getModelKey()).tenantId(tenantId).deploy();
				deploy(deployment, category);
			});
		} else {
			Deployment deployment = repositoryService.createDeployment().addBytes(finalProcessName, bytes).name(model.getName()).key(model.getModelKey()).deploy();
			deploy(deployment, category);
		}
		return true;
	}

	@Override
	public boolean deployModel(String modelId) {
		FlowModel flowModel = this.getById(modelId);
		if (flowModel == null) {
			throw new ServiceException("未找到模型 id: " + modelId);
		}
		//部署流程
		byte[] bytes = getBpmnXML(flowModel);
		String processName = flowModel.getName();
		if (!StringUtil.endsWithIgnoreCase(processName, FlowEngineConstant.SUFFIX)) {
			processName += FlowEngineConstant.SUFFIX;
		}
		Deployment deployment = repositoryService.createDeployment().addBytes(processName, bytes).name(flowModel.getName()).key(flowModel.getModelKey()).deploy();
		deploy(deployment, StringUtil.format(flowModel.getCategory()));
		ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
			.deploymentId(deployment.getId())
			.singleResult();

		update(Wrappers.lambdaUpdate(FlowModel.class).set(FlowModel::getProcessDefinitionId, processDefinition.getId()).eq(FlowModel::getId, flowModel.getId()));

		return true;
	}

	@Override
	public boolean deleteProcessInstance(String processInstanceId, String deleteReason) {
		runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
		return true;
	}

	private void deploy(Deployment deployment, String category) {
		log.debug("流程部署--------deploy:  " + deployment + "  分类---------->" + category);
		List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).list();
		StringBuilder logBuilder = new StringBuilder(500);
		List<Object> logArgs = new ArrayList<>();
		// 设置流程分类
		for (ProcessDefinition processDefinition : list) {
			if (StringUtil.isNotBlank(category)) {
				repositoryService.setProcessDefinitionCategory(processDefinition.getId(), category);
			}
			logBuilder.append("部署成功,流程ID={} \n");
			logArgs.add(processDefinition.getId());
		}
		if (list.size() == 0) {
			throw new ServiceException("部署失败,未找到流程");
		} else {
			log.info(logBuilder.toString(), logArgs.toArray());
		}
	}

	@Override
	public R submitModel(FlowModel model) {
//		FlowModel flowModel = new FlowModel();
//		flowModel.setId(model.getId());
//		flowModel.setVersion(Func.toInt(model.getVersion(), 0) + 1);
//		flowModel.setName(model.getName());
//		flowModel.setModelKey(model.getModelKey());
//		flowModel.setModelType(FlowModel.MODEL_TYPE_BPMN);
//		flowModel.setCreatedBy(TaskUtil.getTaskUser());
//		flowModel.setDescription(model.getDescription());
//		flowModel.setLastUpdated(Calendar.getInstance().getTime());
//		flowModel.setLastUpdatedBy(TaskUtil.getTaskUser());
//		flowModel.setTenantId(AuthUtil.getTenantId());
//		flowModel.setModelEditorXml(model.getModelEditorXml());
//		if (StringUtil.isBlank(model.getId())) {
//			flowModel.setCreated(Calendar.getInstance().getTime());
//		}
//		if (StringUtil.isNotBlank(model.getModelEditorXml())) {
//			flowModel.setModelEditorJson(getBpmnJson(model.getModelEditorXml()));
//		}
		FlowModel flowModel = new FlowModel();
		flowModel.setId(model.getId());
		flowModel.setName(model.getName());
		flowModel.setModelKey(model.getModelKey());
		flowModel.setFormKey(model.getFormKey());
		flowModel.setModelType(FlowModel.MODEL_TYPE_BPMN);
		flowModel.setCreatedBy(String.valueOf(AuthUtil.getUserId()));
		flowModel.setDescription(model.getDescription());
		flowModel.setLastUpdated(Calendar.getInstance().getTime());
		flowModel.setLastUpdatedBy(String.valueOf(AuthUtil.getUserId()));
		flowModel.setTenantId(AuthUtil.getTenantId());
		flowModel.setDesignScheme(model.getDesignScheme());
		if (Func.isNotEmpty(model.getDesignScheme())) {
			BpmnModel bpmnModel = BpmnBuilder.build(JsonUtils.objToJson(model.getDesignScheme()));
			flowModel.setModelEditorXml(new String(new BpmnXMLConverter().convertToXML(bpmnModel)));
		}
		if (StringUtil.isBlank(model.getId())) {
			flowModel.setCreated(Calendar.getInstance().getTime());
			flowModel.setVersion(1);
		} else {
			flowModel.setVersion(baseMapper.selectById(model.getId()).getVersion() + 1);
		}
		if (StringUtil.isNotBlank(flowModel.getModelEditorXml())) {
			flowModel.setModelEditorJson(getBpmnJson(flowModel.getModelEditorXml()));
		}
		flowModel.setModelKey(model.getModelKey());
		flowModel.setCategory(model.getCategory());
		flowModel.setSubclass(model.getSubclass());
		flowModel.setPublishStatus(0);
		flowModel.setDataSource(1);
		this.saveOrUpdate(flowModel);
//		deployModel(flowModel.getId());
		return R.data(flowModel.getId());
	}

	@Override
	public R saveAndDeploy(FlowModelDeployVO model) {
		CacheUtil.clear(FLOW_CACHE);
		FlowModel flowModel = new FlowModel();
		flowModel.setId(model.getId());
		flowModel.setName(model.getName());
		flowModel.setModelKey(model.getModelKey());
		flowModel.setFormKey(model.getFormKey());
		flowModel.setModelType(FlowModel.MODEL_TYPE_BPMN);
		flowModel.setCreatedBy(String.valueOf(AuthUtil.getUserId()));
		flowModel.setDescription(model.getDescription());
		flowModel.setLastUpdated(Calendar.getInstance().getTime());
		flowModel.setLastUpdatedBy(String.valueOf(AuthUtil.getUserId()));
		flowModel.setTenantId(AuthUtil.getTenantId());
		flowModel.setDesignScheme(model.getDesignScheme());
		if (Func.isNotEmpty(model.getDesignScheme())) {
			BpmnModel bpmnModel = BpmnBuilder.build(JsonUtils.objToJson(model.getDesignScheme()));
			flowModel.setModelEditorXml(new String(new BpmnXMLConverter().convertToXML(bpmnModel)));
		}
		if (StringUtil.isBlank(model.getId())) {
			flowModel.setCreated(Calendar.getInstance().getTime());
			flowModel.setVersion(1);
		} else {
			flowModel.setVersion(baseMapper.selectById(model.getId()).getVersion() + 1);
		}
		if (StringUtil.isNotBlank(flowModel.getModelEditorXml())) {
			flowModel.setModelEditorJson(getBpmnJson(flowModel.getModelEditorXml()));
		}
		flowModel.setModelKey(model.getModelKey());
		flowModel.setCategory(model.getCategory());
		flowModel.setSubclass(model.getSubclass());
		flowModel.setPublishStatus(1);
		flowModel.setDataSource(1);
		Boolean save = this.saveOrUpdate(flowModel);
		if (save) {
			//部署流程
			byte[] bytes = getBpmnXML(flowModel);
			String processName = flowModel.getName();
			if (!StringUtil.endsWithIgnoreCase(processName, FlowEngineConstant.SUFFIX)) {
				processName += FlowEngineConstant.SUFFIX;
			}
			Deployment deployment = repositoryService.createDeployment().addBytes(processName, bytes).name(flowModel.getName()).key(flowModel.getModelKey()).deploy();
			deploy(deployment, StringUtil.format(flowModel.getCategory()));
			ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
				.deploymentId(deployment.getId())
				.singleResult();

			update(Wrappers.lambdaUpdate(FlowModel.class).set(FlowModel::getProcessDefinitionId, processDefinition.getId()).eq(FlowModel::getId, flowModel.getId()));

			//保存公有配置
			CustomProcessConfigEntity customProcessConfigEntity = new CustomProcessConfigEntity();
//			customProcessConfigEntity.setId(model.getCustomProcessConfigId());
			customProcessConfigEntity.setProcessDefinitionId(processDefinition.getId());
			customProcessConfigEntity.setNoApprover(model.getNoApprover());
			customProcessConfigEntity.setDeduplication(model.getDeduplication());
//			if (model.getCustomProcessConfigId() == null) {
				customProcessConfigMapper.insert(customProcessConfigEntity);
//			} else {
//				customProcessConfigMapper.updateById(customProcessConfigEntity);
//			}
		}
		return R.status(save);
	}

	@Override
	public R<FlowModelDeployVO> detail(String id) {
		FlowModel flowModel = this.getById(id);
		FlowModelDeployVO flowModelDeployVO = Objects.requireNonNull(BeanUtil.copy(flowModel, FlowModelDeployVO.class));
		CustomProcessConfigEntity customProcessConfigEntity = customProcessConfigMapper.selectOne(Wrappers.<CustomProcessConfigEntity>lambdaQuery().eq(CustomProcessConfigEntity::getProcessDefinitionId, flowModel.getProcessDefinitionId()));
		if (customProcessConfigEntity != null) {
			flowModelDeployVO.setCustomProcessConfigId(customProcessConfigEntity.getId());
			flowModelDeployVO.setProcessDefinitionId(customProcessConfigEntity.getProcessDefinitionId());
			flowModelDeployVO.setNoApprover(customProcessConfigEntity.getNoApprover());
			flowModelDeployVO.setDeduplication(customProcessConfigEntity.getDeduplication());
		}
		return R.data(flowModelDeployVO);
	}

	@Override
	public Map<String, Object> modelView(String processDefinitionId, String processInstanceId) {
		Map<String, Object> result = new HashMap<>();
		// 节点标记
		if (StringUtil.isNotBlank(processInstanceId)) {
			result.put("flow", this.historyFlowList(processInstanceId, null, null));
			HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();
			processDefinitionId = processInstance.getProcessDefinitionId();
		}
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		// 流程图展示
		result.put("xml", new String(new BpmnXMLConverter().convertToXML(bpmnModel)));
		return result;
	}

	@Override
	public void diagramView(String processInstanceId, HttpServletResponse httpServletResponse) {
		// 获得当前活动的节点
		String processDefinitionId;
		// 如果流程已经结束，则得到结束节点
		if (this.isFinished(processInstanceId)) {
			HistoricProcessInstance pi = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
			processDefinitionId = pi.getProcessDefinitionId();
		} else {
			// 如果流程没有结束，则取当前活动节点
			// 根据流程实例ID获得当前处于活动状态的ActivityId合集
			ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
			processDefinitionId = pi.getProcessDefinitionId();
		}
		List<String> highLightedActivities = new ArrayList<>();

		// 获得活动的节点
		List<HistoricActivityInstance> highLightedActivityList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().list();

		for (HistoricActivityInstance tempActivity : highLightedActivityList) {
			String activityId = tempActivity.getActivityId();
			highLightedActivities.add(activityId);
		}

		List<String> flows = new ArrayList<>();
		// 获取流程图
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		ProcessEngineConfiguration engConf = processEngine.getProcessEngineConfiguration();

		ProcessDiagramGenerator diagramGenerator = engConf.getProcessDiagramGenerator();
		InputStream in = diagramGenerator.generateDiagram(bpmnModel, "bmp", highLightedActivities, flows, engConf.getActivityFontName(),
			engConf.getLabelFontName(), engConf.getAnnotationFontName(), engConf.getClassLoader(), 1.0, true);
		OutputStream out = null;
		byte[] buf = new byte[1024];
		int length;
		try {
			out = httpServletResponse.getOutputStream();
			while ((length = in.read(buf)) != -1) {
				out.write(buf, 0, length);
			}
		} catch (IOException e) {
			log.error("操作异常", e);
		} finally {
			IoUtil.closeSilently(out);
			IoUtil.closeSilently(in);
		}
	}

	@Override
	public void resourceView(String processDefinitionId, String processInstanceId, String resourceType, HttpServletResponse response) {
		if (StringUtil.isAllBlank(processDefinitionId, processInstanceId)) {
			return;
		}
		if (StringUtil.isBlank(processDefinitionId)) {
			ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
			processDefinitionId = processInstance.getProcessDefinitionId();
		}
		ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).singleResult();
		String resourceName = "";
		if (resourceType.equals(IMAGE_NAME)) {
			resourceName = processDefinition.getDiagramResourceName();
		} else if (resourceType.equals(XML_NAME)) {
			resourceName = processDefinition.getResourceName();
		}
		try {
			InputStream resourceAsStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), resourceName);
			byte[] b = new byte[1024];
			int len;
			while ((len = resourceAsStream.read(b, 0, INT_1024)) != -1) {
				response.getOutputStream().write(b, 0, len);
			}
		} catch (Exception exception) {
			exception.printStackTrace();
		}
	}

	@Override
	public byte[] getModelEditorXML(FlowModel model) {
		return getBpmnXML(model);
	}

	@Override
	public R getFLowPrediction(String formKey, String processDefinitionId) {
		WorkFlowVO workFlowVO = new WorkFlowVO();
		FlowModel flowModel = this.getOne(Wrappers.lambdaQuery(FlowModel.class)
			.eq(FlowModel::getFormKey, formKey)
			.eq(FlowModel::getStatus, 0).last("limit 1"));
		if (Func.isEmpty(flowModel)) {
			return R.fail("未找到流程模型");
		}
		if (StrUtil.isEmpty(processDefinitionId)) {
			processDefinitionId = flowModel.getProcessDefinitionId();
		}
		BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);
//		Collection<FlowElement> flowElementList = FlowElementPredictionUtils.getAllUserTaskEvent(model);
		Kv variables = Kv.create();
		variables.put(PROC_INSTANCE_FORM_KEY, formKey);
		FlowApproverVariablesUtils.initFlowApproverVariables(processDefinitionId, variables);
		List<FlowElement> flowElementList = FlowElementPredictionUtils.getToDoUserTaskEvent(FlowElementPredictionUtils.getStartEvent(model), variables);
		//根据钉钉样式后台代码转的流程图，顺应应该没有问题，所以不需要排序（测试的时候试试，不行在把排序加上）
//		List<FlowElement> userTaskList = FlowElementPredictionUtils.sortTasksByFlowPath(FlowElementPredictionUtils.getStartEvent(model), flowElementList, variables);
		workFlowVO.setWorkFlowList(FlowElementToWorkFlow(new ArrayList<>(flowElementList), variables, processDefinitionId));
		return R.data(workFlowVO);

	}

	/**
	 * 获取所有的用户流程节点
	 *
	 * @return
	 */
	@Override
	public WorkFlowVO getALLFlowNode(WorkFlowDTO workFlow) {
		WorkFlowVO workFlowVO = new WorkFlowVO();
		if (workFlow.getProcessInstanceId() == null) {
			return null;
		}
		workFlowVO.setProcessInstanceId(workFlow.getProcessInstanceId());
		//前端让添加在字段，前端传的什么返回什么就可以了  --》 已优化返回工作流当前节点，若有用户的taskID，没有的话就是NULL
		// workFlowVO.setTaskId(workFlow.getTaskId());

		List<WorkFlow> historyBladeFlow = historyFlowList(workFlow.getProcessInstanceId());
		if (Func.isEmpty(historyBladeFlow)) {
			return null;
		}
		workFlowVO.setProcessDefinitionId(historyBladeFlow.get(0).getProcessDefinitionId());
		//判断最后一个审批节点是否是结束节点，流程没有结束则进行审批预测
		if (!FlowEngineConstant.END_EVENT.equals(historyBladeFlow.get(historyBladeFlow.size() - 1).getHistoryActivityType())) {

			Map<String, Object> variables = runtimeService.getVariables(workFlow.getProcessInstanceId());

			WorkFlow currentTask = historyBladeFlow.stream()
				.filter(t -> Objects.nonNull(t.getStatus()) && (FlowCommentEnum.TODO.getType().equals(t.getStatus()) || FlowCommentEnum.TRANSFER.getType().equals(t.getStatus())))  // 过滤历史和当前任务
				.findFirst()
				.orElse(null);
			WorkFlow currentRealActivityTask = historyBladeFlow.stream()
					.filter(t -> Objects.nonNull(t.getStatus()) && Func.isNotBlank(t.getHistoryActivityId())  && FlowCommentEnum.TODO.getType().equals(t.getStatus()) || FlowCommentEnum.TRANSFER.getType().equals(t.getStatus()))  // 过滤历史和当前任务
					.findFirst()
					.orElse(null);

			if (historyBladeFlow.size() > 1) {
				WorkFlow lastUserTask = historyBladeFlow.stream()
					.filter(t -> Objects.nonNull(t.getStatus()) && !FlowCommentEnum.TODO.getType().equals(t.getStatus()))
					.reduce((first, second) -> second) // 取最后一个匹配项
					.orElse(null);
				// 按钮
				workFlowVO.setButtonList(UserTaskButtonUtils.
					getButtonList(currentTask, lastUserTask, historyBladeFlow.get(0), variables));
			}
			if (FlowCommentEnum.TODO.getType().equals(currentTask.getStatus()) && !currentTask.getTaskName().equals("发起人")) {
				String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(workFlow.getProcessInstanceId()).singleResult().getProcessDefinitionId();
				BpmnModel bpmnModel = processEngine.getRepositoryService().getBpmnModel(processDefinitionId);
				FlowElement currentNode = bpmnModel.getFlowElement(currentRealActivityTask.getHistoryActivityId());
//				Map<String, Object> variables = runtimeService.getVariables(workFlow.getProcessInstanceId());
				//剩余节点预测
				List<FlowElement> todoList = FlowElementPredictionUtils.getToDoUserTaskEvent(currentNode, variables);
				if (Func.isNotEmpty(todoList)) {
					List<WorkFlow> todoWorkFlowList = FlowElementToWorkFlow(new ArrayList<>(todoList), variables, processDefinitionId);
//					todoWorkFlowList.get(0).setStatus(FlowCommentEnum.TODO.getType());
					historyBladeFlow.addAll(todoWorkFlowList);
				}
			}
			if (currentTask.getTaskName().equals("发起人")) {
				historyBladeFlow.remove(currentTask);
			}
			// taskID返回
			if (Func.isBlank(workFlow.getTaskId())) {
				Task task = taskService.createTaskQuery().processInstanceId(workFlow.getProcessInstanceId()).taskAssignee(String.valueOf(AuthUtil.getUserId())).singleResult();
				if (task != null) {
					String taskId = task.getId();
					workFlowVO.setTaskId(taskId);
				}
			}
		} else {
			//如果最后一个节点是结束节点则删除
			historyBladeFlow.remove(historyBladeFlow.size() - 1);
		}
		workFlowVO.setWorkFlowList(historyBladeFlow);
		return workFlowVO;
	}

	@Override
	public WorkFlowVO getHistoryFlowNode(String formKey, Long formDataId) {
		BusinessProcessEntity businessProcessEntity = businessProcessService.getOne(Wrappers.lambdaQuery(BusinessProcessEntity.class)
				.eq(BusinessProcessEntity::getFormKey, formKey)
				.eq(BusinessProcessEntity::getFormDataId, formDataId));
		WorkFlowVO workFlowVO = new WorkFlowVO();
		if (businessProcessEntity == null || businessProcessEntity.getProcessInstanceId() == null) {
			return null;
		}
		List<WorkFlow> historyBladeFlow = historyFlowList(businessProcessEntity.getProcessInstanceId());
		if (Func.isEmpty(historyBladeFlow)) {
			return null;
		}
		if (FlowEngineConstant.END_EVENT.equals(historyBladeFlow.get(historyBladeFlow.size() - 1).getHistoryActivityType())) {
			//如果最后一个节点是结束节点则删除
			historyBladeFlow.remove(historyBladeFlow.size() - 1);
		}
		workFlowVO.setWorkFlowList(historyBladeFlow);
		return workFlowVO;
	}

	@Override
	public Integer getCompletedInstance(String processInstanceId, String currentActivityId, String executionId, Integer reviewRound) {
		return baseMapper.getCompletedInstance(processInstanceId, currentActivityId, executionId, reviewRound);
	}

	@Override
	public String getProcessInstanceId(String formKey, Long formDataId) {
		BusinessProcessEntity businessProcessEntity = businessProcessService.getOne(Wrappers.lambdaQuery(BusinessProcessEntity.class)
				.eq(BusinessProcessEntity::getFormKey, formKey)
				.eq(BusinessProcessEntity::getFormDataId, formDataId));
		if (businessProcessEntity != null && businessProcessEntity.getProcessInstanceId() != null) {
			return businessProcessEntity.getProcessInstanceId();
		}
		return null;
	}

	/**
	 * 所有历史流程节点
	 *
	 * @param processInstanceId
	 * @return
	 */
	private List<WorkFlow> historyFlowList(String processInstanceId) {
		List<WorkFlow> flowList = new LinkedList<>();
		List<HistoricActivityInstance> historicActivityInstanceListTemp = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().orderByHistoricActivityInstanceEndTime().asc().list();
		historicActivityInstanceListTemp.sort(Comparator.comparing(HistoricActivityInstance::getEndTime, Comparator.nullsLast(Comparator.naturalOrder())));
		Map<String, HistoricActivityInstance> uniqueMap = new LinkedHashMap<>();
		for (HistoricActivityInstance instance : historicActivityInstanceListTemp) {
			if (instance.getEndTime() != null) { // 跳过结束时间为空的记录
				String key = instance.getActivityId() + "|" + instance.getEndTime();
				uniqueMap.putIfAbsent(key, instance);
			} else {
				uniqueMap.putIfAbsent(instance.getTaskId(), instance);
			}
		}
		List<HistoricActivityInstance> historicActivityInstanceList = new ArrayList<>(uniqueMap.values());

		// 避免重复获取转交信息
		List<String> executionIdList = new ArrayList<>();

		for (int i = 0; i < historicActivityInstanceList.size(); i++) {
			HistoricActivityInstance historicActivityInstance = historicActivityInstanceList.get(i);
			// 显示开始节点和结束节点，并且执行人不为空的任务
			if (StringUtils.equals(USR_TASK, historicActivityInstance.getActivityType())) {

				// 判断是否有转交节点
				// 转交列表
				// ACT_HI_VARINST 的 EXECUTION_ID_ 值存的是 getProcessInstanceId ？
				Map<String, Object> transferVars = getVariablesSafelyV2(historicActivityInstance.getProcessInstanceId(), historicActivityInstance.getExecutionId());
				List<WorkFlow> transferTaskList = new ArrayList<>();
  				if (CollectionUtil.isNotEmpty(transferVars)) {
					String parameKeyPrefix = historicActivityInstance.getTaskId() + "_before";
					if (transferVars.containsKey(parameKeyPrefix)) {
						if (!executionIdList.contains(parameKeyPrefix)) {
							Object variable = transferVars.containsKey(parameKeyPrefix) ? transferVars.get(parameKeyPrefix) : null;
							if (variable != null) {
								transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
								flowList.addAll(transferTaskList);
							}
							executionIdList.add(parameKeyPrefix);
						}
					} else {
						//  查看是否存在 节点KEY参数
						String activityId = historicActivityInstance.getActivityId() + "_before";
						if (!executionIdList.contains(activityId)) {
							Object variable = transferVars.containsKey(activityId) ? transferVars.get(activityId) : null;
							if (variable != null) {
								transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
								flowList.addAll(transferTaskList);
							}
							executionIdList.add(activityId);
						}
					}
				}

//				if (null == historicActivityInstance.getEndTime()) {
				//判断是否是会签节点
				boolean sequentialMultiInstance = FlowElementPredictionUtils.isSequentialMultiInstance(repositoryService, historicActivityInstance.getProcessDefinitionId(), historicActivityInstance.getActivityId());
				if (sequentialMultiInstance) {
					// 尝试从运行时获取
//					Map<String, Object> vars = runtimeService.getVariables(historicActivityInstance.getExecutionId());
					Map<String, Object> vars = getVariablesSafely(historicActivityInstance.getProcessInstanceId(), historicActivityInstance.getExecutionId());
					List<String> candidates = (List<String>) vars.get(MULTI_INSTANCE_ASSIGNEE_LIST_VAR);

					if (historicActivityInstance.getEndTime() != null) {
						if (!StrUtil.equals(historicActivityInstance.getExecutionId(), flowList.get(flowList.size() - 1).getExecutionId())) {
							List<HistoricActivityInstance> completedInstances = historyService.createHistoricActivityInstanceQuery()
								.processInstanceId(processInstanceId)
								.executionId(historicActivityInstance.getExecutionId())
								.finished()
								.orderByHistoricActivityInstanceEndTime()
								.asc()
								.list();
							for (HistoricActivityInstance current : completedInstances) {
								setWorkFlow(flowList, current, transferVars);
							}
						}
						continue;
					}

					// 获取待审批人
					List<Task> currentTask = taskService.createTaskQuery()
							.processInstanceId(processInstanceId)
							.taskDefinitionKey(historicActivityInstance.getActivityId())
							.list()
							.stream()
							.filter(Objects::nonNull)
							.collect(Collectors.toList());

					setWorkFlowCurrentTask(flowList, historicActivityInstance, currentTask, FlowCommentEnum.TODO.getType());
					// 等待审批的
					if (candidates != null && candidates.size() > 0) {
						int waitIndex = candidates.indexOf(currentTask.get(currentTask.size() - 1).getAssignee());
						List<String> waitList = candidates.subList(waitIndex + 1, candidates.size());
						setWorkFlowCandidates(flowList, historicActivityInstance, waitList, null);
					}
				} else {
					setWorkFlow(flowList, historicActivityInstance, transferVars);
				}








				// TODO 加签待处理....
				List<WorkFlow> afterSignTaskList = new ArrayList<>();
				if (CollectionUtil.isNotEmpty(transferVars)) {
					String parameKeyPrefix = historicActivityInstance.getTaskId() + "_after";
					if (transferVars.containsKey(parameKeyPrefix)) {
						if (!executionIdList.contains(parameKeyPrefix)) {
							Object variable = transferVars.containsKey(parameKeyPrefix) ? transferVars.get(parameKeyPrefix) : null;
							if (variable != null) {
								transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
								flowList.addAll(transferTaskList);
							}
							executionIdList.add(parameKeyPrefix);
						}
					} else {
						//  查看是否存在 节点KEY参数
						String activityId = historicActivityInstance.getActivityId() + "_after";
						if (!executionIdList.contains(activityId)) {
							Object variable = transferVars.containsKey(activityId) ? transferVars.get(activityId) : null;
							if (variable != null) {
								transferTaskList = JsonUtils.jsonToList((String) variable, WorkFlow.class);
								flowList.addAll(transferTaskList);
							}
							executionIdList.add(activityId);
						}
					}
				}




			} else if (StringUtils.equals(END_EVENT, historicActivityInstance.getActivityType())) {
				WorkFlow flow = new WorkFlow();
				flow.setHistoryActivityType(historicActivityInstance.getActivityType());
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flow.setTaskId(historicActivityInstance.getTaskId());
				flow.setTaskName(historicActivityInstance.getActivityName());
				flow.setCreateTime(historicActivityInstance.getStartTime());
				flow.setEndTime(historicActivityInstance.getEndTime());
				flowList.add(flow);
			} else if (SERVICE_TASK.equals(historicActivityInstance.getActivityType())) {

				List<HistoricVariableInstance> activities = historyService
					.createHistoricVariableInstanceQuery()
					.variableName("ccUsers_" + historicActivityInstance.getActivityId())
					.list();

				if (activities != null && activities.size() > 0) {
					WorkFlow flow = new WorkFlow();
					flow.setHistoryActivityType(historicActivityInstance.getActivityType());
					flow.setHistoryActivityId(historicActivityInstance.getActivityId());
					flow.setTaskId(historicActivityInstance.getTaskId());
					flow.setTaskName(historicActivityInstance.getActivityName());
					flow.setCreateTime(historicActivityInstance.getStartTime());
					flow.setEndTime(historicActivityInstance.getEndTime());

					List<WorkFlow.Approver> assigneeList = new ArrayList<>();
					List<String> cclist = StrUtil.split(activities.get(0).getValue().toString(), StrUtil.COMMA);
					cclist.stream().forEach(userId -> {
						User user = UserCache.getUser(Long.valueOf(userId));
						assigneeList.add(FlowApproverVariablesUtils.setApprover(user));
					});
					flow.setApproverList(assigneeList);
					flowList.add(flow);
				}
			}
		}
		if (flowList != null && flowList.size() > 1) {//  有历史审批记录(第一个节点是发起人不需要获取评论)
			// 获取意见评论内容
			List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
			commentList.sort(Comparator.comparing(Comment::getTime));
			if (commentList.size() > 0) {
				for (Comment comment : commentList) {
					for (WorkFlow flow : flowList) {
						if (StringUtil.equals(flow.getTaskId(), comment.getTaskId())) {
							if (!FlowCommentEnum.TODO.getType().equals(flow.getStatus())) {
								flow.setComment(comment.getFullMessage());
							}
							if (StringUtil.equals(FlowCommentEnum.TRANSFER.getType(), flow.getStatus())) {
								flow.setStatus(comment.getType());
							}
							if (StringUtil.equals(FlowCommentEnum.REVOKE.getType(), comment.getType())) {
								flow.setTaskName("发起人");
							}
						}
					}
				}
			}
		}

		return flowList;
	}

	private List<WorkFlow> setWorkFlow(List<WorkFlow> flowList, HistoricActivityInstance historicActivityInstance, Map<String, Object> vars) {
		if (flowList != null && flowList.size() > 0) {
			WorkFlow lastWorkFlow = flowList.get(flowList.size() - 1);
			if (lastWorkFlow.getEndTime() == null &&
					!(Func.isNotEmpty(lastWorkFlow.getRecordType()) && (lastWorkFlow.getRecordType() == 1 || lastWorkFlow.getRecordType() == 2)) &&
					lastWorkFlow.getHistoryActivityId().equals(historicActivityInstance.getActivityId())) {
				List<WorkFlow.Approver> approverList = lastWorkFlow.getApproverList();
				if (approverList == null) {
					approverList = new ArrayList<>();
					WorkFlow.Approver approver = new WorkFlow.Approver();
					approver.setTaskId(lastWorkFlow.getTaskId());
					approver.setAssignee(lastWorkFlow.getAssignee());
					approver.setAssigneeName(lastWorkFlow.getAssigneeName());
					approver.setAssigneeAvatar(lastWorkFlow.getAssigneeAvatar());
					approver.setAssigneeIsDeleted(lastWorkFlow.getAssigneeIsDeleted());
					approver.setAssigneeStatus(lastWorkFlow.getAssigneeStatus());
					lastWorkFlow.setMultipleApprovalType(FlowElementPredictionUtils.getMultiInstanceType(historicActivityInstance, repositoryService));
					approverList.add(approver);
				}

				User user = UserCache.getUserByTaskUser(historicActivityInstance.getAssignee());
				WorkFlow.Approver approverTodo = new WorkFlow.Approver();
				approverTodo.setTaskId(historicActivityInstance.getTaskId());
				approverTodo.setAssignee(user.getId().toString());
				approverTodo.setAssigneeName(user.getName());
				approverTodo.setAssigneeAvatar(user.getAvatar());
				approverTodo.setAssigneeIsDeleted(user.getIsDeleted());
				approverTodo.setAssigneeStatus(user.getStatus());
				approverList.add(approverTodo);

				flowList.remove(lastWorkFlow);
				String approverIds = approverList.stream().map(approver -> String.valueOf(approver.getAssignee())).collect(Collectors.joining(StrUtil.COMMA));
				lastWorkFlow.setTaskId(null);
				lastWorkFlow.setAssignee(approverIds + historicActivityInstance.getAssignee());
				lastWorkFlow.setAssigneeName(null);
				lastWorkFlow.setAssigneeAvatar(null);
				lastWorkFlow.setApproverList(approverList);
				flowList.add(lastWorkFlow);
				return flowList;
			}
		}

		WorkFlow flow = new WorkFlow();
		flow.setHistoryActivityType(historicActivityInstance.getActivityType());
		flow.setHistoryActivityId(historicActivityInstance.getActivityId());
		flow.setTaskId(historicActivityInstance.getTaskId());
		flow.setTaskName(historicActivityInstance.getActivityName());
		flow.setCreateTime(historicActivityInstance.getStartTime());
		// 获取最新时间
		String variableTimeKey = historicActivityInstance.getTaskId() + "_currentTime";
		Object variableTime = vars.containsKey(variableTimeKey) ? vars.get(variableTimeKey) : null;
		if (variableTime != null) {
			Date currentTime = DateUtil.parse((String) variableTime, "yyyy-MM-dd HH:mm:ss");
			flow.setCreateTime(currentTime);
		}
		flow.setEndTime(historicActivityInstance.getEndTime());
		flow.setExecutionId(historicActivityInstance.getExecutionId());
		flow.setProcessDefinitionId(historicActivityInstance.getProcessDefinitionId());
		if (historicActivityInstance.getDeleteReason() != null && !historicActivityInstance.getDeleteReason().equals("MI_END")) {
			flow.setStatus(FlowCommentEnum.REBACK.getType());
		} else {
			if (null != historicActivityInstance.getEndTime()) {
				flow.setStatus(FlowCommentEnum.APPROVE.getType());
			} else {
				if (FlowProcessConfigConstant.VIRTUAL_APPROVER.equals(historicActivityInstance.getAssignee())) {
					flow.setStatus(FlowCommentEnum.TRANSFER.getType());
				} else {
					flow.setStatus(FlowCommentEnum.TODO.getType());
				}
			}
		}
		// 获取任务执行人名称
		if (StringUtil.isNotBlank(historicActivityInstance.getAssignee())) {
			User user = UserCache.getUser(TaskUtil.getUserId(historicActivityInstance.getAssignee()));
			if (user != null) {
				flow.setAssignee(historicActivityInstance.getAssignee());
				flow.setAssigneeName(user.getName());
				flow.setAssigneeAvatar(user.getAvatar());
				flow.setAssigneeIsDeleted(user.getIsDeleted());
			}
			flowList.add(flow);
		} else {
			boolean containsKey = vars.containsKey(historicActivityInstance.getTaskId() + "_signUp");
			if (!containsKey) {
				// 获取候选人
				List<IdentityLink> userLinks = taskService.getIdentityLinksForTask(historicActivityInstance.getTaskId())
						.stream()
						.filter(link -> link.getUserId() != null)
						.collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(userLinks)) {
					flowList.addAll(getUsersByIdentityLink(userLinks, historicActivityInstance));
				}
			}
//			// 获取候选组
//			List<IdentityLink> groupLinks = taskService.getIdentityLinksForTask(historicActivityInstance.getTaskId())
//				.stream()
//				.filter(link -> link.getGroupId() != null)
//				.collect(Collectors.toList());
		}
//				else {
//					//候选用户解析
//					List<String> candidateUsers = userTask.getCandidateUsers();
//					List<String> candidateGroups = userTask.getCandidateGroups();
//					if (CollectionUtils.isNotEmpty(candidateUsers)) {
//						//候选用户解析
//						flowList.addAll(FlowApproverVariablesUtils.getUsersByUserIdList(candidateUsers,userTask.getName()));
//					} else if (CollectionUtils.isNotEmpty(candidateGroups)) {
//						//候选组解析
//						if (CollectionUtils.isNotEmpty(candidateGroups)) {
//							flowList.addAll(FlowApproverVariablesUtils.getUsersByGroupKeys(candidateGroups,userTask.getName()));
//						}
//					}
//				}
		return flowList;
	}


	/**
	 * 当前任务
	 *
	 * @param flowList
	 * @param historicActivityInstance
	 * @param currentTask
	 * @param status
	 * @return
	 */
	private List<WorkFlow> setWorkFlowCurrentTask(List<WorkFlow> flowList, HistoricActivityInstance historicActivityInstance, List<Task> currentTask, String status) {
		if (Func.isNotEmpty(currentTask)) {
			currentTask.forEach(task -> {
				WorkFlow flow = new WorkFlow();
				flow.setTaskId(task.getId());
				flow.setTaskName(historicActivityInstance.getActivityName());
				flow.setAssignee(task.getAssignee());
				User user = UserCache.getUser(Long.valueOf(task.getAssignee()));
				if(user != null) {
					flow.setAssigneeName(user.getName());
					flow.setAssigneeAvatar(user.getAvatar());
					flow.setAssigneeIsDeleted(user.getIsDeleted());
				}
				flow.setStatus(status);
//				flow.setStatus(FlowCommentEnum.TODO.getType());
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flowList.add(flow);
			});
		}
		return flowList;
	}

	/**
	 * 等待审批的成员
	 *
	 * @param flowList
	 * @param historicActivityInstance
	 * @param candidates
	 * @return
	 */
	private List<WorkFlow> setWorkFlowCandidates(List<WorkFlow> flowList, HistoricActivityInstance historicActivityInstance, List<String> candidates, String status) {
		if (Func.isNotEmpty(candidates)) {
			candidates.forEach(assignee -> {
				WorkFlow flow = new WorkFlow();
				User user = UserCache.getUser(Long.valueOf(assignee));
				flow.setTaskName(historicActivityInstance.getActivityName());
				if (user != null) {
					flow.setAssignee(user.getId().toString());
					flow.setAssigneeName(user.getName());
					flow.setAssigneeAvatar(user.getAvatar());
					flow.setAssigneeIsDeleted(user.getIsDeleted());
					flow.setAssigneeStatus(user.getStatus());
				}
				flow.setStatus(status);
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flowList.add(flow);
			});
		}
		return flowList;
	}

	public static List<WorkFlow> getUsersByIdentityLink(List<IdentityLink> userLinks, HistoricActivityInstance historicActivityInstance) {
		if (Func.isNotEmpty(userLinks)) {
			List<WorkFlow> workFlows = new ArrayList<>();
			userLinks.stream().forEach(userlink -> {
				WorkFlow multFlow = new WorkFlow();
				User user = UserCache.getUser(Long.valueOf(userlink.getUserId()));
				multFlow.setAssignee(user.getId().toString());
				multFlow.setAssigneeName(user.getName());
				multFlow.setAssigneeAvatar(user.getAvatar());
				multFlow.setAssigneeIsDeleted(user.getIsDeleted());
				multFlow.setTaskId(userlink.getTaskId());
				multFlow.setTaskName(historicActivityInstance.getActivityName());
				multFlow.setHistoryActivityId(historicActivityInstance.getActivityId());
				workFlows.add(multFlow);
			});
			return workFlows;
		}
		return null;
	}

	/**
	 * 流程节点转换--钉钉工作流审批样式
	 *
	 * @param flowElementList
	 * @param variables
	 * @return
	 */
	private List<WorkFlow> FlowElementToWorkFlow(List<FlowElement> flowElementList, Map<String, Object> variables, String processDefinitionId) {
		List<WorkFlow> workFlowList = new LinkedList<>();
		for (FlowElement flowElement : flowElementList) {
			if (flowElement instanceof UserTask) {
				UserTask userTask = (UserTask) flowElement;
				//审批人解析
				//流程预测串行会签userTask.getAssignee()是空，具体审批人员在候选人/候选组中
				//流程预测并行会签userTask.getAssignee()有值，这时候是参数"${assignee}"
				if (StringUtil.isNotBlank(userTask.getAssignee())) {
					WorkFlow workFlow = new WorkFlow();
					workFlow.setTaskId(flowElement.getId());
					workFlow.setTaskName(flowElement.getName());

					//多实例
					if (userTask.hasMultiInstanceLoopCharacteristics()) {
						if (userTask.getExecutionListeners() != null) {
							//获取办理人
							String approver = userTask.getExecutionListeners().get(0).getFieldExtensions().get(0).getStringValue();
							ApproverFlowNode approverFlowNode = JSONUtil.toBean(approver, ApproverFlowNode.class);
							Boolean isSequential = userTask.getLoopCharacteristics().isSequential();//false 代表并行，true代表串行
							List<WorkFlow.Approver> approverList = FlowApproverVariablesUtils.getApproverList(approverFlowNode,variables);
							ApproverTypeEnum approverType = EnumUtil.getBy(ApproverTypeEnum.class, item -> Objects.equals(item.getValue(), approverFlowNode.getSettype()));
							switch (approverType) {
								case DESIGNATED://指定审批人
								case ROLE:// 角色审批人
									if (approverList.size() > 1) {
										if (isSequential) {
											approverList.stream().forEach(item -> {
												WorkFlow mWorkFlow = new WorkFlow();
												mWorkFlow.setTaskId(flowElement.getId());
												mWorkFlow.setTaskName(flowElement.getName());
												mWorkFlow.setAssigneeName(item.getAssigneeName());
												mWorkFlow.setAssigneeAvatar(item.getAssigneeAvatar());
												mWorkFlow.setAssigneeIsDeleted(item.getAssigneeIsDeleted());
												mWorkFlow.setAssigneeStatus(item.getAssigneeStatus());
												workFlowList.add(mWorkFlow);
											});
										} else {
											workFlow.setMultipleApprovalType(approverFlowNode.getMultipleApprovalType());
											workFlow.setApproverList(approverList);
											workFlowList.add(workFlow);
										}
									} else {
										workFlow.setTaskId(flowElement.getId());
										workFlow.setTaskName(flowElement.getName());
										if (approverList.size() == 0) {
											workFlow.setRemark(flowElement.getName() + "角色为空，" + getProcessConfigName(processDefinitionId));
										} else {
											workFlow.setAssigneeName(approverList.get(0).getAssigneeName());
											workFlow.setAssigneeAvatar(approverList.get(0).getAssigneeAvatar());
											workFlow.setAssigneeIsDeleted(approverList.get(0).getAssigneeIsDeleted());
											workFlow.setAssigneeStatus(approverList.get(0).getAssigneeStatus());
										}
										workFlowList.add(workFlow);
									}
									break;
//								case USER_IMMEDIATE_SUPERVISOR://直属主管
//								case DEPT_LEADER://部门主管
//									approverList.stream().forEach(item -> {
//										WorkFlow mWorkFlow = new WorkFlow();
//										mWorkFlow.setTaskId(flowElement.getId());
//										mWorkFlow.setTaskName(flowElement.getName());
//										mWorkFlow.setAssigneeName(item.getAssigneeName());
//										mWorkFlow.setAssigneeAvatar(item.getAssigneeAvatar());
//										workFlowList.add(mWorkFlow);
//									});
//									break;
								case INITIATOR_OPTIONAL://发起人自选
									if (approverFlowNode.getSponsorType().startsWith("1-")) {
										workFlow.setSingleSelectedUser(true);
									} else {
										workFlow.setSingleSelectedUser(false);
									}
									workFlow.setMultipleApprovalType(approverFlowNode.getMultipleApprovalType());
									workFlow.setSponsorUserList(FlowApproverVariablesUtils.getSponsorApproverList(approverFlowNode));
									workFlowList.add(workFlow);
									break;
							}
						}
					} else {
						User taskUser = null;
						if (userTask.getAssignee().contains(PROC_INSTANCE_START_USER_NAME_VAR)) {
							taskUser = UserCache.getUser(Long.valueOf(variables.get(PROC_INSTANCE_START_USER_NAME_VAR).toString()));
						} else if (userTask.getAssignee().contains(DEPT_LEVEL_LEADER)) {
							taskUser = UserCache.getUser(Long.valueOf(variables.get(DEPT_LEVEL_LEADER).toString()));
						} else if (userTask.getAssignee().contains(USER_IMMEDIATE_SUPERVISOR)) {
							taskUser = UserCache.getUser(Long.valueOf(variables.get(USER_IMMEDIATE_SUPERVISOR).toString()));
						} else {
							try {
								taskUser = UserCache.getUser(Long.valueOf(userTask.getAssignee()));
							} catch (Exception e) {

							}
						}
						if (taskUser != null) {
							workFlow.setAssignee(taskUser.getId().toString());
							workFlow.setAssigneeName(taskUser.getName());
							workFlow.setAssigneeAvatar(taskUser.getAvatar());
							workFlow.setAssigneeIsDeleted(taskUser.getIsDeleted());
							workFlow.setAssigneeStatus(taskUser.getStatus());
						} else {
							workFlow.setRemark(flowElement.getName() + "无审批人，" + getProcessConfigName(processDefinitionId));
						}
						workFlowList.add(workFlow);
					}

				} else {
					//候选用户解析
					List<String> candidateUsers = userTask.getCandidateUsers();
					List<String> candidateGroups = userTask.getCandidateGroups();
					if (CollectionUtils.isNotEmpty(candidateUsers)) {
						//候选用户解析
						workFlowList.addAll(FlowApproverVariablesUtils.getUsersByUserIdList(candidateUsers, userTask.getName()));
					} else if (CollectionUtils.isNotEmpty(candidateGroups)) {
						//候选组解析
						if (CollectionUtils.isNotEmpty(candidateGroups)) {
							workFlowList.addAll(FlowApproverVariablesUtils.getUsersByGroupKeys(candidateGroups, userTask.getName()));
						}
					}
				}
			}
			if (flowElement instanceof ServiceTask) {
				WorkFlow bladeFlow = new WorkFlow();
				bladeFlow.setTaskId(flowElement.getId());
				bladeFlow.setTaskName(flowElement.getName());
				List<FieldExtension> fieldExtensions = ((ServiceTask) flowElement).getFieldExtensions();
				if (null != fieldExtensions && fieldExtensions.size() > 0) {
					//抄送流程里只定义了一个抄送人参数username，所以这里直接写了，如果参数多了需要判断
					String username = ((ServiceTask) flowElement).getFieldExtensions().get(0).getStringValue();
					String users[] = username.split(",");
					List<WorkFlow.Approver> ccList = new ArrayList<>();
					for (String userId : users) {
						User user = UserCache.getUserByTaskUser(userId);
						WorkFlow.Approver approver = new WorkFlow.Approver();
						approver.setAssignee(user.getId().toString());
						approver.setAssigneeName(user.getName());
						approver.setAssigneeAvatar(user.getAvatar());
						approver.setAssigneeIsDeleted(user.getIsDeleted());
						approver.setAssigneeStatus(user.getStatus());
						ccList.add(approver);
					}
					bladeFlow.setApproverList(ccList);
				}
				workFlowList.add(bladeFlow);
			}
		}
		return workFlowList;
	}

	/**
	 * 是否已完结
	 *
	 * @param processInstanceId 流程实例id
	 * @return bool
	 */
	private boolean isFinished(String processInstanceId) {
		return historyService.createHistoricProcessInstanceQuery().finished()
			.processInstanceId(processInstanceId).count() > 0;
	}


	/**
	 * xml转bpmn json
	 *
	 * @param xml xml
	 * @return json
	 */
	private String getBpmnJson(String xml) {
		return BPMN_JSON_CONVERTER.convertToJson(getBpmnModel(xml)).toString();
	}

	/**
	 * xml转bpmnModel
	 *
	 * @param xml xml
	 * @return bpmnModel
	 */
	private BpmnModel getBpmnModel(String xml) {
		return BPMN_XML_CONVERTER.convertToBpmnModel(new StringStreamSource(xml), false, false);
	}

	private byte[] getBpmnXML(FlowModel model) {
		BpmnModel bpmnModel = getBpmnModel(model);
		return getBpmnXML(bpmnModel);
	}

	private byte[] getBpmnXML(BpmnModel bpmnModel) {
		for (Process process : bpmnModel.getProcesses()) {
			if (StringUtils.isNotEmpty(process.getId())) {
				char firstCharacter = process.getId().charAt(0);
				if (Character.isDigit(firstCharacter)) {
					process.setId("a" + process.getId());
				}
			}
		}
		return BPMN_XML_CONVERTER.convertToXML(bpmnModel);
	}

	private BpmnModel getBpmnModel(FlowModel model) {
		BpmnModel bpmnModel;
		try {
			Map<String, FlowModel> formMap = new HashMap<>(16);
			Map<String, FlowModel> decisionTableMap = new HashMap<>(16);

			List<FlowModel> referencedModels = baseMapper.findByParentModelId(model.getId());
			for (FlowModel childModel : referencedModels) {
				if (FlowModel.MODEL_TYPE_FORM == childModel.getModelType()) {
					formMap.put(childModel.getId(), childModel);

				} else if (FlowModel.MODEL_TYPE_DECISION_TABLE == childModel.getModelType()) {
					decisionTableMap.put(childModel.getId(), childModel);
				}
			}
			bpmnModel = getBpmnModel(model, formMap, decisionTableMap);
		} catch (Exception e) {
			log.error("Could not generate BPMN 2.0 model for {}", model.getId(), e);
			throw new ServiceException("Could not generate BPMN 2.0 model");
		}
		return bpmnModel;
	}

	private BpmnModel getBpmnModel(FlowModel model, Map<String, FlowModel> formMap, Map<String, FlowModel> decisionTableMap) {
		try {
			ObjectNode editorJsonNode = (ObjectNode) objectMapper.readTree(model.getModelEditorJson());
			Map<String, String> formKeyMap = new HashMap<>(16);
			for (FlowModel formModel : formMap.values()) {
				formKeyMap.put(formModel.getId(), formModel.getModelKey());
			}
			Map<String, String> decisionTableKeyMap = new HashMap<>(16);
			for (FlowModel decisionTableModel : decisionTableMap.values()) {
				decisionTableKeyMap.put(decisionTableModel.getId(), decisionTableModel.getModelKey());
			}
			return BPMN_JSON_CONVERTER.convertToBpmnModel(editorJsonNode, formKeyMap, decisionTableKeyMap);
		} catch (Exception e) {
			log.error("Could not generate BPMN 2.0 model for {}", model.getId(), e);
			throw new ServiceException("Could not generate BPMN 2.0 model");
		}
	}

	private Map<String, Object> getVariablesSafely(String processInstanceId, String executionId) {
		Map<String, Object> result = new HashMap<>();

		// 尝试从运行时获取
		ExecutionEntityImpl execution = (ExecutionEntityImpl) runtimeService.createExecutionQuery()
			.executionId(executionId)
			.singleResult();
		if (execution != null) {
			return runtimeService.getVariables(executionId);
		}

		// 运行时找不到，尝试从历史中获取
		List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery()
			.processInstanceId(processInstanceId)
			.executionId(executionId)
			.list();
		for (HistoricVariableInstance var : variables) {
			result.put(var.getVariableName(), var.getValue());
		}
		return result;
	}

	private Map<String, Object> getVariablesSafelyV2(String processInstanceId, String executionId) {
		Map<String, Object> result = new HashMap<>();

		// 尝试从运行时获取
		ExecutionEntityImpl execution = (ExecutionEntityImpl) runtimeService.createExecutionQuery()
				.executionId(executionId)
				.singleResult();
		if (execution != null) {
			return runtimeService.getVariables(processInstanceId);
		}

		// 运行时找不到，尝试从历史中获取
		List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery()
				.processInstanceId(processInstanceId)
				.list();
		for (HistoricVariableInstance var : variables) {
			result.put(var.getVariableName(), var.getValue());
		}
		return result;
	}

	private String getProcessConfigName(String processDefinitionId) {
		CustomProcessConfigEntity processConfig = FlowProcessConfigCache.getProcessConfig(processDefinitionId);
		if (processConfig != null) {
			return ProcessApprovalConfigEunm.getApprovalConfig(processConfig.getNoApprover()).getValue();
		} else {
			return null;
		}
	}
}
