<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveInventoryInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="archiveInventoryInformationResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveInventoryInformation">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="details_id" property="detailsId"/>
        <result column="secondary_service_number" property="secondaryServiceNumber"/>
        <result column="secondary_service_price" property="secondaryServicePrice"/>
        <result column="secondary_service_order" property="secondaryServiceOrder"/>
        <result column="tertiary_service_number" property="tertiaryServiceNumber"/>
        <result column="tertiary_service_price" property="tertiaryServicePrice"/>
        <result column="tertiary_service_order" property="tertiaryServiceOrder"/>
        <result column="grade" property="grade"/>
    </resultMap>


    <select id="selectArchiveInventoryInformationPage" resultMap="archiveInventoryInformationResultMap">
        select * from blade_archive_Inventory_Information where is_deleted = 0
    </select>
    <select id="selectArchiveInventoryInformation"
            resultType="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveInventoryInformation">
        select * from blade_archive_Inventory_Information where is_deleted = 0 and details_id = #{detailsId}
    </select>

</mapper>
