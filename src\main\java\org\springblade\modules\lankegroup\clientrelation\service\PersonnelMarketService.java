package org.springblade.modules.lankegroup.clientrelation.service;

import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.PersonnelMarket;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO;

import java.util.List;
import java.util.Map;

public interface PersonnelMarketService {

    /**
     * 新增人员
     * @param list
     * @return
     */
    void savePersonnelMarket(List<PersonnelMarket> list);

    /**
     * 根据ID查询人员行业ID列表
     * @param id
     * @return
     */
    List<AreaAndMarket> getPersonnelMarketIds(Long id,List<Long> prohibitList);

    /**
     * 删除区域行业信息
     * @param id
     */
    void deletePersonnelMarket(Long id);

    /**
     * 获取某人员所属区域行业列表
     * @param id
     * @return
     */
    List<PersonnelMarketVO> getPersonnelMarketList(Long id);

    /**
     * 根据ID查询人员行业列表
     * @param id
     * @return
     */
    List<AreaAndMarket> getPersonnelMarketByName(Long id,Long marketId,String areaCode,List<Long> prohibitList);

    /**
     * 根据区域市场和行业ID查询自定义应有客户
     * @param professionId
     * @param marketId
     * @return
     */
    Integer getMarketNum(String professionId,String marketId,String areaId);

}
