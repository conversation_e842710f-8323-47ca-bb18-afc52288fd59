package org.springblade.common.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springblade.common.exception.GlobalExceptionHandler;
import org.springblade.common.model.ErrorResponse;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel导入辅助工具类
 * 提供Excel导入过程中常用的数据处理和校验方法
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
@Slf4j
public class ExcelImportHelper {

    /**
     * 全局异常处理器
     */
    @Autowired(required = false)
    private GlobalExceptionHandler globalExceptionHandler;

    /**
     * 批量保存数据，返回成功和失败的结果
     *
     * @param dataList   待保存的数据列表
     * @param saveFunc   保存函数
     * @param entityName 实体名称（用于日志）
     * @param <T>        实体类型
     * @return 保存结果
     */
    public <T> BatchSaveResult<T> batchSave(List<T> dataList, 
                                           Function<T, Boolean> saveFunc, 
                                           String entityName) {
        List<BatchSaveResult.SaveRecord<T>> recordList = new ArrayList<>();

        for (T entity : dataList) {
            boolean success = false;
            String message = null;
            
            try {
                Boolean result = saveFunc.apply(entity);
                success = Boolean.TRUE.equals(result);
                
                if (!success) {
                    // 保存返回false时，获取详细错误信息
                    GlobalExceptionHandler handler = getGlobalExceptionHandler();
                    if (handler != null) {
                        message = handler.getExcelImportOperationFailedMessage("保存" + entityName, entity);
                        // 同时记录详细的日志用于分析
                        handler.handleExcelImportOperationFailed("保存" + entityName, entity);
                    } else {
                        message = "保存失败：操作返回false";
                        log.warn("保存{}失败，实体：{}，原因：操作返回false", entityName, entity);
                    }
                }
            } catch (Exception e) {
                // 异常情况：使用全局异常处理器获取格式化的错误信息
                message = buildDetailErrorMessage(e, entityName);
            }
            
            recordList.add(new BatchSaveResult.SaveRecord<>(entity, success, message));
        }

        return new BatchSaveResult<>(recordList);
    }

    /**
     * 构建详细的错误信息 - 使用全局异常处理器
     */
    private String buildDetailErrorMessage(Exception e, String entityName) {
        GlobalExceptionHandler handler = getGlobalExceptionHandler();
        if (handler != null) {
            // 使用全局异常处理器的专用方法处理Excel导入异常
            // 这个方法会自动处理异常并记录日志，然后返回格式化的错误信息
            return handler.handleExcelImportException(e, "保存" + entityName);
        }
        
        // 如果全局异常处理器不可用，返回基础错误信息
        return "保存" + entityName + "失败：" + e.getMessage();
    }

    /**
     * 获取全局异常处理器实例
     * 优先使用注入的实例，如果为null则尝试从Spring容器中获取
     */
    private GlobalExceptionHandler getGlobalExceptionHandler() {
        if (globalExceptionHandler != null) {
            log.debug("使用注入的GlobalExceptionHandler实例");
            return globalExceptionHandler;
        }
        
        try {
            // 尝试从Spring容器中获取
            GlobalExceptionHandler handler = SpringUtil.getBean(GlobalExceptionHandler.class);
            log.info("通过SpringUtil成功获取到GlobalExceptionHandler实例");
            return handler;
        } catch (Exception ex) {
            log.warn("无法获取GlobalExceptionHandler实例: {}", ex.getMessage());
            return null;
        }
    }

    /**
     * 检查数据重复性
     *
     * @param dataList     数据列表
     * @param keyExtractor 提取唯一标识的函数
     * @param fieldName    字段名称
     * @param <T>          数据类型
     * @param <K>          唯一标识类型
     * @return 重复数据的错误信息列表
     */
    public <T, K> List<String> checkDuplicates(List<T> dataList,
                                                      Function<T, K> keyExtractor,
                                                      String fieldName) {
        List<String> errors = new ArrayList<>();

        Map<K, List<T>> grouped = dataList.stream()
                .filter(item -> keyExtractor.apply(item) != null)
                .collect(Collectors.groupingBy(keyExtractor));

        grouped.forEach((key, items) -> {
            if (items.size() > 1) {
                errors.add(fieldName + "重复：" + key);
            }
        });

        return errors;
    }


    /**
     * 批量保存结果类
     */
    public static class BatchSaveResult<T> {
        private final List<SaveRecord<T>> recordList;

        public BatchSaveResult(List<SaveRecord<T>> recordList) {
            this.recordList = recordList;
        }

        public List<SaveRecord<T>> getRecordList() {
            return recordList;
        }

        public List<T> getSuccessList() {
            return recordList.stream().filter(SaveRecord::isSuccess).map(SaveRecord::getEntity).collect(java.util.stream.Collectors.toList());
        }

        public List<SaveRecord<T>> getErrorList() {
            return recordList.stream().filter(record -> !record.isSuccess()).collect(java.util.stream.Collectors.toList());
        }

        public int getSuccessCount() {
            return (int) recordList.stream().filter(SaveRecord::isSuccess).count();
        }

        public int getErrorCount() {
            return (int) recordList.stream().filter(record -> !record.isSuccess()).count();
        }

        public boolean hasErrors() {
            return recordList.stream().anyMatch(record -> !record.isSuccess());
        }

        /**
         * 保存记录信息（统一的成功失败记录）
         */
        public static class SaveRecord<T> {
            private final T entity;
            private final boolean success;
            private final String message;

            public SaveRecord(T entity, boolean success, String message) {
                this.entity = entity;
                this.success = success;
                this.message = message;
            }

            public T getEntity() {
                return entity;
            }

            public boolean isSuccess() {
                return success;
            }

            public String getMessage() {
                return message;
            }

            // 兼容原有方法名
            public String getErrorMessage() {
                return message;
            }
        }
    }

    /**
     * 通用的数据转换工具方法
     */
    public static class DataConverter {

        /**
         * 字符串转整数
         *
         * @param value        字符串值
         * @param defaultValue 默认值
         * @return 整数值
         */
        public static Integer toInteger(String value, Integer defaultValue) {
            if (StrUtil.isBlank(value)) {
                return defaultValue;
            }
            try {
                return Integer.parseInt(value.trim());
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }

        /**
         * 字符串转长整数
         *
         * @param value        字符串值
         * @param defaultValue 默认值
         * @return 长整数值
         */
        public static Long toLong(String value, Long defaultValue) {
            if (StrUtil.isBlank(value)) {
                return defaultValue;
            }
            try {
                return Long.parseLong(value.trim());
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }

        /**
         * 字符串转布尔值
         *
         * @param value        字符串值
         * @param defaultValue 默认值
         * @return 布尔值
         */
        public static Boolean toBoolean(String value, Boolean defaultValue) {
            if (StrUtil.isBlank(value)) {
                return defaultValue;
            }
            String trimmed = value.trim().toLowerCase();
            if ("true".equals(trimmed) || "1".equals(trimmed) || "是".equals(trimmed) || "yes".equals(trimmed)) {
                return true;
            } else if ("false".equals(trimmed) || "0".equals(trimmed) || "否".equals(trimmed) || "no".equals(trimmed)) {
                return false;
            }
            return defaultValue;
        }

        /**
         * 性别转换（支持中文和数字）
         *
         * @param value 性别字符串
         * @return 性别数值（1=男，2=女）
         */
        public static Integer convertGender(String value) {
            if (StrUtil.isBlank(value)) {
                return null;
            }
            String trimmed = value.trim();
            if ("男".equals(trimmed) || "1".equals(trimmed)) {
                return 1;
            } else if ("女".equals(trimmed) || "2".equals(trimmed)) {
                return 2;
            }
            return null;
        }
    }
} 