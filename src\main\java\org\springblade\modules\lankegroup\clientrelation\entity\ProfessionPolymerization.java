package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 行业聚合
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfessionPolymerization {

    /**
     * 行业ID
     */
    private String id;

    /**
     * 行业名称
     */
    private String name;

    /**
     * 应有客户
     */
    private Integer oughtCount;

    /**
     * 实有客户
     */
    private Integer actualCount;

}
