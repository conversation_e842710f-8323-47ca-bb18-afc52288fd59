package org.springblade.modules.auth.entity.kingdee;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class KdEmployee {
    //    员工id
    public String FID;
    //    员工名称
    public String FName;
    //    员工编号
    public String FStaffNumber;
    //    手机号
    public String FMobile;
    //    固定电话
    public String FTel;
    //创建组织
    public Map FCreateOrgId = new HashMap();
    //    使用组织
    public Map FUseOrgId = new HashMap();
    //任职公司
    public Map FRZGS = new HashMap();
    //    钉钉id
    public String FDDID;

}
