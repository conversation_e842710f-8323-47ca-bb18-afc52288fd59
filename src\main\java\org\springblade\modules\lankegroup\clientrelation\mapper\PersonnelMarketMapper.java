package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.PersonnelMarket;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO;

import java.util.List;
import java.util.Map;

public interface PersonnelMarketMapper extends BaseMapper<PersonnelMarket> {

    void savePersonnelMarket(@Param("list") List<PersonnelMarket> list);

    /**
     * 根据ID查询人员行业ID列表
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getPersonnelMarketIds(Long id, List<Long> prohibitList);

    /**
     * 删除区域行业信息
     *
     * @param id
     */
    void deletePersonnelMarket(Long id);

    /**
     * 获取某人员所属区域行业列表
     *
     * @param id
     * @return
     */
    List<PersonnelMarketVO> getPersonnelMarketList(Long id);

    /**
     * 根据ID查询人员行业列表
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getPersonnelMarketByName(Long id, Long marketId, String areaCode, List<Long> prohibitList);

    List<AreaAndMarket> getPersonnelMarketByNameStr(Long id, Long marketId, String areaCode, List<Long> prohibitList);

    List<AreaAndMarket> getPersonnelMarketByNameMore(Long id, List<String> marketId, List<String> areaCode);

    List<Map> selectUserId(String districtCode, Long marketplaceId);

    /**
     * 根据区域市场和行业ID查询自定义应有客户
     *
     * @param professionId
     * @param marketId
     * @return
     */
    Integer getMarketNum(String professionId, String marketId, String areaId);

    /**
     * 根据人员id查询所负责的行业
     *
     * @param userId
     * @return
     */
    List<PersonnelMarketVO> getMarketListByUserId(Long userId);

    /**
     * 根据部门查询负责行业集合
     *
     * @param deptId
     * @return
     */
    List<Long> getMarketListByDeptId(Long deptId);

}
