package org.springblade.flow.engine.listener.flowable;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.business.service.IBusinessProcessTransferService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.constant.WorkFlowConstants;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.flow.core.enums.ProcessApprovalConfigEunm;
import org.springblade.flow.engine.cache.FlowProcessConfigCache;
import org.springblade.flow.engine.constant.FlowProcessConfigConstant;
import org.springblade.flow.engine.entity.CustomProcessConfigEntity;
import org.springblade.flow.engine.utils.ProcessMessageUtils;
import org.springblade.modules.system.service.IUserService;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;


/**
 * <AUTHOR> mtf
 * @description : 用户任务创建时监听 发送消息 更新流程状态等
 * @create :  2025/4/14 9:05
 **/
@Slf4j
@Component(value = "userTaskCreateListener")
@AllArgsConstructor
public class UserTaskCreateListener implements TaskListener {

	private final ProcessMessageUtils processMessageUtils;
	private final TaskService taskService;
	private final IBusinessProcessTransferService businessProcessTransferService;
	private final RuntimeService runtimeService;
	private final HistoryService historyService;
	private final FlowBusinessService flowBusinessService;

	@Override
	public void notify(DelegateTask delegateTask) {
		//判断相邻两个审批节点，审批人如果是同一个人则第二个审批节点自动通过
		if (adjacentNode(delegateTask)) {
			return;
		}
		// 检查主审批人
  		if (!assigneeExists(delegateTask)) {
			return;
		}

		// 检查候选人
		List<Long> approveUsers = getProcessCandidates(delegateTask);
		if (approveUsers == null || approveUsers.isEmpty()) {
			return;
		}

//		List<Long> approveUsers = new ArrayList<>();
//		approveUsers.add(Long.valueOf(delegateTask.getAssignee()));
//		approveUsers.addAll(candidateUsers);
		// 获取流程变量
		Map<String, Object> variables = delegateTask.getVariables();
		String processInstanceId = delegateTask.getProcessInstanceId();
		// 发送流程消息
		sendProcessMsg(approveUsers, variables, processInstanceId);
	}

	/**
	 * 发送流程通知
	 *
	 * @param approveUsers 审批人
	 * @param variables
	 */
	private void sendProcessMsg(List<Long> approveUsers, Map<String, Object> variables, String processInstanceId) {

		String processName = variables.get(PROC_INSTANCE_NAME).toString();
		String initiator = variables.get(PROC_INSTANCE_START_USER_NAME_VAR).toString();
		String formDataId = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_DATA_ID).toString();
		String formKey = variables.get(WorkFlowConstants.PROC_INSTANCE_FORM_KEY).toString();
		String formCode = Func.toStr(variables.get(PROC_INSTANCE_FORM_CODE));
//		processMessageUtils.sendMessage(initiator, approveUsers, formDataId, StrUtil.format(TemplateConstant.FINANCE_APPROVE_TEMPLATE, processName, formDataId));
		processMessageUtils.sendMessage(initiator, approveUsers, formDataId, formKey, formCode, processName, processInstanceId);
	}


	/**
	 * 获取流程待办人
	 *
	 * @param delegateTask 任务
	 */
	private List<Long> getProcessCandidates(DelegateTask delegateTask) {
		List<Long> approveUsers = new ArrayList<>();
		Set<IdentityLink> candidates;
		if (StringUtil.isNotBlank(delegateTask.getAssignee())) {
			// 获取审批人
			approveUsers.add(Long.valueOf(delegateTask.getAssignee()));
		}
		try {
			// 获取候选组
			candidates = delegateTask.getCandidates();
		} catch (NullPointerException e) {
			return approveUsers;
		}
		if (CollectionUtil.isEmpty(candidates)) {
			return approveUsers;
		}
		candidates.forEach(item -> {
			if (StrUtil.isNotBlank(item.getUserId())) {
				approveUsers.add(Long.valueOf(item.getUserId()));
			}
			// 如果是组
			if (StrUtil.isNotBlank(item.getGroupId())) {
				if (StrUtil.startWith(item.getGroupId(), WorkFlowConstants.TASK_CANDIDATE_GROUP_ROLE)) {
					String roleId = item.getGroupId().replace(WorkFlowConstants.TASK_CANDIDATE_GROUP_ROLE, "");
					List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleId));
					if (userList != null) {
						candidatesUserExists(delegateTask, userList);
						approveUsers.addAll(userList.stream()
							.filter(user -> user.getIsDeleted() == 0)
							.map(user -> user.getId())
							.collect(Collectors.toList()));
					} else {
						handleMissingAssignee(delegateTask, "未找到审批人");
					}
				}
//				else if (StrUtil.startWith(item.getGroupId(), WorkFlowConstants.TASK_CANDIDATE_GROUP_POST)) {
//					long postId = Long.parseLong(item.getGroupId().replace(WorkFlowConstants.TASK_CANDIDATE_GROUP_POST, ""));
//					List<SysUserEntity> roleUserList = flowIdentityExtUtils.getPostUserList(CollUtil.newArrayList(postId));
//					approveUsers.addAll(roleUserList.stream().map(SysUserEntity::getUserName).collect(Collectors.toList()));
//
//				}
			}
		});
		return approveUsers;
	}

	private Boolean assigneeExists(DelegateTask delegateTask) {
		String assignee = delegateTask.getAssignee();
		if (StrUtil.isNotBlank(assignee)) {
			if (FlowProcessConfigConstant.VIRTUAL_APPROVER.equals(assignee)) {
				handleMissingAssignee(delegateTask, "未找到审批人");
				return false;
			}
			User user = UserCache.getUser(Long.valueOf(assignee));
			if (user != null) {
				if(user.getIsDeleted() == 1) {
					delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, user.getName()+"（已离职）");
					handleMissingAssignee(delegateTask, StrUtil.format("审批人{}已离职", user.getRealName()));
					return false;
				}
				if(user.getStatus() == 0) {
					delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, user.getName()+"（已禁用）");
					handleMissingAssignee(delegateTask, StrUtil.format("审批人{}已禁用", user.getRealName()));
					return false;
				}
			}
		}
		return true;
	}

	private void candidatesUserExists(DelegateTask delegateTask, List<User> userList) {
		//在职人数
		int exist = userList.stream()
			.filter(user -> user.getIsDeleted() == 0)
			.collect(Collectors.toList()).size();

		if (exist == 0) {
			///离职人员
			List<User> leaveUser = userList.stream()
				.filter(user -> user.getIsDeleted() == 1)
				.collect(Collectors.toList());
			handleMissingAssignee(delegateTask, StrUtil.format("审批人{}已离职", leaveUser.stream().map(User::getRealName).collect(Collectors.joining(","))));
		}
	}

	/**
	 * transfer
	 * 处理找不到审批人的情况
	 *
	 * @param task
	 */
	private void handleMissingAssignee(DelegateTask task, String reason) {
		// 获取流程定义中的配置或全局配置
		CustomProcessConfigEntity processConfig = FlowProcessConfigCache.getProcessConfig(task.getProcessDefinitionId());

		ProcessApprovalConfigEunm config = ProcessApprovalConfigEunm.getApprovalConfig(processConfig.getNoApprover());
		switch (config) {
			case TRANSFER_DMIN:
				transferTaskToAdmin(task, reason);
				break;
			case OUTO_APPROVE:
				autoApproveTask(task, reason);
				break;
			case OUTO_REJECT:
				autoRejectTask(task, reason);
				break;
			default:
				// 默认策略
//				autoApproveTask(task);
		}
	}

	/**
	 * 转交给管理员
	 *
	 * @param task
	 */
	private void transferTaskToAdmin(DelegateTask task, String reason) {
		// 将任务转交给管理员组（组内成员均可领取）
		String admin = SysCache.getRoleIds(AuthUtil.getTenantId(), TenantConstant.ADMIN_ROLE_NAME);
		taskService.addCandidateGroup(task.getId(), admin);
//		taskService.setAssignee(task.getId(), null); // 清空原处理人
		//添加操作日志
		String message = StrUtil.format("{}，可以联系管理员{}进行配置", reason, getAdminUserName());
		taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.TRANSFER.getType(), message);
//		businessProcessTransferService.saveTransferTask(task.getProcessInstanceId(), task.getId(), reason);
		businessProcessTransferService.saveTransferTask(task.getProcessInstanceId(), task.getId(), task.getVariables().get(WorkFlowConstants.ORIGINAL_APPROVER)+"");
	}

	/**
	 * 自动通过审批
	 *
	 * @param task
	 */
	private void autoApproveTask(DelegateTask task, String reason) {
		taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.APPROVE.getType(), StrUtil.format("{}，已自动通过", reason));
		Map<String, Object> variables = new HashMap<>();
		variables.put(ProcessConstant.PASS_KEY, true);
		taskService.complete(task.getId(), variables);

	}

	/**
	 * 自动拒绝审批(退回到发起人)
	 *
	 * @param task
	 */
	private void autoRejectTask(DelegateTask task, String reason) {
		WorkFlowDTO workFlowDTO = new WorkFlowDTO();
		workFlowDTO.setProcessInstanceId(task.getProcessInstanceId());
		workFlowDTO.setTaskId(task.getId());
		String message = StrUtil.format("{}，已自动拒绝，可以联系管理员{}进行配置", reason, getAdminUserName());
		workFlowDTO.setComment(message);
		workFlowDTO.setApprover(FlowProcessConfigConstant.VIRTUAL_APPROVER);
		workFlowDTO.setDelegateTask(task);
		flowBusinessService.rejectTask(workFlowDTO);
	}

	/**
	 * 获取管理员名称
	 *
	 * @return
	 */

	private String getAdminUserName() {
		// 添加撤回审批信息
		String roleId = SysCache.getRoleIds(AuthUtil.getTenantId(), TenantConstant.ADMIN_ROLE_NAME);
		List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleId));;
		return userList.stream().map(User::getRealName).collect(Collectors.joining(","));
	}

	/**
	 * 判断相邻两个审批节点，审批人如果是同一个人则第二个审批节点自动通过
	 *
	 * @param delegateTask
	 * @return
	 */
	private Boolean adjacentNode(DelegateTask delegateTask) {
		CustomProcessConfigEntity processConfig = FlowProcessConfigCache.getProcessConfig(delegateTask.getProcessDefinitionId());
		if (processConfig == null) {
			return false;
		}
		//是否自动去重(1去重，0不去重)
		if (processConfig.getDeduplication() == 0) {
			return false;
		}
		if (FlowProcessConfigConstant.VIRTUAL_APPROVER.equals(delegateTask.getAssignee())) {
			handleMissingAssignee(delegateTask, "未找到审批人");
			return true;
		}
		//获取上一个节点的审批人（这个时候数据还没有写到hi数据表中）
		Task historyTask = taskService.createTaskQuery().processInstanceId(delegateTask.getProcessInstanceId()).singleResult();
		if (historyTask != null && !StrUtil.equals(historyTask.getName(), "发起人")) {
			//判断相邻两个审批节点，审批人如果是同一个人则第二个审批节点自动通过
			if (historyTask.getAssignee().equals(delegateTask.getAssignee())) {
				autoApproveTask(delegateTask, "自动通过");
				return true;
			}
		}
		return false;
	}
}
