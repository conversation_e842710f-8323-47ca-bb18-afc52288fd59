/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.entity.Approve;
import org.springblade.modules.lankegroup.contractManagement.vo.ApproveVO;
import org.springblade.modules.lankegroup.contractManagement.service.IApproveService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 流程审批记录操作表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/approve/approve")
@Api(value = "流程审批记录操作表", tags = "流程审批记录操作表接口")
public class ApproveController extends BladeController {

	private final IApproveService approveService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入approve")
	public R<Approve> detail(Approve approve) {
		Approve detail = approveService.getOne(Condition.getQueryWrapper(approve));
		return R.data(detail);
	}

	/**
	 * 分页 流程审批记录操作表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入approve")
	public R<IPage<Approve>> list(Approve approve, Query query) {
		IPage<Approve> pages = approveService.page(Condition.getPage(query), Condition.getQueryWrapper(approve));
		return R.data(pages);
	}

	/**
	 * 自定义分页 流程审批记录操作表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入approve")
	public R<IPage<ApproveVO>> page(ApproveVO approve, Query query) {
		IPage<ApproveVO> pages = approveService.selectApprovePage(Condition.getPage(query), approve);
		return R.data(pages);
	}

	/**
	 * 新增 流程审批记录操作表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入approve")
	public R save(@Valid @RequestBody Approve approve) {
		return R.status(approveService.save(approve));
	}

	/**
	 * 修改 流程审批记录操作表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入approve")
	public R update(@Valid @RequestBody Approve approve) {
		return R.status(approveService.updateById(approve));
	}

	/**
	 * 新增或修改 流程审批记录操作表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入approve")
	public R submit(@Valid @RequestBody Approve approve) {
		return R.status(approveService.saveOrUpdate(approve));
	}

	
	/**
	 * 删除 流程审批记录操作表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(approveService.deleteLogic(Func.toLongList(ids)));
	}

	
}
