/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.common.enums.UserLevelEnum;
import org.springblade.common.manager.BusinessAuthManager;
import org.springblade.common.manager.UserLevelManager;
import org.springblade.common.utils.PinYinConverter;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.mapper.BusinessProcessMapper;
import org.springblade.flow.core.constant.FlowFormConstant;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.modules.lankegroup.contractManagement.constant.ContractToWordConstant;
import org.springblade.modules.lankegroup.contractManagement.dto.*;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.enums.ContractCategoryEnum;
import org.springblade.modules.lankegroup.contractManagement.manager.ContractWordManager;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMainMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractTemplateMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.SignatoryCompanyMapper;
import org.springblade.modules.lankegroup.contractManagement.service.*;
import org.springblade.modules.lankegroup.contractManagement.vo.*;
import org.springblade.modules.lankegroup.file.service.ProjectFileService;
import org.springblade.modules.lankegroup.jlopportunity.entity.BusinessOpportunity;
import org.springblade.modules.lankegroup.jlopportunity.mapper.BusinessOpportunityMapper;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.workflow.dto.Approver;
import org.springblade.workflow.dto.FlowVariableDTO;
import org.springblade.workflow.service.WorkFlowService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同主表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractMainServiceImpl extends BaseServiceImpl<ContractMainMapper, ContractMain> implements IContractMainService {


    private final IUserService userService;
    private final BusinessAuthManager businessAuthManager;
    private final UserLevelManager userLevelManager;
    private final SignatoryCompanyMapper signatoryCompanyMapper;
    private final ContractWordManager contractWordManager;
    private final ContractTemplateMapper contractTemplateMapper;
    private final IContractExperimentService contractExperimentService;
    private final IContractPrestoreService contractPrestoreervice;
    private final IContractReagentService contractReagentService;
    private final IContractReagentQuotationMaterialService contractReagentQuotationMaterialService;
    private final IContractBidDocumentService contractBidDocumentService;
    private final IContractAnimalBreedingService contractAnimalBreedingService;
    private final IContractAnimalBreedingExpensesService contractAnimalBreedingExpensesService;
    private final WorkFlowService workFlowService;
    private final ControlsLogService controlsLogService;
    private final BusinessOpportunityMapper businessOpportunityMapper;
    private final BusinessProcessMapper businessProcessMapper;
    private final ProjectFileService projectFileService;

    private void commonHandleSearchPage(ContractMainDTO dto) {
        // 处理DTO中的查询条件，与分页查询相同
        Integer timeType = dto.getTimeType();
        if (Func.isNotEmpty(timeType)) {
            if (timeType == 1) {
                // 创建时间
                if (dto.getStartTime() != null) {
                    dto.setCreateStartTimeSql(dto.getStartTime().atStartOfDay());
                }
                if (dto.getEndTime() != null) {
                    dto.setCreateEndTimeSql(dto.getEndTime().atTime(23, 59, 59));
                }
            }
            if (timeType == 2) {
                // 签约时间
                if (dto.getStartTime() != null) {
                    dto.setSignStartTimeSql(dto.getStartTime().atStartOfDay());
                }
                if (dto.getEndTime() != null) {
                    dto.setSignEndTimeSql(dto.getEndTime().atTime(23, 59, 59));
                }
            }
        }

        // 处理关键字搜索
        List<String> seekList = new ArrayList<>();
        String name = dto.getName();
        if (Func.isNotBlank(name)) {
            seekList.addAll(Arrays.stream(name.split("\\s+"))
                    .flatMap(word -> Arrays.stream(word.split("(?<=[a-z])(?=[A-Z])")))
                    .collect(Collectors.toList()));
            dto.setSeekList(seekList);
        }

        // 处理创建人员
        if (Func.isNotBlank(dto.getCreateUserParam())) {
            List<Long> createUserIds = userService.resolveUserIdList(dto.getCreateUserParam());
            dto.setCreateUserIds(createUserIds);
        }

        // 当前人员级别
        UserLevelEnum userLevel = userLevelManager.getUserLevel(AuthUtil.getUserId());
        dto.setTargetUserLevel(userLevel.getCode());
        if (UserLevelEnum.部门领导.equals(userLevel)) {
            dto.setDeptUserIds(userService.getDeptUserIdsByLeader(AuthUtil.getUserId()));
        }
        if (UserLevelEnum.普通员工.equals(userLevel)) {
            dto.setCurrentUserId(AuthUtil.getUserId());
        }
    }

    @Override
    public IPage<ContractMainVO> selectContractMainPageByCustomerVisit(IPage<ContractMainVO> page, ContractMainDTO dto) {
        // 处理搜索参数
        commonHandleSearchPage(dto);
        List<ContractMainVO> list = baseMapper.selectContractMainPage(page, dto);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setApprovalStatusName(ProcessStatusEnum.getProcessStatusEnumNameV2(item.getStatus(), 2));
            });
        }
        return page.setRecords(list);
    }

    @Override
    public IPage<ContractMainVO> selectContractMainPage(IPage<ContractMainVO> page, ContractMainDTO dto) {
        List<ContractMainVO> list;
        if (Func.isNotEmpty(dto.getDraftStatus()) && 0 == dto.getDraftStatus()) {
            dto.setCreateUserIds(List.of(AuthUtil.getUserId()));
            // 草稿列表
            list = baseMapper.selectContractMainPageByDraft(page, dto);
        } else {
            // 处理搜索参数
            commonHandleSearchPage(dto);
            // 正常列表
            list = baseMapper.selectContractMainPage(page, dto);
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    item.setApprovalStatusName(ProcessStatusEnum.getProcessStatusEnumNameV2(item.getStatus(), 2));
                });
            }
        }
        boolean notEmpty = CollectionUtil.isNotEmpty(list);
        if (notEmpty) {
            list.forEach(item -> {
                BusinessProcessEntity oneByFormKeyAndFormDataId = businessProcessMapper.getOneByFormKeyAndFormDataId(FlowFormConstant.FLOW_CONTRACT_KEY, item.getId());
                if (oneByFormKeyAndFormDataId != null) {
                    item.setProcessInstanceId(oneByFormKeyAndFormDataId.getProcessInstanceId());
                }
            });
        }
        return page.setRecords(notEmpty ? list : new ArrayList<>());
    }

    @Override
    public List<ContractMainHeadVO> pageHeadStatistics(IPage<ContractMainVO> page, ContractMainDTO dto) {
        // 处理搜索参数
        commonHandleSearchPage(dto);

        // 创建一个空的分页结果对象，用于返回统计数据
        List<ContractMainHeadVO> resultList = new ArrayList<>();

        // 无论是我的商机还是公海商机，都需要业务类型统计
        ContractMainHeadVO headVO = new ContractMainHeadVO();
        headVO.setStatisticsType("businessType");
        headVO.setStatisticsName("业务类型");

        // 获取查询结果
        List<ContractMainStatisticsVO> businessTypeStats = baseMapper.selectStatistics(dto);

        // 确保所有业务类型都有统计数据，即使是0
        List<ContractMainStatisticsVO> completeBusinessTypeStats = ensureCompleteBusinessTypeStats(
                businessTypeStats);
        headVO.setStatistics(completeBusinessTypeStats);
        resultList.add(headVO);

        return resultList;
    }

    /**
     * 确保所有业务类型都有统计数据，即使是0
     */
    private List<ContractMainStatisticsVO> ensureCompleteBusinessTypeStats(
            List<ContractMainStatisticsVO> businessTypeStats) {
        // 定义所有业务类型
        String[] allBusinessTypes = {"CRO服务", "科研服务", "经费预存", "标书设计", "方案设计", "中间商", "试剂销售", "耗材销售", "设备销售", "饲养服务",
                "长期综合服务", "培训服务", "场地租赁"};

        // 创建一个Map存放已有的统计数据
        Map<String, ContractMainStatisticsVO> businessTypeStatsMap = new HashMap<>();
        if (businessTypeStats != null) {
            for (ContractMainStatisticsVO stat : businessTypeStats) {
                businessTypeStatsMap.put(stat.getStatisticsKey(), stat);
            }
        }

        // 创建完整的统计列表
        List<ContractMainStatisticsVO> completeStats = new ArrayList<>();
        for (String type : allBusinessTypes) {
            if (businessTypeStatsMap.containsKey(type)) {
                completeStats.add(businessTypeStatsMap.get(type));
            } else {
                ContractMainStatisticsVO newStat = new ContractMainStatisticsVO();
                newStat.setStatisticsType("businessType");
                newStat.setStatisticsKey(type);
                newStat.setStatisticsName(type);
                newStat.setCount(0);
                newStat.setAmount(BigDecimal.ZERO);
                completeStats.add(newStat);
            }
        }

        return completeStats;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Kv> saveByDraft(ContractMainDTO dto) {
        // 获取商机编号
        Optional.ofNullable(dto.getBusinessOpportunityId())
                .map(businessOpportunityMapper::selectById)
                .ifPresent(opportunity -> {
            dto.setBusinessOpportunityCode(opportunity.getCode());
        });

        // 处理主表
        // 状态(0:草稿,1:正常)
        dto.setDraftStatus(0);
        dto.setStatus(0);
        if (Func.isEmpty(dto.getId())) {
            // 无ID代表初次保存草稿
            save(dto);
        } else {
            // 存在ID，更新草稿
            updateById(dto);
        }

        // 清空生成文件信息 编号
        update(Wrappers.lambdaUpdate(ContractMain.class)
                .set(ContractMain::getPreviewUrl, null)
                .set(ContractMain::getPreviewName, null)
                .set(ContractMain::getCode, null)
                .eq(ContractMain::getId, dto.getId()));
        // 处理子表
        handleSubData(dto);
        return R.data(Kv.create().set("id", dto.getId()));
    }

    /**
     * 只要点击下一步，就要校验各种数据准确性
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Kv> saveByNext(ContractMainDTO dto) {
        // 合同 模板路径
        String filePath = contractTemplateMapper.getFilePathByType(dto.getCategory());
        if (filePath == null) {
            throw new ServiceException("合同模板不存在");
        }
        String templateUrl = filePath.replace(ContractToWordConstant.FILE_SUFFIX, ContractToWordConstant.TEMPLATE_FILE_SUFFIX);
        dto.setTemplateUrl(templateUrl);
        // 获取签约公司信息
        if (Func.isNotEmpty(dto.getPartyBId())) {
            SignatoryCompany signatoryCompany = signatoryCompanyMapper.selectById(dto.getPartyBId());
            if (signatoryCompany != null) {
                dto.setPartyBScBankCode(signatoryCompany.getBankCode());
                dto.setPartyBScAccountName(signatoryCompany.getAccountName());
                dto.setPartyBScTaxNumber(signatoryCompany.getTaxNumber());
                dto.setPartyBScPhone(signatoryCompany.getPhone());
            }
        }

        // 获取商机编号
        Optional.ofNullable(dto.getBusinessOpportunityId())
                .map(businessOpportunityMapper::selectById)
                .ifPresent(opportunity -> {
                    dto.setBusinessOpportunityCode(opportunity.getCode());
                });

        // 处理主表
        // 状态(0:草稿,1:正常)
        dto.setDraftStatus(0);
        dto.setStatus(0);
        if (Func.isEmpty(dto.getId())) {
            // 无ID代表初次保存草稿
            // 合同编号
            dto.setCode(generateContractNumber(dto.getPartyBPerson(), dto.getCustomerContactName(), dto.getSignDate()));
            save(dto);
        } else {
            // 存在ID，更新草稿
            updateById(dto);
        }
        // 处理子表
        handleSubData(dto);
        // 检查数据 待完善??? 看着没必要
//        boolean checkData = contractWordManager.checkData(dto);
//        if (checkData) {
//            throw new ServiceException("合同数据校验失败");
//        }
        // 生成合同文件
        Kv kv = contractWordManager.exportWordAndPutOss(dto);

        // 回更生成文件信息
        if (kv.containsKey("filePath")) {
            update(Wrappers.lambdaUpdate(ContractMain.class)
                    .set(ContractMain::getPreviewUrl, kv.getStr("filePath"))
                    .set(ContractMain::getPreviewName, kv.getStr("fileName"))
                    .eq(ContractMain::getId, dto.getId()));
        }

        return R.data(kv.set("id", dto.getId()));
    }

    private void handleSubData(ContractMainDTO dto) {
        // 处理子表
        ContractCategoryEnum targetEnum = ContractCategoryEnum.getTargetEnum(dto.getCategory());
        if (targetEnum == null) {
            throw new ServiceException("合同类型不存在");
        }
        switch (targetEnum) {
            case EXPERIMENT:
                // 实验合同子表
                if (dto.getExperimentModel() != null) {
                    ContractExperimentDTO experimentModel = dto.getExperimentModel();
                    experimentModel.setMainId(dto.getId());
                    if (Func.isEmpty(experimentModel.getId())) {
                        contractExperimentService.save(experimentModel);
                    } else {
                        contractExperimentService.updateById(experimentModel);
                    }
                }
                break;
            case PRESTORE:
                // 预存合同子表
                if (dto.getPrestoreModel() != null) {
                    ContractPrestoreDTO prestoreModel = dto.getPrestoreModel();
                    prestoreModel.setMainId(dto.getId());
                    if (Func.isEmpty(prestoreModel.getId())) {
                        contractPrestoreervice.save(prestoreModel);
                    } else {
                        contractPrestoreervice.updateById(prestoreModel);
                    }
                }
                break;
            case REAGENT:
                // 试剂合同子表
                if (dto.getReagentModel() != null) {
                    ContractReagentDTO reagentModel = dto.getReagentModel();
                    reagentModel.setMainId(dto.getId());
                    if (Func.isEmpty(reagentModel.getId())) {
                        contractReagentService.save(reagentModel);
                    } else {
                        contractReagentService.updateById(reagentModel);
                    }
                }
                // 试剂-实验合同子表
                if (CollectionUtil.isNotEmpty(dto.getReagentQuotationMaterialDModel())) {
                    List<ContractReagentQuotationMaterialDTO> reagentQuotationMaterialDModel = dto.getReagentQuotationMaterialDModel();
                    for (ContractReagentQuotationMaterialDTO reagentQuotationMaterialDTO : reagentQuotationMaterialDModel) {
                        reagentQuotationMaterialDTO.setMainId(dto.getId());
                        if (Func.isEmpty(reagentQuotationMaterialDTO.getId())) {
                            contractReagentQuotationMaterialService.save(reagentQuotationMaterialDTO);
                        } else {
                            contractReagentQuotationMaterialService.updateById(reagentQuotationMaterialDTO);
                        }
                    }
                }
                break;
            case ANIMAL_BREEDING:
                // 动物饲养合同子表
                if (dto.getAnimalBreedingModel() != null) {
                    ContractAnimalBreedingDTO animalBreedingModel = dto.getAnimalBreedingModel();
                    animalBreedingModel.setMainId(dto.getId());
                    if (Func.isEmpty(animalBreedingModel.getId())) {
                        contractAnimalBreedingService.save(animalBreedingModel);
                    } else {
                        contractAnimalBreedingService.updateById(animalBreedingModel);
                    }
                }
                // 动物饲养-费用合同子表
                if (CollectionUtil.isNotEmpty(dto.getAnimalBreedingExpensesModel())) {
                    List<ContractAnimalBreedingExpensesDTO> animalBreedingExpensesModel = dto.getAnimalBreedingExpensesModel();
                    for (ContractAnimalBreedingExpensesDTO animalBreedingExpensesDTO : animalBreedingExpensesModel) {
                        animalBreedingExpensesDTO.setMainId(dto.getId());
                        if (Func.isEmpty(animalBreedingExpensesDTO.getId())) {
                            contractAnimalBreedingExpensesService.save(animalBreedingExpensesDTO);
                        } else {
                            contractAnimalBreedingExpensesService.updateById(animalBreedingExpensesDTO);
                        }
                    }
                }
                break;
            case BID_DOCUMENT:
                // 标书合同子表
                if (dto.getContractBidDocumentModel() != null) {
                    ContractBidDocumentDTO bidDocumentModel = dto.getContractBidDocumentModel();
                    bidDocumentModel.setMainId(dto.getId());
                    if (Func.isEmpty(bidDocumentModel.getId())) {
                        contractBidDocumentService.save(bidDocumentModel);
                    } else {
                        contractBidDocumentService.updateById(bidDocumentModel);
                    }
                }
                break;
            default:
                break;
        }
    }


    /**
     * 生成合同编号
     * -合同编号生成规则： 商机负责销售的大写首字母+ - +客户姓名大写首字母+ - +签约日期年月日
     *
     * @return 合同编号
     */
    private String generateContractNumber(String salesOwnerName, String customerContactName, LocalDate signDate) {
        // 商机负责销售的大写首字母
        String pinyinInitials1 = PinYinConverter.toPinyinInitials(salesOwnerName);
        // 客户姓名大写首字母
        String pinyinInitials2 = PinYinConverter.toPinyinInitials(customerContactName);
        // 签约日期年月日
        String signDateStr = DateUtil.format(signDate, "yyyyMMdd");
        return StrUtil.format("{}-{}-{}", pinyinInitials1, pinyinInitials2, signDateStr);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initiateAnApproval(ContractMainDTO dto) {
        // 获取合同主表信息
        ContractMain contractMain = getById(dto.getId());
        if (contractMain == null) {
            throw new ServiceException("合同不存在");
        }
        if (Func.isNotEmpty(contractMain.getStatus()) &&
                !(
                        ProcessStatusEnum.待审批.getCode().equals(contractMain.getStatus()) ||
                                ProcessStatusEnum.已驳回.getCode().equals(contractMain.getStatus()) ||
                                ProcessStatusEnum.已撤回.getCode().equals(contractMain.getStatus())
                )
        ) {
            throw new ServiceException("合同已经发起过审批，不能再次发起");
        }

        // 是否二次发起工作流
        boolean isSecondInitiate = false;
        // 获取流程表当前业务数据
        BusinessProcessEntity businessProcess = businessProcessMapper.getOneByFormKeyAndFormDataId(FlowFormConstant.FLOW_CONTRACT_KEY, contractMain.getId());
        if (businessProcess != null) {
            // 判断下旧的工作流状态是否正常
            if (ProcessStatusEnum.已完成.getCode().equals(businessProcess.getStatus())) {
                throw new ServiceException("合同已经审批完成，不能再次发起");
            }
            // 判断下旧的工作流状态是否审批中
            if (ProcessStatusEnum.审批中.getCode().equals(businessProcess.getStatus())) {
                throw new ServiceException("合同已经审批中，不能再次发起");
            }
            isSecondInitiate = true;
        }


        // 将合同状态从草稿改成正常
        LambdaUpdateWrapper<ContractMain> updateWrapper = Wrappers.lambdaUpdate(ContractMain.class)
                .set(ContractMain::getDraftStatus, 1)
                .eq(ContractMain::getId, dto.getId());
        boolean update = update(updateWrapper);
        if (!update) {
            throw new ServiceException("发起审批失败");
        }

        // 获取审批流审批节点taskId userTask_6891b8bb10fca967be8f7972
        BusinessOpportunity businessOpportunity = businessOpportunityMapper.selectById(contractMain.getBusinessOpportunityId());
        if (businessOpportunity == null) {
            throw new ServiceException("商机不存在");
        }
        // 售前
        Long preSalesId = businessOpportunity.getPreSalesId();
        if (preSalesId == null) {
            throw new ServiceException("商机售前不存在");
        }
        List<FlowVariableDTO> selectSponsorList = new ArrayList<>(){{
            add(new FlowVariableDTO() {{
                setTaskId("userTask_6891b8bb10fca967be8f7972");
                setApproverList(Collections.singletonList(new Approver() {{
                    setAssignee(String.valueOf(preSalesId));
                }}));
            }});
        }};
        // 发起审批
        // 审批待办 展示业务字段
        List<Kv> businessFieldList = new ArrayList<>();
        businessFieldList.add(Kv.create().set("key", "客户").set("value", contractMain.getCustomerName()));
        if (!ContractCategoryEnum.EXPERIMENT_FRAMEWORK.getName().equals(contractMain.getCategory())) {
            businessFieldList.add(Kv.create().set("key", "合同金额").set("value", contractMain.getAmount()));
        }
        businessFieldList.add(Kv.create().set("key", "单据编号").set("value", contractMain.getCode()));
        businessFieldList.add(Kv.create().set("key", "单据时间").set("value", cn.hutool.core.date.DateUtil.now()));
        String businessField = Func.toJson(businessFieldList);

        // 开启工作流
        if (isSecondInitiate) {
            // 二次发起工作流
            workFlowService.updateProcess(
                    FlowFormConstant.FLOW_CONTRACT_KEY,
                    contractMain.getId(),
                    contractMain.getCode(),
                    selectSponsorList,
                    businessField,
                    null
            );
        } else {
            // 第一次发起工作流
            R startProcessR = workFlowService.startProcess(
                    FlowFormConstant.FLOW_CONTRACT_KEY,
                    contractMain.getId(),
                    contractMain.getCode(),
                    selectSponsorList,
                    businessField,
                    contractMain.getCategory()
            );
            if (!startProcessR.isSuccess()) {
                throw new ServiceException("发起审批失败");
            }
            // 存储动态至商机
            LogModel log = new LogModel();
            log.setUserId(AuthUtil.getUserId());
            log.setMsg(StrUtil.format("创建了{}合同", Func.toStr(contractMain.getCategory())));
            log.setPid(contractMain.getBusinessOpportunityId());
            log.setType(0);
            controlsLogService.saveLog(log);
        }

        return update;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean removeModel(Long id) {
//        boolean removeById = removeById(id);
//        if (removeById) {
//            // TODO ？？？
//        }
//        return removeById;
//    }

    @Override
    public ContractMainVO detailModel(ContractMain contractMain) {
        // 获取主表
        ContractMain detail = getById(contractMain.getId());
        if (detail == null) {
            throw new ServiceException("未查询到合同信息");
        }
        ContractMainVO vo = BeanUtil.copyProperties(detail, ContractMainVO.class);
        if (vo == null) {
            throw new ServiceException("未查询到合同信息");
        }
        vo.setCreateUserName(UserCache.getUserName(vo.getCreateUser()));

        // 处理子表
        ContractCategoryEnum targetEnum = ContractCategoryEnum.getTargetEnum(vo.getCategory());
        if (targetEnum == null) {
            throw new ServiceException("合同类型不存在");
        }
        switch (targetEnum) {
            case EXPERIMENT:
                // 实验合同子表
                ContractExperiment experiment = contractExperimentService.getOne(Wrappers.lambdaQuery(ContractExperiment.class)
                        .eq(ContractExperiment::getMainId, vo.getId()));
                if (experiment == null) {
                    experiment = new ContractExperiment();
                }
                ContractExperimentVO contractExperimentVO = BeanUtil.copyProperties(experiment, ContractExperimentVO.class);
                vo.setExperimentModel(contractExperimentVO);
                break;
            case PRESTORE:
                // 预存合同子表
                ContractPrestore prestore = contractPrestoreervice.getOne(Wrappers.lambdaQuery(ContractPrestore.class)
                        .eq(ContractPrestore::getMainId, vo.getId()));
                if (prestore == null) {
                    prestore = new ContractPrestore();
                }
                ContractPrestoreVO contractPrestoreVO = BeanUtil.copyProperties(prestore, ContractPrestoreVO.class);
                vo.setPrestoreModel(contractPrestoreVO);
                break;
            case REAGENT:
                // 试剂合同子表
                ContractReagent reagent = contractReagentService.getOne(Wrappers.lambdaQuery(ContractReagent.class)
                        .eq(ContractReagent::getMainId, vo.getId()));
                if (reagent == null) {
                    reagent = new ContractReagent();
                }
                ContractReagentVO contractReagentVO = BeanUtil.copyProperties(reagent, ContractReagentVO.class);
                vo.setReagentModel(contractReagentVO);
                // 试剂-实验材料合同子表
                List<ContractReagentQuotationMaterial> reagentQuotationMaterials = contractReagentQuotationMaterialService.list(Wrappers.lambdaQuery(ContractReagentQuotationMaterial.class)
                        .eq(ContractReagentQuotationMaterial::getMainId, vo.getId()));
                List<ContractReagentQuotationMaterialVO> reagentQuotationMaterialVOS = CollectionUtil.isEmpty(reagentQuotationMaterials) ? new ArrayList<>() : reagentQuotationMaterials.stream().map(r -> BeanUtil.copyProperties(r, ContractReagentQuotationMaterialVO.class)).collect(Collectors.toList());
                vo.setReagentQuotationMaterialDModel(reagentQuotationMaterialVOS);
                break;
            case BID_DOCUMENT:
                //标书合同子表
                ContractBidDocument bidDocument = contractBidDocumentService.getOne(Wrappers.lambdaQuery(ContractBidDocument.class)
                        .eq(ContractBidDocument::getMainId, vo.getId()));
                if (bidDocument == null) {
                    bidDocument = new ContractBidDocument();
                }
                ContractBidDocumentVO contractBidDocumentVO = BeanUtil.copyProperties(bidDocument, ContractBidDocumentVO.class);
                vo.setContractBidDocumentModel(contractBidDocumentVO);
                break;
            case ANIMAL_BREEDING:
                //动物饲养
                ContractAnimalBreeding animalBreeding = contractAnimalBreedingService.getOne(Wrappers.lambdaQuery(ContractAnimalBreeding.class)
                        .eq(ContractAnimalBreeding::getMainId, vo.getId()));
                if (animalBreeding == null) {
                    animalBreeding = new ContractAnimalBreeding();
                }
                ContractAnimalBreedingVO animalBreedingVO = BeanUtil.copyProperties(animalBreeding, ContractAnimalBreedingVO.class);
                vo.setAnimalBreedingModel(animalBreedingVO);
                //动物饲养费用合同子表
                List<ContractAnimalBreedingExpenses> animalBreedingExpenses = contractAnimalBreedingExpensesService.list(Wrappers.lambdaQuery(ContractAnimalBreedingExpenses.class)
                        .eq(ContractAnimalBreedingExpenses::getMainId, vo.getId()));
                List<ContractAnimalBreedingExpensesVO> animalBreedingExpensesVOS = CollectionUtil.isEmpty(animalBreedingExpenses) ? new ArrayList<>() : animalBreedingExpenses.stream().map(r -> BeanUtil.copyProperties(r, ContractAnimalBreedingExpensesVO.class)).collect(Collectors.toList());
                vo.setAnimalBreedingExpensesModel(animalBreedingExpensesVOS);
                break;
            case EXPERIMENT_FRAMEWORK:
                //实验框架
                break;
        }

        return vo;
    }

    @Override
    public void download(String url, String name, HttpServletResponse response) {
        projectFileService.getFileIoV2(url, name, response);
    }

}
