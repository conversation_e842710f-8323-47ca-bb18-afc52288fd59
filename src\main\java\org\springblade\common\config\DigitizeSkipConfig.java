package org.springblade.common.config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 兰科数字化跳过配置
 *
 * <AUTHOR>
 * @date 2024/4/9
 */
@Data
@Configuration
@ConfigurationProperties("digitize-skip")
public class DigitizeSkipConfig {

    /**
     * oss前缀
     */
    private String ossPrefix;
    /**
     * 跳转地址前缀
     */
    private String jumpPrefix;


}
