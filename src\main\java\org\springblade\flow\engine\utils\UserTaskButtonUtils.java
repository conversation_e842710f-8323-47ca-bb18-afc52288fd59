package org.springblade.flow.engine.utils;

import org.springblade.common.constant.TenantConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.common.cache.SysCache;
import org.springblade.modules.system.vo.ButtonVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.FlowFormConstant.FLOW_CUSTOMER_KEY;
import static org.springblade.flow.core.constant.WorkFlowConstants.PROC_INSTANCE_FORM_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/24 10:56
 */
public class UserTaskButtonUtils {

	/**
	 * 获取工作流任务按钮(todo 以后扩展到流程设计器中更好,目前先这么写死)
	 *
	 * @param currentTask  当前任务
	 * @param lastUserTask 最后一个审批任务,如果是null表示还没有人进行审批过
	 * @return
	 */
	public static List<ButtonVO> getButtonList(WorkFlow currentTask, WorkFlow lastUserTask, WorkFlow startUserTask, Map<String, Object> variables) {
		List<ButtonVO> buttonVOList = new ArrayList<>();
		//获取任务的所有按钮
		List<ButtonVO> list = SysCache.getButtonByMenuCode("workflowList");

		if (lastUserTask.getStatus() == null || lastUserTask.getStatus().equals(FlowCommentEnum.REBACK.getType()) || lastUserTask.getStatus().equals(FlowCommentEnum.REVOKE.getType())) {
			if (startUserTask.getAssignee().equals(AuthUtil.getUserId().toString())) {
				// 是否需要展示编辑按钮
				boolean needEdit = !(CollectionUtil.isNotEmpty(variables) && variables.containsKey(PROC_INSTANCE_FORM_KEY) && Objects.equals(variables.get(PROC_INSTANCE_FORM_KEY), FLOW_CUSTOMER_KEY));
				buttonVOList.addAll(list.stream().filter(buttonVO ->
					buttonVO.getCode().contains("delete") || (needEdit && buttonVO.getCode().contains("edit"))
				).collect(Collectors.toList()));
				return buttonVOList;
			}
		}
		if (Func.equals(FlowCommentEnum.TRANSFER.getType(), currentTask.getStatus())) {
			String userRole = AuthUtil.getUserRole();
			if (CollectionUtil.contains(Func.toStrArray(userRole), TenantConstant.ADMIN_ROLE_ALIAS)) {
				buttonVOList.addAll(list.stream().filter(buttonVO ->
					buttonVO.getCode().contains("transfer")
				).collect(Collectors.toList()));
				return buttonVOList;
			}
		}

		// 2025-07-22  问一问发起人是否要撤销？答：审批前可以撤回
		if (Func.equals(FlowCommentEnum.TODO.getType(), currentTask.getStatus()) && Func.equals(lastUserTask.getTaskName(), "发起人") && lastUserTask.getAssignee().equals(AuthUtil.getUserId().toString())) {
			buttonVOList.addAll(list.stream().filter(buttonVO ->
				buttonVO.getCode().contains("revoke")
			).collect(Collectors.toList()));
		}

		if (currentTask.getAssignee() != null && currentTask.getAssignee().contains(AuthUtil.getUserId().toString())) {
			buttonVOList.addAll(list.stream().filter(buttonVO ->
				buttonVO.getCode().contains("approve") || buttonVO.getCode().contains("reback")
			).collect(Collectors.toList()));
			// TODO 暂时限制 加签节点不能再次转交或者加签
			if (Func.isEmpty(currentTask.getRecordType()) || (Func.isNotEmpty(currentTask.getRecordType()) && !(currentTask.getRecordType() == 1 || currentTask.getRecordType() == 2))) {
				buttonVOList.addAll(list.stream().filter(buttonVO ->
						buttonVO.getCode().contains("transfer") || buttonVO.getCode().contains("signup")
				).collect(Collectors.toList()));
			}
		}
//		if (currentTask.getApproverList()!= null && currentTask.getApproverList().contains(AuthUtil.getUserId().toString())) {
//			buttonVOList.addAll(list.stream().filter(buttonVO ->
//				buttonVO.getCode().contains("approve") || buttonVO.getCode().contains("reback")
//			).collect(Collectors.toList()));
//		}

		return buttonVOList;
	}
}
