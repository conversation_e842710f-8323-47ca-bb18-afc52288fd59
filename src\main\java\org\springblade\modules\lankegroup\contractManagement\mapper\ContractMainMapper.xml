<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractMainMapper">


    <select id="selectContractMainPageByDraft"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractMainVO">
        select
        cm.*,
        u1.real_name as createUserName
        from cm_contract_main as cm
        left join blade_user u1 on u1.id = cm.create_user
        <where>
            cm.is_deleted = 0
            <!-- 名称 -->
            <if test="dto.seekList != null and dto.seekList.size() > 0">
                <foreach collection="dto.seekList" item="item">
                    and cm.name like concat('%', #{item}, '%')
                </foreach>
            </if>
            <!-- 创建人 -->
            <if test="dto.createUserIds != null and dto.createUserIds.size() > 0">
                and cm.create_user in
                <foreach collection="dto.createUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 草稿状态(0:草稿,1:非草稿) -->
            <if test="dto.draftStatus != null">
                and cm.draft_status = #{dto.draftStatus}
            </if>
            <!-- 类型 -->
            <if test="dto.category != null and dto.category != ''">
                and cm.category = #{dto.category}
            </if>
        </where>
        order by cm.create_time desc, cm.id desc
    </select>

    <select id="selectContractMainPage"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractMainVO">
        select
        cm.*,
        u1.real_name as createUserName
        from cm_contract_main as cm
        left join blade_user u1 on u1.id = cm.create_user
        <where>
            cm.is_deleted = 0 and cm.draft_status = 1
            <!-- 名称 -->
            <if test="dto.seekList != null and dto.seekList.size() > 0">
                <foreach collection="dto.seekList" item="item">
                    and cm.name like concat('%', #{item}, '%')
                </foreach>
            </if>
            <!-- 客户id -->
            <if test="dto.customerContactId != null">
                and cm.customer_contact_id = #{dto.customerContactId}
            </if>
            <!-- 创建时间范围 -->
            <if test="dto.createStartTimeSql != null">
                and cm.create_time &gt;= #{dto.createStartTimeSql}
            </if>
            <if test="dto.createEndTimeSql != null">
                and cm.create_time &lt;= #{dto.createEndTimeSql}
            </if>
            <!-- 签约时间范围 -->
            <if test="dto.signStartTimeSql != null">
                and cm.sign_date &gt;= #{dto.signStartTimeSql}
            </if>
            <if test="dto.signEndTimeSql != null">
                and cm.sign_date &lt;= #{dto.signEndTimeSql}
            </if>
            <!-- 创建人 -->
            <if test="dto.createUserIds != null and dto.createUserIds.size() > 0">
                and cm.create_user in
                <foreach collection="dto.createUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 合同审批状态筛选 审批状态(0:待审批,1:审批中,2:已驳回,3:已撤回,4:已通过) -->
            <if test="dto.status != null and dto.status != ''">
                and cm.status = #{dto.status}
            </if>
            <!-- 业务类型数组 -->
            <if test="dto.businessTypeList != null and dto.businessTypeList.size() > 0">
                and cm.business_type in
                <foreach collection="dto.businessTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 人员级别 -->
<!--            <choose>
                <when test="dto.targetUserLevel != null and dto.targetUserLevel == 1"></when>
                <when test="dto.targetUserLevel != null and dto.targetUserLevel == 2"></when>
                <when test="dto.targetUserLevel != null and dto.targetUserLevel == 3">
                    <if test="param2.deptUserIds != null and param2.deptUserIds.size() > 0">
                        and(
                        cm.create_user in
                        <foreach collection="param2.deptUserIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                    and(cm.create_user = #{dto.currentUserId})
                </otherwise>
            </choose>-->
        </where>
        order by cm.create_time desc, cm.id desc
    </select>

    <select id="selectStatistics"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractMainStatisticsVO">
        select
        'businessType' as statistics_type,
        cm.business_type as statistics_key,
        cm.business_type as statistics_name,
        COUNT(*) as count,
        COALESCE(SUM(cm.amount), 0) as amount
        from cm_contract_main as cm
        <where>
            cm.is_deleted = 0 and cm.draft_status = 1
            <!-- 名称 -->
            <if test="seekList != null and seekList.size() > 0">
                <foreach collection="seekList" item="item">
                    and cm.name like concat('%', #{item}, '%')
                </foreach>
            </if>
            <!-- 创建时间范围 -->
            <if test="createStartTimeSql != null">
                and cm.create_time &gt;= #{createStartTimeSql}
            </if>
            <if test="createEndTimeSql != null">
                and cm.create_time &lt;= #{createEndTimeSql}
            </if>
            <!-- 签约时间范围 -->
            <if test="signStartTimeSql != null">
                and cm.sign_date &gt;= #{signStartTimeSql}
            </if>
            <if test="signEndTimeSql != null">
                and cm.sign_date &lt;= #{signEndTimeSql}
            </if>
            <!-- 创建人 -->
            <if test="createUserIds != null and createUserIds.size() > 0">
                and cm.create_user in
                <foreach collection="createUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 合同审批状态筛选 审批状态(0:待审批,1:审批中,2:已驳回,3:已撤回,4:已通过) -->
            <if test="status != null and status != ''">
                and cm.status = #{status}
            </if>
            <!-- 业务类型数组 -->
            <if test="businessTypeList != null and businessTypeList.size() > 0">
                and cm.business_type in
                <foreach collection="businessTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 人员级别 -->
            <choose>
                <when test="targetUserLevel != null and targetUserLevel == 1"></when>
                <when test="targetUserLevel != null and targetUserLevel == 2"></when>
                <when test="targetUserLevel != null and targetUserLevel == 3">
                    <if test="deptUserIds != null and deptUserIds.size() > 0">
                        and(
                        cm.create_user in
                        <foreach collection="deptUserIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                    and(cm.create_user = #{currentUserId})
                </otherwise>
            </choose>
        </where>
        GROUP BY cm.business_type
    </select>

    <select id="selectDraftCountByCategoryGroup" resultType="java.util.Map">
        select category, count(*) as draftCount
        from cm_contract_main
        where is_deleted = 0
          and draft_status = 0
          and create_user = #{userId}
        group by category
    </select>

</mapper>
