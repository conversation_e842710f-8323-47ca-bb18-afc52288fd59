//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.util.Date;

public class Cookie {
    String name;
    String value;
    Date expires;
    String path;
    String domain;
    boolean secure;

    public Cookie(String cookie) {
        String[] array = cookie.split(";");

        for(int i = 0; i < array.length; ++i) {
            String s = array[i].trim();
            if (s.length() > 2) {
                String[] sarr = s.split("=");
                if (sarr.length == 2) {
                    label40: {
                        label39: {
                            String var6;
                            switch((var6 = sarr[0].toLowerCase()).hashCode()) {
                                case -1326197564:
                                    if (!var6.equals("domain")) {
                                        break label39;
                                    }
                                    break;
                                case -1309235404:
                                    if (var6.equals("expires")) {
                                        break label40;
                                    }
                                    break label39;
                                case 3433509:
                                    if (var6.equals("path")) {
                                        this.path = sarr[1];
                                        break;
                                    }
                                default:
                                    break label39;
                            }

                            this.domain = sarr[1];
                            break label40;
                        }

                        if (i == 0) {
                            this.name = sarr[0];
                            this.value = sarr[1];
                        }
                    }
                }

                if (s.equals("SECURE")) {
                    this.secure = true;
                }
            }
        }

    }

    public static Cookie parse(String ck) {
        Cookie c = new Cookie(ck);
        return c.getName() != null && c.getName() != "" ? c : null;
    }

    public String toString() {
        return String.format("%s=%s", this.name, this.value);
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getExpires() {
        return this.expires;
    }

    public void setExpires(Date expires) {
        this.expires = expires;
    }

    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDomain() {
        return this.domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public boolean isSecure() {
        return this.secure;
    }

    public void setSecure(boolean secure) {
        this.secure = secure;
    }
}
