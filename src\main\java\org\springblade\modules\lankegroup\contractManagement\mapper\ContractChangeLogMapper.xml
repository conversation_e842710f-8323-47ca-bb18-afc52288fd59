<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractChangeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractChangeLogResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractChangeLog">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="description" property="description"/>
        <result column="contract_id" property="contractId"/>
        <result column="contract_change_id" property="contractChangeId"/>
    </resultMap>


    <select id="selectContractChangeLogPage" resultMap="contractChangeLogResultMap">
        select * from blade_contract_change_log where is_deleted = 0
    </select>

</mapper>
