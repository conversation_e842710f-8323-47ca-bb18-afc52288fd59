package org.springblade.common.excel;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.common.utils.ExcelImportUtil;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Excel导入助手类
 * 提供更简洁的API来创建Excel导入功能
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class ExcelImportHelper {

    /**
     * 创建Excel导入构建器
     *
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param <T> Excel模型类型
     * @param <E> 实体类型
     * @return 导入构建器
     */
    public static <T, E extends BaseEntity> ImportBuilder<T, E> builder(
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service) {
        return new ImportBuilder<>(excelModelClass, entityClass, service);
    }

    /**
     * Excel导入构建器
     */
    public static class ImportBuilder<T, E extends BaseEntity> {
        private final Class<T> excelModelClass;
        private final Class<E> entityClass;
        private final BaseService<E> service;
        private String errorFileName = "导入错误数据";
        private String errorSheetName = "错误数据";
        private Function<T, List<String>> customValidator;
        private Function<T, E> customConverter;
        private Function<E, Boolean> customSaver;
        private HttpServletResponse response;
        private Integer headRowNumber = 1; // 默认表头在第1行

        public ImportBuilder(Class<T> excelModelClass, Class<E> entityClass, BaseService<E> service) {
            this.excelModelClass = excelModelClass;
            this.entityClass = entityClass;
            this.service = service;
        }

        /**
         * 设置错误文件名
         */
        public ImportBuilder<T, E> errorFileName(String errorFileName) {
            this.errorFileName = errorFileName;
            return this;
        }

        /**
         * 设置错误Sheet名
         */
        public ImportBuilder<T, E> errorSheetName(String errorSheetName) {
            this.errorSheetName = errorSheetName;
            return this;
        }

        /**
         * 设置表头所在行数（从1开始计数）
         * 例如：headRowNumber = 2 表示表头在第2行，第1行是其他内容
         */
        public ImportBuilder<T, E> headRowNumber(Integer headRowNumber) {
            this.headRowNumber = headRowNumber;
            return this;
        }

        /**
         * 设置自定义校验函数
         */
        public ImportBuilder<T, E> validator(Function<T, List<String>> customValidator) {
            this.customValidator = customValidator;
            return this;
        }

        /**
         * 设置自定义转换函数
         */
        public ImportBuilder<T, E> converter(Function<T, E> customConverter) {
            this.customConverter = customConverter;
            return this;
        }

        /**
         * 设置自定义保存函数
         */
        public ImportBuilder<T, E> saver(Function<E, Boolean> customSaver) {
            this.customSaver = customSaver;
            return this;
        }

        /**
         * 设置HTTP响应对象
         */
        public ImportBuilder<T, E> response(HttpServletResponse response) {
            this.response = response;
            return this;
        }

        /**
         * 执行导入
         */
        public R<Map<String, Object>> importExcel(MultipartFile file) {
            return ExcelImportUtil.importExcelWithService(
                file,
                excelModelClass,
                entityClass,
                service,
                errorFileName,
                errorSheetName,
                customValidator,
                customConverter,
                customSaver,
                response,
                headRowNumber
            );
        }
    }

    /**
     * 快速创建基于BeanUtil.copyProperties的转换函数
     *
     * @param entityClass 实体类
     * @param <T> Excel模型类型
     * @param <E> 实体类型
     * @return 转换函数
     */
    public static <T, E extends BaseEntity> Function<T, E> beanCopyConverter(Class<E> entityClass) {
        return (data) -> {
            try {
                E entity = entityClass.newInstance();
                BeanUtil.copyProperties(data, entity);
                return entity;
            } catch (Exception e) {
                log.error("BeanUtil转换异常", e);
                return null;
            }
        };
    }

    /**
     * 快速创建基于service.save的保存函数
     *
     * @param service 业务服务接口
     * @param <E> 实体类型
     * @return 保存函数
     */
    public static <E extends BaseEntity> Function<E, Boolean> serviceSaver(BaseService<E> service) {
        return service::save;
    }
}