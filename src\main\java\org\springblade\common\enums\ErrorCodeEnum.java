package org.springblade.common.enums;

/**
 * 统一错误码枚举
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
public enum ErrorCodeEnum {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 4xx 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    VALIDATION_ERROR(422, "参数校验失败"),
    
    // 5xx 服务器错误
    INTERNAL_SERVER_ERROR(500, "系统内部错误"),
    DATABASE_ERROR(501, "数据库操作异常"),
    NETWORK_ERROR(502, "网络连接异常"),
    SERVICE_UNAVAILABLE(503, "服务暂不可用"),
    
    // 业务异常 6xxx
    BUSINESS_ERROR(6000, "业务操作异常"),
    DATA_NOT_FOUND(6001, "数据不存在"),
    DATA_ALREADY_EXISTS(6002, "数据已存在"),
    OPERATION_NOT_ALLOWED(6003, "操作不被允许"),
    
    // 多租户异常 7xxx
    TENANT_NOT_FOUND(7001, "租户不存在"),
    TENANT_DATASOURCE_ERROR(7002, "租户数据源配置异常"),
    TENANT_INVALID(7003, "租户信息无效"),
    TENANT_EXPIRED(7004, "租户已过期"),
    
    // 用户认证异常 8xxx
    LOGIN_FAILED(8001, "登录失败"),
    TOKEN_EXPIRED(8002, "登录已过期"),
    TOKEN_INVALID(8003, "登录凭证无效"),
    PERMISSION_DENIED(8004, "权限不足"),
    
    // 第三方服务异常 9xxx
    KINGDEE_API_ERROR(9001, "金蝶API调用异常"),
    DINGTALK_API_ERROR(9002, "钉钉API调用异常"),
    WECHAT_API_ERROR(9003, "微信API调用异常");
    
    private final int code;
    private final String message;
    
    ErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据异常类型获取对应的错误码
     */
    public static ErrorCodeEnum getByException(Exception exception) {
        String exceptionName = exception.getClass().getSimpleName();
        
        // 多租户相关异常
        if (exceptionName.contains("TenantDataSource")) {
            return TENANT_DATASOURCE_ERROR;
        }
        if (exceptionName.contains("Tenant")) {
            return TENANT_NOT_FOUND;
        }
        
        // 数据库相关异常
        if (exceptionName.contains("SQL") || exceptionName.contains("Database") || 
            exceptionName.contains("DataAccess") || exceptionName.contains("Mybatis")) {
            return DATABASE_ERROR;
        }
        
        // 网络相关异常
        if (exceptionName.contains("Connect") || exceptionName.contains("Timeout") || 
            exceptionName.contains("Network")) {
            return NETWORK_ERROR;
        }
        
        // 参数校验异常
        if (exceptionName.contains("Validation") || exceptionName.contains("MethodArgument") ||
            exceptionName.contains("Bind")) {
            return VALIDATION_ERROR;
        }
        
        return INTERNAL_SERVER_ERROR;
    }
} 