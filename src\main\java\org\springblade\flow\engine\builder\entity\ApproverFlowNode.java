package org.springblade.flow.engine.builder.entity;


import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * 审批人节点
 */
@Data
@FieldNameConstants
public class ApproverFlowNode {

    /**
     * 类型
     */
    private Integer settype;
    /**
     * 多人审批方式 1会签（需所有人审批通过） 2或签（一名审批人同意即可）
     */
    private Integer multipleApprovalType;

    private List<NodeUser> nodeUserList;
    private List<NodeRole> nodeRoleList;
    private List<NodePost> nodePostList;
    private TaskEvents taskEvents;

	/**
	 * 发起人自选的标志位
	 * 自选人数-自选范围：1-1（自选一个人-全公司）；1-2（自选一个人-指定成员）；1-3（自选一个人-角色）；2-1（自选多人-全公司）；2-2（自选多人-指定成员）；2-3（自选多人-角色）
	 */
	private String sponsorType;
	private List<NodeUser> sponsorUserList;
	private List<NodeRole> sponsorRoleList;

    /**
     * 主管审核等级
     */
    private Integer approveDirectorLevel;
    private String formUserFields;

    private List<Object> formProperties;

    /**
     * 连接线
     */
    private List<String> incoming;


    @Data
    public static class NodeUser {
        private String id;
        private String name;
        private String nickName;
    }

    @Data
    public static class NodePost {
        private Long id;
        private String postName;
        private String postCode;
    }
    @Data
    public static class NodeRole {
        private Long id;
        private String name;
        private String roleKey;
    }


    /**
     * 任务事件
     */
    @Data
    public static class TaskEvents {
        /**
         * 同意
         */
        private boolean agree;
        /**
         * 同意地址
         */
        private String agreeNotifyUrl;
        /**
         * 拒绝
         */
        private boolean reject;
        /**
         * 拒绝地址
         */
        private boolean rejectNotifyUrl;
    }


}
