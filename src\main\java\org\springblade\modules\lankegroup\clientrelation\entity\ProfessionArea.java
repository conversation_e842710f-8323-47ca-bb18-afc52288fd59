package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 市场区划
 */
@Data
@TableName("blade_profession_area")
public class ProfessionArea extends BaseEntity {

    private Long id;

    /**
     * 省区域编码
     */
    private String provinceCode;

    /**
     * 省区域名称
     */
    private String provinceName;

    /**
     * 市区域编码
     */
    private String cityCode;

    /**
     * 市区域名称
     */
    private String cityName;

    /**
     * 区县区域编码
     */
    private String countiesCode;

    /**
     * 区县区域名称
     */
    private String countiesName;

    /**
     * 归属市场ID
     */
    private String joinId;

}
