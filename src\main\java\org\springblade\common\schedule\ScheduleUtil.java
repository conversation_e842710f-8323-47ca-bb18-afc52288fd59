package org.springblade.common.schedule;

import org.springblade.common.constant.SzhConstant;
import org.springblade.common.enums.FinanceTypeEnum;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.finance.entry.ProfitLoss;
import org.springblade.modules.lankegroup.finance.service.FinanceService;
import org.springblade.modules.lankegroup.invoiceStatistics.service.IInvoiceLogService;
import org.springblade.modules.lankegroup.invoiceStatistics.vo.InvoiceLogVO;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.pro_management.service.IProjectBasicService;
import org.springblade.modules.lankegroup.pro_management.vo.ProjectBasicVO;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Async
public class ScheduleUtil {


    private final IProjectBasicService projectBasicService;
    private final IUserService userService;

    private final IInvoiceLogService invoiceLogService;

    private final SendBillMsgUtil sendBillMsgUtil;
    private final FinanceService financeService;


    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public ScheduleUtil(@Lazy IProjectBasicService projectBasicService, IUserService userService, IInvoiceLogService invoiceLogService, SendBillMsgUtil sendBillMsgUtil, FinanceService financeService) {

        this.projectBasicService = projectBasicService;
        this.userService = userService;
        this.invoiceLogService = invoiceLogService;
        this.sendBillMsgUtil = sendBillMsgUtil;
        this.financeService = financeService;
    }

    /**
     * 有超期未回的款项，超期的第二天上午10点钟给项目负责人推送消息提醒
     * <p>
     * 提醒内容标题：回款提醒
     * <p>
     * 内容：您有超期未回的项目款，请及时关注，若不能及时回款请在项目看板更新预计回款日期
     * <p>
     * 发送时间：2023-06-0814:50:09
     * <p>
     * 0619加三个消息提醒：
     * <p>
     * 开票了超过审批通过的时间7天以上，同时距离预计最近的预计回款时间剩余15天/7天/3天的进行消息提醒
     * <p>
     * 提醒内容标题：回款提醒
     * <p>
     * 内容：您的项目已开票7天以上，距离预计回款时间仅剩5天/7天/3天，请跟进回款进度
     * <p>
     * 发送时间：2023-06-0814:50:09
     */

//    @Scheduled(cron = "0 0 10 * * ?")
//    @Scheduled(cron = "0/10 * * * * ?")
    public void invoiceMsg() {
        List<InvoiceLogVO> invoiceLogs = invoiceLogService.selectInvoiceMsg(1, null);

        if (invoiceLogs != null && invoiceLogs.size() > 0) {
            System.out.println("************************开票统计消息通知***************************");
            for (InvoiceLogVO invoiceLog : invoiceLogs) {

                Map map = new HashMap();
                map.put("id", invoiceLog.getKdProjectId());
                ProjectBasicVO projectBasicVO = projectBasicService.selectProjectBasicDetail(map);
                //协助项目（走账项目）不强制提醒业务负责人更新预计回款日期和每周填写回款计划
                if (null == projectBasicVO || projectBasicVO.getCooperation() == 2) {
                    continue;
                }
                invoiceLog.setProjectId(projectBasicVO.getId());

                if (invoiceLog.getModifierAccount() != null && Func.isNotEmpty(invoiceLog.getModifierAccount())) {
                    User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, invoiceLog.getModifierAccount());
                    if (null == user) {
                        continue;
                    }
                    invoiceLog.setModifierId(user.getId());
                }

                /**
                 * 消息类型
                 * 1==>十五天消息提醒
                 * 2==>七天消息提醒
                 * 3==>三天消息提醒
                 */
                switch (invoiceLog.getMsgType()) {
                    case 1:
                        invoiceLog.setDescription("项目已开票7天以上，距离预计回款时间仅剩15天，请跟进回款进度");
                        break;
                    case 2:
                        invoiceLog.setDescription("项目已开票7天以上，距离预计回款时间仅剩7天，请跟进回款进度");
                        break;
                    case 3:
                        invoiceLog.setDescription("项目已开票7天以上，距离预计回款时间仅剩3天，请跟进回款进度");
                        break;
                    case 4:
                        invoiceLog.setDescription("您有超期未回的项目款，请及时关注，若不能及时回款请在开票看板更新预计回款日期");
                        break;

                }
                String endOperate = invoiceLogService.queryEndOperate(invoiceLog.getKdProjectId());
                BillMsgParams billMsgParams = new BillMsgParams();
                billMsgParams.setType("开票统计");
                billMsgParams.setBillNo(invoiceLog.getDescription());

                if (endOperate == null || Func.isEmpty(endOperate) || !endOperate.equals("项目已超过预计回款日期")) {
                    Boolean save = invoiceLogService.save(invoiceLog);
//                    if (save&&invoiceLog.getMsgType()!=4) {
                    billMsgParams.setFlag(0);
                    User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, invoiceLog.getModifierAccount());
                    billMsgParams.setUserId(String.valueOf(user.getId()));
                    billMsgParams.setUserName(user.getName());
                    billMsgParams.setStatus(0);
                    if (save && invoiceLog.getMsgType() == 4) {
                        billMsgParams.setIsKingDee(2);
                    }
                    sendBillMsgUtil.msg(billMsgParams);
//                        KDBillSimpleMsgQueue instance = KDBillSimpleMsgQueue.instance();
//                        KDBillDingMessageSender billDingMessageSender = new KDBillDingMessageSender(0, "kptj", "0", invoiceLog.getModifierAccount(), invoiceLog.getDescription());
//                        instance.add(billDingMessageSender);
//                    }
                }
            }
            System.out.println("***************开票统计消息通知已结束*************************");
        }

    }

    /**
     * 每月一号的凌晨三点将财务看板损益表上月数据写入本地数据库
     * 每个月六号，将上个月数据更新一下
     */
//    @Scheduled(cron = "0 0 3 1 * ?")
    public void financeKanBan() {
        financeService.insertLastMonthFinanceKanData(getLastMonthData());
    }

    /**
     * 每个月六号把一号跑的上个月的数据删掉
     * 重新录入
     */
//    @Scheduled(cron = "0 0 3 6 * ?")
    public void financeKanBanUpdate() {
        financeService.financeKanBanUpdate(getLastMonthData());
    }

    private ProfitLoss getLastMonthData(){
        //    年
        int year = cn.hutool.core.date.DateUtil.year(cn.hutool.core.date.DateUtil.date());
        //    月
        int month = cn.hutool.core.date.DateUtil.month(cn.hutool.core.date.DateUtil.date());
//        如果当前月份是1月，则此时month是0，需要把年和月重新赋值为上一年的12月
        if(Integer.valueOf(month)==0){
            year=Integer.valueOf(year)-1 ;
            month=12;
        }
//       费用=销售费用6601+(管理费用6602-税金6602.02）+财务费用6603+营业外支出6711-（营业外收入6301+围标资质费6051.02）-（主营业务收入6001*0.05）
        Double lastMonthSelling = financeService.getLastMonthFinanceKan(year, month, "6601", 0, FinanceTypeEnum.getTypeCodeByCode("6601"));
        Double lastMonthManagement=financeService.getLastMonthFinanceKan(year, month, "6602", 0, FinanceTypeEnum.getTypeCodeByCode("6602"));
        Double lastMonthTax=financeService.getLastMonthFinanceKan(year, month, "6602.02", 1, FinanceTypeEnum.getTypeCodeByCode("6602"));
        Double lastMonthExpenses=financeService.getLastMonthFinanceKan(year, month, "6603", 0, FinanceTypeEnum.getTypeCodeByCode("6603"));
        Double lastMonthNonExpenses=financeService.getLastMonthFinanceKan(year, month, "6711", 0, FinanceTypeEnum.getTypeCodeByCode("6711"));
        Double lastMonthNonIncome=financeService.getLastMonthFinanceKan(year, month, "6301", 0, FinanceTypeEnum.getTypeCodeByCode("6301"));
        Double lastMonthBid=financeService.getLastMonthFinanceKan(year, month, "6051.02", 1, FinanceTypeEnum.getTypeCodeByCode("6051"));
        Double lastMonthMainIncome=financeService.getLastMonthFinanceKan(year, month, "6001", 0, FinanceTypeEnum.getTypeCodeByCode("6001"));
        Double fee=(lastMonthSelling!=null?lastMonthSelling:0.0) +((lastMonthManagement!=null?lastMonthManagement:0.0)-(lastMonthTax!=null?lastMonthTax:0.0))+
                (lastMonthExpenses!=null ?lastMonthExpenses:0.0)+(lastMonthNonExpenses!=null?lastMonthNonExpenses:0.0)-
                ((lastMonthNonIncome!=null ?lastMonthNonIncome:0.0)+(lastMonthBid!=null?lastMonthBid:0.0))-((lastMonthMainIncome!=null?lastMonthMainIncome:0.0)*0.05);

//        收入=主营业务收入6001
//        成本=主营业务成本6401
        Double lastMonthCostMainIncome=financeService.getLastMonthFinanceKan(year, month, "6401", 0, FinanceTypeEnum.getTypeCodeByCode("6401"));
//        利润=收入-成本-费用
        Double profit=(lastMonthMainIncome!=null?lastMonthMainIncome:0.0)-(lastMonthCostMainIncome!=null?lastMonthCostMainIncome:0.0)-(fee!=null?fee:0.0);
        ProfitLoss profitLoss=new ProfitLoss();
        profitLoss.setYear(Integer.valueOf(year));
        profitLoss.setPeriod(Integer.valueOf(month));
        profitLoss.setRevenue(new BigDecimal((lastMonthMainIncome!=null?lastMonthMainIncome:0.0)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        profitLoss.setCost(new BigDecimal((lastMonthCostMainIncome!=null?lastMonthCostMainIncome:0.0)).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        profitLoss.setExpenses(new BigDecimal(fee).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        profitLoss.setProfit(new BigDecimal(profit).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
//       区分上半年0、下半年1
        profitLoss.setStatus(0);
        if(Integer.valueOf(month)>6){
            profitLoss.setStatus(1);
        }
        return profitLoss;
    }
}
