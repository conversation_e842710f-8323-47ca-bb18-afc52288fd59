package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;
import org.springblade.modules.lankegroup.disposition.bo.OtherProjectGroupBO;

import java.util.ArrayList;
import java.util.List;

@Data
public class ContractListResult {
    /**
     * 合同id或者 变更id 或者归档id
     */
    private Long id;
    /**
     *合同名称/变更标题/归档标题
     */
    private String  contractName;
    /**
     * 合同审批状态
     */
    private Integer contractStatus;
    /**
     * 合同类型，（0销售合同/1采购合同/2其他合同/3变更 ）
     */
    private String contractType;
    /**
     * 客户/供应商名称，变更用来存放合同名称
     */
    private String signedName;
    /**
     * 合同创建时间
     */
    private String create_time;
    /**
     * 合同更新时间（审批时间）
     */
    private String  update_time;
    /**
     * 合同金额/合同变更金额
     */
    private String amount;
    /**
     * 发起人
     */
    private String sponsor;

    /**
     * 是否有变更(0有变更/1无变更/2有变更且为审批完成的变更作废)
     */
    private Integer isChanges=1;
    /**
     * 有变更中/作废的合同不允许归档
     * 有变更中的/作废的合同true===》不允许归档
     */
    private Boolean allowArchiving=false;
    /**
     * 固定的合同id
     */
    private Long cid;
    /**
     * 当前登录人是否为合同发起人
     * true为是
     * false为不是
     * 前端根据此字段控制合同归档是否要出现
     */
    private Boolean isOwner=false;
    /**
     * 当前审批人id
     */
    private String taskId;
    //update 20250421 hz 回显产品类型
    private List<OtherProjectGroupBO> contractProjectGroupList = new ArrayList<>();
}
