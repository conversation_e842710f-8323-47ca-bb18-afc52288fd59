/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.common.entity.SimpleSerialNumber;

/**
 * 简单全局序列号表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface SimpleSerialNumberMapper extends BaseMapper<SimpleSerialNumber> {

    /**
     * 获取业务序列
     *
     * @param bizType
     * @return
     */
    SimpleSerialNumber selectByBizType(String bizType);

    /**
     * 乐观锁更新序列值
     *
     * @param id
     * @param oldValue
     * @param newValue
     * @param version
     * @return
     */
    Integer incrementValue(@Param("id") Long id,
                           @Param("oldValue") Long oldValue,
                           @Param("newValue") Long newValue,
                           @Param("version") Integer version);


}
