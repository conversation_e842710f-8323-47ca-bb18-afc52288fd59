/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerProductType;
import org.springblade.modules.lankegroup.crm.vo.CustomerProductTypeVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerProductTypeService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户产品类型表 控制器
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/product/customerproducttype")
@Api(value = "客户产品类型表", tags = "客户产品类型表接口")
public class CustomerProductTypeController extends BladeController {

	private final ICustomerProductTypeService customerProductTypeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerProductType")
	public R<CustomerProductType> detail(CustomerProductType customerProductType) {
		CustomerProductType detail = customerProductTypeService.getOne(Condition.getQueryWrapper(customerProductType));
		return R.data(detail);
	}

	/**
	 * 分页 客户产品类型表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerProductType")
	public R<IPage<CustomerProductType>> list(CustomerProductType customerProductType, Query query) {
		IPage<CustomerProductType> pages = customerProductTypeService.page(Condition.getPage(query), Condition.getQueryWrapper(customerProductType));
		return R.data(pages);
	}

	/**
	 * 自定义分页 客户产品类型表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerProductType")
	public R<IPage<CustomerProductTypeVO>> page(CustomerProductTypeVO customerProductType, Query query) {
		IPage<CustomerProductTypeVO> pages = customerProductTypeService.selectCustomerProductTypePage(Condition.getPage(query), customerProductType);
		return R.data(pages);
	}

	/**
	 * 新增 客户产品类型表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerProductType")
	public R save(@Valid @RequestBody CustomerProductType customerProductType) {
		return R.status(customerProductTypeService.save(customerProductType));
	}

	/**
	 * 修改 客户产品类型表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerProductType")
	public R update(@Valid @RequestBody CustomerProductType customerProductType) {
		return R.status(customerProductTypeService.updateById(customerProductType));
	}

	/**
	 * 新增或修改 客户产品类型表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerProductType")
	public R submit(@Valid @RequestBody CustomerProductType customerProductType) {
		return R.status(customerProductTypeService.saveOrUpdate(customerProductType));
	}

	
	/**
	 * 删除 客户产品类型表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerProductTypeService.deleteLogic(Func.toLongList(ids)));
	}

	
}
