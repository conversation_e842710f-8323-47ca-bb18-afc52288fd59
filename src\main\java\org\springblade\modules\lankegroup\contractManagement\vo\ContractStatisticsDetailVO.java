package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
@Data
public class ContractStatisticsDetailVO {

    private String conId;

    private String conCode;

    private String proId;

    private String proName;

    private String gpId;

    private String gpName;

    private String conName;

    private String conTotal;

    private String signName;

    private String signManName;

    private String signTal;

    private String unitName;

    private String userId;

    private String userName;

    private String deptId;

    private String deptName;

    private String typeVals;

    private String ofNum;

    private String annex;

    private String annexName;

}
