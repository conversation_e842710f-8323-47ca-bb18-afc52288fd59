package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库元数据工具类
 * 用于动态查询表结构和字段注释信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 */
@Slf4j
@Component
public class DatabaseMetadataUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 表注释缓存
     */
    private static final Map<String, String> TABLE_COMMENT_CACHE = new ConcurrentHashMap<>();

    /**
     * 字段注释缓存 (格式: 表名.字段名)
     */
    private static final Map<String, String> FIELD_COMMENT_CACHE = new ConcurrentHashMap<>();

    /**
     * 索引字段映射缓存 (格式: 表名.索引名 -> 字段名列表)
     */
    private static final Map<String, String> INDEX_FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 字段是否可为空缓存 (格式: 表名.字段名)
     */
    private static final Map<String, Boolean> FIELD_NULLABLE_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取表的中文注释
     */
    public String getTableComment(String tableName) {
        if (tableName == null) {
            return null;
        }
                // 先从缓存查找
        String comment = TABLE_COMMENT_CACHE.get(tableName);
        if (comment != null) {
            return comment;
        }
        
        try {
            String sql = "SELECT table_comment FROM information_schema.tables " +
                    "WHERE table_schema = DATABASE() AND table_name = ?";

            List<String> comments = jdbcTemplate.queryForList(sql, String.class, tableName);
            if (!comments.isEmpty()) {
                comment = comments.get(0);
                if (comment != null && !comment.trim().isEmpty()) {
                    TABLE_COMMENT_CACHE.put(tableName, comment);
                    return comment;
                }
            }
        } catch (Exception e) {
            log.warn("查询表注释失败: tableName={}, error={}", tableName, e.getMessage());
        }

        return null;
    }

    /**
     * 获取字段的中文注释
     */
    public String getFieldComment(String tableName, String fieldName) {
        if (tableName == null || fieldName == null) {
            return null;
        }

        String key = tableName + "." + fieldName;

        // 先从缓存查找
        String comment = FIELD_COMMENT_CACHE.get(key);
        if (comment != null) {
            return comment;
        }
        
        try {
            String sql = "SELECT column_comment FROM information_schema.columns " +
                    "WHERE table_schema = DATABASE() AND table_name = ? AND column_name = ?";

            // 先尝试驼峰格式
            List<String> comments = jdbcTemplate.queryForList(sql, String.class, tableName, fieldName);

            // 如果驼峰格式查不到，尝试下划线格式
            if (comments.isEmpty()) {
                String underscoreFieldName = camelToUnderscore(fieldName);
                log.debug("驼峰格式查询注释失败，尝试下划线格式: {} -> {}", fieldName, underscoreFieldName);
                comments = jdbcTemplate.queryForList(sql, String.class, tableName, underscoreFieldName);
            }

            if (!comments.isEmpty()) {
                comment = comments.get(0);
                if (comment != null && !comment.trim().isEmpty()) {
                    FIELD_COMMENT_CACHE.put(key, comment);
                    return comment;
                }
            }
        } catch (Exception e) {
            log.debug("查询字段注释失败: tableName={}, fieldName={}, error={}",
                    tableName, fieldName, e.getMessage());
        }

        return null;
    }

    /**
     * 获取索引对应的字段名称
     */
    public String getIndexFields(String tableName, String indexName) {
        if (tableName == null || indexName == null) {
            return null;
        }

        String key = tableName + "." + indexName;

        // 先从缓存查找
        String fields = INDEX_FIELD_CACHE.get(key);
        if (fields != null) {
            return fields;
        }

        try {
            String sql = "SELECT GROUP_CONCAT(column_name ORDER BY seq_in_index) as fields " +
                    "FROM information_schema.statistics " +
                    "WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ? " +
                    "GROUP BY index_name";

            List<String> fieldsList = jdbcTemplate.queryForList(sql, String.class, tableName, indexName);
            if (!fieldsList.isEmpty()) {
                fields = fieldsList.get(0);
                if (fields != null && !fields.trim().isEmpty()) {
                    INDEX_FIELD_CACHE.put(key, fields);
                    return fields;
                }
            }
        } catch (Exception e) {
            log.warn("查询索引字段失败: tableName={}, indexName={}, error={}",
                    tableName, indexName, e.getMessage());
        }

        return null;
    }

    /**
     * 获取增强的字段描述（优先使用注释，其次使用配置映射）
     */
    public String getEnhancedFieldDescription(String tableName, String fieldName) {
        // 优先使用数据库注释
        String comment = getFieldComment(tableName, fieldName);
        if (comment != null && !comment.trim().isEmpty()) {
            return comment;
        }

        // 其次使用字段名推测
        if (fieldName != null) {
            if (fieldName.contains("name")) {
                return "名称";
            } else if (fieldName.contains("code")) {
                return "编码";
            } else if (fieldName.contains("id")) {
                return "ID";
            } else if (fieldName.contains("account")) {
                return "账号";
            } else if (fieldName.contains("phone")) {
                return "手机号";
            } else if (fieldName.contains("email")) {
                return "邮箱";
            }
        }

        return fieldName;
    }

    /**
     * 获取增强的表描述（优先使用注释，其次使用配置映射）
     */
    public String getEnhancedTableDescription(String tableName) {
        // 优先使用数据库注释
        String comment = getTableComment(tableName);
        if (comment != null && !comment.trim().isEmpty()) {
            return comment;
        }

        // 其次根据表名前缀推测
        if (tableName != null) {
            if (tableName.startsWith("crm_")) {
                return "CRM模块";
            } else if (tableName.startsWith("sys_")) {
                return "系统模块";
            } else if (tableName.startsWith("project_")) {
                return "项目模块";
            }
        }

        return "数据表";
    }

    /**
     * 获取索引涉及字段的友好描述
     */
    public String getIndexFieldsDescription(String tableName, String indexName) {
        String fields = getIndexFields(tableName, indexName);
        if (fields == null) {
            return null;
        }

        // 将字段名转换为友好描述
        String[] fieldArray = fields.split(",");
        StringBuilder description = new StringBuilder();

        for (int i = 0; i < fieldArray.length; i++) {
            String fieldName = fieldArray[i].trim();
            String fieldDesc = getEnhancedFieldDescription(tableName, fieldName);

            if (i > 0) {
                description.append(" + ");
            }
            description.append(fieldDesc);
        }

        return description.toString();
    }

    /**
     * 将驼峰格式字段名转换为下划线格式
     * 例如：customerId -> customer_id
     */
    private String camelToUnderscore(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camelCase.charAt(0)));

        for (int i = 1; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_');
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }

        return result.toString();
    }

    /**
     * 获取字段是否可为空
     *
     * @param tableName 表名
     * @param fieldName 字段名（支持驼峰格式，会自动转换为下划线格式查询）
     * @return true表示可为空，false表示不可为空(即必填)
     */
    public Boolean getFieldNullable(String tableName, String fieldName) {
        if (tableName == null || fieldName == null) {
            return true; // 默认可为空
        }

        String key = tableName + "." + fieldName;

        // 先从缓存查找
        Boolean nullable = FIELD_NULLABLE_CACHE.get(key);
        if (nullable != null) {
            return nullable;
        }

        try {
            String sql = "SELECT is_nullable FROM information_schema.columns " +
                    "WHERE table_schema = DATABASE() AND table_name = ? AND column_name = ?";

            // 先尝试驼峰格式
            List<String> results = jdbcTemplate.queryForList(sql, String.class, tableName, fieldName);

            // 如果驼峰格式查不到，尝试下划线格式
            if (results.isEmpty()) {
                String underscoreFieldName = camelToUnderscore(fieldName);
                log.debug("驼峰格式查询失败，尝试下划线格式: {} -> {}", fieldName, underscoreFieldName);
                results = jdbcTemplate.queryForList(sql, String.class, tableName, underscoreFieldName);
            }

            if (!results.isEmpty()) {
                String isNullable = results.get(0);
                nullable = "YES".equalsIgnoreCase(isNullable);
                FIELD_NULLABLE_CACHE.put(key, nullable);
                log.debug("字段约束查询成功: {}#{} is_nullable={} -> nullable={}",
                        tableName, fieldName, isNullable, nullable);
                return nullable;
            } else {
                log.warn("未找到字段信息: tableName={}, fieldName={}, 下划线格式={}",
                        tableName, fieldName, camelToUnderscore(fieldName));
            }
        } catch (Exception e) {
            log.warn("查询字段是否可为空失败: tableName={}, fieldName={}, error={}",
                    tableName, fieldName, e.getMessage());
        }

        return true; // 默认可为空
    }

    /**
     * 判断字段是否必填
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @return true表示必填，false表示非必填
     */
    public Boolean isFieldRequired(String tableName, String fieldName) {
        Boolean nullable = getFieldNullable(tableName, fieldName);
        return !nullable; // 不可为空即为必填
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        TABLE_COMMENT_CACHE.clear();
        FIELD_COMMENT_CACHE.clear();
        INDEX_FIELD_CACHE.clear();
        FIELD_NULLABLE_CACHE.clear();
    }

    /**
     * 预加载指定表的元数据
     */
    public void preloadTableMetadata(String tableName) {
        try {
            // 预加载表注释
            getTableComment(tableName);

            // 预加载所有字段注释和可空性
            String sql = "SELECT column_name FROM information_schema.columns " +
                    "WHERE table_schema = DATABASE() AND table_name = ?";
            List<String> fields = jdbcTemplate.queryForList(sql, String.class, tableName);
            for (String fieldName : fields) {
                getFieldComment(tableName, fieldName);
                getFieldNullable(tableName, fieldName);
            }

            // 预加载所有索引信息
            String indexSql = "SELECT DISTINCT index_name FROM information_schema.statistics " +
                    "WHERE table_schema = DATABASE() AND table_name = ? AND index_name != 'PRIMARY'";
            List<String> indexes = jdbcTemplate.queryForList(indexSql, String.class, tableName);
            for (String indexName : indexes) {
                getIndexFields(tableName, indexName);
            }

        } catch (Exception e) {
            log.warn("预加载表元数据失败: tableName={}, error={}", tableName, e.getMessage());
        }
    }
}