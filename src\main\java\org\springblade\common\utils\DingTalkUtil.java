package org.springblade.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;

import java.util.HashMap;

/**
 * <AUTHOR> Liu
 * @since 2022年09月05日 15:44
 **/
public class DingTalkUtil {

    /**
     * 参数名：at
     */
    private static final String MSG_PARAM_AT = "at";
    /**
     * 参数名：atMobiles
     */
    private static final String MSG_PARAM_MOBILES = "atMobiles";
    /**
     * 参数名：atUserIds
     */
    private static final String MSG_PARAM_USERIDS = "atUserIds";
    /**
     * 参数名：isAtAll
     */
    private static final String MSG_PARAM_AT_ALL = "isAtAll";
    /**
     * 参数名：msgtype
     */
    private static final String MSG_PARAM_MSGTYPE = "msgtype";
    /**
     * 参数名：title
     */
    private static final String MSG_PARAM_TITLE = "title";
    /**
     * 参数名：content
     */
    private static final String MSG_PARAM_CONTENT = "content";
    /**
     * text
     */
    private static final String MSG_TYPE_TEXT = "text";
    /**
     * markdown
     */
    private static final String MSG_TYPE_MARKDOWN = "markdown";
    /**
     * webhook为空，发送消息失败。
     */
    private static final String MSG_CHECK_PARAM_URL_IS_NULL = "webhook为空，发送消息失败。";
    /**
     * 消息内容为空，发送消息失败。
     */
    private static final String MSG_CHECK_PARAM_MSG_CONTENT_IS_NULL = "消息内容为空，发送消息失败。";
    /**
     * 消息标题为空，发送消息失败。
     */
    private static final String MSG_CHECK_PARAM_MSG_TITLE_IS_NULL = "消息标题为空，发送消息失败。";


    /**
     * 给指定人发送Text类型消息
     *
     * @param webhook   机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param content   消息内容
     * @param atMobiles 指定人接口预警的手机号（多个手机号，用英文逗号分隔）
     * @return
     */
    public static String sendTextMsgForSomeOne(String webhook, String content, String atMobiles) {
        return sendTextMsgCoreHandle(webhook, content, false, atMobiles);
    }

    /**
     * 给所有人发送Text类型消息
     *
     * @param webhook 机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param content 消息内容
     * @return
     */
    public static String sendTextMsgForAll(String webhook, String content) {
        return sendTextMsgCoreHandle(webhook, content, true, null);
    }

    /**
     * 发送Text类型消息
     *
     * @param webhook   机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param content   消息内容
     * @param atAll     @所有人标识 true-@所有人；false-不@所有人
     * @param atMobiles 指定人接口预警的手机号（多个手机号，用英文逗号分隔）
     * @return
     */
    private static String sendTextMsgCoreHandle(String webhook, String content, boolean atAll, String atMobiles) {
        if (StrUtil.isBlank(webhook)) {
            return MSG_CHECK_PARAM_URL_IS_NULL;
        }
        if (StrUtil.isBlank(content)) {
            return MSG_CHECK_PARAM_MSG_CONTENT_IS_NULL;
        }
        HashMap<String, Object> hashMapByContent = new HashMap<String, Object>(1) {{
            put(MSG_PARAM_CONTENT, content);
        }};
        HashMap<String, Object> assembleParam = assembleParam(MSG_TYPE_TEXT, atAll, atMobiles, hashMapByContent);
        return httpPostRequest(webhook, JSONUtil.toJsonStr(assembleParam));
    }

    /**
     * 给指定人发送Markdown类型消息
     *
     * @param webhook   机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param title     消息标题（首屏会话透出的展示内容。）
     * @param text      消息内容
     * @param atMobiles 指定人接口预警的手机号（多个手机号，用英文逗号分隔）
     * @return
     */
    public static String sendMarkdownMsgForSomeOne(String webhook, String title, String text, String atMobiles) {
        return sendMarkdownMsgCoreHandle(webhook, title, text, false, atMobiles);
    }

    /**
     * 给所有人发送Markdown类型消息
     *
     * @param webhook 机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param title   消息标题（首屏会话透出的展示内容。）
     * @param text    消息内容
     * @return
     */
    public static String sendMarkdownMsgForAll(String webhook, String title, String text) {
        return sendMarkdownMsgCoreHandle(webhook, title, text, true, null);
    }

    /**
     * 发送Markdown类型消息
     *
     * @param webhook   机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param title     消息标题（首屏会话透出的展示内容。）
     * @param text      消息内容
     * @param atAll     @所有人标识 true-@所有人；false-不@所有人
     * @param atMobiles 指定人接口预警的手机号（多个手机号，用英文逗号分隔）
     * @return
     */
    private static String sendMarkdownMsgCoreHandle(String webhook, String title, String text, boolean atAll, String atMobiles) {
        if (StrUtil.isBlank(webhook)) {
            return MSG_CHECK_PARAM_URL_IS_NULL;
        }
        if (StrUtil.isBlank(title)) {
            return MSG_CHECK_PARAM_MSG_TITLE_IS_NULL;
        }
        if (StrUtil.isBlank(text)) {
            return MSG_CHECK_PARAM_MSG_CONTENT_IS_NULL;
        }
        HashMap<String, Object> hashMapByContent = new HashMap<String, Object>(2) {{
            StringBuffer prex = new StringBuffer();
            if (StrUtil.isNotBlank(atMobiles)) {
                String[] splits = atMobiles.split(StrUtil.COMMA);
                for (String split : splits) {
//                    prex.append(StrUtil.AT);
                    prex.append(split);
                    prex.append(StrUtil.C_SPACE);
                }
                prex.append(StrUtil.LF);
            }
            put(MSG_PARAM_TITLE, title);
            put(MSG_TYPE_TEXT, prex.toString() + text);
        }};
        HashMap<String, Object> assembleParam = assembleParam(MSG_TYPE_MARKDOWN, atAll, atMobiles, hashMapByContent);
        return httpPostRequest(webhook, JSONUtil.toJsonStr(assembleParam));
    }


    /**
     * 组装参数
     *
     * @param msgtype          消息类型（text、markdown）
     * @param atAll            所有人接收标识 true-@所有人；false-不@所有人
     * @param atMobiles        指定人接口预警的手机号（多个手机号，用英文逗号分隔）
     * @param hashMapByContent 消息内容
     * @return
     */
    private static HashMap<String, Object> assembleParam(String msgtype, boolean atAll, String atMobiles, HashMap<String, Object> hashMapByContent) {
        HashMap<String, Object> hashMap = new HashMap<>(3);
        hashMap.put(MSG_PARAM_AT, new HashMap<String, Object>(2) {{
            if (atAll) {
                put(MSG_PARAM_AT_ALL, atAll);
            }
            if (StrUtil.isNotBlank(atMobiles)) {
                put(MSG_PARAM_MOBILES, atMobiles.split(StrUtil.COMMA));
            }
        }});
        if (StrUtil.isNotBlank(msgtype)) {
            switch (msgtype) {
                case MSG_TYPE_TEXT:
                    hashMap.put(MSG_TYPE_TEXT, hashMapByContent);
                    hashMap.put(MSG_PARAM_MSGTYPE, MSG_TYPE_TEXT);
                    break;
                case MSG_TYPE_MARKDOWN:
                    hashMap.put(MSG_TYPE_MARKDOWN, hashMapByContent);
                    hashMap.put(MSG_PARAM_MSGTYPE, MSG_TYPE_MARKDOWN);
                    break;
                default:
                    break;
            }
        }
        return hashMap;
    }


    /**
     * 调用钉钉消息接口
     *
     * @param webhook 机器人webhook(不要公布在外部网站上，泄露有安全风险)
     * @param json    消息json化
     * @return
     */
    private static String httpPostRequest(String webhook, String json) {
        return HttpRequest.post(webhook)
                .setConnectionTimeout(3000)
                .setReadTimeout(5000)
                .body(json)
                .execute()
                .body();
    }


}
