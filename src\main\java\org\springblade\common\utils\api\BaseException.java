package org.springblade.common.utils.api;

import io.swagger.models.auth.In;
import org.springblade.core.tool.api.IResultCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 15:01
 */
public class BaseException extends RuntimeException implements IResultCode {
    /**
     * 异常信息
     */
    protected String message;

    /**
     * 具体异常码
     */
    protected int code;

    public BaseException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }
    @Override
    public int getCode() {
        return code;
    }
}