/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * common_attachment表基础视图对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "AttachmentBaseVO对象", description = "common_attachment表基础视图对象")
@EqualsAndHashCode(callSuper = false)
public class AttachmentBaseVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 业务类型
	 */
	@NotBlank(message = "业务类型不能为空")
	@Size(max = 255, message = "业务类型长度不能超过255个字符")
	@ApiModelProperty(value = "业务类型", required = true)
	private String businessType;
	/**
	 * 业务ID
	 */
	@ApiModelProperty(value = "业务ID")
	private Long businessId;
	/**
	 * 子业务类型
	 */
	@NotBlank(message = "子业务类型不能为空")
	@Size(max = 255, message = "子业务类型长度不能超过255个字符")
	@ApiModelProperty(value = "子业务类型", required = true)
	private String subBusinessType;
	/**
	 * 附件名称
	 */
	@NotBlank(message = "附件名称不能为空")
	@Size(max = 255, message = "附件名称长度不能超过255个字符")
	@ApiModelProperty(value = "附件名称", required = true)
	private String fileName;
	/**
	 * 附件地址
	 */
	@ApiModelProperty(value = "附件地址")
	private String fileUrl;
	/**
	 * 附件类型
	 */
	@NotBlank(message = "附件类型不能为空")
	@Size(max = 255, message = "附件类型长度不能超过255个字符")
	@ApiModelProperty(value = "附件类型", required = true)
	private String fileType;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	// BaseVO 只包含业务字段，不包含系统自动填充的字段

} 