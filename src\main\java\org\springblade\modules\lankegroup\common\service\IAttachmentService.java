/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.service;

import org.springblade.modules.lankegroup.common.entity.Attachment;
import org.springblade.modules.lankegroup.common.vo.AttachmentVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 通用附件 服务类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface IAttachmentService extends BaseService<Attachment> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attachment
	 * @return
	 */
	IPage<AttachmentVO> selectAttachmentPage(IPage<AttachmentVO> page, AttachmentVO attachment);

	/**
	 * 根据业务类型和业务ID查询附件列表
	 *
	 * @param businessType 业务类型
	 * @param businessId 业务ID
	 * @return 附件列表
	 */
	List<Attachment> getAttachmentsByBusiness(String businessType, Long businessId);

	/**
	 * 删除指定业务的所有附件
	 *
	 * @param businessType 业务类型
	 * @param businessId 业务ID
	 * @return 删除结果
	 */
	boolean deleteByBusiness(String businessType, Long businessId);

	/**
	 * 批量保存附件列表，无id则新增，有id则修改
	 * 此方法会先删除旧的附件，再保存新的附件
	 *
	 * @param businessType 业务类型
	 * @param businessId 业务ID
	 * @param attachmentList 附件列表
	 * @return 处理结果
	 */
	boolean saveList(String businessType, Long businessId, List<Attachment> attachmentList);

}
