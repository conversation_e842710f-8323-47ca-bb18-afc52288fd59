//package org.springblade.common.utils;
//
//import com.aliyun.dingtalkrobot_1_0.Client;
//import com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendHeaders;
//import com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendRequest;
//import com.aliyun.tea.TeaException;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.hp.dingtalk.component.factory.token.DingAccessTokenFactory;
//import org.springblade.modules.lankegroup.dingrobot.SystemRoBot;
//import org.springblade.modules.lankegroup.dingrobot.entity.DingTalkSingleMessage;
//
//public class SendDingTalkMessageUtil {
//
//    public static Client createClient() throws Exception {
//        Config config = new Config();
//        config.protocol = "https";
//        config.regionId = "central";
//        return new Client(config);
//    }
//
//    public static void sendMessgae(DingTalkSingleMessage dingTalkSingleMessage, SystemRoBot systemRoBot) throws Exception {
//        Client client = createClient();
//        OrgGroupSendHeaders orgGroupSendHeaders = new OrgGroupSendHeaders();
//        orgGroupSendHeaders.xAcsDingtalkAccessToken = DingAccessTokenFactory.accessToken(systemRoBot);
//        OrgGroupSendRequest orgGroupSendRequest = new OrgGroupSendRequest()
//                .setMsgParam(dingTalkSingleMessage.getMsgParam())
//                .setMsgKey(dingTalkSingleMessage.getMsgKey())
//                .setOpenConversationId(dingTalkSingleMessage.getOpenConversationId())
//                .setRobotCode(dingTalkSingleMessage.getRobotCode());
//        try {
//            client.orgGroupSendWithOptions(orgGroupSendRequest, orgGroupSendHeaders, new RuntimeOptions());
//        } catch (TeaException err) {
//            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
//                // err 中含有 code 和 message 属性，可帮助开发定位问题
//            }
//
//        } catch (Exception _err) {
//            TeaException err = new TeaException(_err.getMessage(), _err);
//            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
//                // err 中含有 code 和 message 属性，可帮助开发定位问题
//            }
//
//        }
//    }
//}
