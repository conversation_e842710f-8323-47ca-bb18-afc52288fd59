/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.cache;

import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;

import java.util.List;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;
import static org.springblade.core.launch.constant.FlowConstant.TASK_USR_PREFIX;

/**
 * 系统缓存
 *
 * <AUTHOR>
 */
public class UserCache {
	private static final String USER_CACHE_ID = "user:id:";
	private static final String USER_CACHE_ACCOUNT = "user:account:";
	private static final String USER_TOP_DEPT_LEADER = "user:deptId:";
	private static final String USER_USER_ALL_LEADER = "userLeader:id:";

	private static final IUserService userService;

	static {
		userService = SpringUtil.getBean(IUserService.class);
	}

	/**
	 * 根据任务用户id获取用户信息
	 *
	 * @param taskUserId 任务用户id
	 * @return
	 */
	public static User getUserByTaskUser(String taskUserId) {
		Long userId = Func.toLong(StringUtil.removePrefix(taskUserId, TASK_USR_PREFIX));
		return getUser(userId);
	}

	/**
	 * 获取用户
	 *
	 * @param userId 用户id
	 * @return
	 */
	public static User getUser(Long userId) {
		return CacheUtil.get(USER_CACHE, USER_CACHE_ID, userId, () -> userService.getById(userId));
	}

	/**
	 * 获取用户姓名
	 *
	 * @param userId 用户id
	 * @return
	 */
	public static String getUserName(Long userId) {
		if (userId != null){
			User user = getUser(userId);
			if (user != null){
				return user.getRealName();
			}
		}
		return "";
	}

	/**
	 * 获取用户姓名List
	 *
	 * @param userIdList 用户id
	 * @return
	 */
	public static String getUserNameList(List<Long> userIdList) {
		if (CollectionUtil.isNotEmpty(userIdList)){
            String collect = StringPool.EMPTY;
            try {
                collect = userIdList.stream().map(e -> getUser(e)).map(User::getRealName).distinct().collect(Collectors.joining(StringPool.COMMA));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return collect;
		}
		return StringPool.EMPTY;
	}

	/**
	 * 获取用户
	 *
	 * @param tenantId 租户id
	 * @param account  账号名
	 * @return
	 */
	public static User getUser(String tenantId, String account) {
		return CacheUtil.get(USER_CACHE, USER_CACHE_ACCOUNT, tenantId + StringPool.DASH + account, () -> userService.userByAccount(tenantId, account));
	}

	/**
	 * 获取顶级部门的负责人
	 * @param deptId
	 * @return
	 */
	public static User getTopDeptLeader(Long deptId) {
		return CacheUtil.get(USER_CACHE, USER_TOP_DEPT_LEADER, deptId , () -> userService.getTopDeptLeaderByDeptId(deptId));
	}
	/**
	 * 获取顶级部门的负责人
	 * @param userId
	 * @return
	 */
	public static List<Long> getUserAllLeaderList(Long userId) {
		return CacheUtil.get(USER_CACHE, USER_USER_ALL_LEADER, userId , () -> userService.getALlParentByUserId(userId));
	}

	/**
	 * 刷新用户所有领导列表缓存USER_USER_ALL_LEADER
	 * 一般测试的时候需要改各领导会签的时候使用
	 * 测试完成后需要将调用此方法的地方注释掉
	 * @param userId 用户id
	 */
	public static void refreshUserAllLeaderListCache(Long userId) {
		List<Long> newList = userService.getALlParentByUserId(userId);
		if (newList != null) {
			// 将新列表更新到缓存中
			CacheUtil.put(USER_CACHE, USER_USER_ALL_LEADER, userId, newList);
			CacheUtil.get(USER_CACHE, USER_CACHE_ID, userId, () -> userService.getById(userId));
		}
	}
}
