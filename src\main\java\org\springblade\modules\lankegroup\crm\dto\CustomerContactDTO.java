/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.lankegroup.crm.entity.CustomerContact;
import org.springblade.modules.lankegroup.crm.entity.CustomerContactTag;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 客户联系人表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerContactDTO对象", description = "客户联系人表视图对象")
public class CustomerContactDTO extends CustomerContact {
    private static final long serialVersionUID = 1L;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private List<List<CustomerContactTag>> tagsList;


    /**
     * 创建开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建开始时间")
    private LocalDate createStartTime;
    /**
     * 创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建结束时间")
    private LocalDate createEndTime;

    /**
     * 创建开始时间
     */
    private LocalDateTime createStartTimeSql;
    /**
     * 创建结束时间
     */
    private LocalDateTime createEndTimeSql;


}
