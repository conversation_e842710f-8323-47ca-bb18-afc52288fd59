<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionJoinMapper">
    <delete id="deleteProfessionJoin">
        delete from blade_join_profession where profession_id = #{id}
    </delete>

    <select id="getJoinList" resultType="java.lang.Long">
        select id from blade_join_profession where profession_id = #{id}
    </select>
    <select id="getJoinListByObject"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select area.counties_code AS areaCode, market.market_code AS marketCode,market.join_id joinId
        FROM blade_join_profession jo
                 JOIN blade_profession_area area ON area.join_id = jo.id
                 JOIN blade_profession_market market ON market.join_id = jo.id
        WHERE jo.profession_id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
