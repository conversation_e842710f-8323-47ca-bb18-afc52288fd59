package org.springblade.modules.lankegroup.contractManagement.utils;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.AllArgsConstructor;
import org.ddr.poi.html.HtmlRenderPolicy;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractAnimalBreedingExpensesDTO;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractMainDTO;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractPrestoreDTO;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractReagentQuotationMaterialDTO;
import org.springblade.modules.lankegroup.contractManagement.enums.ContractCategoryEnum;
import org.springblade.modules.lankegroup.contractManagement.enums.SignMethodEnum;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractExperimentMapper;
import org.springblade.modules.lankegroup.contractManagement.word.*;
import org.springblade.modules.lankegroup.jl.entity.Quotation;
import org.springblade.modules.lankegroup.jl.mapper.PlanMapper;
import org.springblade.modules.lankegroup.jl.mapper.QuotationChargeItemMapper;
import org.springblade.modules.lankegroup.jl.mapper.QuotationMapper;
import org.springblade.modules.lankegroup.project.service.MinioService;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 生成
 */
@Service
@AllArgsConstructor
public class CreateWordUtil {

    private final MinioService minioService;
    private final PlanMapper planMapper;
    private final ContractExperimentMapper contractExperimentMapper;
    private final QuotationChargeItemMapper quotationChargeItemMapper;
    private final QuotationMapper quotationMapper;

    /**
     * 导出文件名称规则：客户名称+模版名称+签约日期
     */
    public static final String EXPORT_WORD_FILE_NAME = "{}{}{}.docx";



    private void handleOtherWordField(ContractMainWord tragetWordEntity,  Map<String, Object> targetMap) {
        // 完善字段
        // 甲方是用 客户还是机构
        boolean isCustomer = SignMethodEnum.CUSTOMER_SIGN.getDesc().equals(tragetWordEntity.getSignMethod());
        targetMap.put("partyAName", isCustomer ? tragetWordEntity.getCustomerName() : tragetWordEntity.getCustomerContactName());
        targetMap.put("partyAIsCustomer", isCustomer);
        targetMap.put("partyAIsCustomerContact", !isCustomer);
    }

    /**
     * 导出到word文档
     *
     * @param dto 销售合同实体，可以为空
     * @return 字节数组
     */
    public byte[] export(ContractMainDTO dto) {
        // 总数据
        Map<String, Object> targetMap = new HashMap<>();

        // 转化主表数据
        ContractMainWord tragetWordEntity = BeanUtil.copyProperties(dto, ContractMainWord.class);
        // 转化部分数据
        handleOtherWordField(tragetWordEntity, targetMap);
        // 签约时间
        if (Func.isNotEmpty(tragetWordEntity.getSignDate())) {
            tragetWordEntity.setSignDateStr(DateUtil.format(tragetWordEntity.getSignDate(), DatePattern.CHINESE_DATE_PATTERN));
        }
        // 截止日期、交货日期
        if (Func.isNotEmpty(tragetWordEntity.getEndDate())) {
            tragetWordEntity.setEndDateStr(DateUtil.format(tragetWordEntity.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }
        // 创建时间
        if (Func.isNotEmpty(dto.getCreateTime())) {
            tragetWordEntity.setCreateTimeStr(DateUtil.format(dto.getCreateTime(), DatePattern.NORM_DATE_PATTERN));
        }
        targetMap.putAll(ReplaceParamToWordUtil.toMapV2(tragetWordEntity, ContractMainWord.class));

        // 转化子表数据
        ConfigureBuilder configureBuilder = Configure.builder();
        ContractCategoryEnum targetEnum = ContractCategoryEnum.getTargetEnum(dto.getCategory());
        switch (targetEnum) {
            // TODO getPlanTextByBzOpIdV2暂时用着，后变换  实验
            case EXPERIMENT:
                String planText1 = planMapper.getPlanTextByBzOpIdV2(dto.getBusinessOpportunityId());
                targetMap.put("experiment_attach_1", RichTextToWordUtil.toDoc(Func.toStr(planText1)));
                configureBuilder.bind("experiment_attach_1", new HtmlRenderPolicy());
                targetMap.putAll(ReplaceParamToWordUtil.toMapV2(BeanUtil.copyProperties(dto.getExperimentModel(), ContractExperimentWord.class), ContractExperimentWord.class));
                break;
            case PRESTORE:
                targetMap.putAll(ReplaceParamToWordUtil.toMapV2(BeanUtil.copyProperties(dto.getPrestoreModel(), ContractPrestoreWord.class), ContractPrestoreWord.class));
                break;
            case REAGENT:
                targetMap.putAll(ReplaceParamToWordUtil.toMapV2(BeanUtil.copyProperties(dto.getReagentModel(), ContractReagentWord.class), ContractReagentWord.class));
                // 待实现 试剂-实验材料
                List<ContractReagentQuotationMaterialDTO> reagentQuotationMaterialDModel = dto.getReagentQuotationMaterialDModel();
                if (CollectionUtil.isNotEmpty(reagentQuotationMaterialDModel)) {
                    List<ContractReagentQuotationMaterialWord> reagentQuotationMaterialWordList = new ArrayList<>(reagentQuotationMaterialDModel.size());
                    for (int i = 0; i < reagentQuotationMaterialDModel.size(); i++) {
                        ContractReagentQuotationMaterialWord reagentQuotationMaterialWord = new ContractReagentQuotationMaterialWord();
                        reagentQuotationMaterialWord.setSerialNumber(Func.toStr(i + 1));
                        reagentQuotationMaterialWord.setMaterialName(reagentQuotationMaterialDModel.get(i).getMaterialName());
                        reagentQuotationMaterialWord.setMaterialSpec(reagentQuotationMaterialDModel.get(i).getMaterialSpec());
                        reagentQuotationMaterialWord.setQuantityStr(reagentQuotationMaterialDModel.get(i).getQuantity().toPlainString());
                        reagentQuotationMaterialWord.setFinalAmountStr(reagentQuotationMaterialDModel.get(i).getFinalAmount().toPlainString());
                        reagentQuotationMaterialWord.setItemNumber(reagentQuotationMaterialDModel.get(i).getItemNumber());
                        reagentQuotationMaterialWordList.add(reagentQuotationMaterialWord);
                    }
                    targetMap.put("rqms1", reagentQuotationMaterialWordList);
                     configureBuilder.bind("rqms1", new LoopRowTableRenderPolicy());
                }
                break;
            case ANIMAL_BREEDING:
                targetMap.putAll(ReplaceParamToWordUtil.toMapV2(BeanUtil.copyProperties(dto.getAnimalBreedingModel(), ContractAnimalBreedingWord.class), ContractAnimalBreedingWord.class));
                // 待实现 动物饲养-费用
                List<ContractAnimalBreedingExpensesDTO> animalBreedingExpensesModels = dto.getAnimalBreedingExpensesModel();
                if (CollectionUtil.isNotEmpty(animalBreedingExpensesModels)) {
                    List<ContractAnimalBreedingExpensesWord> animalBreedingExpensesWordList = new ArrayList<>(animalBreedingExpensesModels.size());
                    for (int i = 0; i < animalBreedingExpensesModels.size(); i++) {
                        ContractAnimalBreedingExpensesWord animalBreedingExpensesWord = new ContractAnimalBreedingExpensesWord();
                        animalBreedingExpensesWord.setSerialNumber(Func.toStr(i + 1));
                        animalBreedingExpensesWord.setItemName(animalBreedingExpensesModels.get(i).getItemName());
                        animalBreedingExpensesWord.setItemSpec(animalBreedingExpensesModels.get(i).getItemSpec());
                        animalBreedingExpensesWord.setUnitPrice(animalBreedingExpensesModels.get(i).getUnitPrice().toPlainString());
                        animalBreedingExpensesWord.setRemark(animalBreedingExpensesModels.get(i).getRemark());
                        animalBreedingExpensesWordList.add(animalBreedingExpensesWord);
                    }
                    targetMap.put("abes1", animalBreedingExpensesWordList);
                    configureBuilder.bind("abes1", new LoopRowTableRenderPolicy());
                }
                break;
            case BID_DOCUMENT:
                targetMap.putAll(ReplaceParamToWordUtil.toMapV2(BeanUtil.copyProperties(dto.getContractBidDocumentModel(), ContractBidDocumentWord.class), ContractBidDocumentWord.class));
                break;
            case EXPERIMENT_FRAMEWORK:
                // 获取最大报价
                LambdaQueryWrapper<Quotation> versionWrapper = new LambdaQueryWrapper<>();
                versionWrapper.eq(Quotation::getBzOpId, dto.getBusinessOpportunityId())
                        .orderByDesc(Quotation::getVersionNo)
                        .last("LIMIT 1");
                Quotation maxVersionQuotation = quotationMapper.selectOne(versionWrapper);
                // 获取收费项
                List<ExperimentFrameworkWord> listByExperimentFrameworkList = Optional.ofNullable(maxVersionQuotation).map(Quotation::getId).map(quotationChargeItemMapper::getListByExperimentFramework).orElse(new ArrayList<>());
                if (CollectionUtil.isNotEmpty(listByExperimentFrameworkList)) {
                    for (int i = 0; i < listByExperimentFrameworkList.size(); i++) {
                        listByExperimentFrameworkList.get(i).setSerialNumber(Func.toStr(i + 1));
                    }
                    targetMap.put("efs1", listByExperimentFrameworkList);
                    configureBuilder.bind("efs1", new LoopRowTableRenderPolicy());
                }
               break;
        }
        Configure configure = configureBuilder.build();

        // 下载模板
        byte[] templateBytes = minioService.download(dto.getTemplateUrl());
        ByteArrayInputStream inputStream = new ByteArrayInputStream(templateBytes);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        // 渲染
        XWPFTemplate template = XWPFTemplate.compile(inputStream, configure).render(targetMap);
        try {
            template.writeAndClose(baos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return baos.toByteArray();
    }
}
