package org.springblade.common.utils;

import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.crm.excel.BaseExcelImportListener;
import org.springblade.modules.lankegroup.crm.excel.ExcelImportResult;
import org.springblade.common.excel.GenericExcelImportListener;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Excel导入通用工具类
 * 封装Excel导入的通用逻辑，简化Controller代码
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class ExcelImportUtil {

    /**
     * 导出模式枚举
     */
    public enum ExportMode {
        /**
         * 只导出错误数据
         */
        ERROR_ONLY,
        /**
         * 导出所有数据，标红错误数据
         */
        ALL_WITH_ERROR_HIGHLIGHT,
        /**
         * 导出所有数据，标红错误数据，不包含行号列
         */
        ALL_WITH_ERROR_HIGHLIGHT_NO_ROW_INDEX
    }

    /**
     * 执行Excel导入 - 使用自定义监听器（兼容旧版本）
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param listenerSupplier 监听器提供者
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcel(MultipartFile file,
                                                           Class<T> excelModelClass,
                                                           Supplier<BaseExcelImportListener<T, E>> listenerSupplier) {
        return importExcel(file, excelModelClass, listenerSupplier, null);
    }

    /**
     * 执行Excel导入 - 使用自定义监听器完整版本（兼容旧版本）
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param listenerSupplier 监听器提供者
     * @param response HTTP响应对象（可选，如果提供则在有错误时直接导出错误文件）
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcel(MultipartFile file,
                                                           Class<T> excelModelClass,
                                                           Supplier<BaseExcelImportListener<T, E>> listenerSupplier,
                                                           HttpServletResponse response) {

        // 1. 文件验证
        R<Void> validation = validateFile(file);
        if (!validation.isSuccess()) {
            return R.fail(validation.getMsg());
        }

        try {
            // 2. 创建导入监听器
            BaseExcelImportListener<T, E> listener = listenerSupplier.get();

            // 3. 读取Excel文件
            EasyExcel.read(file.getInputStream(), excelModelClass, listener)
                    .sheet()
                    .doRead();

            // 4. 获取导入结果
            ExcelImportResult<E> result = listener.getImportResult();

            // 5. 构造返回结果
            return buildResultResponse(result, response);

        } catch (IOException e) {
            log.error("Excel文件读取失败", e);
            return R.fail("Excel文件读取失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return R.fail("Excel导入失败：" + e.getMessage());
        }
    }

    /**
     * 执行Excel导入 - 使用通用监听器（推荐方式）
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param listenerSupplier 通用监听器提供者
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithGeneric(MultipartFile file,
                                                                      Class<T> excelModelClass,
                                                                      Supplier<GenericExcelImportListener<T, E>> listenerSupplier) {
        return importExcelWithGeneric(file, excelModelClass, listenerSupplier, null);
    }

    /**
     * 执行Excel导入 - 使用通用监听器完整版本（推荐方式）
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param listenerSupplier 通用监听器提供者
     * @param response HTTP响应对象（可选，如果提供则在有错误时直接导出错误文件）
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithGeneric(MultipartFile file,
                                                                      Class<T> excelModelClass,
                                                                      Supplier<GenericExcelImportListener<T, E>> listenerSupplier,
                                                                      HttpServletResponse response) {
        return importExcelWithGeneric(file, excelModelClass, listenerSupplier, response, 1);
    }

    /**
     * 执行Excel导入 - 使用通用监听器完整版本（支持自定义表头行数）
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param listenerSupplier 通用监听器提供者
     * @param response HTTP响应对象（可选，如果提供则在有错误时直接导出错误文件）
     * @param headRowNumber 表头所在行数（从1开始计数）
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithGeneric(MultipartFile file,
                                                                      Class<T> excelModelClass,
                                                                      Supplier<GenericExcelImportListener<T, E>> listenerSupplier,
                                                                      HttpServletResponse response,
                                                                      Integer headRowNumber) {

        // 1. 文件验证
        R<Void> validation = validateFile(file);
        if (!validation.isSuccess()) {
            return R.fail(validation.getMsg());
        }

        try {
            // 2. 创建导入监听器
            GenericExcelImportListener<T, E> listener = listenerSupplier.get();

            // 3. 读取Excel文件，支持自定义表头行数
            EasyExcel.read(file.getInputStream(), excelModelClass, listener)
                    .sheet()
                    .headRowNumber(headRowNumber == null ? 1 : headRowNumber)
                    .doRead();

            // 4. 获取导入结果
            ExcelImportResult<E> result = listener.getImportResult();

            // 5. 构造返回结果
            return buildResultResponse(result, response);

        } catch (IOException e) {
            log.error("Excel文件读取失败", e);
            return R.fail("Excel文件读取失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return R.fail("Excel导入失败：" + e.getMessage());
        }
    }

    /**
     * 下载Excel模板
     *
     * @param response HTTP响应对象
     * @param templateData 模板数据
     * @param excelModelClass Excel模型类
     * @param fileName 文件名（不含扩展名）
     * @param sheetName Sheet名称
     * @param <T> Excel模型类型
     */
    public static <T> void downloadTemplate(HttpServletResponse response,
                                          List<T> templateData,
                                          Class<T> excelModelClass,
                                          String fileName,
                                          String sheetName) {
        try {
            setExcelResponseHeaders(response, fileName);

            EasyExcel.write(response.getOutputStream(), excelModelClass)
                    .sheet(sheetName)
                    .doWrite(templateData);

        } catch (Exception e) {
            log.error("下载Excel模板失败", e);
            handleDownloadError(response, "下载失败");
        }
    }

    /**
     * 导出Excel错误数据
     *
     * @param response HTTP响应对象
     * @param errorData 错误数据
     * @param excelModelClass Excel模型类
     * @param fileName 文件名（不含扩展名）
     * @param sheetName Sheet名称
     * @param <T> Excel模型类型
     */
    public static <T> void exportErrorData(HttpServletResponse response,
                                         List<T> errorData,
                                         Class<T> excelModelClass,
                                         String fileName,
                                         String sheetName) {
        try {
            setExcelResponseHeaders(response, fileName);

            // 尝试使用模板文件（针对CustomerContactExcel）
            String templatePath = "./src/main/resources/exceltemplate/customer-contact-template.xlsx";
            java.io.File templateFile = new java.io.File(templatePath);
            
            if (excelModelClass.getSimpleName().contains("CustomerContact") && templateFile.exists()) {
                // 使用模板导出
                java.io.InputStream templateStream = null;
                try {
                    templateStream = new java.io.FileInputStream(templateFile);
                    log.info("使用模板文件导出: {}", templatePath);

                    EasyExcel.write(response.getOutputStream(), excelModelClass)
                            .withTemplate(templateStream)
                            .sheet()
                            .needHead(false)  // 不写入表头，使用模板中的表头
                            .doWrite(errorData);
                } finally {
                    if (templateStream != null) {
                        try {
                            templateStream.close();
                        } catch (Exception e) {
                            log.warn("关闭模板文件流失败", e);
                        }
                    }
                }
            } else {
                // 使用默认方式导出
                EasyExcel.write(response.getOutputStream(), excelModelClass)
                        .sheet(sheetName)
                        .doWrite(errorData);
            }

        } catch (Exception e) {
            log.error("导出错误数据失败", e);
            handleDownloadError(response, "导出失败");
        }
    }



    /**
     * 通用的错误数据导出接口
     * 从缓存中获取错误数据，转换格式后导出，并清理缓存
     *
     * @param errorKey 错误数据缓存键
     * @param response HTTP响应对象
     * @param excelModelClass Excel模型类
     * @param fileName 文件名（不含扩展名）
     * @param sheetName Sheet名称
     * @param dataConverter 数据转换器，将缓存中的错误数据转换为Excel格式
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     */
    public static <T, E extends BaseEntity> void exportImportErrorData(String errorKey,
                                                   HttpServletResponse response,
                                                   Class<T> excelModelClass,
                                                   String fileName,
                                                   String sheetName,
                                                   ErrorDataConverter<T, E> dataConverter) {
        exportImportData(errorKey, response, excelModelClass, fileName, sheetName, dataConverter, ExportMode.ERROR_ONLY);
    }

    /**
     * 通用的导入数据导出接口（支持选择导出模式）
     * 从缓存中获取导入数据，转换格式后导出，并清理缓存
     *
     * @param dataKey 数据缓存键
     * @param response HTTP响应对象
     * @param excelModelClass Excel模型类
     * @param fileName 文件名（不含扩展名）
     * @param sheetName Sheet名称
     * @param dataConverter 数据转换器，将缓存中的数据转换为Excel格式
     * @param exportMode 导出模式
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     */
    public static <T, E extends BaseEntity> void exportImportData(String dataKey,
                                                   HttpServletResponse response,
                                                   Class<T> excelModelClass,
                                                   String fileName,
                                                   String sheetName,
                                                   ErrorDataConverter<T, E> dataConverter,
                                                   ExportMode exportMode) {
        try {
            // 从缓存中获取导入数据
            ExcelImportResult<E> importResult = org.springblade.common.cache.LocalExcelErrorDataCache.getImportData(dataKey);

            if (importResult == null) {
                handleNoErrorDataFound(response);
                return;
            }

            List<T> dataToExport;

            if (exportMode == ExportMode.ERROR_ONLY) {
                // 只导出错误数据
                dataToExport = getErrorDataFromCache(dataKey, dataConverter);
                if (dataToExport.isEmpty()) {
                    handleNoErrorDataFound(response);
                    return;
                }
            } else {
                // 导出所有数据
                dataToExport = getAllDataFromCache(dataKey, dataConverter, exportMode);
                if (dataToExport.isEmpty()) {
                    handleNoDataFound(response);
                    return;
                }
            }

                         // 导出数据
             exportErrorData(response, dataToExport, excelModelClass, fileName, sheetName);

            // 导出完成后清理缓存
            org.springblade.common.cache.LocalExcelErrorDataCache.removeImportData(dataKey);
            log.info("导入数据导出完成并清理缓存，dataKey: {}, 导出模式: {}", dataKey, exportMode);

        } catch (Exception e) {
            log.error("导出导入数据失败，dataKey: {}", dataKey, e);
            handleDownloadError(response, "导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出所有数据并标红错误数据（推荐使用的新方法）
     */
    public static <T, E extends BaseEntity> void exportAllImportDataWithErrorHighlight(String dataKey,
                                                                      HttpServletResponse response,
                                                                      Class<T> excelModelClass,
                                                                      String fileName,
                                                                      String sheetName,
                                                                      ErrorDataConverter<T, E> dataConverter) {
        exportImportData(dataKey, response, excelModelClass, fileName, sheetName, dataConverter, ExportMode.ALL_WITH_ERROR_HIGHLIGHT);
    }

    /**
     * 导出所有数据并标红错误数据（不包含行号列）
     */
    public static <T, E extends BaseEntity> void exportAllImportDataWithErrorHighlightNoRowIndex(String dataKey,
                                                                      HttpServletResponse response,
                                                                      Class<T> excelModelClass,
                                                                      String fileName,
                                                                      String sheetName,
                                                                      ErrorDataConverter<T, E> dataConverter) {
        exportImportData(dataKey, response, excelModelClass, fileName, sheetName, dataConverter, ExportMode.ALL_WITH_ERROR_HIGHLIGHT_NO_ROW_INDEX);
    }



    /**
     * 从缓存中获取错误数据
     */
    private static <T, E extends BaseEntity> List<T> getErrorDataFromCache(String errorKey, ErrorDataConverter<T, E> dataConverter) {
        try {
            // 从缓存中获取错误数据
            ExcelImportResult<E> importResult = org.springblade.common.cache.LocalExcelErrorDataCache.getErrorData(errorKey);

            if (importResult == null || importResult.getFailureList().isEmpty()) {
                log.warn("未找到错误数据，errorKey: {}", errorKey);
                return new ArrayList<>();
            }

            List<T> errorDataList = new ArrayList<>();

            // 按行号排序错误数据，保持与原始文件相同的顺序
            List<ExcelImportResult.ExcelImportError> sortedErrorList = new ArrayList<>(importResult.getFailureList());
            sortedErrorList.sort((e1, e2) -> {
                if (e1.getRowIndex() == null && e2.getRowIndex() == null) return 0;
                if (e1.getRowIndex() == null) return 1;
                if (e2.getRowIndex() == null) return -1;
                return e1.getRowIndex().compareTo(e2.getRowIndex());
            });
            
            // 转换错误数据为Excel格式
            for (ExcelImportResult.ExcelImportError error : sortedErrorList) {
                T errorData = dataConverter.convert(error);
                if (errorData != null) {
                    errorDataList.add(errorData);
                }
            }

            log.info("获取错误数据成功，errorKey: {}, 错误数据数量: {}", errorKey, errorDataList.size());
            return errorDataList;

        } catch (Exception e) {
            log.error("获取错误数据失败，errorKey: {}", errorKey, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从缓存中获取所有数据（成功+失败）
     */
    private static <T, E extends BaseEntity> List<T> getAllDataFromCache(String dataKey, ErrorDataConverter<T, E> dataConverter, ExportMode exportMode) {
        try {
            // 从缓存中获取导入结果
            ExcelImportResult<E> importResult = org.springblade.common.cache.LocalExcelErrorDataCache.getImportData(dataKey);

            if (importResult == null || importResult.getAllDataForExport().isEmpty()) {
                log.warn("未找到导入数据，dataKey: {}", dataKey);
                return new ArrayList<>();
            }

            List<T> allDataList = new ArrayList<>();

            // 按行号排序所有数据，保持与原始文件相同的顺序
            List<ExcelImportResult.AllDataRecord> sortedAllDataList = new ArrayList<>(importResult.getAllDataForExport());
            sortedAllDataList.sort((d1, d2) -> {
                if (d1.getRowIndex() == null && d2.getRowIndex() == null) return 0;
                if (d1.getRowIndex() == null) return 1;
                if (d2.getRowIndex() == null) return -1;
                return d1.getRowIndex().compareTo(d2.getRowIndex());
            });

            // 转换所有数据为Excel格式
            for (ExcelImportResult.AllDataRecord dataRecord : sortedAllDataList) {
                T excelData = convertToExcelData(dataRecord, dataConverter, exportMode);
                if (excelData != null) {
                    allDataList.add(excelData);
                }
            }

            log.info("获取所有导入数据成功，dataKey: {}, 总数据数量: {}", dataKey, allDataList.size());
            return allDataList;

        } catch (Exception e) {
            log.error("获取所有导入数据失败，dataKey: {}", dataKey, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换数据记录为Excel格式
     */
    @SuppressWarnings("unchecked")
    private static <T, E extends BaseEntity> T convertToExcelData(ExcelImportResult.AllDataRecord dataRecord, ErrorDataConverter<T, E> dataConverter, ExportMode exportMode) {
        try {
            // 如果原始数据已经是Excel格式，直接使用
            if (dataRecord.getOriginalExcelData() != null) {
                T excelData = (T) dataRecord.getOriginalExcelData();
                
                // 设置处理结果信息
                if (dataRecord.getIsSuccess()) {
                    // 成功数据：显示"导入成功"
                    setErrorMessage(excelData, "导入成功");
                } else {
                    // 失败数据：显示具体错误信息
                    setErrorMessage(excelData, dataRecord.getErrorMessage());
                }
                
                // 只有在非"不包含行号"模式下才设置行号
                if (exportMode != ExportMode.ALL_WITH_ERROR_HIGHLIGHT_NO_ROW_INDEX) {
                    setRowIndex(excelData, dataRecord.getRowIndex());
                }
                
                return excelData;
            }
            
            // 如果没有原始Excel数据，使用转换器创建记录
            ExcelImportResult.ExcelImportError error = new ExcelImportResult.ExcelImportError(
                dataRecord.getOriginalExcelData(), 
                dataRecord.getRowIndex(), 
                dataRecord.getIsSuccess() ? "导入成功" : dataRecord.getErrorMessage()
            );
            return dataConverter.convert(error);
            
        } catch (Exception e) {
            log.warn("转换数据记录为Excel格式失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 设置错误信息到Excel对象（反射方式）
     */
    private static void setErrorMessage(Object excelData, String errorMessage) {
        try {
            Method setErrorMessageMethod = excelData.getClass().getMethod("setErrorMessage", String.class);
            setErrorMessageMethod.invoke(excelData, errorMessage);
        } catch (Exception e) {
            // 忽略反射异常，可能该Excel类没有errorMessage字段
        }
    }

    /**
     * 设置行号到Excel对象（反射方式）
     */
    private static void setRowIndex(Object excelData, Integer rowIndex) {
        try {
            Method setRowIndexMethod = excelData.getClass().getMethod("setRowIndex", Integer.class);
            setRowIndexMethod.invoke(excelData, rowIndex);
        } catch (Exception e) {
            // 忽略反射异常，可能该Excel类没有rowIndex字段
        }
    }

    /**
     * 处理未找到错误数据的情况
     */
    private static void handleNoErrorDataFound(HttpServletResponse response) {
        try {
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"success\":false,\"message\":\"未找到错误数据，可能已过期或不存在\"}");
        } catch (Exception e) {
            log.error("写入无错误数据响应失败", e);
        }
    }

    /**
     * 处理未找到任何数据的情况
     */
    private static void handleNoDataFound(HttpServletResponse response) {
        try {
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"success\":false,\"message\":\"未找到导入数据，可能已过期或不存在\"}");
        } catch (Exception e) {
            log.error("写入无数据响应失败", e);
        }
    }

    /**
     * 错误数据转换器接口
     */
    @FunctionalInterface
    public interface ErrorDataConverter<T, E extends BaseEntity> {
        /**
         * 将导入错误转换为Excel格式数据
         *
         * @param error 导入错误信息
         * @return Excel格式数据
         */
        T convert(ExcelImportResult.ExcelImportError error);
    }



    // ==================== 私有方法 ====================

    /**
     * 验证上传文件
     */
    private static R<Void> validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return R.fail("请选择要导入的文件");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return R.fail("请上传Excel文件（.xlsx或.xls格式）");
        }

        // 检查文件大小（例如限制10MB）
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxSize) {
            return R.fail("文件大小不能超过10MB");
        }

        return R.success("");
    }

    /**
     * 构造返回结果
     */
    private static <E extends BaseEntity> R<Map<String, Object>> buildResultResponse(ExcelImportResult<E> result,
                                                                 HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalCount", result.getTotalCount());
        resultMap.put("successCount", result.getSuccessCount());
        resultMap.put("failureCount", result.getFailureCount());

        if (result.getFailureCount() > 0) {
            // 有失败数据
            resultMap.put("hasError", true);
            resultMap.put("errorDetails", buildErrorDetails(result));
            resultMap.put("errorMessage", "部分数据导入失败，请查看详细错误信息");

            // 生成错误数据的唯一标识
            String errorKey = "import_error_" + System.currentTimeMillis();
            resultMap.put("errorKey", errorKey);

            // 将错误数据缓存起来，供后续导出使用
            try {
                org.springblade.common.cache.LocalExcelErrorDataCache.putErrorData(errorKey, result);
                log.debug("错误数据已缓存，errorKey: {}", errorKey);
            } catch (Exception e) {
                log.error("缓存错误数据失败，errorKey: {}", errorKey, e);
            }

            return R.data(resultMap, "导入完成，但有 " + result.getFailureCount() + " 条数据失败");
        } else {
            // 全部成功
            resultMap.put("hasError", false);
            resultMap.put("errorDetails", new ArrayList<>());
            return R.data(resultMap, "导入成功！共导入 " + result.getSuccessCount() + " 条数据");
        }
    }

    /**
     * 构造错误详情
     */
    private static <E extends BaseEntity> List<Map<String, Object>> buildErrorDetails(ExcelImportResult<E> result) {
        List<Map<String, Object>> errorDetails = new ArrayList<>();

        for (ExcelImportResult.ExcelImportError error : result.getFailureList()) {
            Map<String, Object> errorDetail = new HashMap<>();
            errorDetail.put("rowIndex", error.getRowIndex());
            errorDetail.put("errorMessage", error.getErrorMessage());
            errorDetail.put("errorMessages", error.getErrorMessages());
            errorDetail.put("rowData", error.getOriginalData());

            // 添加更多详细信息
            if (error.getOriginalData() != null) {
                errorDetail.put("hasRowData", true);
                errorDetail.put("dataType", error.getOriginalData().getClass().getSimpleName());
            } else {
                errorDetail.put("hasRowData", false);
                errorDetail.put("dataType", "未知");
            }

            // 错误类型分类
            String errorType = categorizeError(error.getErrorMessage());
            errorDetail.put("errorType", errorType);

            errorDetails.add(errorDetail);
        }

        return errorDetails;
    }

    /**
     * 错误分类
     */
    private static String categorizeError(String errorMessage) {
        if (errorMessage == null) {
            return "未知错误";
        }

        String lowerMessage = errorMessage.toLowerCase();

        if (lowerMessage.contains("数据库错误") || lowerMessage.contains("sql")) {
            return "数据库错误";
        } else if (lowerMessage.contains("数据完整性") || lowerMessage.contains("约束")) {
            return "数据完整性错误";
        } else if (lowerMessage.contains("重复") || lowerMessage.contains("duplicate")) {
            return "数据重复";
        } else if (lowerMessage.contains("空指针") || lowerMessage.contains("null")) {
            return "空值错误";
        } else if (lowerMessage.contains("参数") || lowerMessage.contains("argument")) {
            return "参数错误";
        } else if (lowerMessage.contains("验证") || lowerMessage.contains("validation")) {
            return "数据验证错误";
        } else if (lowerMessage.contains("保存失败")) {
            return "保存失败";
        } else {
            return "业务逻辑错误";
        }
    }

    /**
     * 设置Excel文件响应头
     */
    private static void setExcelResponseHeaders(HttpServletResponse response, String fileName) throws Exception {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 防止中文乱码
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName + ".xlsx");
    }

    /**
     * 处理下载错误
     */
    private static void handleDownloadError(HttpServletResponse response, String errorMessage) {
        try {
            response.reset();
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"success\":false,\"message\":\"" + errorMessage + "\"}");
        } catch (IOException ex) {
            log.error("写入错误响应失败", ex);
        }
    }

    // ==================== 建造者模式配置类（可选） ====================

    // ==================== 通用导入方法（从GenericExcelImportUtil合并） ====================

    /**
     * 执行Excel导入 - 基础版本
     * 使用默认的BeanUtil.copyProperties进行实体转换
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithService(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName) {

        return importExcelWithService(file, excelModelClass, entityClass, service,
                          errorFileName, "错误数据", null, null, null, null);
    }

    /**
     * 执行Excel导入 - 完整版本
     * 支持自定义校验、转换和保存逻辑
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param errorSheetName 错误Sheet名
     * @param customValidator 自定义校验函数
     * @param customConverter 自定义转换函数
     * @param customSaver 自定义保存函数
     * @param response HTTP响应对象
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithService(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName,
            String errorSheetName,
            Function<T, List<String>> customValidator,
            Function<T, E> customConverter,
            Function<E, Boolean> customSaver,
            HttpServletResponse response) {
        
        return importExcelWithService(file, excelModelClass, entityClass, service, 
                errorFileName, errorSheetName, customValidator, customConverter, 
                customSaver, response, 1);
    }

    /**
     * 执行Excel导入 - 完整版本（支持自定义表头行数）
     * 支持自定义校验、转换和保存逻辑
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param errorSheetName 错误Sheet名
     * @param customValidator 自定义校验函数
     * @param customConverter 自定义转换函数
     * @param customSaver 自定义保存函数
     * @param response HTTP响应对象
     * @param headRowNumber 表头所在行数（从1开始计数）
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithService(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName,
            String errorSheetName,
            Function<T, List<String>> customValidator,
            Function<T, E> customConverter,
            Function<E, Boolean> customSaver,
            HttpServletResponse response,
            Integer headRowNumber) {

        try {
            // 创建通用导入监听器
            Supplier<GenericExcelImportListener<T, E>> listenerSupplier = () ->
                new GenericExcelImportListener<>(
                    service,
                    entityClass,
                    excelModelClass,
                    response,
                    errorFileName,
                    errorSheetName,
                    customValidator,
                    customConverter,
                    customSaver
                );

            // 执行导入
            return importExcelWithGeneric(file, excelModelClass, listenerSupplier, response, headRowNumber);

        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return R.fail("Excel导入失败：" + e.getMessage());
        }
    }

    /**
     * 执行Excel导入 - 带自定义校验
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param customValidator 自定义校验函数
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithValidator(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName,
            Function<T, List<String>> customValidator) {

        return importExcelWithService(file, excelModelClass, entityClass, service,
                          errorFileName, "错误数据", customValidator, null, null, null);
    }

    /**
     * 执行Excel导入 - 带自定义转换
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param customConverter 自定义转换函数
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithConverter(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName,
            Function<T, E> customConverter) {

        return importExcelWithService(file, excelModelClass, entityClass, service,
                          errorFileName, "错误数据", null, customConverter, null, null);
    }

    /**
     * 执行Excel导入 - 带自定义保存
     *
     * @param file Excel文件
     * @param excelModelClass Excel模型类
     * @param entityClass 实体类
     * @param service 业务服务接口
     * @param errorFileName 错误文件名
     * @param customSaver 自定义保存函数
     * @param <T> Excel模型类型
     * @param <E> 实体类型，必须继承BaseEntity
     * @return 导入结果
     */
    public static <T, E extends BaseEntity> R<Map<String, Object>> importExcelWithSaver(
            MultipartFile file,
            Class<T> excelModelClass,
            Class<E> entityClass,
            BaseService<E> service,
            String errorFileName,
            Function<E, Boolean> customSaver) {

        return importExcelWithService(file, excelModelClass, entityClass, service,
                          errorFileName, "错误数据", null, null, customSaver, null);
    }

    // ==================== 注意事项 ====================

    /**
     * 使用说明：
     * 1. 推荐使用 ExcelImportHelper.builder() 来创建导入任务，API更简洁
     * 2. importExcelWithService() 系列方法是最通用的导入方法
     * 3. importExcel() 系列方法用于兼容旧版本的自定义监听器
     * 4. importExcelWithGeneric() 系列方法是推荐的通用导入方法
     */
}
