/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.contractManagement.entity.SignatoryCompany;
import org.springblade.modules.lankegroup.contractManagement.vo.SignatoryCompanyVO;

import java.util.List;

/**
 * 签约公司表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface SignatoryCompanyMapper extends BaseMapper<SignatoryCompany> {

    /**
     * 自定义分页
     *
     * @param page
     * @param signatoryCompany
     * @return
     */
    List<SignatoryCompanyVO> selectSignatoryCompanyPage(IPage page, SignatoryCompanyVO signatoryCompany);

    /**
     * 校验 纳税登记号 唯一
     */
    Integer getCountByTaxNumber(@Param("taxNumber") String taxNumber, @Param("id") Long id);

    /**
     * 校验 排序 唯一
     */
    Integer getCountBySortOrder(@Param("sortOrder") Integer sortOrder, @Param("id") Long id);

}
