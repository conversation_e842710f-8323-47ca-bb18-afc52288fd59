package org.springblade.modules.lankegroup.clientrelation.dto;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.Profession;

import java.util.List;

@Data
public class ProfessionDTO extends Profession {

    private String joinId;

    private List<ProfessionJoinDTO> joinList;

    private List<Long> userList;

    private List<String> deptList;

    /**
     * 删除的行业ID
     */
    private List<AreaAndMarket> notMarketList;

    private List<AreaAndMarket> list;

    private String professionId;
}
