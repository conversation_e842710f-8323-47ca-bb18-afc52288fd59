package org.springblade.common.utils;

import lombok.AllArgsConstructor;
import org.springblade.common.constant.SzhConstant;
import org.springblade.common.enums.DocumentType;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.lankegroup.message.dto.SystemMessageDTO;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 各个单据消息通知工具类
 */
@RestController
@AllArgsConstructor
@RequestMapping("/billSend")
public class SendBillMsgUtil {
    private SystemMessageService systemMessageService;

    private IUserService userService;

    //    static {
//        userService = SpringUtil.getBean(IUserService.class);
//        systemMessageService = SpringUtil.getBean(SystemMessageService.class);
//    }
    @PostMapping("/sendMsg")
    public R msg(@RequestBody BillMsgParams billMsgParams) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        String content = "";
        String title = "";
        String url = null;
//        用于开票统计存储的备用正式列表头
        String listTitle;
        if (billMsgParams.getFlag() != 1 && !billMsgParams.getType().equals("开票统计") &&
                !billMsgParams.getType().equals("共享客户申请") &&
                !billMsgParams.getType().equals("共享客户申请结果")) {
            title = "我的@bill@审批";
            switch (billMsgParams.getStatus()) {
                case 0:
                    content = "我的@bill@审批，已提交给" + billMsgParams.getName() + "审批中，名称为" + billMsgParams.getBillName();
                    if (billMsgParams.getIsKingDee() == 1) {
                        content = "我的@bill@审批，已提交给" + billMsgParams.getName() + "审批中，单号为" + billMsgParams.getBillNo();
                    }
                    break;
                case 1:
                    content = "我的@bill@审批，已同意，名称为" + billMsgParams.getBillName();
                    if (billMsgParams.getIsKingDee() == 1) {
                        content = "我的@bill@审批，已同意，单号为" + billMsgParams.getBillNo();
                    }
                    break;
                case 2:
                    content = "我的@bill@审批，" + billMsgParams.getName() + "已驳回，名称为" + billMsgParams.getBillName();
                    if (billMsgParams.getIsKingDee() == 1) {
                        content = "我的@bill@审批，" + billMsgParams.getName() + "已驳回，单号为" + billMsgParams.getBillNo();
                    }
                    if (StringUtil.equals(billMsgParams.getType(), "归档")) {
                        content = "我的@bill@审批，" + billMsgParams.getName() + "已驳回，名称为" + billMsgParams.getBillName();
                    }
//                    content = "我的@bill@审批，" + billMsgParams.getName() + "已驳回，名称为" + billMsgParams.getBillNo()+"，驳回理由："+billMsgParams.getReason();
//                    if (billMsgParams.getIsKingDee() == 1) {
//                        content = "我的@bill@审批，" + billMsgParams.getName() + "已驳回，单号为" + billMsgParams.getBillNo()+"，驳回理由："+billMsgParams.getReason();
//                    }
                    break;
                case 3:
                    content = "我的@bill@审批，" + billMsgParams.getName() + "已作废，名称为" + billMsgParams.getBillName();
                    if (billMsgParams.getIsKingDee() == 1) {
                        content = "我的@bill@审批，" + billMsgParams.getName() + "已作废，单号为" + billMsgParams.getBillNo();
                    }
                    break;
                case 4:
                    content = "我的@bill@审批，已撤回，名称为" + billMsgParams.getBillName();
                    if (billMsgParams.getIsKingDee() == 1) {
                        content = "我的@bill@审批，已撤回，单号为" + billMsgParams.getBillNo();
                    }
                    break;
            }
        }
        if (billMsgParams.getFlag() == 1 && !billMsgParams.getType().equals("开票统计") &&
                !billMsgParams.getType().equals("共享客户申请") &&
                !billMsgParams.getType().equals("共享客户申请结果")) {
            title = billMsgParams.getName() + "的@bill@审批";
            if (billMsgParams.getType().equals("入职申请") || billMsgParams.getType().equals("考核确认") ||
                    billMsgParams.getType().equals("印章申请")||billMsgParams.getType().equals("任务交付")) {
                content = "您有一个@bill@需要审批，单号为" + billMsgParams.getBillNo();
            } else {
                content = "您有一个@bill@需要审批，名称为" + billMsgParams.getBillName();
            }
            //            如果是抄送人
            if (billMsgParams.getIsCC()) {
                if (billMsgParams.getIsKingDee() == 1) {
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，抄送给您，单号为" + billMsgParams.getBillNo();
                }else {
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，抄送给您，名称为" + billMsgParams.getBillName();
                }
            } else {
                if (billMsgParams.getIsKingDee() == 1) {
                    content = "您有一个@bill@需要审批，单号为" + billMsgParams.getBillNo();
                }
            }

        }
        if (billMsgParams.getFlag() == 2&&!billMsgParams.getType().equals("开票提醒")) {
            title = billMsgParams.getCreateUserName() + "的@bill@审批";
            switch (billMsgParams.getStatus()) {
                case 0:
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，已提交给【" + billMsgParams.getName() + "】审批中，名称为" + billMsgParams.getBillNo();
                    if (billMsgParams.getIsKingDee() == 1) {
                        if (billMsgParams.getIsCC()) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已提交给【" + billMsgParams.getName() + "】审批中，抄送给您，单号为" + billMsgParams.getBillNo();
                        } else {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已提交给【" + billMsgParams.getName() + "】审批中，单号为" + billMsgParams.getBillNo();
                        }
                    } else {
                        content = billMsgParams.getCreateUserName() + "的@bill@审批，需要审批，名称为" + billMsgParams.getBillNo();
                    }
                    break;
                case 1:
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，名称为" + billMsgParams.getBillNo();
                    if (billMsgParams.getIsKingDee() == 1) {
                        if (billMsgParams.getIsCC()) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，抄送给您，单号为" + billMsgParams.getBillNo();
                        } else {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，单号为" + billMsgParams.getBillNo();
                        }
//                        content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，单号为" + billMsgParams.getBillNo();
                    } else {
                        if (billMsgParams.getIsCC() && billMsgParams.getIsKingDee() == 0) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已同意，抄送给您，名称为" + billMsgParams.getBillNo();
                        }
                    }
                    break;
                case 2:
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已驳回，名称为" + billMsgParams.getBillNo() + "，驳回理由：" + billMsgParams.getReason();
                    if (billMsgParams.getIsKingDee() == 1) {
                        if (billMsgParams.getIsCC()) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已驳回，抄送给您，单号为" + billMsgParams.getBillNo();
                        } else {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已驳回，单号为" + billMsgParams.getBillNo() + "，驳回理由：" + billMsgParams.getReason();
                        }
                    }
                    break;
                case 3:
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已作废，名称为" + billMsgParams.getBillNo();
                    if (billMsgParams.getIsKingDee() == 1) {
                        if (billMsgParams.getIsCC()) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已作废，抄送给您，单号为" + billMsgParams.getBillNo();
                        } else {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，【" + billMsgParams.getName() + "】已作废，单号为" + billMsgParams.getBillNo();
                        }
                    }
                    break;
                case 4:
                    content = billMsgParams.getCreateUserName() + "的@bill@审批，已撤回，名称为" + billMsgParams.getBillNo();
                    if (billMsgParams.getIsKingDee() == 1) {
                        if (billMsgParams.getIsCC()) {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已撤回，抄送给您，单号为" + billMsgParams.getBillNo();
                        } else {
                            content = billMsgParams.getCreateUserName() + "的@bill@审批，已撤回，单号为" + billMsgParams.getBillNo();
                        }
                    }
                    break;
            }
        }

        if (billMsgParams.getType().equals("共享客户申请") || billMsgParams.getType().equals("共享客户申请结果")) {
            title = billMsgParams.getType();
            content = billMsgParams.getContent();
//           /lxr/ContactsDetails/lxrDetails&1806501247283380226&0?deep='lxr'
            url = DocumentType.共享联系人申请.getUrl() + "&" + billMsgParams.getName();
//            systemMessageDTO.setRouteFlag(false);
//            systemMessageDTO.setRoute(url);
            systemMessageDTO.setTargetType(DocumentType.共享联系人申请.getType());
            // 企微
//            systemMessageDTO.setRoute(DocumentType.共享联系人申请.getUrl());
//            systemMessageDTO.setUserList(List.of(billMsgParams.getUserId()));
//            Map<String, Object> wxcpmsgparams = systemMessageDTO.getWxcpmsgparams();
//            wxcpmsgparams.put("type", systemMessageDTO.getTargetType());
//            wxcpmsgparams.put("id", billMsgParams.getName());
        }
        switch (billMsgParams.getType()) {
            case "归档":
                title = title.replace("@bill@", DocumentType.归档.name());
                content = content.replace("@bill@", DocumentType.归档.name());
//				"https://lkszh.shimingbao.cn/#/?type=@billType@&dataId=@dataId@&contractType=@contractType@&contractStatus=@contractStatus@";
                url = DocumentType.归档.getUrl() + "/" + billMsgParams.getContractId() + "&" + billMsgParams.getContractType() + "&" + billMsgParams.getContractStatus();
                systemMessageDTO.setTargetType(DocumentType.归档.getType());
                break;
            case "合同":
                //合同类型  （0销售合同/1采购合同/2其他合同）
                String detail = "";
                switch (billMsgParams.getContractType()) {
                    case "0":
                        detail = "销售";
                        break;
                    case "1":
                        detail = "采购";
                        break;
                    case "2":
                        detail = "其他";
                        break;
                }
                title = title.replace("@bill@", detail + DocumentType.合同.name());
                content = content.replace("@bill@", detail + DocumentType.合同.name());
                url = DocumentType.合同.getUrl() + "/" + billMsgParams.getContractId() + "&" + billMsgParams.getContractType() + "&" + billMsgParams.getContractStatus();
                systemMessageDTO.setTargetType(DocumentType.合同.getType());
                break;
            case "变更":
                //合同类型  （0销售合同/1采购合同/2其他合同）
                String detaiBg = "";
                switch (billMsgParams.getContractType()) {
                    case "0":
                        detaiBg = "销售";
                        break;
                    case "1":
                        detaiBg = "采购";
                        break;
                    case "2":
                        detaiBg = "其他";
                        break;
                }
                title = title.replace("@bill@", detaiBg + "合同" + DocumentType.变更.name());
                content = content.replace("@bill@", detaiBg + "合同" + DocumentType.变更.name());
                url = DocumentType.变更.getUrl() + "/" + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.变更.getType());
                break;
            case "报销单":
                title = title.replace("@bill@", DocumentType.报销单.name());
                content = content.replace("@bill@", DocumentType.报销单.name());
                url = DocumentType.报销单.getUrl() + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.报销单.getType());
                break;
            case "收藏联系人":
                title = title.replace("@bill@", DocumentType.收藏联系人.name());
                content = content.replace("@bill@", DocumentType.收藏联系人.name());
                url = DocumentType.收藏联系人.getUrl();
                systemMessageDTO.setTargetType(DocumentType.收藏联系人.getType());
                break;
            case "开票统计":
                title = title.replace("@bill@", DocumentType.开票统计.name());
                content = content.replace("@bill@", DocumentType.开票统计.name());
                url = DocumentType.开票统计.getUrl();
                systemMessageDTO.setTargetType(DocumentType.开票统计.getType());
                if (billMsgParams.getIsKingDee() == 2) {
                    systemMessageDTO.setTargetType("errorInvoice");
//                    其他消息
                    systemMessageDTO.setStatus(3);
                }
                break;
            case "付款单":
                title = title.replace("@bill@", DocumentType.付款单.name());
                content = content.replace("@bill@", DocumentType.付款单.name());
                url = DocumentType.付款单.getUrl() + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.付款单.getType());
                break;
            case "立项":
                title = title.replace("@bill@", DocumentType.立项.name());
                content = content.replace("@bill@", DocumentType.立项.name());
                url = DocumentType.立项.getUrl();
                systemMessageDTO.setTargetType(DocumentType.立项.getType());
                break;
            case "应收单":
                title = title.replace("@bill@", DocumentType.应收单.name());
                content = content.replace("@bill@", DocumentType.应收单.name());
                url = DocumentType.应收单.getUrl() + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.应收单.getType());
                break;
            case "变更抄送":
                String detail2 = "";
                switch (billMsgParams.getContractType()) {
                    case "0":
                        detail2 = "销售";
                        break;
                    case "1":
                        detail2 = "采购";
                        break;
                    case "2":
                        detail2 = "其他";
                        break;
                }
                title = title.replace("@bill@", detail2 + "合同变更");
                content = billMsgParams.getName() + "的@bill@审批完成，抄送给您，名称为" + billMsgParams.getBillNo();
                content = content.replace("@bill@", detail2 + "合同变更");
                url = DocumentType.变更.getUrl() + "/" + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.变更.getType());
                break;
            case "项目质保流程":
                title = title.replace("@bill@", DocumentType.项目质保流程.name());
                content = content.replace("@bill@", DocumentType.项目质保流程.name());
                url = DocumentType.项目质保流程.getUrl() + "/" + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.项目质保流程.getType());
                break;
            case "项目申请法务流程":
                title = title.replace("@bill@", DocumentType.项目申请法务流程.name());
                content = content.replace("@bill@", DocumentType.项目申请法务流程.name());
                url = DocumentType.项目申请法务流程.getUrl() + "/" + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.项目申请法务流程.getType());
                break;
            case "项目坏账流程":
                title = title.replace("@bill@", DocumentType.项目坏账流程.name());
                content = content.replace("@bill@", DocumentType.项目坏账流程.name());
                url = DocumentType.项目坏账流程.getUrl() + "/" + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.项目坏账流程.getType());
                break;
            case "项目申请法务流程抄送":
                title = title.replace("@bill@", DocumentType.项目申请法务流程.name());
                content = billMsgParams.getName() + "的@bill@审批通过，抄送给您，项目名称为：" + billMsgParams.getBillNo();
                content = content.replace("@bill@", DocumentType.项目申请法务流程.name());
                url = DocumentType.项目申请法务流程.getUrl() + "/" + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.项目申请法务流程.getType());
                break;
            case "渠道商":
                title = title.replace("@bill@", DocumentType.渠道商.name());
                content = content.replace("@bill@", DocumentType.渠道商.name());
                url = DocumentType.渠道商.getUrl() + billMsgParams.getContractId();
                systemMessageDTO.setTargetType(DocumentType.渠道商.getType());
                break;
            case "渠道合同归档":
                title = title.replace("@bill@", DocumentType.渠道合同归档.name());
                content = content.replace("@bill@", DocumentType.渠道合同归档.name());
//				"https://lkszh.shimingbao.cn/#/?type=@billType@&dataId=@dataId@&contractType=@contractType@&contractStatus=@contractStatus@";
                url = DocumentType.渠道合同归档.getUrl() + "/" + billMsgParams.getContractId() + "&" + billMsgParams.getContractType() + "&" + billMsgParams.getContractStatus();
                systemMessageDTO.setTargetType(DocumentType.渠道合同归档.getType());
                break;
            case "渠道合同":
                title = title.replace("@bill@", DocumentType.渠道合同.name());
                content = content.replace("@bill@", DocumentType.渠道合同.name());
                url = DocumentType.渠道合同.getUrl() + "/" + billMsgParams.getContractId() + "&" + billMsgParams.getContractType() + "&" + billMsgParams.getContractStatus();
                systemMessageDTO.setTargetType(DocumentType.渠道合同.getType());
                break;
            case "渠道合同变更":
                title = title.replace("@bill@", DocumentType.渠道合同变更.name());
                content = content.replace("@bill@", DocumentType.渠道合同变更.name());
                url = DocumentType.渠道合同变更.getUrl() + "/" + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.渠道合同变更.getType());
                break;
            case "挂账应付单":
                title = title.replace("@bill@", DocumentType.挂账应付单.name());
                content = content.replace("@bill@", DocumentType.挂账应付单.name());
                url = DocumentType.挂账应付单.getUrl() + "/" + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.挂账应付单.getType());
                break;
            case "回款单":
                title = title.replace("@bill@", DocumentType.回款单.name());
                content = content.replace("@bill@", DocumentType.回款单.name());
                url = DocumentType.回款单.getUrl() + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.回款单.getType());
                break;
            case "挂账提醒":
                title = "项目及时挂账提醒";
                content = billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.挂账应付单.getType());
                break;
            case "采购申请单":
                title = title.replace("@bill@", DocumentType.采购申请单.name());
                content = content.replace("@bill@", DocumentType.采购申请单.name());
                url = DocumentType.采购申请单.getUrl() + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.采购申请单.getType());
                break;
            case "出库单":
                title = title.replace("@bill@", DocumentType.出库单.name());
                content = content.replace("@bill@", DocumentType.出库单.name());
                url = DocumentType.出库单.getUrl() + billMsgParams.getBillNo();
                systemMessageDTO.setTargetType(DocumentType.出库单.getType());
                break;
            case "开票单":
                title = title.replace("@bill@", DocumentType.开票单.name());
                content = content.replace("@bill@", DocumentType.开票单.name());
                url = DocumentType.开票单.getUrl() + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.开票单.getType());
                break;
            case "印章申请":
                title = title.replace("@bill@", DocumentType.印章申请.name());
                content = content.replace("@bill@", DocumentType.印章申请.name());
                url = DocumentType.印章申请.getUrl() + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.印章申请.getType());
                break;
            case "任务交付":
                title = title.replace("@bill@", DocumentType.任务交付.name());
                content = content.replace("@bill@", DocumentType.任务交付.name());
                url = DocumentType.任务交付.getUrl() + billMsgParams.getPId();
                systemMessageDTO.setTargetType(DocumentType.任务交付.getType());
                break;
            default:
                break;
        }

        systemMessageDTO.setMsgContent(content);
        systemMessageDTO.setMsgTitle(title);
        if (billMsgParams.getAccount() != null && !billMsgParams.getAccount().isBlank() && !billMsgParams.getAccount().isEmpty()) {
            User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, billMsgParams.getAccount());
            systemMessageDTO.setCrowdRelevance(String.valueOf(user.getId()));
            systemMessageDTO.setCrowdRelevanceName(user.getName());
        } else {
            systemMessageDTO.setCrowdRelevance(billMsgParams.getUserId());
            systemMessageDTO.setCrowdRelevanceName(billMsgParams.getUserName());
        }
        if (billMsgParams.getType().equals("项目质保流程")
                || billMsgParams.getType().equals("项目申请法务流程")
                || billMsgParams.getType().equals("项目坏账流程")
                || billMsgParams.getType().equals("项目申请法务流程抄送")) {
            systemMessageDTO.setTargetId(billMsgParams.getContractId());
        } else if (billMsgParams.getType().equals("共享客户申请") ||
                billMsgParams.getType().equals("共享客户申请结果")) {
            systemMessageDTO.setTargetId(billMsgParams.getBillNo());
        } else {
            systemMessageDTO.setTargetId(billMsgParams.getPId());
        }
        systemMessageDTO.setPinnedStatus(1);
        if (billMsgParams.getIsKingDee() != 2) {
            systemMessageDTO.setStatus(0);
        }
        systemMessageDTO.setSkipUrl(url);
        return systemMessageService.sendSystemMessage(systemMessageDTO);

    }


    @PostMapping("/tipoCommento")
    public R tipoCommento(@RequestBody BillMsgParams billMsgParams) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
//        用于开票统计存储的备用正式列表头
        String result = "";
        String title = "";
//        "xxx点赞了/评论了我的拜访记录，2023-05-08 10:52:55"
//        @userName@：XXX人名
//        @operate@：操作（点赞/评论）
//        final String visit = "@userName@@operate@了我的拜访记录\n" + sdf.format(new Date());
        final String visit = "@userName@@operate@了我的@name@";
//        xxx点赞了/回复了我的评论，2023-05-08 10:52:55
        final String comment = "@userName@@operate@了我的评论";
//flag:0 拜访, 1 评论
        if (billMsgParams.getFlag() == 0) {
            result = visit.replace("@userName@", billMsgParams.getName());
            result = result.replace("@operate@", billMsgParams.getContractType());
        } else {
            result = comment.replace("@userName@", billMsgParams.getName());
            result = result.replace("@operate@", billMsgParams.getContractType());
        }

        if (billMsgParams.getType().equals("bf")) {
            result = result.replace("@name@", "拜访记录");
            systemMessageDTO.setSkipUrl(DocumentType.拜访看板.getUrl() + billMsgParams.getContractId());
            systemMessageDTO.setTargetType(DocumentType.拜访看板.getType());
            title = "客户拜访";
        }
        if (billMsgParams.getType().equals("rz")) {
            result = result.replace("@name@", "日志");
            systemMessageDTO.setTargetType(DocumentType.日志管理.getType());
            systemMessageDTO.setSkipUrl(DocumentType.日志管理.getUrl() + billMsgParams.getContractId());
            title = "日志管理";
        }
        if (billMsgParams.getType().equals("zb")) {
            result = result.replace("@name@", "周报");
            systemMessageDTO.setTargetType(DocumentType.日志管理.getType());
            systemMessageDTO.setSkipUrl("/khbf/customerLogAddWeek?id=" + billMsgParams.getContractId() + "&type=detail");
            title = "日志管理";
        }


        systemMessageDTO.setMsgContent(result);
        systemMessageDTO.setMsgTitle(title);
        systemMessageDTO.setCrowdRelevance(billMsgParams.getUserId());
        systemMessageDTO.setCrowdRelevanceName(billMsgParams.getUserName());
        systemMessageDTO.setTargetId(billMsgParams.getContractId());
        systemMessageDTO.setPinnedStatus(1);
        systemMessageDTO.setStatus(0);
        return systemMessageService.sendSystemMessage(systemMessageDTO);

    }
}
