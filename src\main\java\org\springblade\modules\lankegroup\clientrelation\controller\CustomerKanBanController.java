package org.springblade.modules.lankegroup.clientrelation.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.CustomerKanBanDTO;
import org.springblade.modules.lankegroup.clientrelation.service.CustomerKanBanService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户看板
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("customerKanBan")
public class CustomerKanBanController {

    private final CustomerKanBanService customerKanBanService;

    /**
     * 客户看板-客户数量统计
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerCount")
    public R getCustomerCount(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerCount(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户数量统计------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户行业分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerProfession")
    public R getCustomerProfession(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerProfession(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户行业分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户行业分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerProfessionPieChart")
    public R getCustomerProfessionPieChart(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerProfessionPieChart(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户行业分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户行业分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerProfessionListDetail")
    public R getCustomerProfessionListDetail(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerProfessionListDetail(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户行业分析-列表详情------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户区域分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerRegion")
    public R getCustomerRegion(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerRegion(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户区域分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户区域分析-列表
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerRegionList")
    public R getCustomerRegionList(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerRegionList(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户区域分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户区域分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerRegionListDetail")
    public R getCustomerRegionListDetail(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerRegionListDetail(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户区域分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-客户区域分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerRegionPieChart")
    public R getCustomerRegionPieChart(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerRegionPieChart(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户区域分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-行业产值
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerProfessionValue")
    public R getCustomerProfessionValue(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerProfessionValue(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-行业产值------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-行业产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerProfessionValueGroup")
    public R getCustomerProfessionValueGroup(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerProfessionValueGroup(customerKanBanDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("客户看板-行业产值------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户分析-客户产值
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerValue")
    public R getCustomerValue(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerValue(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户产值------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户分析-客户产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerValueGroup")
    public R getCustomerValueGroup(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerValueGroup(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-客户产值------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-重要程度分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerImportance")
    public R getCustomerImportance(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return customerKanBanService.getCustomerImportance(customerKanBanDTO);
        } catch (Exception e) {
            log.error("客户看板-重要程度分析------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户看板-重要程度客户列表
     *
     * @param customerKanBanDTO
     * @return
     */
    @PostMapping("getCustomerImportancePage")
    public R getCustomerImportancePage(@RequestBody CustomerKanBanDTO customerKanBanDTO) {
        try {
            return R.data(customerKanBanService.getCustomerImportancePage(customerKanBanDTO));
        } catch (Exception e) {
            log.error("客户看板-重要程度列表------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }
}
