package org.springblade.modules.lankegroup.contractManagement.constant;

/**
 * 销售合同导出word 常量
 *
 * <AUTHOR> Liu
 * @since 2024年03月04日 9:06
 **/
public class ContractToWordConstant {

    /**
     * 文件后缀
     */
    public static final String FILE_SUFFIX = ".docx";

    /**
     * 模板文件后缀
     */
    public static final String TEMPLATE_FILE_SUFFIX = "_java.docx";

    /**
     * 复选框-勾选状态
     */
    public static final String CHECK_BOX_CHANGE = "☑";
    /**
     * 复选框-非勾选状态
     */
    public static final String CHECK_BOX_NO_CHANGE = "□";
    /**
     * 字段注解属性类型-FIELD
     */
    public static final String FIELD = "FIELD";
    /**
     * 字段注解属性类型-SPLIT
     */
    public static final String SPLIT = "SPLIT";
    /**
     * 字段注解属性类型-ENUMS
     */
    public static final String ENUMS = "ENUMS";
    /**
     * 字段注解属性类型-BOOK
     */
    public static final String BOOK = "BOOK";
//    /**
//     * 字段属性-前缀
//     */
//    public static final String FIELD_PREFIX = "xt_";
    /**
     * 书名号-左侧
     */
    public static final String BOOK_TITLE_LEFT = "《";
    /**
     * 书名号-右侧
     */
    public static final String BOOK_TITLE_RIGHT = "》";
//	/**
//	 * 名称标签
//	 */
//	public static final String BOOK_LABEL_DWMC = "{{dwmc}}";
//	/**
//	 * 名称标签
//	 */
//	public static final String BOOK_LABEL_XTMC = "{{xtmc}}";

}
