package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.SelectDeptOrUserUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractStatisticsDTO;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractStatisticsMapper;
import org.springblade.modules.lankegroup.contractManagement.service.ContractStatisticsService;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractChangeService;
import org.springblade.modules.lankegroup.contractManagement.vo.*;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.lankegroup.pro_management.service.IProjectBasicService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class ContractStatisticsServiceImpl extends BaseServiceImpl<ContractStatisticsMapper, ContractStatisticsVO> implements ContractStatisticsService {

    private final DeptByUserService deptByUserService;

    private final ContractMapper contractMapper;

    private final IContractChangeService contractChangeService;

    private final IArchiveDetailsService archiveDetailsService;

    private final IUserService userService;

    private final IProjectBasicService projectBasicService;
    private final SelectDeptOrUserUtil selectDeptOrUserUtil;

    @Override
    public R getContractStatus(ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        List<ContractStatisticsGroupVO> contractStatus = baseMapper.getContractStatus(contractStatisticsDTO);
        List<ContractStatisticsGroupVO> list = new ArrayList<>();
        if (null != contractStatus && !contractStatus.isEmpty()) {
            // 定义分组类型
            Map<String, String> statusMapping = Map.of(
                    "0", "审批中",
                    "1", "待归档",
                    "5", "已归档",
                    "9", "已作废"
            );
            Map<String, ContractStatisticsGroupVO> groupedMap = contractStatus.stream()
                    .collect(Collectors.toMap(
                            contractStat -> statusMapping.getOrDefault(contractStat.getStatusId(), "未分组"),
                            contractStat -> {
                                ContractStatisticsGroupVO groupVO = new ContractStatisticsGroupVO();
                                groupVO.setStatusId(contractStat.getStatusId());
                                groupVO.setStatusCount(contractStat.getStatusCount());
                                groupVO.setStatusTotal(contractStat.getStatusTotal());
                                return groupVO;
                            },
                            (existing, replacement) -> {
                                existing.accumulateStatusTotal(replacement.getStatusTotal());
                                existing.setStatusCount(existing.getStatusCount() + replacement.getStatusCount());
                                return existing;
                            }
                    ));
            list = new ArrayList<>(groupedMap.values());
            list.forEach(obj -> {
                String statusName = statusMapping.get(obj.getStatusId());
                if (statusName != null) {
                    obj.setStatusName(statusName);
                }
            });
        }
        return R.data(list);
    }

    @Override
    public R getContractStatistics(ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        return R.data(baseMapper.getContractStatistics(contractStatisticsDTO));
    }

    @Override
    public R getContractStatisticsByUser(ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        List<ContractStatisticsVO> contractStatisticsByUser = new ArrayList<>();
        if (null != contractStatisticsDTO.getContractName() && !"".equals(contractStatisticsDTO.getContractName())) {
            return R.data(contractStatisticsByUser);
        }
        contractStatisticsByUser = baseMapper.getContractStatisticsByUser(contractStatisticsDTO);
        List<List<ContractStatisticsVO>> groupedAndSortedResult = new ArrayList<>();
        if (null != contractStatisticsByUser && contractStatisticsByUser.size() > 0) {
            Map<String, List<ContractStatisticsVO>> groupedMap = contractStatisticsByUser.stream()
                    .collect(Collectors.groupingBy(ContractStatisticsVO::getDeptId));
            groupedAndSortedResult = groupedMap.values().stream()
                    // 对每个子列表按sort字段进行降序排序（null值排在最后）
                    .map(subList -> {
                        BigDecimal totalConTotal = subList.stream()
                                .map(ContractStatisticsVO::getConTotalAsBigDecimal)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        int totalConCount = subList.stream()
                                .mapToInt(ContractStatisticsVO::getConCount)
                                .sum();
                        ContractStatisticsVO totalObject = new ContractStatisticsVO();
                        if (null != subList && subList.size() > 0) {
                            totalObject.setDeptId(subList.get(0).getDeptId());
                        }
                        totalObject.setUserName("合计");
                        totalObject.setConCount(totalConCount);
                        totalObject.setConTotal(totalConTotal.setScale(2, RoundingMode.HALF_UP).toString());
                        // 将合计对象添加到子列表末尾
                        subList.add(totalObject);
                        return subList;
                    })
                    .collect(Collectors.toList());
            return R.data(groupedAndSortedResult);
        }
        return R.data(groupedAndSortedResult);
    }

    @Override
    public R getContractStatisticsType(ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        // 判断是否是查询待归档数据
        if (2 != contractStatisticsDTO.getSelectContractType()) {
            if (null == contractStatisticsDTO.getContractStatus() || contractStatisticsDTO.getContractStatus().isEmpty() || !"1".equals(contractStatisticsDTO.getContractStatus().get(0))) {
                contractStatisticsDTO.setHaveBeenFiled(0);
            }
        } else if (null != contractStatisticsDTO.getHaveBeenFiled()) {
            contractStatisticsDTO.setContractStatus(List.of("1"));
        }
        return R.data(baseMapper.getContractStatisticsType(contractStatisticsDTO));
    }

    @Override
    public R getContractStatisticsList(IPage<ContractStatisticsListVO> page, ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        List<ContractStatisticsListVO> contractStatisticsList = baseMapper.getContractStatisticsList(page, contractStatisticsDTO);
        contractStatisticsList.stream().forEach(obj -> {
            Map map = new HashMap();
            map.put("contractId", obj.getConId());
//            是否有变更
            Integer isChangeCount = contractChangeService.countChange(map);
            if (isChangeCount > 0) {
                obj.setIsChanges(0);
                map.put("status", 1);
                map.put("isFlag", "void");
                Integer voidCount = contractChangeService.countChange(map);
                if (voidCount > 0) {
                    obj.setIsChanges(2);
                }
            } else {
                obj.setIsChanges(1);
            }

            Map m = new HashMap();
            m.put("allowArchivingCondition", "true");
            m.put("contractId", obj.getConId());
            Integer allowArchivingCount = contractChangeService.countChange(m);
            if (allowArchivingCount > 0) {
                obj.setAllowArchiving(true);
            }
            ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(obj.getConId());
            Contract contract = contractMapper.selectById(obj.getConId());
            if (archiveDetails != null) {
                obj.setIsOwner(true);
            } else {
                if (contract.getCreateUser().longValue() == AuthUtil.getUserId().longValue()) {
                    obj.setIsOwner(true);
                }
            }
        });
        return R.data(page.setRecords(contractStatisticsList));
    }

    @Override
    public R getContractStatisticsStatusList(IPage<ContractStatisticsListVO> page, ContractStatisticsDTO contractStatisticsDTO) {
        getParamData(contractStatisticsDTO);
        if (null != contractStatisticsDTO.getContractStatus() && !contractStatisticsDTO.getContractStatus().isEmpty() &&
                "5".equals(contractStatisticsDTO.getContractStatus().get(0)) &&
                (null != contractStatisticsDTO.getHaveBeenFiled() && (contractStatisticsDTO.getHaveBeenFiled() == 7 ||
                contractStatisticsDTO.getHaveBeenFiled() == 14 ||
                contractStatisticsDTO.getHaveBeenFiled() == 30))) {
            return R.data(page);
        }
        if (2 != contractStatisticsDTO.getSelectContractType()) {
            if (null == contractStatisticsDTO.getContractStatus() || contractStatisticsDTO.getContractStatus().isEmpty() || !"1".equals(contractStatisticsDTO.getContractStatus().get(0))) {
                contractStatisticsDTO.setHaveBeenFiled(0);
            }
        } else if (null != contractStatisticsDTO.getHaveBeenFiled()) {
            contractStatisticsDTO.setContractStatus(Arrays.asList("1"));
        }
        List<ContractStatisticsListVO> contractStatisticsStatusList = baseMapper.getContractStatisticsStatusList(page, contractStatisticsDTO);
        contractStatisticsStatusList.stream().forEach(obj -> {
            Map map = new HashMap();
            map.put("contractId", obj.getConId());
//            是否有变更
            Integer isChangeCount = contractChangeService.countChange(map);
            if (isChangeCount > 0) {
                obj.setIsChanges(0);
                map.put("status", 1);
                map.put("isFlag", "void");
                Integer voidCount = contractChangeService.countChange(map);
                if (voidCount > 0) {
                    obj.setIsChanges(2);
                }
            } else {
                obj.setIsChanges(1);
            }

            Map m = new HashMap();
            m.put("allowArchivingCondition", "true");
            m.put("contractId", obj.getConId());
            Integer allowArchivingCount = contractChangeService.countChange(m);
            if (allowArchivingCount > 0) {
                obj.setAllowArchiving(true);
            }
            ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(obj.getConId());
            Contract contract = contractMapper.selectById(obj.getConId());
            if (archiveDetails != null) {
                obj.setIsOwner(true);
            } else {
                if (contract.getCreateUser().longValue() == AuthUtil.getUserId().longValue()) {
                    obj.setIsOwner(true);
                }
            }
        });
        return R.data(page.setRecords(contractStatisticsStatusList));
    }

    @Override
    public R getContractStatisticsDetail(Long id) {
        Map<String, Object> result = new HashMap<>();
        // 查询合同详情
        ContractStatisticsDetailVO contractStatisticsDetail = baseMapper.getContractStatisticsDetail(id);
        result.put("contract", contractStatisticsDetail);
        // 查询归档详情
        ContractStatisticsArchiveDetailVO contractStatisticsArchiveDetail = baseMapper.getContractStatisticsArchiveDetail(id);
        result.put("archive", contractStatisticsArchiveDetail);
        return R.data(result);
    }

    /**
     * 归档统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    @Override
    public R getContractHaveBeenFiled(ContractStatisticsDTO contractStatisticsDTO) {
        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<Future<Integer>> futures = new ArrayList<>();
        Map<String, Integer> result = new HashMap<>();
        result.put("sevenDays", 0);
        result.put("fourteenDays", 0);
        result.put("thirtyDays", 0);
        if (2 != contractStatisticsDTO.getSelectContractType()) {
            if (null == contractStatisticsDTO.getContractStatus() || contractStatisticsDTO.getContractStatus().isEmpty()) {
                return R.data(result);
            }
            if (!"1".equals(contractStatisticsDTO.getContractStatus().get(0))) {
                return R.data(result);
            }
        } else {
            contractStatisticsDTO.setContractStatus(List.of("1"));
        }
        ContractStatisticsDTO paramData = getParamData(contractStatisticsDTO);
        ContractStatisticsDTO seven = new ContractStatisticsDTO();
        BeanUtils.copyProperties(paramData, seven);
        seven.setHaveBeenFiled(7);
        ContractStatisticsDTO fourteen = new ContractStatisticsDTO();
        BeanUtils.copyProperties(paramData, fourteen);
        fourteen.setHaveBeenFiled(14);
        ContractStatisticsDTO thirty = new ContractStatisticsDTO();
        BeanUtils.copyProperties(paramData, thirty);
        thirty.setHaveBeenFiled(30);
        futures.add((Future<Integer>) executor.submit(() -> {
            contractStatisticsDTO.setHaveBeenFiled(7);
            result.put("sevenDays", baseMapper.getContractHaveBeenFiled(seven));
        }));
        futures.add((Future<Integer>) executor.submit(() -> {
            contractStatisticsDTO.setHaveBeenFiled(14);
            result.put("fourteenDays", baseMapper.getContractHaveBeenFiled(fourteen));
        }));
        futures.add((Future<Integer>) executor.submit(() -> {
            contractStatisticsDTO.setHaveBeenFiled(30);
            result.put("thirtyDays", baseMapper.getContractHaveBeenFiled(thirty));
        }));
        List<Integer> results = new ArrayList<>();
        try {
            // 循环阻塞且判断结果是否完整
            for (Future<Integer> future : futures) {
                results.add(future.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("查询异常");
        }
        executor.shutdown();
        return R.data(result);
    }

    /**
     * 组装参数及权限
     *
     * @param contractStatisticsDTO
     * @return
     */
    private ContractStatisticsDTO getParamData(ContractStatisticsDTO contractStatisticsDTO) {
        // 判断是否有项目名称输入
        if (null != contractStatisticsDTO.getContractName() && !"".equals(contractStatisticsDTO.getContractName())) {
            List<String> contractNameList = Arrays.stream(contractStatisticsDTO.getContractName().
                    split("\\s+")).flatMap(word -> Arrays.stream(word.split("(?<=[a-z])(?=[A-Z])")))
                    .collect(Collectors.toList());
            contractStatisticsDTO.setContractNameList(contractNameList);
        }
        //要查看并可查看的人员
        contractStatisticsDTO.setSelectUserIdList(selectDeptOrUserUtil.selectDeptOrUser(contractStatisticsDTO.getSelectDeptId(),contractStatisticsDTO.getSelectUserId()));
        //合同状态，兼容未审批
        if (contractStatisticsDTO.getContractStatus()!= null && contractStatisticsDTO.getContractStatus().size()>0){
            if (contractStatisticsDTO.getContractStatus().contains("0")){
                contractStatisticsDTO.getContractStatus().add("-1");
            }
        }
        return contractStatisticsDTO;
    }
}
