//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class CommonConfig {
    boolean ResponseUnzip;
    String CustomLoginUI;

    public CommonConfig() {
    }

    public boolean getResponseUnzip() {
        return this.ResponseUnzip;
    }

    public void setResponseUnzip(boolean responseUnzip) {
        this.ResponseUnzip = responseUnzip;
    }

    public String getCustomLoginUI() {
        return this.CustomLoginUI;
    }

    public void setCustomLoginUI(String customLoginUI) {
        this.CustomLoginUI = customLoginUI;
    }
}
