<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractTemplateResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ContractTemplate">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="template_description" property="templateDescription"/>
        <result column="file_path" property="filePath"/>
        <result column="file_name" property="fileName"/>
    </resultMap>


    <select id="selectContractTemplatePage"
            resultType="org.springblade.modules.lankegroup.contractManagement.vo.ContractTemplateVO">
        select * from cm_contract_template where
        is_deleted = 0
        <if test="query.name!=null and query.name!=''">
            and name LIKE concat('%',#{query.name},'%')
        </if>
        <if test="query.type!=null">
            and type = #{query.type}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getFilePathByType" resultType="java.lang.String">
        select file_path from cm_contract_template where is_deleted = 0 and type = #{fileType} limit 1
    </select>

</mapper>
