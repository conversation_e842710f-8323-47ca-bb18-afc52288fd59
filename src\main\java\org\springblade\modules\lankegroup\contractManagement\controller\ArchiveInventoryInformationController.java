/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveInventoryInformation;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveInventoryInformationVO;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveInventoryInformationService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 合同归档 --清单信息 控制器
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/Information/archiveinventoryinformation")
@Api(value = "合同归档 --清单信息", tags = "合同归档 --清单信息接口")
public class ArchiveInventoryInformationController extends BladeController {

	private final IArchiveInventoryInformationService archiveInventoryInformationService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入archiveInventoryInformation")
	public R<ArchiveInventoryInformation> detail(ArchiveInventoryInformation archiveInventoryInformation) {
		ArchiveInventoryInformation detail = archiveInventoryInformationService.getOne(Condition.getQueryWrapper(archiveInventoryInformation));
		return R.data(detail);
	}

	/**
	 * 分页 合同归档 --清单信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入archiveInventoryInformation")
	public R<IPage<ArchiveInventoryInformation>> list(ArchiveInventoryInformation archiveInventoryInformation, Query query) {
		IPage<ArchiveInventoryInformation> pages = archiveInventoryInformationService.page(Condition.getPage(query), Condition.getQueryWrapper(archiveInventoryInformation));
		return R.data(pages);
	}

	/**
	 * 自定义分页 合同归档 --清单信息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入archiveInventoryInformation")
	public R<IPage<ArchiveInventoryInformationVO>> page(ArchiveInventoryInformationVO archiveInventoryInformation, Query query) {
		IPage<ArchiveInventoryInformationVO> pages = archiveInventoryInformationService.selectArchiveInventoryInformationPage(Condition.getPage(query), archiveInventoryInformation);
		return R.data(pages);
	}

	/**
	 * 新增 合同归档 --清单信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入archiveInventoryInformation")
	public R save(@Valid @RequestBody ArchiveInventoryInformation archiveInventoryInformation) {
		return R.status(archiveInventoryInformationService.save(archiveInventoryInformation));
	}

	/**
	 * 修改 合同归档 --清单信息
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入archiveInventoryInformation")
	public R update(@Valid @RequestBody ArchiveInventoryInformation archiveInventoryInformation) {
		return R.status(archiveInventoryInformationService.updateById(archiveInventoryInformation));
	}

	/**
	 * 新增或修改 合同归档 --清单信息
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入archiveInventoryInformation")
	public R submit(@Valid @RequestBody ArchiveInventoryInformation archiveInventoryInformation) {
		return R.status(archiveInventoryInformationService.saveOrUpdate(archiveInventoryInformation));
	}

	
	/**
	 * 删除 合同归档 --清单信息
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(archiveInventoryInformationService.deleteLogic(Func.toLongList(ids)));
	}

	
}
