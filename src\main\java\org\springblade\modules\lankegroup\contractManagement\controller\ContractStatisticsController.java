package org.springblade.modules.lankegroup.contractManagement.controller;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractStatisticsDTO;
import org.springblade.modules.lankegroup.contractManagement.service.ContractStatisticsService;
import org.springframework.web.bind.annotation.*;

/**
 * 合同统计
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("contractStatistics")
public class ContractStatisticsController {

    private final ContractStatisticsService contractStatisticsService;

    /**
     * 状态统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    @PostMapping("getContractStatus")
    public R getContractStatus(@RequestBody ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatus(contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 情况统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    @GetMapping("getContractStatistics")
    public R getContractStatistics(ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatistics(contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 人员统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    @PostMapping("getContractStatisticsByUser")
    public R getContractStatisticsByUser(@RequestBody ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatisticsByUser(contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 合同类型统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    @PostMapping("getContractStatisticsType")
    public R getContractStatisticsType(@RequestBody ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatisticsType(contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 履行情况列表（合同看板，点击人员进入的合同列表接口）
     *
     * @param contractStatisticsDTO
     * @return
     */
    @PostMapping("getContractStatisticsList")
    public R getContractStatisticsList(@RequestBody Query query, @RequestBody ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatisticsList(Condition.getPage(query), contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 状态统计列表（合同看板，点击合同状态统计和合同履行情况统计的合同列表展示）
     *
     * @param contractStatisticsDTO
     * @return
     */
    @PostMapping("getContractStatisticsStatusList")
    public R getContractStatisticsStatusList(@RequestBody ContractStatisticsDTO contractStatisticsDTO) throws InterruptedException {
        try {
            Query query = new Query();
            query.setCurrent(contractStatisticsDTO.getCurrent());
            query.setSize(contractStatisticsDTO.getSize());
            return contractStatisticsService.getContractStatisticsStatusList(Condition.getPage(query), contractStatisticsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 履行情况详情(20240713经王浩然确定这个接口前端没有用到)
     *
     * @param id
     * @return
     */
    @GetMapping("getContractStatisticsDetail")
    public R getContractStatisticsDetail(Long id) throws InterruptedException {
        try {
            return contractStatisticsService.getContractStatisticsDetail(id);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(2000);
            return R.fail("服务繁忙，请稍后重试");
        }
    }

    /**
     * 归档统计
     *
     * @return
     */
    @PostMapping("getContractHaveBeenFiled")
    public R getContractHaveBeenFiled(@RequestBody ContractStatisticsDTO contractStatisticsDTO) {
        try {
            return contractStatisticsService.getContractHaveBeenFiled(contractStatisticsDTO);
        }catch (Exception e){
            e.printStackTrace();
            return R.fail("归档统计异常，请联系相关人员");
        }
    }
}
