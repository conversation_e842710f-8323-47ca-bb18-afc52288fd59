/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 通用附件数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "AttachmentDTO对象", description = "通用附件数据传输对象")
@EqualsAndHashCode(callSuper = false)
public class AttachmentDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id", required = true)
	@NotNull(message = "主键id不能为空")
	private Long id;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;
	/**
	 * 业务ID
	 */
	@ApiModelProperty(value = "业务ID")
	private Long businessId;
	/**
	 * 子业务类型
	 */
	@ApiModelProperty(value = "子业务类型")
	private String subBusinessType;
	/**
	 * 附件名称
	 */
	@ApiModelProperty(value = "附件名称")
	private String fileName;
	/**
	 * 附件地址
	 */
	@ApiModelProperty(value = "附件地址")
	private String fileUrl;
	/**
	 * 附件类型
	 */
	@ApiModelProperty(value = "附件类型")
	private String fileType;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

} 