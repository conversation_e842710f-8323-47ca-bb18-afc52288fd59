package org.springblade.common.enums;

import org.springblade.core.tool.utils.Func;

public enum BladeFormId {
    客户拜访("customer_visit"),
    日志管理("blade_complete_plan_log"),
    消息管理("blade_system_message"),
    知识库产品("blade_knowledge_product"),
    项目机会表("blade_project_opportunity"),
    项目表("blade_project_basic"),
    // 自定义字符串 非表名
    关键点("pm_business_opportunity_keypoints"),
    // 自定义字符串 非表名
    沟通("pm_business_opportunity_communicate"),
    // 自定义字符串 非表名
    转化建议("pm_business_opportunity_transformation_suggest"),

    // 报价沟通
    报价沟通("pm_business_quotation_communicate"),
    ;


    private String formId;

    public String getFormId() {
        return formId;
    }

    BladeFormId(String formId) {
        this.formId = formId;
    }

    public static String getEnumNameByFormId(String formId) {
        if (Func.isNotBlank(formId)) {
            BladeFormId[] values = BladeFormId.values();
            for (BladeFormId value : values) {
                if (value.getFormId().equals(formId)) {
                    return value.name();
                }
            }
        }
        return null;
    }
}
