package org.springblade.common.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 按钮权限详情VO
 *
 * <AUTHOR> <PERSON>
 * @since 2025年07月01日 16:00
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonPermissionDetailVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否可见
     */
    private Boolean visible;
    
    /**
     * 是否可用
     */
    private Boolean enabled;
    
    /**
     * 提示信息
     */
    private String tooltip;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extraProperties;
} 