/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.entity.CustomerCare;
import org.springblade.modules.lankegroup.crm.entity.CustomerCareMaterial;
import org.springblade.modules.lankegroup.crm.service.ICustomerCareMaterialService;
import org.springblade.modules.lankegroup.crm.service.ICustomerCareService;
import org.springblade.modules.lankegroup.crm.vo.CustomerCareVO;
import org.springblade.modules.lankegroup.crm.wrapper.CustomerCareWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户关怀表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/care/customercare")
@Api(value = "客户关怀表", tags = "客户关怀表接口")
public class CustomerCareController extends BladeController {

    private final ICustomerCareService customerCareService;

    private final ICustomerCareMaterialService customerCareMaterialService;

    /**
     * 新增 客户关怀表
     */
    @ApiLog("新增 客户关怀表")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入customerCare")
    public R save(@Valid @RequestBody CustomerCareVO customerCare) {
        return R.status(customerCareService.saveAfterVerification(customerCare));
    }

    /**
     * 新增前的数据校验
     * 0都可保存
     * 1部分可保存
     * 2都不可保存
     *
     * @param customerCare
     * @return
     */
    @ApiLog("新增前的数据校验")
    @PostMapping("/checkMan")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "新增前重复数据校验", notes = "传入customerId")
    public R msgCustomer(@RequestBody CustomerCareVO customerCare) {
        return R.data(customerCareService.checkMan(customerCare));

    }

    /**
     * 通过客户关怀 id查看关怀详情
     *
     * @return
     */
    @ApiLog("关怀详情")
    @GetMapping("/detailById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入customerId")
    public R<CustomerCareVO> detailById(@RequestParam Long id) {
        return R.data(customerCareService.selectCustomerCareDetail(id));
    }

    /**
     * 客户关怀列表
     * * current：当前页数
     * size：每页数量
     * executorName:搜索框（执行人、客户联系人、客户）
     * isMyself：是否是本人
     * iscared：是否已关怀
     * createTime:年份
     * carecontent：关怀内容
     * carelevel：关怀级别
     * careway：关怀方式
     * customerContact:客户联系人id（联系人详情使用）
     */
    @ApiLog("关怀列表")
    @PostMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入customerCare")
    public R<IPage<CustomerCareVO>> page(@RequestBody CustomerCareVO customerCare,@RequestBody Query query) {
        IPage<CustomerCareVO> pages = customerCareService.selectCustomerCarePage(Condition.getPage(query), customerCare);
        return R.data(pages);
    }

    /**
     * 更新客户是否关怀状态
     *
     * @param caredStatus 关怀状态
     * @param billId      客户关怀表id
     * @return
     */
    @ApiLog("更新客户是否关怀状态")
    @PostMapping("/caredStatus")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表是否已关怀按钮", notes = "")
    public R caredStatus(@RequestParam Integer caredStatus, @RequestParam Long billId) {
        return R.status(customerCareService.updateCaredStatus(billId, caredStatus));

    }

    /**
     * 修改 客户关怀表
     */
    @ApiLog("修改 客户关怀表")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入customerCare")
    public R update(@Valid @RequestBody CustomerCareVO customerCare) {
        customerCare.setIscared(1);
        boolean b = customerCareService.updateById(customerCare);
        Boolean save = false;
        if (b) {
            customerCareMaterialService.remove(customerCare.getId());
            if (customerCare.getCustomerMaterialList() != null && customerCare.getCustomerMaterialList().size() > 0) {
                CustomerCareMaterial customerCareMaterial=new CustomerCareMaterial();
                customerCareMaterial.setBillId(customerCare.getId());
                for (int i = 0; i < customerCare.getCustomerMaterialList().size(); i++) {
                    customerCareMaterial.setId(null);
                    customerCareMaterial.setMaterialId(customerCare.getCustomerMaterialList().get(i).getId());
                    customerCareMaterial.setCount(customerCare.getCustomerMaterialList().get(i).getCount());
//                    customerCare.getCustomerMaterialList().get(i).setBillId(customerCare.getId());
                    save = customerCareMaterialService.save(customerCareMaterial);
                }
            }else{
                save=true;
            }
        }
        return R.status(save);
    }

    /**
     * 详情
     */
    @ApiLog("详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入customerCare")
    public R<CustomerCareVO> detail(CustomerCare customerCare) {
        CustomerCare detail = customerCareService.getOne(Condition.getQueryWrapper(customerCare));
        return R.data(CustomerCareWrapper.build().entityVO(detail));
    }

    /**
     * 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入customerCare")
    public R<IPage<CustomerCareVO>> list(CustomerCare customerCare, Query query) {
        IPage<CustomerCare> pages = customerCareService.page(Condition.getPage(query), Condition.getQueryWrapper(customerCare));
        return R.data(CustomerCareWrapper.build().pageVO(pages));
    }


    /**
     * 新增或修改 客户关怀表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入customerCare")
    public R submit(@Valid @RequestBody CustomerCare customerCare) {
        return R.status(customerCareService.saveOrUpdate(customerCare));
    }


    /**
     * 删除 客户关怀表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(customerCareService.deleteLogic(Func.toLongList(ids)));
    }


}
