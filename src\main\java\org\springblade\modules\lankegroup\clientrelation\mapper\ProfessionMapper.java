package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.clientrelation.dto.ProfessionDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.*;
import org.springblade.modules.lankegroup.clientrelation.vo.*;

import java.util.List;

import java.util.Map;

public interface ProfessionMapper extends BaseMapper<Profession> {

    /**
     * 查询区域市场列表
     *
     * @param profession
     * @param page
     * @return
     */
    IPage<ProfessionVO> getProfessionList(@Param("query") ProfessionDTO profession, IPage<ProfessionVO> page);

    /**
     * 查询区域市场列表
     *
     * @param profession
     * @param page
     * @return
     */
    IPage<ProfessionVO> getProfessionListAll(@Param("query") ProfessionDTO profession, IPage<ProfessionVO> page);


    /**
     * 根据部门查询市场列表
     *
     * @param deptId
     * @return
     */
    ProfessionVO getProfessionListByDept(@Param("deptId") String deptId);

    /**
     * 客户管理使用
     * 客户详情，根据客户填写的客户所属行业及客户所属区域，匹配客户的所属市场区域
     *
     * @return
     */
    List<Profession> selectByAreAndMarketForProfession(Map map);

    /**
     * 根据区域市场关联ID查询区域行业列表
     *
     * @return
     */
    List<AreaAndMarket> getAreaAndMarketList(Long id, Long marketId, String areaCode, List<Long> prohibitList);

    List<AreaAndMarket> getIndustryInformationAreaAndMarketList(Long id, List<Long> prohibitList);

    List<AreaAndMarket> getIndustryInformationAreaAndMarketListByUser(String id, List<AreaAndMarket> areaList, List<Long> prohibitList);

    List<AreaAndMarket> getAreaAndMarketListStr(Long id, Long marketId, String areaCode, List<Long> prohibitList);

    List<AreaAndMarket> getAreaAndMarketListMore(Long id, List<String> marketId, List<String> areaCode);

    Integer getActualClientCount(@Param("list") List<AreaAndMarket> areaAndMarket, @Param("userId") String userId);

    List<String> getActualClientCountList(@Param("list") List<AreaAndMarket> areaAndMarket, @Param("userId") String userId);

    Integer getActualClientCountByUserGroup(@Param("list") List<AreaAndMarket> areaAndMarket, @Param("professionId") Long professionId);

    List<String> getActualClientCountByUserGroupList(@Param("list") List<AreaAndMarket> areaAndMarket, @Param("professionId") Long professionId);

    /**
     * 获取实有客户列表
     *
     * @param areaAndMarket
     * @return
     */
    List<ActualResultVO> getActualClientList(@Param("area") AreaAndMarket areaAndMarket, @Param("userId") String userId);

    /**
     * 获取实有客户列表
     *
     * @param areaAndMarket
     * @return
     */
    List<ActualResultVO> getActualClientListByUserList(@Param("area") AreaAndMarket areaAndMarket, @Param("userList") List<String> userId);

    /**
     * 按区划查询应有客户
     *
     * @param map
     * @return
     */
    List<AreaListVO> getAreaOughtList(@Param("query") Map map);

    List<AreaListVO> getAreaOughtListByMarketList(@Param("query") Map map);

    /**
     * 按区划查询实有客户
     *
     * @param map
     * @return
     */
    Integer getAreaActualList(@Param("query") Map map);

    /**
     * 查询区域行业是否可编辑
     * @param professionId
     * @return
     */
    List<String> checkMarketAndArea(String professionId,List<AreaAndMarket> list);

    /**
     * 按区划查询实有客户
     *
     * @param map
     * @return
     */
    Integer getAreaActualListAndUserCustomer(@Param("query") Map map);

    /**
     * 获取省
     *
     * @param id
     * @return
     */
    List<Map> getProvince(String id);

    /**
     * 获取市
     *
     * @param id
     * @param code
     * @return
     */
    List<Map> getCity(String id, String code);

    /**
     * 获取区县
     *
     * @param id
     * @param code
     * @return
     */
    List<Map> getCounties(String id, String code);

    /**
     * 获取行业
     *
     * @return
     */
    List<Map> getProfessionListByArea(String id);

    /**
     * 获取人员实有行业
     *
     * @param id
     * @return
     */
    List<Map> getProfessionPersonnelList(String id);

    /**
     * 根据区域市场ID获取人员列表
     *
     * @param id
     * @return
     */
    List<String> getProfessionUserIdList(String id);

    /**
     * 获取行业应有
     *
     * @param areaAndMarket
     * @return
     */
    ProfessionPolymerizationNew getIndustryInformationMarketListY(@Param("query") AreaAndMarket areaAndMarket, @Param("professionId") Long professionId);

    /**
     * 获取行业实有
     *
     * @param areaAndMarket
     * @return
     */
    ProfessionPolymerization getIndustryInformationMarketListS(AreaAndMarket areaAndMarket);

    /**
     * 获取行业实有
     *
     * @param areaAndMarket
     * @return
     */
    ProfessionPolymerizationNew getIndustryInformationPersonnelListS(AreaAndMarket areaAndMarket);

    /**
     * 编辑对应行业信息
     *
     * @param marketplace
     */
    void updateByMarketplaceData(@Param("param") Marketplace marketplace);

    /**
     * 删除对应行业信息
     *
     * @param marketplace
     */
    void deleteByMarketplaceData(@Param("param") Marketplace marketplace);

    /**
     * 查询行业ID
     *
     * @param professionId
     * @param joinId
     * @return
     */
    List<MarketplaceVO> getMarketIdByProfessionId(@Param("professionId") String professionId, @Param("joinId") String joinId);

    /**
     * 根据区域市场ID和行业ID查询对应人员信息
     *
     * @param professionId
     * @param marketCode
     * @return
     */
    List<Personnel> getProfessionIdByPersonnelList(Long professionId, String marketCode);

    /**
     * 修改对应人员客户数量
     *
     * @param personnelId
     * @param marketCode
     * @param num
     */
    void updatePersonnelCustomerNum(List<Long> personnelId, String marketCode, String areaId, Integer num);

    /**
     * 查询人员使用行业数量
     *
     * @param marketId
     * @param professionId
     * @return
     */
    List<String> getMarketByPersonnelCount(List<AreaAndMarket> marketId, Long professionId);

    /**
     * 修改区域市场统计数据
     * @param professionVO
     */
    void updateProfessionData(@Param("param") ProfessionVO professionVO);

    /**
     * 查询某区域市场下市区客户数
     * @param professionId
     * @return
     */
    List<AreaResultListVO> getProfessionAreaList(String professionId);

    /**
     * 查询某区域市场下市区区县客户数
     * @param professionId
     * @return
     */
    List<AreaResultListVO> getProfessionAreaCityList(String professionId,String cityCode);

    /**
     * 获取某区域市场行业应有实有客户
     * @param professionId
     * @param userList
     * @return
     */
    List<ProfessionPolymerizationNew> getProfessionMarketList(String professionId,List<String> userList);

    /**
     * 查询应负责客户
     * @param professionId
     * @param areaCode
     * @return
     */
    List<MarketplaceResultVO> getAProfessionlCustomerList(String professionId,List<String> areaCode,List<String> marketCode);

    /**
     * 查询应负责区域行业
     * @param professionId
     * @param areaCode
     * @return
     */
    List<MarketplaceResultVO> getOProfessionlCustomerList(String professionId,List<String> areaCode,List<String> marketCode);
}
