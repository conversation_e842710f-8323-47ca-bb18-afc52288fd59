<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ProjectCollectionArchiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="projectCollectionArchiveResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ProjectCollectionArchive">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="customer_id" property="customerId"/>
        <result column="project_id" property="projectId"/>
        <result column="estimated_collection_amount" property="estimatedCollectionAmount"/>
        <result column="estimated_collection_date" property="estimatedCollectionDate"/>
        <result column="collection_status" property="collectionStatus"/>
        <result column="plan_status" property="planStatus"/>
        <result column="amount_cashed" property="amountCashed"/>
        <result column="remark" property="remark"/>
        <result column="expected_payment_percentage" property="expectedPaymentPercentage"/>
        <result column="payment_condition" property="paymentCondition"/>
    </resultMap>


    <select id="selectProjectCollectionArchivePage" resultMap="projectCollectionArchiveResultMap">
        select * from blade_project_collection_archive where is_deleted = 0
    </select>

</mapper>
