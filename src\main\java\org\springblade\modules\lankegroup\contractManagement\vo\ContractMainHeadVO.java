/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 合同主表头部统计视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@ApiModel(value = "ContractMainHeadVO", description = "合同主表头部统计视图实体类")
public class ContractMainHeadVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 统计类型
     */
    @ApiModelProperty(value = "统计类型")
    private String statisticsType;

    /**
     * 统计名称
     */
    @ApiModelProperty(value = "统计名称")
    private String statisticsName;

    /**
     * 统计数据列表
     */
    @ApiModelProperty(value = "统计数据列表")
    private List<ContractMainStatisticsVO> statistics;

}
