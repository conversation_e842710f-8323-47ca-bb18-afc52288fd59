/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;

/**
 * 动物饲养合同子表-收费项实体类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@TableName("cm_contract_animal_breeding_expenses")
@EqualsAndHashCode(callSuper = true)
public class ContractAnimalBreedingExpenses extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 合同主表ID
     */
    private Long mainId;
    /**
     * 收费项名称
     */
    @ApiModelProperty("收费项名称")
    private String itemName;
    /**
     * 收费项规格
     */
    @ApiModelProperty("收费项规格")
    private String itemSpec;
    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


}
