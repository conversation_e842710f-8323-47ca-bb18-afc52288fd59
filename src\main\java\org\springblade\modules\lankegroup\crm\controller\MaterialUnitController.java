/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.MaterialUnit;
import org.springblade.modules.lankegroup.crm.vo.MaterialUnitVO;
import org.springblade.modules.lankegroup.crm.service.IMaterialUnitService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 物料单位表 控制器
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/unit/materialunit")
@Api(value = "物料单位表", tags = "物料单位表接口")
public class MaterialUnitController extends BladeController {

	private final IMaterialUnitService materialUnitService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入materialUnit")
	public R<MaterialUnit> detail(MaterialUnit materialUnit) {
		MaterialUnit detail = materialUnitService.getOne(Condition.getQueryWrapper(materialUnit));
		return R.data(detail);
	}

	/**
	 * 分页 物料单位表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入materialUnit")
	public R<IPage<MaterialUnit>> list(MaterialUnit materialUnit, Query query) {
		IPage<MaterialUnit> pages = materialUnitService.page(Condition.getPage(query), Condition.getQueryWrapper(materialUnit));
		return R.data(pages);
	}

	/**
	 * 自定义分页 物料单位表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入materialUnit")
	public R<IPage<MaterialUnitVO>> page(MaterialUnitVO materialUnit, Query query) {
		IPage<MaterialUnitVO> pages = materialUnitService.selectMaterialUnitPage(Condition.getPage(query), materialUnit);
		return R.data(pages);
	}

	/**
	 * 新增 物料单位表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入materialUnit")
	public R save(@Valid @RequestBody MaterialUnit materialUnit) {
		return R.status(materialUnitService.save(materialUnit));
	}

	/**
	 * 修改 物料单位表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入materialUnit")
	public R update(@Valid @RequestBody MaterialUnit materialUnit) {
		return R.status(materialUnitService.updateById(materialUnit));
	}

	/**
	 * 新增或修改 物料单位表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入materialUnit")
	public R submit(@Valid @RequestBody MaterialUnit materialUnit) {
		return R.status(materialUnitService.saveOrUpdate(materialUnit));
	}

	
	/**
	 * 删除 物料单位表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(materialUnitService.deleteLogic(Func.toLongList(ids)));
	}

	
}
