package org.springblade.modules.auth.service;

import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.util.crypto.WxCpCryptUtil;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WxcpCallbackService {
	private final WxCpService wxcpService;
	private final WxCpCryptUtil cryptUtil;

	public String handleEchoStr(String echoStr) {
		return cryptUtil.decrypt(echoStr);
	}
}
