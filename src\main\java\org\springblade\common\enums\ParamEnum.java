package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ParamEnum {
	SAVE(0,"新增"),
	DELETE(1,"删除"),
	UPDATE(2,"编辑"),
	SELECT(3,"查询"),
	DETAIL(4,"查看"),
	;
	/**
	 * 键
	 */
	final Integer key;
	/**
	 * 值
	 */
	final String value;

	/**
	 * 匹配枚举值
	 *
	 * @param key 名称
	 * @return OssEnum
	 */
	public static ParamEnum of(Integer key) {
		if (key == null) {
			return null;
		}
		ParamEnum[] values = values();
		for (ParamEnum smsEnum : values) {
			if (smsEnum.key == key) {
				return smsEnum;
			}
		}
		return null;
	}
}
