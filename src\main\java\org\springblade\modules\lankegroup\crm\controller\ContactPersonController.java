/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.enums.BusinessAuthTypeEnum;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.manager.BusinessAuthManager;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.crm.dto.ContactPersonDTO;
import org.springblade.modules.lankegroup.crm.entity.ContactPerson;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.entity.CustomerContact;
import org.springblade.modules.lankegroup.crm.mapper.ConcentUserMapper;
import org.springblade.modules.lankegroup.crm.mapper.CustomerContactMapper;
import org.springblade.modules.lankegroup.crm.service.IContactPersonService;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.crm.vo.ContactPersonVO;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 客户联系人表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/crm/contactperson")
@Api(value = "客户联系人表【新】", tags = "客户联系人表接口【新】")
public class ContactPersonController extends BladeController {

    private final IContactPersonService contactPersonService;
    private final CustomerContactMapper customerContactMapper;
    private final BusinessAuthManager businessAuthManager;
    private final ConcentUserMapper concentUserMapper;
    private final ICustomerConnectLogService customerConnectLogService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contactPerson")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = ContactPersonVO.class)
    })
    public R<ContactPersonVO> detail(ContactPerson contactPerson) {
        ContactPerson detail = contactPersonService.getOne(Condition.getQueryWrapper(contactPerson));
        if (detail == null) {
            return R.fail("未查询到联系人数据");
        }
        ContactPersonVO vo = new ContactPersonVO();
        BeanUtils.copyProperties(detail, vo);
        Optional<CustomerContact> customerContact = Optional.ofNullable(vo.getCustomerContactId()).map(customerContactMapper::selectById);
        if (!customerContact.isPresent()) {
            return R.fail("未查询到客户数据");
        }
        vo.setCustomerContactName(customerContact.map(CustomerContact::getContacts).orElse(null));
        // 需要加 按钮 权限
        boolean chechAuth = businessAuthManager.chechAuth(AuthUtil.getUserId(), customerContact.map(CustomerContact::getPrincipal).orElse(null),
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级
                )
        );
        if (chechAuth) {
            vo.setButtonAuth(vo.getButtonAuth().set("updateAuth", true).set("deleteAuth", true));
        }
        return R.data(vo);
    }

    /**
     * 分页 客户联系人表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入contactPerson")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "size", value = "每页显示数量", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "customerContactId", value = "客户id", paramType = "query", dataType = "string", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = ContactPersonVO.class)
    })
    public R<IPage<ContactPersonVO>> page(ContactPerson contactPerson, Query query) {
        // 客户
        CustomerContact customerContact = customerContactMapper.selectById(contactPerson.getCustomerContactId());
        if (Func.isEmpty(customerContact)) {
            IPage<ContactPersonVO> contactPersonIPage = new Page<>();
            contactPersonIPage.setRecords(new ArrayList<>(0));
            return R.data(contactPersonIPage);
        }
        // 客户负责人 和 共享人
        List<Long> list = new ArrayList<>();
        list.add(customerContact.getPrincipal());

        // 共享人列表
        Map map = new HashMap();
        map.put("contactsId", customerContact.getId());
        List<Long> sharUserIds = concentUserMapper.selectSharUserIds(map);
        if (CollectionUtil.isNotEmpty(sharUserIds)) {
            list.addAll(sharUserIds);
        }
        // 需要加权限
        boolean chechAuth = businessAuthManager.chechAuth(AuthUtil.getUserId(), list,
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级,
                        BusinessAuthTypeEnum.财务,
                        BusinessAuthTypeEnum.高层
                )
        );
        // 需要加 按钮 权限
        boolean chechAuth2 = businessAuthManager.chechAuth(AuthUtil.getUserId(), customerContact.getPrincipal(),
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级
                )
        );
        IPage<ContactPerson> pages = contactPersonService.page(Condition.getPage(query), Condition.getQueryWrapper(contactPerson));
        IPage<ContactPersonVO> voPages = pages.convert(entity -> {
            ContactPersonVO vo = new ContactPersonVO();
            BeanUtils.copyProperties(entity, vo);
            if (!chechAuth) {
                vo.setContactsInformation(StringUtils.overlay(vo.getContactsInformation(), "******", 3, 9));
            }
            if (chechAuth2) {
                vo.setButtonAuth(vo.getButtonAuth().set("updateAuth", true).set("deleteAuth", true));
            }
            return vo;
        });
        return R.data(voPages);
    }

    /**
     * 新增 客户联系人表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入contactPersonDTO")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R save(@RequestBody @ApiParam(value = "客户联系人表对象", required = true) ContactPersonDTO contactPersonDTO) {
        if (Func.isEmpty(contactPersonDTO)) {
            return R.fail("参数不能为空");
        }
        if (Func.isEmpty(contactPersonDTO.getCustomerContactId())) {
            return R.fail("客户不能为空");
        }

        Optional<CustomerContact> customerContact = Optional.ofNullable(contactPersonDTO.getCustomerContactId()).map(customerContactMapper::selectById);
        if (!customerContact.isPresent()) {
            return R.fail("未查询到客户数据");
        }
        // 需要加 按钮 权限
        boolean chechAuth = businessAuthManager.chechAuth(AuthUtil.getUserId(), customerContact.map(CustomerContact::getPrincipal).orElse(null),
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级
                )
        );

        if (!chechAuth) {
            return R.fail("暂无权限新增联系人");
        }
        if (Func.isBlank(contactPersonDTO.getContacts())) {
            return R.fail("姓名不能为空");
        }
        if (Func.isBlank(contactPersonDTO.getContactsInformation()) && Func.isBlank(contactPersonDTO.getWechat())) {
            return R.fail("电话或微信不能为空");
        }
        if (Func.isEmpty(contactPersonDTO.getDecisionMakerStatus())) {
            return R.fail("是否决策者不能为空");
        }

        R checkContact = contactPersonService.checkContact(contactPersonDTO);
        if (!checkContact.isSuccess()) {
            return checkContact;
        }

        ContactPerson contactPerson = new ContactPerson();
        BeanUtils.copyProperties(contactPersonDTO, contactPerson);
        boolean save = contactPersonService.save(contactPerson);
        if (save) {
            // 写入记录日志表
            CustomerConnectLog contractChangeLog = new CustomerConnectLog();
            Long customerId = Optional.ofNullable(contactPerson.getCustomerContactId())
                    .map(cid -> customerContactMapper.selectOne(Wrappers.lambdaQuery(CustomerContact.class).eq(CustomerContact::getId, cid)))
                    .map(cc -> Func.isNotBlank(cc.getCustomerId()) ? Long.valueOf(cc.getCustomerId()) : null).orElse(null);
            contractChangeLog.setCustomerId(customerId);            contractChangeLog.setChangeTable(TableIdEnum.客户联系人.getTableId());
            contractChangeLog.setContactId(contactPerson.getId());
            contractChangeLog.setCustomerContactId(contactPerson.getCustomerContactId());
            contractChangeLog.setAboutDescription("添加了客户联系人");
            contractChangeLog.setDescription("添加了客户联系人");
            contractChangeLog.setChargeUser(AuthUtil.getUserId());
            customerConnectLogService.save(contractChangeLog);
        }

        return R.status(save);
    }

    /**
     * 修改 客户联系人表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入contactPersonDTO")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R update(@Valid @RequestBody @ApiParam(value = "客户联系人表对象", required = true) ContactPersonDTO contactPersonDTO) {
        if (Func.isEmpty(contactPersonDTO) || Func.isEmpty(contactPersonDTO.getId())) {
            return R.fail("参数不能为空");
        }
        if (Func.isEmpty(contactPersonDTO.getCustomerContactId())) {
            return R.fail("客户不能为空");
        }
        Optional<CustomerContact> customerContact = Optional.ofNullable(contactPersonDTO.getCustomerContactId()).map(customerContactMapper::selectById);
        if (!customerContact.isPresent()) {
            return R.fail("未查询到客户数据");
        }

        // 需要加 按钮 权限
        boolean chechAuth = businessAuthManager.chechAuth(AuthUtil.getUserId(), customerContact.map(CustomerContact::getPrincipal).orElse(null),
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级
                )
        );
        if (!chechAuth) {
            return R.fail("暂无权限修改联系人");
        }

        if (Func.isBlank(contactPersonDTO.getContacts())) {
            return R.fail("姓名不能为空");
        }
        if (Func.isBlank(contactPersonDTO.getContactsInformation()) && Func.isBlank(contactPersonDTO.getWechat())) {
            return R.fail("电话或微信不能为空");
        }
        if (Func.isEmpty(contactPersonDTO.getDecisionMakerStatus())) {
            return R.fail("是否决策者不能为空");
        }

        R checkContact = contactPersonService.checkContact(contactPersonDTO);
        if (!checkContact.isSuccess()) {
            return checkContact;
        }

        ContactPerson contactPerson = new ContactPerson();
        BeanUtils.copyProperties(contactPersonDTO, contactPerson);
        return R.status(contactPersonService.updateById(contactPerson));
    }

    /**
     * 删除 客户联系人表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 500, message = "内部服务器错误")
    })
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        ContactPerson detail = contactPersonService.getById(Long.valueOf(ids));
        if (detail == null) {
            return R.fail("未查询到联系人数据");
        }
        Optional<CustomerContact> customerContact = Optional.ofNullable(detail.getCustomerContactId()).map(customerContactMapper::selectById);
        if (!customerContact.isPresent()) {
            return R.fail("未查询到客户数据");
        }
        // 需要加 按钮 权限
        boolean chechAuth = businessAuthManager.chechAuth(AuthUtil.getUserId(), customerContact.map(CustomerContact::getPrincipal).orElse(null),
                Func.ofImmutableList(
                        BusinessAuthTypeEnum.目标人及其所有上级
                )
        );
        if (!chechAuth) {
            return R.fail("暂无权限删除联系人");
        }
        boolean deleted = contactPersonService.deleteLogic(Func.toLongList(ids));

        if (deleted) {
            // 写入记录日志表
            CustomerConnectLog contractChangeLog = new CustomerConnectLog();
            Long customerId = Optional.ofNullable(detail.getCustomerContactId())
                    .map(cid -> customerContactMapper.selectOne(Wrappers.lambdaQuery(CustomerContact.class).eq(CustomerContact::getId, cid)))
                    .map(cc -> Func.isNotBlank(cc.getCustomerId()) ? Long.valueOf(cc.getCustomerId()) : null).orElse(null);
            contractChangeLog.setCustomerId(customerId);
            contractChangeLog.setChangeTable(TableIdEnum.客户联系人.getTableId());
            contractChangeLog.setContactId(detail.getId());
            contractChangeLog.setCustomerContactId(detail.getCustomerContactId());
            contractChangeLog.setAboutDescription("删除了客户联系人");
            contractChangeLog.setDescription("删除了客户联系人");
            contractChangeLog.setChargeUser(AuthUtil.getUserId());
            customerConnectLogService.save(contractChangeLog);
        }

        return R.status(deleted);
    }

}
