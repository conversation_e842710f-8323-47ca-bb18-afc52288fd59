package org.springblade.common.constant;

public class FormNameConstant {
    /**
     * 其他应付单
     */
    public static final String OTHER_PAYABLES = "blade_other_payables";

    /**
     * 合同
     */
    public static final String CONTRACT = "blade_contract";
    public static final String CONTRACTCHANGE = "blade_contract_change";

    /**
     * 合同归档
     */
    public static final String CONTRACTARCHIVE = "blade_archive_details";


    /**
     * 开票作废
     */
    public static final String INVOICEVOID = "blade_invoice_void";

    /**
     * 渠道商审批
     */
    public static final String CHANNELSUPPLIER = "blade_channel_supplier";

    /**
     * 项目
     */
    public static final String PROJECTBASIC = "blade_project_basic";
    /**
     * 出库
     */
    public  static final String OUTBOUND="blade_outbound";
    /**
     * 开票
     */
//    public  static final String INVOICE="blade_invoice";
    public  static final String INVOICE="fms_ticket";


    /**
     * 应收单
     */
    public static final String RECEIVABLE = "blade_receivable";

    /**
     * 报销单
     */
    public static final String REIM = "blade_reim";

    /**
     * 开票看板质保、法务、坏账流程
     */
    public static final String INVOICEKANBANPROJECTPROCESS = "blade_invoice_kanban_project_process";
    /**
     * 付款单
     */
    public static final String PAYBILL = "blade_paybill";
    /**
     * 采购申请单
     */
    public static final String PURCHASEREQUISITION = "blade_purchase_requisition";
    /**
     * 回款单
     */
    public static final String PAYBACKRECEIPT = "payback_receipt_manage";
    /**
     * 新采购申请单
     */
    public static final String PURCHASE = "blade_purchase_requisition";
    /**
     * 印章申请表
     */
    public static final String SEALAPPLICATION = "blade_seal_application";
    /**
     * 任务交付
     */
    public static final String TASKDELIVERY = "blade_task_delivery";
}
