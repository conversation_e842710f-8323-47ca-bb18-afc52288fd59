package org.springblade.common.enums;

public enum FormIdEnum {
    非正式项目档案("BAS_PreBaseDataTwo"),
    客户("BD_Customer"),
    供应商("BD_Supplier"),
    员工("BD_Empinfo"),
    销售合同("CRM_Contract"),
    项目基础信息("BAS_PreBaseDataOne"),
    收款单("AR_RECEIVEBILL"),
    报销单("AP_PAYBILL"),
    采购合同("PUR_Contract"),
    应收单("AR_receivable"),
    出库单("STK_MisDelivery"),
    开票申请("RWSQ_KPSQ");

    private String formId;

    public String getFormId() {
        return formId;
    }

    FormIdEnum(String formId) {
        this.formId = formId;
    }
}
