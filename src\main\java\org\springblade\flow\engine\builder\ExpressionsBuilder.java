package org.springblade.flow.engine.builder;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

import static org.springblade.flow.core.constant.WorkFlowConstants.PROC_INSTANCE_START_USER_DEPT_VAR;

/**
 * <AUTHOR> wangqing
 * @description : 构建条件为EL表达式
 * @create :  2021/12/03 15:53
 **/
public class ExpressionsBuilder {


//    public static void main(String[] args) {
//        String build = build("[\n" +
//                "    {\n" +
//                "        \"subConditionList\": [\n" +
//                "            {\n" +
//                "                \"op\": \"eq\",\n" +
//                "                \"field\": \"radio-1638195662169label\",\n" +
//                "                \"value\": \"1212\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"op\": \"eq\",\n" +
//                "                \"field\": \"textarea-1638195857789\",\n" +
//                "                \"value\": \"121212\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"subConditionList\": [\n" +
//                "            {\n" +
//                "                \"op\": \"eq\",\n" +
//                "                \"field\": \"radio-1638195662169label\",\n" +
//                "                \"value\": \"1212\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    }\n" +
//                "]");
//        System.out.println(build);
//    }

	/**
	 * 构建
	 *
	 * @param conditionList
	 * @return
	 */
	public static String build(String conditionList) {
		JSONArray jsonArray = JSONUtil.parseArray(conditionList);
		if (jsonArray.isEmpty()) {
			return null;
		}
		List<JSONObject> jsonObjects = jsonArray.toList(JSONObject.class);
		// 拼接为el表达式
		StringBuilder expression = new StringBuilder("${ ");
		expression.append(StrUtil.format("{} {} '{}'", PROC_INSTANCE_START_USER_DEPT_VAR, "==", jsonObjects.get(0).get("id")));
		expression.append(" }");
//        for (int i = 0; i < jsonObjects.size(); i++) {
//            if (i != 0) {
//                expression.append(" or ");
//            }
//            JSONObject cl = jsonObjects.get(i);
//            JSONArray subConditionList = cl.getJSONArray("subConditionList");
//            List<Condition> conditions = subConditionList.toList(Condition.class);
//            StringBuilder subExp = new StringBuilder(" (");
//            for (int j = 0; j < conditions.size(); j++) {
//                // 移除掉字段名中的中划线 否则el表达式里面会被认为是减号 报错
//                StringBuilder tempExp = new StringBuilder();
//                Condition condition = conditions.get(j);
//                if (j != 0) {
//                    subExp.append(" and ");
//                }
//                Symbol symbol = Symbol.getByCode(condition.op);
//                String field = condition.getField().replace("-", "");
//                // 是否是自定义表达式
//                if (symbol.custom) {
//                    // tFun.dateGe(age,'1') 格式 customExpressionFunc是自定的表达式bean类
//                    tempExp.append(StrUtil.format("customExpressionFunc.{}({},'{}')", symbol.code, field, condition.getValue()));
//                } else {
//                    tempExp.append(StrUtil.format("{} {} '{}'", field, symbol.code, condition.getValue()));
//                }
//
//                subExp.append(tempExp);
//                if (j == conditions.size() - 1) {
//                    subExp.append(" )");
//                }
//            }
//            expression.append(subExp);
//            if (i == jsonObjects.size() - 1) {
//                expression.append(" }");
//            }
//        }
		return expression.toString();
	}

	@AllArgsConstructor
	public enum Symbol {
		LE("le", false),
		LT("lt", false),
		GE("ge", false),
		GT("gt", false),
		EQ("eq", false),
		NE("ne", false),
		NOT_LIKE("notLike", true),
		LIKE("like", true);
		/**
		 * code
		 */
		private final String code;


		/**
		 * 自定义表达式
		 */
		private final Boolean custom;


		@JsonCreator
		public static Symbol getByCode(String code) {
			return Arrays.stream(Symbol.values()).filter(item -> item.code.equals(code)).findFirst().get();
		}

	}

	@Data
	private class Condition {
		private String field;
		private String op;
		private String value;
	}


}
