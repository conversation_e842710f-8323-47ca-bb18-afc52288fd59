/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerGroup;
import org.springblade.modules.lankegroup.crm.vo.CustomerGroupVO;
import org.springblade.modules.lankegroup.crm.wrapper.CustomerGroupWrapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerGroupService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 客户分组表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/group/customergroup")
@Api(value = "客户分组表", tags = "客户分组表接口")
public class CustomerGroupController extends BladeController {

	private final ICustomerGroupService customerGroupService;

	/**
	 * 详情
	 */
	@ApiLog("客户分组详情")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerGroup")
	public R<CustomerGroupVO> detail(CustomerGroup customerGroup) {
		CustomerGroup detail = customerGroupService.getOne(Condition.getQueryWrapper(customerGroup));
		return R.data(CustomerGroupWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 客户分组表
	 */
	@ApiLog("客户分组列表")
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerGroup")
	public R list(CustomerGroup customerGroup, Query query) {
		query.setSize(50);
		IPage<CustomerGroup> pages = customerGroupService.page(Condition.getPage(query), Condition.getQueryWrapper(customerGroup));
		return R.data(pages);
	}
//	/**
//	 * 分页 客户分组表
//	 */
//	@ApiLog("客户分组列表")
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 2)
//	@ApiOperation(value = "分页", notes = "传入customerGroup")
//	public R<List<CustomerGroupVO>> groupList() {
//		List<CustomerGroup> pages = customerGroupService.selectGroups();
//		return R.data(CustomerGroupWrapper.build().listVO(pages));
//	}


	/**
	 * 自定义分页 客户分组表
	 */
	@ApiLog("客户分组列表")
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerGroup")
	public R<IPage<CustomerGroupVO>> page(CustomerGroupVO customerGroup, Query query) {
		IPage<CustomerGroupVO> pages = customerGroupService.selectCustomerGroupPage(Condition.getPage(query), customerGroup);
		return R.data(pages);
	}

	/**
	 * 新增 客户分组表
	 */
	@ApiLog("客户分组保存")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerGroup")
	public R save(@Valid @RequestBody CustomerGroup customerGroup) {
		return R.status(customerGroupService.save(customerGroup));
	}

	/**
	 * 修改 客户分组表
	 */
	@ApiLog("客户分组修改")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerGroup")
	public R update(@Valid @RequestBody CustomerGroup customerGroup) {
		return R.status(customerGroupService.updateById(customerGroup));
	}

	/**
	 * 新增或修改 客户分组表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerGroup")
	public R submit(@Valid @RequestBody CustomerGroup customerGroup) {
		return R.status(customerGroupService.saveOrUpdate(customerGroup));
	}

	
	/**
	 * 删除 客户分组表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerGroupService.deleteLogic(Func.toLongList(ids)));
	}

	
}
