package org.springblade.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectCollaborationTask;
import org.springblade.modules.lankegroup.pro_management.enums.ProjectCollaborationTaskStatusEnum;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IProjectCollaborationTaskService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 项目的协作任务状态（0未开始/1进行中/2已超期/3已完成/4已终止）定时
 * -每天凌晨2点运行
 *
 * <AUTHOR> Liu
 * @since 2023年10月10日 10:52
 **/
@Slf4j
@Component
@AllArgsConstructor
public class ProjectCollaborationTaskTimer {

    private final IProjectCollaborationTaskService projectCollaborationTaskService;
    private final ProjectBasicMapper projectBasicMapper;


    /**
     * 定时变更协作任务状态
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void changeProjectCollaborationTaskStatus() {
        // 获取所有的协作任务+状态为（未开始、进行中）+未删除的数据
        R<List<ProjectCollaborationTask>> dataListR = getNeedChangeStatusDataList();
        if (dataListR.isSuccess()) {
            for (ProjectCollaborationTask data : dataListR.getData()) {
                // 判断状态是否需要变更
                Date startDate = data.getStartDate();
                Date endDate = data.getEndDate();
                ProjectCollaborationTaskStatusEnum taskStatusEnum = projectCollaborationTaskService.getProjectCollaborationTaskStatus(startDate, endDate);
                if (taskStatusEnum != null && !(taskStatusEnum.getCode().equals(data.getStatus()))) {
                    try {
                        R updateDataR = updateData(data.getId(), taskStatusEnum);
                        if (updateDataR.isSuccess() && taskStatusEnum.getCode().equals(ProjectCollaborationTaskStatusEnum.已超期.getCode())) {
                            // 发送消息
                            ProjectBasic projectBasic = projectBasicMapper.selectById(data.getProjectId());
                            if (null == projectBasic) {
                                log.info("【项目协作任务定时】发送消息-获取项目信息失败，跳过本次发送。协作任务ID={},项目ID={}", data.getId(), data.getProjectId());
                                continue;
                            }
                            projectCollaborationTaskService.sendMsgCoreBefore(3, data, projectBasic.getProjectName(), null);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取待变更状态数据
     */
    private R<List<ProjectCollaborationTask>> getNeedChangeStatusDataList() {
        LambdaQueryWrapper<ProjectCollaborationTask> queryWrapper = new QueryWrapper<ProjectCollaborationTask>().lambda();
        queryWrapper.and(wrapper -> wrapper.eq(ProjectCollaborationTask::getStatus, 0).or().eq(ProjectCollaborationTask::getStatus, 1));
        List<ProjectCollaborationTask> list = null;
        try {
            list = projectCollaborationTaskService.list(queryWrapper);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (list == null || list.size() == 0) {
            return R.fail("未获取到数据");
        }
        return R.data(list);
    }

    /**
     * 修改对应数据
     *
     * @param id 协作任务ID
     * @return
     */
    private R updateData(Long id, ProjectCollaborationTaskStatusEnum taskStatusEnum) {
        LambdaUpdateWrapper<ProjectCollaborationTask> updateWrapper = new UpdateWrapper<ProjectCollaborationTask>().lambda();
        updateWrapper.eq(ProjectCollaborationTask::getId, id);
        updateWrapper.set(ProjectCollaborationTask::getStatus, taskStatusEnum.getCode());
        return R.status(projectCollaborationTaskService.update(updateWrapper));
    }

}
