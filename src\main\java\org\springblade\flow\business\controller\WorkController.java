/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.flowable.engine.TaskService;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.dto.SignUpTaskDTO;
import org.springblade.flow.business.dto.TransferTaskDTO;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springframework.web.bind.annotation.*;

/**
 * 流程事务通用接口
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("work")
@Api(value = "流程事务通用接口【审批操作】", tags = "流程事务通用接口")
public class WorkController {

	private final TaskService taskService;
	private final FlowEngineService flowEngineService;
	private final FlowBusinessService flowBusinessService;

	/**
	 * 发起事务列表页
	 */
	@GetMapping("start-list")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "发起事务列表页", notes = "传入流程类型")
	public R<IPage<FlowProcess>> startList(@ApiParam("流程类型") String category, Query query, @RequestParam(required = false, defaultValue = "1") Integer mode) {
		IPage<FlowProcess> pages = flowEngineService.selectProcessPage(Condition.getPage(query), category, mode);
		return R.data(pages);
	}

	/**
	 * 待签事务列表页
	 */
	@GetMapping("claim-list")
//	@ApiOperationSupport(order = 2)
//	@ApiOperation(value = "待签事务列表页", notes = "传入流程信息")
	public R<IPage<BladeFlow>> claimList(@ApiParam("流程信息") BladeFlow bladeFlow, Query query) {
		IPage<BladeFlow> pages = flowBusinessService.selectClaimPage(Condition.getPage(query), bladeFlow);
		return R.data(pages);
	}

	/**
	 * 待办事务列表页
	 */
	@GetMapping("todo-list")
//	@ApiOperationSupport(order = 3)
//	@ApiOperation(value = "待办事务列表页", notes = "传入流程信息")
	public R<IPage<BladeFlow>> todoList(@ApiParam("流程信息") BladeFlow bladeFlow, Query query) {
		IPage<BladeFlow> pages = flowBusinessService.selectTodoPage(Condition.getPage(query), bladeFlow);
		return R.data(pages);
	}

	/**
	 * 已发事务列表页
	 */
	@GetMapping("send-list")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "已发事务列表页", notes = "传入流程信息")
	public R<IPage<BladeFlow>> sendList(@ApiParam("流程信息") BladeFlow bladeFlow, Query query) {
		IPage<BladeFlow> pages = flowBusinessService.selectSendPage(Condition.getPage(query), bladeFlow);
		return R.data(pages);
	}

	/**
	 * 办结事务列表页
	 */
	@GetMapping("done-list")
//	@ApiOperationSupport(order = 5)
//	@ApiOperation(value = "办结事务列表页", notes = "传入流程信息")
	public R<IPage<BladeFlow>> doneList(@ApiParam("流程信息") BladeFlow bladeFlow, Query query) {
		IPage<BladeFlow> pages = flowBusinessService.selectDonePage(Condition.getPage(query), bladeFlow);
		return R.data(pages);
	}

	/**
	 * 签收事务
	 *
	 * @param taskId 任务id
	 */
	@PostMapping("claim-task")
//	@ApiOperationSupport(order = 6)
//	@ApiOperation(value = "签收事务", notes = "传入流程信息")
	public R claimTask(@ApiParam("任务id") String taskId) {
		taskService.claim(taskId, String.valueOf(AuthUtil.getUserId()));
		return R.success("签收事务成功");
	}

	/**
	 * 完成任务
	 *
	 * @param flow 流程信息
	 */
	@PostMapping("complete-task")
//	@ApiOperationSupport(order = 7)
//	@ApiOperation(value = "完成任务", notes = "传入流程信息")
	public R completeTask(@ApiParam("任务信息") @RequestBody BladeFlow flow) {
		return R.status(flowBusinessService.completeTask(flow));
	}

	/**
	 * 同意任务
	 *
	 * @param flow 流程信息
	 */
	@PostMapping("approve-task")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "完成任务", notes = "传入流程信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true),
			@ApiImplicitParam(name = "targetUserId", value = "分配人ID", dataType = "string", required = true)
	})
	public R approveTask(@ApiParam("任务信息") @RequestBody WorkFlowDTO flow) {
		return flowBusinessService.approveTask(flow);
	}

	/**
	 * 驳回任务（20250424目前和撤销方法用的是一样的逻辑，都是回到开始人节点，考虑到后期的扩展所以写了两个方法）
	 * 实际处理方式：退回到发起人节点
	 *
	 * @param flow 流程信息
	 */
	@PostMapping("reback-task")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "驳回任务", notes = "传入流程信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true),
			@ApiImplicitParam(name = "targetUserId", value = "分配人ID", dataType = "string", required = true)
	})
	public R rebackTask(@ApiParam("任务信息") @RequestBody WorkFlowDTO flow) {
		return flowBusinessService.rejectTask(flow);
	}

	/**
	 * 撤销任务
	 *
	 * @param flow
	 * @return
	 */
	@PostMapping("revoke-task")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "撤销任务", notes = "传入流程信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerContactId", value = "客户ID", dataType = "string", required = true),
			@ApiImplicitParam(name = "targetUserId", value = "分配人ID", dataType = "string", required = true)
	})
	public R revokeTask(@ApiParam("任务信息") @RequestBody WorkFlowDTO flow) {
		return flowBusinessService.revokeTask(flow);
	}

	/**
	 * 删除任务
	 */
	@PostMapping("delete-task")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除任务", notes = "传入流程信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "processInstanceId", value = "流程实例ID", dataType = "string", required = true),
			@ApiImplicitParam(name = "formDataId", value = "业务ID", dataType = "string", required = true)
	})
	public R deleteTask(@RequestBody BusinessProcessEntity businessProcessEntity) {
		if (Func.isBlank(businessProcessEntity.getProcessInstanceId()) || Func.isEmpty(businessProcessEntity.getFormDataId())) {
			return R.fail("参数不完整");
		}
		return flowBusinessService.deleteTask(businessProcessEntity.getProcessInstanceId(), businessProcessEntity.getFormDataId());
	}

	/**
	 * 新销客 转交任务
	 *
	 * @param taskId 任务id
	 * @param userId 转交用户
	 */
	@PostMapping("transfer-task")
//	@ApiOperationSupport(order = 8)
//	@ApiOperation(value = "转交任务", notes = "传入流程信息")
//	@ApiImplicitParams({
//		@ApiImplicitParam(name = "taskId", value = "任务id", required = true),
//		@ApiImplicitParam(name = "userId", value = "转交用户", required = true),
//	})
	public R transferTask(@ApiParam("任务id") String taskId, @ApiParam("转交用户") String userId) {
		return	flowBusinessService.transferTask(taskId, userId);
	}

//	public R addSignTask(@ApiParam("任务信息") @RequestBody WorkFlowDTO flow) {
////		return	flowBusinessService.transferTask(taskId, userId);
//	}

	/**
	 * 晶莱 转交任务
	 *
	 * - taskId 任务id
	 * - userId 转交用户
	 */
	@PostMapping("transfer-task-v2")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "转交任务", notes = "传入流程信息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "taskId", value = "任务id", dataType = "string", required = true),
		@ApiImplicitParam(name = "userId", value = "转交用户", dataType = "string", required = true),
		@ApiImplicitParam(name = "reason", value = "转交理由", dataType = "string", required = true),
	})
	public R transferTaskV2(@RequestBody TransferTaskDTO transferTaskDTO) {
		if (Func.isAnyBlank(transferTaskDTO.getTaskId(), transferTaskDTO.getUserId())) {
			return R.fail("taskId不能为空");
		}
		return	flowBusinessService.transferTaskV2(transferTaskDTO.getTaskId(), transferTaskDTO.getUserId(), transferTaskDTO.getReason());
	}

	/**
	 * 晶莱 加签任务
	 *
	 * - taskId 任务id
	 * - userIds 转交用户
	 * - signType 加签方式 1.前加签；2.后加签
	 */
	@PostMapping("sign-task")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "加签任务【待实现】", notes = "传入流程信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "taskId", value = "任务id", dataType = "string", required = true),
			@ApiImplicitParam(name = "userIds", value = "加签用户,多个用英文逗号分隔", dataType = "string", required = true),
			@ApiImplicitParam(name = "signType", value = "加签方式 1.前加签；2.后加签", dataType = "integer", required = true),
			@ApiImplicitParam(name = "approvalType", value = "审批方式 1.会签；2.或签", dataType = "integer", required = true),
			@ApiImplicitParam(name = "reason", value = "加签理由", dataType = "string", required = true),
	})
	public R signTask(@RequestBody SignUpTaskDTO signUpTaskDTO) {
		if (Func.isAnyBlank(signUpTaskDTO.getTaskId(), signUpTaskDTO.getUserIds())) {
//			return R.fail("taskId不能为空");
			throw new ServiceException("功能开发中");
		}
		if (Func.isEmpty(signUpTaskDTO.getSignType())) {
			return R.fail("加签方式不能为空");
		}
		if (Func.toStrList(signUpTaskDTO.getUserIds()).size() > 1 && Func.isEmpty(signUpTaskDTO.getApprovalType())) {
			return R.fail("审批方式不能为空");
		}
		return	flowBusinessService.signTask(signUpTaskDTO.getTaskId(), signUpTaskDTO.getUserIds(), signUpTaskDTO.getSignType(), signUpTaskDTO.getApprovalType(), signUpTaskDTO.getReason());
	}

}
