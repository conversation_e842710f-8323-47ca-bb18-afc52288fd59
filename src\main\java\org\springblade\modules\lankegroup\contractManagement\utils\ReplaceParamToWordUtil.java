package org.springblade.modules.lankegroup.contractManagement.utils;

import cn.hutool.core.date.DatePattern;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.lankegroup.contractManagement.annotation.FieldToWord;
import org.springblade.modules.lankegroup.contractManagement.constant.ContractToWordConstant;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;
import org.springblade.modules.lankegroup.contractManagement.enums.SignMethodEnum;
import org.springblade.modules.lankegroup.contractManagement.word.ContractMainWord;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 销售合同转化成map
 *
 * <AUTHOR> Liu
 * @since 2024年03月07日 10:33
 **/
@Slf4j
public class ReplaceParamToWordUtil {


//    /**
//     * 销售合同转化成map
//     *
//     * @param entity 销售合同对象
//     * @return Map<String, String> 用于替换模板标签的Map
//     * @throws Exception
//     */
//    public static Map<String, Object> toMap(ContractMain entity) {
//        if (entity == null) {
//            entity = new ContractMain();
//        }
//        HashMap<String, Object> targetMap = new HashMap<>();
//        try {
//            // 转化
//            ContractMainWord tragetWordEntity = BeanUtil.copyProperties(entity, ContractMainWord.class);
//            // 完善字段
//            // 甲方是用 客户还是机构
//            boolean isCustomer = SignMethodEnum.CUSTOMER_SIGN.getDesc().equals(tragetWordEntity.getSignMethod());
//            targetMap.put("partyAName", isCustomer ? tragetWordEntity.getCustomerName() : tragetWordEntity.getCustomerContactName());
//            targetMap.put("partyAIsCustomer", isCustomer);
//            targetMap.put("partyAIsCustomerContact", !isCustomer);
//
//            // 签约时间
//            if (Func.isNotEmpty(tragetWordEntity.getSignDate())) {
//                tragetWordEntity.setSignDateStr(DateUtil.format(tragetWordEntity.getSignDate(), DatePattern.CHINESE_DATE_PATTERN));
//            }
//            // 截止日期、交货日期
//            if (Func.isNotEmpty(tragetWordEntity.getEndDate())) {
//                tragetWordEntity.setEndDateStr(DateUtil.format(tragetWordEntity.getEndDate(), DatePattern.NORM_DATE_PATTERN));
//            }
//            handleXTxxFieldV2(tragetWordEntity, targetMap);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return targetMap;
//    }


    /**
     * 销售合同--实验子表转化成map
     *
     * @param targetWordEntity 销售合同-实验子表对象
     * @return Map<String, String> 用于替换模板标签的Map
     * @throws Exception
     */
    public static <T> Map<String, Object> toMapV2(T targetWordEntity,  Class<T> clazz) {
        if (targetWordEntity == null) {
            targetWordEntity = BeanUtil.newInstance(clazz);
        }
        HashMap<String, Object> targetMap = new HashMap<>();
        try {
            handleXTxxFieldV2(targetWordEntity, targetMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return targetMap;
    }

//    /**
//     * 处理个别参数
//     */
//    private static void handleDwxxMap(HashMap<String, String> dwxxMap) {
//        if (dwxxMap.containsKey("xt_kssl") && Func.isNotEmpty(dwxxMap.get("xt_kssl")) && Integer.parseInt(dwxxMap.get("xt_kssl")) > 0) {
//            if (dwxxMap.containsKey("xt_fwfw2") && Func.isNotEmpty(dwxxMap.get("xt_fwfw2")) && ContractToWordConstant.CHECK_BOX_CHANGE.equals(dwxxMap.get("xt_fwfw2"))) {
//                dwxxMap.put("xt_kssl1", dwxxMap.get("xt_kssl"));
//            }
//            if (dwxxMap.containsKey("xt_fwfw4") && Func.isNotEmpty(dwxxMap.get("xt_fwfw4")) && ContractToWordConstant.CHECK_BOX_CHANGE.equals(dwxxMap.get("xt_fwfw4"))) {
//                dwxxMap.put("xt_kssl2", dwxxMap.get("xt_kssl"));
//            }
//        }
//    }
//
//    private static void handleXTxxField(ContractMainWord tragetWordEntity, HashMap<String, Object> dwxxMap) {
//        // 甲方是用 客户还是机构
//        boolean isCustomer = SignMethodEnum.CUSTOMER_SIGN.getDesc().equals(tragetWordEntity.getSignMethod());
//        dwxxMap.put("partyAName", isCustomer ? tragetWordEntity.getCustomerName() : tragetWordEntity.getCustomerContactName());
//        dwxxMap.put("partyAIsCustomer", isCustomer);
//        dwxxMap.put("partyAIsCustomerContact", !isCustomer);
//
//
////        String xtmc = tragetWordEntity.getXtmc();
//        Class<? extends ContractMainWord> xtxxWordClass = tragetWordEntity.getClass();
//        Field[] fields = xtxxWordClass.getDeclaredFields();
//        for (Field field : fields) {
//            field.setAccessible(true);
//            FieldToWord fieldAnnotation = field.getAnnotation(FieldToWord.class);
//            if (fieldAnnotation == null) {
//                continue;
//            }
//
//            String fieldName = field.getName();
//            Object fieldValueObj = null;
//            try {
//                fieldValueObj = field.get(tragetWordEntity);
//            } catch (IllegalAccessException e) {
//                e.printStackTrace();
//            }
//            boolean emptyFieldValueObj = Func.isEmpty(fieldValueObj);
//            String fieldValue = emptyFieldValueObj ? StringPool.EMPTY : String.valueOf(fieldValueObj);
//            String split = fieldAnnotation.split();
//            int splitSum = fieldAnnotation.splitSum();
//            String[] fieldNameArray = fieldAnnotation.fieldName();
//            String[] attachNameArray = fieldAnnotation.attachName();
//
//            // 获取类型
//            switch (fieldAnnotation.type()) {
//                case ContractToWordConstant.FIELD:
//                    dwxxMap.put(fieldName, fieldValue);
//                    break;
////                case ContractToWordConstant.ENUMS:
////                    handleEnumsCore(getEnumIndexList(fieldAnnotation.enumClass()), dwxxMap, emptyFieldValueObj, fieldName, fieldValue, fieldNameArray, attachNameArray, dwmc, xtmc);
////                    break;
//                case ContractToWordConstant.SPLIT:
//                    for (int i = 0; i < splitSum; i++) {
//                        try {
//                            dwxxMap.put(fieldName + (i + 1), emptyFieldValueObj ?
//                                    fieldValue : (fieldValue.split(split).length == splitSum ? fieldValue.split(split)[i] : StringPool.EMPTY));
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                    }
////                    break;
//                case ContractToWordConstant.BOOK:
//                    break;
//                default:
//                    break;
//            }
//        }
//    }

    private static <T> void handleXTxxFieldV2(T tragetWordEntity, HashMap<String, Object> targetMap) {
        Class<?> xtxxWordClass = tragetWordEntity.getClass();
        Field[] fields = xtxxWordClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            FieldToWord fieldAnnotation = field.getAnnotation(FieldToWord.class);
            if (fieldAnnotation == null) {
                continue;
            }

            String fieldName = field.getName();
            Object fieldValueObj = null;
            try {
                fieldValueObj = field.get(tragetWordEntity);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            boolean emptyFieldValueObj = Func.isEmpty(fieldValueObj);
            String fieldValue = emptyFieldValueObj ? StringPool.EMPTY : String.valueOf(fieldValueObj);
            String split = fieldAnnotation.split();
            int splitSum = fieldAnnotation.splitSum();
            String[] fieldNameArray = fieldAnnotation.fieldName();
            String[] attachNameArray = fieldAnnotation.attachName();

            // 获取类型
            switch (fieldAnnotation.type()) {
                case ContractToWordConstant.FIELD:
                    targetMap.put(fieldName, fieldValue);
                    break;
//                case ContractToWordConstant.ENUMS:
//                    handleEnumsCore(getEnumIndexList(fieldAnnotation.enumClass()), dwxxMap, emptyFieldValueObj, fieldName, fieldValue, fieldNameArray, attachNameArray, dwmc, xtmc);
//                    break;
                case ContractToWordConstant.SPLIT:
                    for (int i = 0; i < splitSum; i++) {
                        try {
                            targetMap.put(fieldName + (i + 1), emptyFieldValueObj ?
                                    fieldValue : (fieldValue.split(split).length == splitSum ? fieldValue.split(split)[i] : StringPool.EMPTY));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
//                    break;
                case ContractToWordConstant.BOOK:
                    break;
                default:
                    break;
            }
        }
    }
//
//    private static String getAttachmentName(String attachName, String dwmc, String xtmc) {
//        if (Func.isNotBlank(attachName)) {
//            if (attachName.indexOf(ContractToWordConstant.BOOK_LABEL_DWMC) != -1) {
//                attachName = attachName.replace(ContractToWordConstant.BOOK_LABEL_DWMC, Func.isNotBlank(dwmc) ? dwmc : StringPool.EMPTY);
//            }
//            if (attachName.indexOf(ContractToWordConstant.BOOK_LABEL_XTMC) != -1) {
//                attachName = attachName.replace(ContractToWordConstant.BOOK_LABEL_XTMC, Func.isNotBlank(xtmc) ? xtmc : StringPool.EMPTY);
//            }
//        }
//        return attachName;
//    }
//
//    private static List<Integer> getEnumIndexList(Class<? extends Enum> enumClass) {
//        Object invoke = null;
//        try {
//            invoke = enumClass.getMethod("getEnumIndexList").invoke(null);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return invoke == null ? null : (List<Integer>) invoke;
//    }
//
//    private static void handleEnumsCore(List<Integer> enumIndexList, HashMap<String, String> dwxxMap,
//                                        boolean emptyFieldValueObj, String fieldName, String fieldValue,
//                                        String[] fieldNameArray, String[] attachNameArray,
//                                        String dwmc, String xtmc) {
//        if (CollectionUtil.isNotEmpty(enumIndexList)) {
//            for (int i = 0; i < enumIndexList.size(); i++) {
//                dwxxMap.put(ContractToWordConstant.FIELD_PREFIX + fieldName + (i + 1), emptyFieldValueObj ?
//                        ContractToWordConstant.CHECK_BOX_NO_CHANGE : getCheckBoxString(enumIndexList.get(i), Func.toIntList(fieldValue)));
//            }
//            // 有无附件选择【有】且附件相关参数有值，就创建对应键值对
//            if (fieldNameArray != null && fieldNameArray.length > 0 && YesOrNoEnum.YES.getIndex().toString().equals(fieldValue)) {
//                for (int i = 0; i < fieldNameArray.length; i++) {
//                    dwxxMap.put(ContractToWordConstant.FIELD_PREFIX + fieldNameArray[i], ("xtaqzzjgjglzdmce".equals(fieldNameArray[i]) ? "、" : StringPool.EMPTY) + ContractToWordConstant.BOOK_TITLE_LEFT + getAttachmentName(attachNameArray[i], dwmc, xtmc) + ContractToWordConstant.BOOK_TITLE_RIGHT);
//                }
//            }
//        }
//    }
//
//    private static String getCheckBoxString(Integer enumValue, List<Integer> params) {
//        return (CollectionUtil.isNotEmpty(params) && params.contains(enumValue)) ? ContractToWordConstant.CHECK_BOX_CHANGE : ContractToWordConstant.CHECK_BOX_NO_CHANGE;
//    }
}
