package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SystemMessageEnum {

    SYSTEM_MSG_TYPE("system",0), // 系统公告
    SUM_MSG_TYPE("collect",1),// 收藏
    ERROR_MSG_TYPE("error",2), // 违规
    LOG_ERROR_MSG_TYPE("errorLog",2), // 违规
    INVOICE_ERROR_MSG_TYPE("errorInvoice",2), // 违规
    DONE_ERROR_MSG_TYPE("errorDone",2), // 违规
    COLLECT_ERROR_MSG_TYPE("errorCollect",2), // 违规
    SUM_ERROR_MSG_TYPE("errorSum",2), // 违规
    CALL_ERROR_MSG_TYPE("errorCall",2), // 违规
    PROJECT_ERROR_MSG_TYPE("errorProject",2), // 违规
    CONTRACT_ERROR_MSG_TYPE("errorContract",2), // 违规
    PERSON_ERROR_MSG_TYPE("errorPerson",2), // 违规
    SYSTEM_ERROR_MSG_TYPE("errorSystem",2), // 违规
    PERSON_MSG_TYPE("person",3), // 联系人
    INVOICE_MSG_TYPE("invoice",4), // 开票
    CONTRACT_MSG_TYPE("contract",5), // 合同
    PROJECT_MSG_TYPE("project",6), // 项目
    CALL_MSG_TYPE("call",7), // 拜访
    LOG_MSG_TYPE("log",8), // 日志
    DONE_MSG_TYPE("done",9),// 金蝶待办
    COLLECT_MSG_TYPE("sum",10), // 统一消息提醒
    OPPOTRUNITY_MSG_TYPE("opportunity",11),//项目机会消息提醒
    PROJECT_COLLABORATION_TASK_TYPE("pct", 12),// 项目协作任务
    PROJECT_COOPERATION_TASK_TYPE("biddingcooperation", 12),// 项目招标协作任务
    PROJECT_PROGRESS_TEAM_ADMIN_TYPE("ppta", 13),// 项目进度阶段负责人，进度更新
    PROJECT_PROGRESS_TEAM_NOT_ADMIN_TYPE("pptna", 14),// 项目进度阶段参与人，进度更新
    PROJECT_SUCCESS_CLOSE_TYPE("psc", 15),// 项目成功关闭
    PROJECT_FAIL_CLOSE_TYPE("pfc", 16),// 项目失败关闭
    PROJECT_INSERT_PRINCIPAL_TYPE("pip", 17),// 项目添加业务负责人
    PROJECT_INSERT_TEAM_ADMIN_TYPE("pita", 18),// 项目添加阶段负责人
    PROJECT_INSERT_TEAM_NOT_ADMIN_TYPE("pitnt", 19),// 项目添加阶段参与人
    CHANNEL_MSG_TYPE("channel",20), // 渠道管理
    BADJXMYWFZR_MSG_TYPE("badjxmywfzr", 21), // 备案登记项目业务负责人
    BADJDWXXCJR_MSG_TYPE("badjdwxxcjr", 22), // 备案登记单位信息创建人
//    版本管理
    VERSION_MSG_TYPE("bbgl",0),
//    共享联系人申请
    SHARE_MSG_TYPE("gxlxrsq",3),
    SEAL("yzsq",23),
    TASK("rwjf",24),
    CUSTOMER("customer",25),
    ;

    final String key;
    final Integer value;

    public static SystemMessageEnum of(String key) {
        if (key == null) {
            return null;
        }
        SystemMessageEnum[] values = values();
        for (SystemMessageEnum msgEnum : values) {
            if (msgEnum.key.equals(key)) {
                return msgEnum;
            }
        }
        return null;
    }

}
