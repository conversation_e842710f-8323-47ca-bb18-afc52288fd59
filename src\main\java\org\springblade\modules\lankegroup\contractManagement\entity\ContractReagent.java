/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 试剂合同子表实体类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@TableName("cm_contract_reagent")
@EqualsAndHashCode(callSuper = true)
public class ContractReagent extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 合同主表ID
     */
    private Long mainId;
    /**
     * 交货期
     */
    @ApiModelProperty("交货期")
    private String deliveryDateStr;
    /**
     * 发票类型 全电增值税专票, 全电增值税普票
     */
    @ApiModelProperty("发票类型 全电增值税专票, 全电增值税普票")
    private String invoiceCategory;
    /**
     * 开票单位名称
     */
    @ApiModelProperty("开票单位名称")
    private String invoiceCompanyName;


}
