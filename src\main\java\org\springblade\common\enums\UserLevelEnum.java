package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用户级别枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserLevelEnum {

	/**
	 * 高层
	 */
	高层(1, "高层"),
	/**
	 * 财务
	 */
	财务(2, "财务"),
	/**
	 * 部门领导
	 */
	部门领导(3, "部门领导"),
	/**
	 * 普通员工
	 */
	普通员工(4, "普通员工");

	/**
	 * 代码
	 */
	private final Integer code;
	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 通过代码获取枚举
	 *
	 * @param code 代码
	 * @return 用户级别枚举
	 */
	public static UserLevelEnum getByCode(Integer code) {
		if (code == null) {
			return null;
		}
		return Arrays.stream(values())
			.filter(level -> level.getCode().equals(code))
			.findFirst()
			.orElse(null);
	}

	/**
	 * 通过代码获取描述
	 *
	 * @param code 代码
	 * @return 描述
	 */
	public static String getDescByCode(Integer code) {
		UserLevelEnum userLevel = getByCode(code);
		return userLevel == null ? null : userLevel.getDesc();
	}
}
