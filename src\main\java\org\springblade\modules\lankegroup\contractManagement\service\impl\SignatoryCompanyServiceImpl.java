/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.contractManagement.entity.SignatoryCompany;
import org.springblade.modules.lankegroup.contractManagement.mapper.SignatoryCompanyMapper;
import org.springblade.modules.lankegroup.contractManagement.service.ISignatoryCompanyService;
import org.springblade.modules.lankegroup.contractManagement.vo.SignatoryCompanyVO;
import org.springframework.stereotype.Service;

/**
 * 签约公司表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class SignatoryCompanyServiceImpl extends BaseServiceImpl<SignatoryCompanyMapper, SignatoryCompany> implements ISignatoryCompanyService {

    @Override
    public IPage<SignatoryCompanyVO> selectSignatoryCompanyPage(IPage<SignatoryCompanyVO> page, SignatoryCompanyVO signatoryCompany) {
        return page.setRecords(baseMapper.selectSignatoryCompanyPage(page, signatoryCompany));
    }

    @Override
    public boolean disableEnablement(SignatoryCompany signatoryCompany) {
        SignatoryCompany byId = getById(signatoryCompany.getId());
        if (byId == null) {
            throw new ServiceException("签约公司不存在");
        }
        // 状态(0:禁用 1:启用)
        Integer status = byId.getStatus();
        if (status == null) {
            throw new ServiceException("签约公司禁用启用状态不能为空");
        }
        return update(Wrappers.lambdaUpdate(SignatoryCompany.class)
                .set(SignatoryCompany::getStatus, status == 0 ? 1 : 0)
                .eq(SignatoryCompany::getId, signatoryCompany.getId()));
    }

    @Override
    public boolean saveModel(SignatoryCompany signatoryCompany) {
        signatoryCompany.setTaxNumber(signatoryCompany.getTaxNumber().trim());
        Integer count = baseMapper.getCountByTaxNumber(signatoryCompany.getTaxNumber(), null);
        if (count > 0) {
            throw new ServiceException("该纳税登记号已存在，请勿重复创建");
        }
        Integer count2 = baseMapper.getCountBySortOrder(signatoryCompany.getSortOrder(), null);
        if (count2 > 0) {
            throw new ServiceException("该排序已存在，请重新输入");
        }
        return save(signatoryCompany);
    }

    @Override
    public boolean updateModel(SignatoryCompany signatoryCompany) {
        signatoryCompany.setTaxNumber(signatoryCompany.getTaxNumber().trim());
        Integer count = baseMapper.getCountByTaxNumber(signatoryCompany.getTaxNumber(), signatoryCompany.getId());
        if (count > 0) {
            throw new ServiceException("该纳税登记号已存在，请勿重复创建");
        }
        Integer count2 = baseMapper.getCountBySortOrder(signatoryCompany.getSortOrder(), signatoryCompany.getId());
        if (count2 > 0) {
            throw new ServiceException("该排序已存在，请重新输入");
        }
        return updateById(signatoryCompany);
    }
}
