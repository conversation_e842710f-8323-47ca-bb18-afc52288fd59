/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.auth.entity.RoleDept;
import org.springblade.modules.auth.vo.RoleDeptVO;
import java.util.Objects;

/**
 * 角色与部门的对照表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public class RoleDeptWrapper extends BaseEntityWrapper<RoleDept, RoleDeptVO>  {

	public static RoleDeptWrapper build() {
		return new RoleDeptWrapper();
 	}

	@Override
	public RoleDeptVO entityVO(RoleDept roleDept) {
		RoleDeptVO roleDeptVO = Objects.requireNonNull(BeanUtil.copy(roleDept, RoleDeptVO.class));

		//User createUser = UserCache.getUser(roleDept.getCreateUser());
		//User updateUser = UserCache.getUser(roleDept.getUpdateUser());
		//roleDeptVO.setCreateUserName(createUser.getName());
		//roleDeptVO.setUpdateUserName(updateUser.getName());

		return roleDeptVO;
	}

}
