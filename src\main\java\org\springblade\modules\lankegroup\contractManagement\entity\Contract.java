/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NullSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.ForUpdate;
import org.springblade.common.utils.Update;
import org.springblade.core.mp.base.BaseEntity;

import java.time.LocalDate;
import java.util.Date;

/**
 * 合同管理表实体类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@TableName("blade_contract")
@EqualsAndHashCode(callSuper = true)
public class Contract extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 金蝶主键
     */
    private String kdFid;
    /**
     * 金蝶编码
     */
    @Update(fieldName = "金蝶编码")
    private String kdCode;
    /**
     * 项目id
     */
    @Update(fieldName = "项目id")
    private Long projectId;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目名称
     */
    @Update(fieldName = "项目名称")
    private String projectName;
    /**
     * 项目类型id
     * 金蝶分组id
     */
    @Update(fieldName = "项目类型")
    private Long projectTypeId;
    /**
     * 项目类型名称
     */
    @Update(fieldName = "项目类型名称")
    private String projectTypeName;
    /**
     * 合同编号
     */
    @Update(fieldName = "合同编号")
    private String code;
    /**
     * 合同名称
     */
    @Update(fieldName = "合同名称")
    private String name;
    /**
     * 合同金额
     */
    @ForUpdate(fieldName = "合同金额")
    @Update(fieldName = "合同金额")
    private String contractAmount;
    /**
     * 签订公司（本公司所有组织）
     */
    @Update(fieldName = "签订公司id")
    private Long partyBUnitId;
    /**
     * 签订公司编码
     */
    private String partyBUnitCode;
    /**
     * 签订公司名称
     */
    @Update(fieldName = "签订公司名称")
    private String partyBUnitName;
    /**
     * 用章类型（用逗号隔开）
     */
    @Update(fieldName = "用章类型")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String useChapterTypeIds;
    /**
     * 用章类型名称（用逗号隔开）
     */
    @Update(fieldName = "用章类型名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String useChapterTypeVals;
    /**
     * 用印份数
     */
    @Update(fieldName = "用印份数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonSerialize(nullsUsing = NullSerializer.class)
    private Integer numOfCopies;
    /**
     * 附件地址
     */
    private String annex;
    /**
     * 附件名称
     */
    @Update(fieldName = "附件名称")
    private String annexName;
    /**
     * 备注
     */
    @Update(fieldName = "备注")
    private String remarks;
    /**
     * 合同归档日期
     */
    @Update(fieldName = "合同归档日期")
    private LocalDate archiveDate;
    /**
     * 甲方单位id(销售合同/其他合同)
     */
    @Update(fieldName = "甲方单位id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long signCompanyId;
    /**
     * 甲方单位编码(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String signCompanyCode;
    /**
     * 甲方单位名称(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "甲方单位名称")
    private String signCompanyName;
    /**
     * 甲方联系人id(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "甲方联系人id")
    private String signCompanyManId;
    /**
     * 甲方联系人姓名(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "甲方联系人名称")
    private String signCompanyManName;
    /**
     * 甲方联系电话(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "甲方联系人电话")
    private String signCompanyTal;
    /**
     * 业务员id(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "业务员id")
    private Long saleManId;
    /**
     * 业务员名称(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "业务员名称")
    private String saleManName;
    /**
     * 业务部门id(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long saleDeptId;
    /**
     * 业务部门名称(销售合同/其他合同)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "业务部门")
    private String saleDeptName;
    /**
     * 合同类别id(采购合同)
     */
    private Long contractCategoryId;
    /**
     * 合同类别名称(采购合同)
     */
    @Update(fieldName = "合同类别")
    private String contractCategoryName;
    /**
     * 供应商id(采购合同/渠道合同)
     */
    @Update(fieldName = "供应商id")
    private Long supplierId;
    /**
     * 供应商编码(采购合同)
     */
    private String supplierCode;
    /**
     * 供应商名称(采购合同/渠道合同)
     */
    @Update(fieldName = "供应商名称")
    private String supplierName;
    /**
     * 供应商收款账号(采购合同)
     */
    @Update(fieldName = "供应商收款账号")
    private String supplierBankCode;
    /**
     * 供应商开户行(采购合同)
     */
    @Update(fieldName = "供应商开户行")
    private String supplierBankName;
    /**
     * 付款条件(采购合同)
     */
    @Update(fieldName = "付款条件")
    private String payWay;
    /**
     * 合同类型（0销售合同/1采购合同/2其他合同/4渠道合同）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Update(fieldName = "合同类型")
    private String contractType;
    /**
     * 流程定义主键
     */
    private String processDefinitionId;
    /**
     * 流程实例主键
     */
    private String processInstanceId;
    /**
     * 当前审批人
     */
    private String taskUser;
    /**
     * 合同签订日期（采购合同）
     */
    @Update(fieldName = "合同签订日期")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String signingDate;
    /**
     * 合同起始日期（采购合同）
     */
    @Update(fieldName = "合同起始日期")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startDate;
    /**
     * 合同终止日期（采购合同）
     */
    @Update(fieldName = "合同终止日期")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String endDate;
    /**
     * 状态
     */
    @Update(fieldName = "合同状态")
    @ForUpdate(fieldName = "合同状态")
    private Integer status;
    /**
     * 部门主管（采购合同部门主管选择）
     */
//    private Long purHeadDeptId;
    // 渠道合同相关
    /**
     * 签订方式（direct直签/channel渠道）
     */
    private String signingMode;

    /**
     * 用章地点
     */
    private String useChapterAddress;
    /**
     * 销售合同（是否存在业务费）
     * true存在业务费
     * false不存在业务费
     * 此处的业务费将用于项目详情展示
     */
    private Integer isExpenses;
    /**
     * 销售合同业务费
     * 此处的业务费将用于项目详情展示
     * (项目看板统计时，只统计合同在状态1待归档，4归档审批，5已归档)
     */
    private String operateFee;

    /**
     * 合同审批通过时间
     */
    private LocalDate approveDate;
}
