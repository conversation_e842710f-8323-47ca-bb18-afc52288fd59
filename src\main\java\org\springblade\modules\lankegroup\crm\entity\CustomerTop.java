package org.springblade.modules.lankegroup.crm.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.utils.Update;
import org.springblade.modules.lankegroup.crm.vo.CustomerUseorgVO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CustomerTop {
    /**
     * 客户id
     */
   private Long  id;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户状态（A正常/B作废）
     */
    private String forbidStatus;
    private String forbidStatusName;
    /**
     * 客户负责人名称
     */
    private String chargeName;
    /**
     * 客户负责人电话
     */
//    private String chargePhone;

//    /**
//     * 使用组织id
//     */
//    private Long useOrgId;
//    /**
//     * 使用组织名称
//     */
//    private String useOrgName;
    /**
     * 使用组织列表
     */
    private List<CustomerUseorgVO> useOrgIds = new ArrayList<>();
 /**
  * 客户关联的所有合同额
  */
    private String customerContractAmount;
 /**
  * 客户关联的所有回款额
  */
    private String customerBackAmount;
 /**
  * 客户关联的所有应收账款
  */
    private String customerAccountsReceivable;

    /*
     * 开票额
     * */
    @ApiModelProperty(value = "开票额")
    private String invoiceAmount="0.00";

//    private  String  customerInvociAmount;
    /**
     * 返给前端是否可改标志位
     * 编辑、删除、禁用、启用按钮也用此控制
     */
    private String canAmend = "0";
    /**
     * 金蝶客户id
     */
    private  String kdCustomerId;
    /**
     * 增加客户分配按钮显隐
     * true显示
     * false隐藏
     * 客户于2023-09-12 改为全分配，故该分支上线日期为分割点，
     * 上线日期之前的展示分配按钮，之后的不展示
     */
    private Boolean isAssign=false;
}
