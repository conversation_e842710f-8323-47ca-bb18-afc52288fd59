# dev-db-smb-0120分支更新内容

## mysql

### 等保看板阶段表-blade_equal_protection_stage
```sql
ALTER TABLE `blade_equal_protection_stage` 
ADD COLUMN `remark` json DEFAULT NULL COMMENT '备注json  [{i=用户id，t=时间, c=内容}]' AFTER `stage_end_time`;
```


### 等保看板信息表-blade_equal_protection_kanban
```sql
ALTER TABLE `blade_equal_protection_kanban` 
ADD COLUMN `expected_end_time` datetime DEFAULT NULL COMMENT '预期结束时间' AFTER `data_source`;
ALTER TABLE `blade_equal_protection_kanban` 
ADD COLUMN `process_document_status` int DEFAULT NULL COMMENT '流程文档完成状态 0-未完成，1-已完成' AFTER `data_source`;
```

### 等保小组-blade_equal_protection_group_member
```sql
UPDATE blade_equal_protection_group_member 
SET group_name = concat( '测评', group_name );
```

### 等保看板阶段成员表-blade_equal_protection_stage_member-新增索引
```sql
CREATE INDEX equal_protection_id ON blade_equal_protection_stage_member (equal_protection_id);
```
