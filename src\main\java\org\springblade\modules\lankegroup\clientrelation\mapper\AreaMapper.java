package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.clientrelation.entity.Area;
import org.springblade.modules.lankegroup.clientrelation.vo.AreaVO;

import java.util.List;

public interface AreaMapper extends BaseMapper<Area> {

    /**
     * 区划列表
     * @param parentCode
     * @return
     */
    List<AreaVO> getAreaList(@Param("parentCode") String parentCode);

    String getName(String code);

    /**
     * 批量查询
     * @param codeList
     * @return
     */
    List<Area> getList(List<String> codeList);
}
