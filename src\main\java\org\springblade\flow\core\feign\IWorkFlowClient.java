package org.springblade.flow.core.feign;

import org.springblade.core.tool.api.R;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22 14:19
 */
public interface IWorkFlowClient {

    /**
     * 开启流程
     *
     * @param formKey       业务表单key
     * @param fromDataId    业务表单id
     * @param businessField 业务字段json
     * @param businessCategory 业务分类，可为空
     * @return Boolean
     */
    R startProcessInstance(Map<String, Object> variables, String formKey, Long fromDataId, String fromDataCode, String businessField, String businessCategory);

    /**
     * 更新流程发起人自动通过
     *
     * @param formKey    业务表单key
     * @param fromDataId 业务表单id
     * @param businessField 自定义业务字段
     * @param businessFieldIgnore businessField为空时，是否忽略自定义业务字段：true，忽略；false=不忽略，将对应字段更新为空【默认为true】
     * @return Boolean
     */
    R initiatorCompleteTask(Map<String, Object> variables, String formKey, Long fromDataId, String businessField, Boolean businessFieldIgnore);

    /**
     * 删除业务表单时，删除业务表单的运行时任务记录
     * @param formDataId
     * @return
     */
    R removeBussinessForm(String formKey, Long formDataId);


}
