package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;

@Data
public class ContractListParam {
//    页面类型（待审批1、我发起的2、已审批3、我收到的4）
    private Integer pageType;
//    合同类型（销售合同/采购合同/其他合同/渠道合同）
    private String contractType;
//    合同状态
    private String contractStatus;
//    项目类型
    private Long projectType;
//    合同筛选开始时间
    private String startTime;
//    合同筛选结束时间
    private String endTime;
//    人员/部门
    private String deptOrPerson;
//    登录人id
    private Long userId;
//    搜索框
    private String searchBox;
//   抄送
    private Integer carbonCopy=0;
//    项目id
    private Long projectId;
}
