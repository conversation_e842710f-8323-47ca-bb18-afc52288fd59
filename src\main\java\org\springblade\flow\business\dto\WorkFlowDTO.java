package org.springblade.flow.business.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.flow.core.enums.FlowCommentEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22 17:12
 */
@Data
public class WorkFlowDTO {
	private static final long serialVersionUID = 1L;

	/**
	 * 流程定义ID
	 */
	@ApiModelProperty(value = "流程实例ID")
	private String processInstanceId;

	/**
	 * 任务ID
	 */
	@ApiModelProperty(value = "任务ID")
	private String taskId;

	/**
	 * 审批意见
	 */
	@ApiModelProperty(value = "审批意见")
	private String comment;

	/**
	 * 用户id，用户区分是否是系统用户
	 */
	private String approver;

	/**
	 * 执行实例id
	 */
	private DelegateTask delegateTask;

	/**
	 * 加签列表
	 */
	private List<String> assigneeList;

	/**
	 * 加签类型
	 */
	private FlowCommentEnum addSignType;
}
