package org.springblade.common.utils;

import org.springblade.modules.lankegroup.crm.entity.Customer;
import org.springblade.modules.lankegroup.crm.mapper.CustomerIndustryInvolvedMapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerService;
import org.springblade.modules.lankegroup.customerVisit.mapper.CustomerVisitMapper;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.lankegroup.opportunity.dto.ProjectOpportunityDTO;
import org.springblade.modules.lankegroup.opportunity.entity.ProjectOpportunity;
import org.springblade.modules.lankegroup.opportunity.service.IProjectOpportunityService;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.vo.ProjectBasicVO;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


//@RestController
@Component
//@RequestMapping("/test")
public class previousProjects {

    private final CustomerVisitMapper customerVisitMapper;
    private final IUserService userService;
    private final SystemMessageService systemMessageService;
    private final ProjectBasicMapper projectBasicMapper;
    private final IProjectOpportunityService projectOpportunityService;

    private final ICustomerService customerService;
    private final CustomerIndustryInvolvedMapper customerIndustryInvolvedMapper;


    public previousProjects(CustomerVisitMapper customerVisitMapper, IUserService userService, SystemMessageService systemMessageService, ProjectBasicMapper projectBasicMapper, IProjectOpportunityService projectOpportunityService, ICustomerService customerService, CustomerIndustryInvolvedMapper customerIndustryInvolvedMapper) {
        this.customerVisitMapper = customerVisitMapper;
        this.userService = userService;
        this.systemMessageService = systemMessageService;
        this.projectBasicMapper = projectBasicMapper;
        this.projectOpportunityService = projectOpportunityService;
        this.customerService = customerService;
        this.customerIndustryInvolvedMapper = customerIndustryInvolvedMapper;
    }


    /**
     * 往年项目
     * 定时器 每天早上 10点跑
     */
//    @Scheduled(cron = "0 0 10 * * ?") //每天上午10触发
    public void timer() throws Exception {
        Map<String, String> map = new HashMap<>();
        // 获取当天日期
        LocalDate now = LocalDate.now();
//        当前时间的后60天
        LocalDate futureDate = now.plusDays(60);
//        去年当前时间的后60天
        LocalDate localDate = futureDate.minusYears(1);
//        前年当前时间的后60天
        LocalDate QnDate = futureDate.minusYears(2);
//        大前年当前时间的后60天
        LocalDate DqnDate = futureDate.minusYears(3);
        map.put("localDate", String.valueOf(localDate));
        map.put("QnDate", String.valueOf(QnDate));
        map.put("DqnDate", String.valueOf(DqnDate));
        List<ProjectBasicVO> projectBasicVOS = projectBasicMapper.pageProjectTime(map);
        for (ProjectBasicVO projectBasicVO : projectBasicVOS) {
            ProjectOpportunityDTO projectOpportunity = new ProjectOpportunityDTO();
            projectOpportunity.setOpportunity(2);
            projectOpportunity.setCustomerId(projectBasicVO.getContractingUnitId());
//            projectOpportunity.setOpportunityType(2);
            projectOpportunity.setOpportunityType(projectBasicVO.getGroupId());
//            机会时间为创建时间的年份，之前项目立项审批通过的月份和日
//            Month month = projectBasicVO.getEndTime().getMonth();
//            int dayOfMonth = projectBasicVO.getEndTime().getDayOfMonth();
//            projectOpportunity.setOpportunityTime(LocalDate.of(now.getYear(),month,dayOfMonth));
            //项目机会的机会时间为创建时间+60天
            projectOpportunity.setOpportunityTime(futureDate);
            projectOpportunity.setOpportunityContent(projectBasicVO.getProjectName());
            projectOpportunity.setProjectId(projectBasicVO.getId());
            projectOpportunity.setOpportunitySource(6);

//            增加重要程度字段，根据预计成交金额变化自动变化，0-20万包含20万，个人重要，20万-200万包含200万，部门重要，200万以上公司重要，自动赋值后支持修改
//            跑出来的往年项目的项目机会，增加一个字段预计成交金额，将往年项目的 预计合同金额 跑出来的时候，赋值在这个字段上
//            跑出来的项目机会根据预计成交金额，自动赋上重要程度
            projectOpportunity.setExpectedTransactionAmount(projectBasicVO.getExpectedContractAmount().toString());
            if (projectBasicVO.getExpectedContractAmount() < 200000) {
                projectOpportunity.setImportanceLevel("1");
                projectOpportunity.setImportanceLevelName("个人");
            } else if (projectBasicVO.getExpectedContractAmount() > 2000000) {
                projectOpportunity.setImportanceLevel("3");
                projectOpportunity.setImportanceLevelName("公司");
            } else {
                projectOpportunity.setImportanceLevel("2");
                projectOpportunity.setImportanceLevelName("部门");
            }
            //客户区域
            Customer customer = customerService.getById(projectBasicVO.getContractingUnitId());
            projectOpportunity.setProvinceCode(customer.getProvince());
            projectOpportunity.setProvinceName(customer.getProvinceName());
//            projectOpportunity.setCountiesCode(customer.getUrbanArea());
//            projectOpportunity.setCountiesName(customer.getUrbanAreaName());
            projectOpportunity.setCityCode(customer.getCity());
            projectOpportunity.setCityName(customer.getCityName());

            //客户行业
            projectOpportunity.setMarketplaceId(customerIndustryInvolvedMapper.selectIndustryByCustomerId(customer.getId()).get(0));


//            系统创建 id
            projectOpportunity.setCreateUser(1590591570426580996L);
            projectOpportunity.setCreateDept(0L);

            //（2024-01-25修改：需要判断一下之前项目的业务负责人是否在职，如果在职的话，直接将原来项目的业务负责人赋值在项目机会的负责人上）
            User user = userService.getById(projectBasicVO.getPrincipalId());
            if (user != null && user.getIsDeleted() == 0) {
                projectOpportunity.setPrincipalId(user.getId());
                projectOpportunity.setTransactionProbability(1);
                projectOpportunity.setTransactionProbabilityName("0%");
            }

            projectOpportunityService.saveProjectOpportunity(projectOpportunity);
        }
    }

    /**
     * 20240407需求：公海项目机会原来是创建时间一个月之后无人认领自动关闭，改成不自动失败放弃，只能人工手动失败放弃
     * 去掉定时器
     */
    //    @Scheduled(cron = "0 0 2 * * ?") //每天凌晨2点触发  （公海机会 待跟进状态的 按照创建日期计算，一月没有人认领或分配，状态更新为已放弃）
//    @GetMapping("/testTwo")
    public void update() {
        try {
            List<ProjectOpportunity> projectOpportunities = projectOpportunityService.selectAll();
            for (ProjectOpportunity projectOpportunity : projectOpportunities) {
                // 获取当天日期
                LocalDate now = LocalDate.now();
                Date createTime = projectOpportunity.getCreateTime();
                LocalDate localDate = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//            创建时间 往后30天的时间
                LocalDate futureDate = localDate.plusDays(30);
                int compareTo = futureDate.compareTo(now);
//            往后30天的时间 小于 当前时间  更改状态
                if (compareTo < 0) {
                    projectOpportunity.setStatus(5);
                    projectOpportunity.setUpdateTime(new Date());
                    projectOpportunity.setAbandonReason("系统自动放弃");
                    projectOpportunityService.updateById(projectOpportunity);
                }
            }
        } catch (Exception e) {
            return;
        }
    }

}
