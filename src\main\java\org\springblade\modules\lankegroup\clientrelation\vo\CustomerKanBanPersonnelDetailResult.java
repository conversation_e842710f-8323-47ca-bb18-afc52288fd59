package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;


@Data
public class CustomerKanBanPersonnelDetailResult {

    /**
     * 组员ID
     */
    private String userId;

    /**
     * 组员名称
     */
    private String userName;

    /**
     * 区域ID
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 行业ID
     */
    private String marketCode;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

}
