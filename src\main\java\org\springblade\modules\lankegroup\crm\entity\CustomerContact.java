/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import org.springblade.common.utils.Update;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户联系人表实体类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@TableName("blade_customer_contact")
@EqualsAndHashCode(callSuper = true)
public class CustomerContact extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 联系人名称
	*/
	@Update(fieldName = "姓名")
		private String contacts="";
	/**
	* 所属机构类型
	*/
	@Update(fieldName = "所属机构类型")
		private String customerType;
	/**
	* 关联客户id【现晶莱机构ID】
	*/
	@Update(fieldName = "客户")
		private String customerId;
	/**
	* 科室、专业、部门【原部门字段】
	*/
	@Update(fieldName = "部门")
		private String department;
	/**
	* 级别、学历
	*/
	@Update(fieldName = "级别")
		private String khLevel;
	/**
	* 联系方式
	*/
	@Update(fieldName = "电话")
		private String contactsInformation;

	/**
	 * 微信号
	 */
	@Update(fieldName = "微信")
		private String wechat;
	/**
	 * 邮箱
	 */
	@Update(fieldName = "邮箱")
		private String email;
	/**
	 * 来源
	 */
	@Update(fieldName = "来源")
		private String source;
	/**
	 * 类型
	 */
	@Update(fieldName = "类型")
		private String category;
//	/**
//	 * 课题组Id
//	 */
//	@Update(fieldName = "课题组")
//		private String researchGroupId;
	/**
	 * 课题组
	 */
	@Update(fieldName = "课题组")
		private String researchGroupContent;
	/**
	* 职务
	*/
	@Update(fieldName = "职务")
		private String job;
	/**
	* 重要程度
	*/
	@Update(fieldName = "重要程度")
		private String importance;
	/**
	* 负责人id（负责销售）
	*/
	@Update(fieldName = "负责销售")
		private Long principal;
	/**
	* 收件人
	*/
	@Update(fieldName = "收件人")
		private String recipient;
	/**
	* 收件地址
	*/
	@Update(fieldName = "收件地址")
		private String consigneeAddress;
	/**
	* 收件电话
	*/
	@Update(fieldName = "收件电话")
		private String consigneePhone;
	/**
	* 性别
	*/
	@Update(fieldName = "性别")
		private Integer sex;
	/**
	* 备注
	*/
	@Update(fieldName = "备注")
		private String remark;

	/**
	 * 共享联系人集合【不使用该字段，请使用ConcentUserMapper.selectSharUserIds获取】
	 */
	@Update(fieldName = "共享联系人")
	private  String shareUser;

	/**
	 * 标签（实际存储内容为二维数组）
	 */
	private  String tags;

	/**
	 * 标签（实际存储内容为二维数组）【用于比较变更】
	 */
	@Update(fieldName = "标签")
	@TableField(exist = false)
	private  String tagsForLog;
	/**
	 * 获取时间
	 */
	@Update(fieldName = "获取时间")
	private Date acquisitionTime;

}
