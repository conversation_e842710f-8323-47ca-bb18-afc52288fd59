package org.springblade.common.cache;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 通用本地缓存工具类
 * 使用本地内存缓存，支持泛型和过期时间
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class LocalCache<T> {
    
    /**
     * 本地缓存存储
     */
    private final ConcurrentHashMap<String, CacheEntry<T>> cache = new ConcurrentHashMap<>();
    
    /**
     * 默认缓存过期时间（30分钟）
     */
    private final long defaultExpireTime;
    
    /**
     * 缓存名称，用于日志标识
     */
    private final String cacheName;
    
    /**
     * 定时清理器
     */
    private final ScheduledExecutorService cleanupExecutor;
    
    /**
     * 构造函数
     *
     * @param cacheName        缓存名称
     * @param defaultExpireTime 默认过期时间（毫秒）
     * @param cleanupInterval   清理间隔（分钟）
     */
    public LocalCache(String cacheName, long defaultExpireTime, int cleanupInterval) {
        this.cacheName = cacheName;
        this.defaultExpireTime = defaultExpireTime;
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, cacheName + "-cleanup");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动定时清理任务
        this.cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredData, 
                                               cleanupInterval, cleanupInterval, TimeUnit.MINUTES);
    }
    
    /**
     * 构造函数（使用默认配置）
     *
     * @param cacheName 缓存名称
     */
    public LocalCache(String cacheName) {
        this(cacheName, 30 * 60 * 1000L, 5); // 30分钟过期，5分钟清理间隔
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry<T> {
        private final T data;
        private final long expireTime;
        
        public CacheEntry(T data, long expireTime) {
            this.data = data;
            this.expireTime = expireTime;
        }
        
        public T getData() {
            return data;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }
    
    /**
     * 缓存数据（使用默认过期时间）
     *
     * @param key  缓存键
     * @param data 数据
     */
    public void put(String key, T data) {
        put(key, data, defaultExpireTime);
    }
    
    /**
     * 缓存数据（指定过期时间）
     *
     * @param key        缓存键
     * @param data       数据
     * @param expireTime 过期时间（毫秒）
     */
    public void put(String key, T data, long expireTime) {
        try {
            long expireTimestamp = System.currentTimeMillis() + expireTime;
            CacheEntry<T> entry = new CacheEntry<>(data, expireTimestamp);
            cache.put(key, entry);
            
            log.debug("{}缓存数据成功，key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("{}缓存数据失败，key: {}", cacheName, key, e);
        }
    }
    
    /**
     * 获取缓存数据
     *
     * @param key 缓存键
     * @return 数据
     */
    public T get(String key) {
        try {
            CacheEntry<T> entry = cache.get(key);
            if (entry == null) {
                log.debug("{}未找到缓存数据，key: {}", cacheName, key);
                return null;
            }
            
            if (entry.isExpired()) {
                cache.remove(key);
                log.debug("{}缓存数据已过期，key: {}", cacheName, key);
                return null;
            }
            
            log.debug("{}获取缓存数据成功，key: {}", cacheName, key);
            return entry.getData();
        } catch (Exception e) {
            log.error("{}获取缓存数据失败，key: {}", cacheName, key, e);
            return null;
        }
    }
    
    /**
     * 获取缓存数据，如果不存在则通过loader函数加载
     *
     * @param key    缓存键
     * @param loader 数据加载函数
     * @return 数据
     */
    public T getOrLoad(String key, Function<String, T> loader) {
        T data = get(key);
        if (data != null) {
            return data;
        }
        
        try {
            data = loader.apply(key);
            if (data != null) {
                put(key, data);
            }
            return data;
        } catch (Exception e) {
            log.error("{}加载数据失败，key: {}", cacheName, key, e);
            return null;
        }
    }
    
    /**
     * 移除缓存数据
     *
     * @param key 缓存键
     */
    public void remove(String key) {
        try {
            cache.remove(key);
            log.debug("{}移除缓存数据成功，key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("{}移除缓存数据失败，key: {}", cacheName, key, e);
        }
    }
    
    /**
     * 检查缓存数据是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean contains(String key) {
        try {
            CacheEntry<T> entry = cache.get(key);
            if (entry == null) {
                return false;
            }
            
            if (entry.isExpired()) {
                cache.remove(key);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("{}检查缓存数据存在性失败，key: {}", cacheName, key, e);
            return false;
        }
    }
    
    /**
     * 清理过期数据
     */
    private void cleanupExpiredData() {
        try {
            final int[] cleanupCount = {0};
            
            cache.entrySet().removeIf(entry -> {
                if (entry.getValue().isExpired()) {
                    log.debug("{}清理过期数据，key: {}", cacheName, entry.getKey());
                    cleanupCount[0]++;
                    return true;
                }
                return false;
            });
            
            if (cleanupCount[0] > 0) {
                log.info("{}清理过期数据完成，清理数量: {}", cacheName, cleanupCount[0]);
            }
        } catch (Exception e) {
            log.error("{}清理过期数据失败", cacheName, e);
        }
    }
    
    /**
     * 清理所有缓存数据
     */
    public void clear() {
        try {
            int size = cache.size();
            cache.clear();
            log.info("{}清理所有缓存数据完成，清理数量: {}", cacheName, size);
        } catch (Exception e) {
            log.error("{}清理所有缓存数据失败", cacheName, e);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getStats() {
        try {
            long activeCount = cache.values().stream()
                    .filter(entry -> !entry.isExpired())
                    .count();
            
            return String.format("%s缓存统计: 总数=%d, 活跃数=%d", 
                               cacheName, cache.size(), activeCount);
        } catch (Exception e) {
            log.error("{}获取缓存统计信息失败", cacheName, e);
            return cacheName + "获取缓存统计信息失败";
        }
    }
    
    /**
     * 获取缓存大小
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * 获取活跃缓存数量
     */
    public long getActiveCount() {
        return cache.values().stream()
                .filter(entry -> !entry.isExpired())
                .count();
    }
    
    /**
     * 销毁缓存（关闭定时任务）
     */
    public void destroy() {
        try {
            clear();
            cleanupExecutor.shutdown();
            log.info("{}缓存销毁成功", cacheName);
        } catch (Exception e) {
            log.error("{}缓存销毁失败", cacheName, e);
        }
    }
} 