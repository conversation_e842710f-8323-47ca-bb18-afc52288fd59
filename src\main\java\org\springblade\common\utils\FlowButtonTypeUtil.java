package org.springblade.common.utils;

import lombok.AllArgsConstructor;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springframework.stereotype.Component;

/**
 * 审批流程中的按钮显示
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 16:08
 */
@Component
@AllArgsConstructor
public class FlowButtonTypeUtil {
    private final IHistoricProcessService historicProcessService;

    public Integer getButtonType(Long fromDataId, Long createUserId, Integer status) {
        //1撤回  2通过/驳回  3通过/驳回/选择科目  4什么按钮都没有  5编辑 6删除 12 撤回/通过/驳回
        Integer buttonType = 4;
        //驳回状态，只有发起人可以进行修改
        if (status == ApprovedEnum.REFUSED.getIndex() || status == ApprovedEnum.WITHDRAWED.getIndex()) {
            if (AuthUtil.getUserId().longValue() == createUserId.longValue()) {
                buttonType = 5;
            }
        }
        //未审批
        if (status == ApprovedEnum.UNAPRROVED.getIndex()) {
            HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(fromDataId);
            if (historicProcess != null) {
                if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                    buttonType = 2;
                    if (AuthUtil.getUserId().longValue() == createUserId.longValue()) {
                        buttonType = 12;
                    }
                }else{
                    if (AuthUtil.getUserId().longValue() == createUserId.longValue()) {
                        buttonType = 1;
                    }
                }
            }
        }
        //审批中
        if (status == ApprovedEnum.APPROVING.getIndex()) {
            HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(fromDataId);
            if (historicProcess != null) {
                if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                    buttonType = 2;
                }
            }
        }
        return buttonType;
    }
}
