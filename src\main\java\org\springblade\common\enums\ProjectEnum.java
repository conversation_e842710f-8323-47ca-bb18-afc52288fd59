package org.springblade.common.enums;

public enum ProjectEnum {
    网盾(2,104859L,104859l,"090008","网盾公共"),
    智慧城市(3,104878L,104878l,"200000","智慧城市公共"),
    访客(4,1712295847238246401L,226934l,"011206","2023年门大门二公共-0-王丛丛"),
    司法鉴定(5,1712296767162359809L,226933l,"000419","2023年司法鉴定公共-0-王丛丛"),
    其他(6,109120L,109120l,"99","集团公共");
    private Integer index;
    private Long projectId;
    private Long kdId;

    private String kdCode;
    private String projectName;

    ProjectEnum(Integer index, Long projectId,Long kdId, String kdCode,String projectName) {
        this.index = index;
        this.projectId = projectId;
        this.kdId = kdId;
        this.kdCode = kdCode;
        this.projectName = projectName;
    }

    public Integer getIndex() {
        return index;
    }

    public Long getProjectId() {
        return projectId;
    }
    public Long getKdId() {
        return kdId;
    }

    public String getKdCode() {
        return kdCode;
    }
    public String getProjectName() {
        return projectName;
    }

    /**
     *     根据类型index，获取金蝶项目id
     *     目前项目机会根据机会类型id返回金蝶项目id跳出库单使用
     */
    public static Long getKdFidByIndex(Integer index) {
        for (ProjectEnum projectEnum : ProjectEnum.values()) {
            if (index.equals(projectEnum.getIndex())) {
                return projectEnum.kdId;
            }
        }
        return null;
    }
}
