package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.model.ErrorResponse;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;

/**
 * 异常处理工具类
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
public class ExceptionUtil {
    
    /**
     * 构建详细的异常响应
     */
    public static ErrorResponse buildErrorResponse(Exception exception) {
        ErrorResponse errorResponse = new ErrorResponse(exception);
        
        // 设置请求信息
        setRequestInfo(errorResponse);
        
        // 生成请求ID
        errorResponse.setRequestId(generateRequestId());
        
        // 生成修复建议
        errorResponse.generateSuggestion();
        
        return errorResponse;
    }
    
    /**
     * 设置请求相关信息
     */
    private static void setRequestInfo(ErrorResponse errorResponse) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                errorResponse.setPath(request.getRequestURI());
                errorResponse.setMethod(request.getMethod());
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
    }
    
    /**
     * 生成请求ID
     */
    private static String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 获取异常的完整堆栈信息
     */
    public static String getFullStackTrace(Exception exception) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        exception.printStackTrace(pw);
        return sw.toString();
    }
    
    /**
     * 获取异常的根本原因
     */
    public static Throwable getRootCause(Throwable throwable) {
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }
    
    /**
     * 分析异常链，获取详细的异常传播路径
     */
    public static String analyzeExceptionChain(Throwable throwable) {
        StringBuilder analysis = new StringBuilder();
        analysis.append("异常链分析：\n");
        
        Throwable current = throwable;
        int level = 0;
        
        while (current != null) {
            String indent = "  ".repeat(level);
            analysis.append(String.format("%s[%d] %s: %s\n", 
                    indent, level, 
                    current.getClass().getSimpleName(),
                    current.getMessage() != null ? current.getMessage() : "无具体信息"));
            
            // 如果有堆栈信息，显示前几行关键位置
            if (current.getStackTrace() != null && current.getStackTrace().length > 0) {
                StackTraceElement firstElement = current.getStackTrace()[0];
                analysis.append(String.format("%s    -> 发生位置: %s.%s(%s:%d)\n",
                        indent,
                        firstElement.getClassName(),
                        firstElement.getMethodName(),
                        firstElement.getFileName(),
                        firstElement.getLineNumber()));
            }
            
            current = current.getCause();
            level++;
            
            // 防止无限循环
            if (level > 10) {
                analysis.append("    ... (异常链过长，已截断)\n");
                break;
            }
        }
        
        return analysis.toString();
    }
    
    /**
     * 检查异常是否由特定类型的根本原因引起
     */
    public static boolean isCausedBy(Throwable throwable, Class<? extends Throwable> causeType) {
        Throwable current = throwable;
        while (current != null) {
            if (causeType.isInstance(current)) {
                return true;
            }
            current = current.getCause();
        }
        return false;
    }
    
    /**
     * 获取异常链中第一个指定类型的异常
     */
    @SuppressWarnings("unchecked")
    public static <T extends Throwable> T findExceptionInChain(Throwable throwable, Class<T> exceptionType) {
        Throwable current = throwable;
        while (current != null) {
            if (exceptionType.isInstance(current)) {
                return (T) current;
            }
            current = current.getCause();
        }
        return null;
    }
    
    /**
     * 格式化异常信息用于日志记录
     */
    public static String formatExceptionForLog(Exception exception, String methodName, Object... params) {
        StringBuilder logMessage = new StringBuilder();
        
        // 基本信息
        logMessage.append("异常详情: ");
        logMessage.append("异常类型=").append(exception.getClass().getSimpleName());
        logMessage.append(", 异常消息=").append(exception.getMessage());
        
        // 方法信息
        if (StringUtil.isNotBlank(methodName)) {
            logMessage.append(", 发生方法=").append(methodName);
        }
        
        // 参数信息
        if (params != null && params.length > 0) {
            logMessage.append(", 方法参数=[");
            for (int i = 0; i < params.length; i++) {
                if (i > 0) logMessage.append(", ");
                logMessage.append(params[i]);
            }
            logMessage.append("]");
        }
        
        // 用户信息
        try {
            BladeUser user = AuthUtil.getUser();
            if (user != null) {
                logMessage.append(", 用户ID=").append(user.getUserId());
                logMessage.append(", 租户ID=").append(user.getTenantId());
                logMessage.append(", 用户名=").append(user.getUserName());
            }
        } catch (Exception ignored) {
            // 忽略获取用户信息异常
        }
        
        // 请求信息
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                logMessage.append(", 请求路径=").append(request.getRequestURI());
                logMessage.append(", 请求方法=").append(request.getMethod());
                logMessage.append(", 客户端IP=").append(getClientIpAddress(request));
            }
        } catch (Exception ignored) {
            // 忽略获取请求信息异常
        }
        
        // 根本原因
        Throwable rootCause = getRootCause(exception);
        if (rootCause != exception) {
            logMessage.append(", 根本原因=").append(rootCause.getClass().getSimpleName());
            logMessage.append(", 根本原因消息=").append(rootCause.getMessage());
        }
        
        return logMessage.toString();
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private static String getClientIpAddress(HttpServletRequest request) {
        String[] ipHeaders = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : ipHeaders) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip.split(",")[0];
            }
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 判断是否为开发环境
     */
    public static boolean isDevelopmentEnvironment() {
        String profile = System.getProperty("spring.profiles.active");
        return "dev".equals(profile) || "test".equals(profile);
    }
    
    /**
     * 记录详细的异常日志
     */
    public static void logDetailedException(Exception exception, String methodName, Object... params) {
        // 记录格式化的异常信息
        String formattedMessage = formatExceptionForLog(exception, methodName, params);
        log.error(formattedMessage);
        
        // 在开发环境下记录详细的异常链分析和完整堆栈
        if (isDevelopmentEnvironment()) {
            String chainAnalysis = analyzeExceptionChain(exception);
            log.error("详细异常链分析:\n{}", chainAnalysis);
//            log.error("完整堆栈信息:", exception);
        }
    }
} 