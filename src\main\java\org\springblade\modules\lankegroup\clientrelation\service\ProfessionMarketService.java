package org.springblade.modules.lankegroup.clientrelation.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionMarket;

import java.util.List;

public interface ProfessionMarketService extends BaseService<ProfessionMarket> {


    /**
     * 新增市场与区域行业关系
     * @param profession
     * @return
     */
    void saveProfessionMarket(List<ProfessionMarket> profession);

    /**
     * 删除行业列表
     * @param joinId
     * @return
     */
    void deleteProfessionMarket(String joinId);

    /**
     * 根据关联ID查询行业数据
     * @param ids
     * @return
     */
    List<Long> getProfessionMarketList(List<Long> ids);

    /**
     * 批量删除市场行业信息
     * @param ids
     */
    void deleteProfessionMarkets(List<Long> ids);


}
