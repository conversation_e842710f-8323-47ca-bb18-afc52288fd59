<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractReagentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractReagentResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ContractReagent">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="main_id" property="mainId"/>
        <result column="delivery_date_str" property="deliveryDateStr"/>
        <result column="invoice_category" property="invoiceCategory"/>
        <result column="invoice_company_name" property="invoiceCompanyName"/>
    </resultMap>


    <select id="selectContractReagentPage" resultMap="contractReagentResultMap">
        select *
        from cm_contract_reagent
        where is_deleted = 0
    </select>

</mapper>
