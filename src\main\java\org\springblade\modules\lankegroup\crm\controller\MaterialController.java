/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.common.config.UrlConstant;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.Material;
import org.springblade.modules.lankegroup.crm.vo.MaterialVO;
import org.springblade.modules.lankegroup.crm.wrapper.MaterialWrapper;
import org.springblade.modules.lankegroup.crm.service.IMaterialService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.ArrayList;
import java.util.List;

/**
 * 物料表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/material")
@Api(value = "物料表", tags = "物料表接口")
public class MaterialController extends BladeController {

    private final IMaterialService materialService;

    /**
     * 物料列表
     *
     * @param materialName 物料名称所含关键字
     * @return 相关物料列表
     */
    @ApiLog("列表")
    @GetMapping("/materialList")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表", notes = "传入materialName   billType")
    public R<List<MaterialVO>> listMaterial(String materialName, Long billTypeId) {
        return R.data(materialService.listMaterial(materialName, billTypeId));
    }

    /**
     * 自定义分页 物料表
     * materialName：物料名
     */
    @ApiLog("列表")
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入material")
    public R<IPage<MaterialVO>> page(MaterialVO material, Query query) {
        IPage<MaterialVO> pages = materialService.selectMaterialPage(Condition.getPage(query), material);
        return R.data(pages);
    }

    /**
     * 详情
     */
    @ApiLog("详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入material")
    public R<MaterialVO> detail(Long materialId) {
        Material detail = materialService.selectById(materialId);
        return R.data(MaterialWrapper.build().entityVO(detail));
    }

    /**
     * 分页 物料表
     */
    @ApiLog("分页")
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入material")
    public R<IPage<MaterialVO>> list(Material material, Query query) {
        IPage<Material> pages = materialService.page(Condition.getPage(query), Condition.getQueryWrapper(material));
        return R.data(MaterialWrapper.build().pageVO(pages));
    }


    /**
     * 新增 物料表
     */
    @ApiLog("新增")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入material")
    public R save(@Valid @RequestBody Material material) {
        return R.status(materialService.save(material));
    }

    /**
     * 修改 物料表
     */
    @ApiLog("修改")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入material")
    public R update(@Valid @RequestBody Material material) {
        return R.status(materialService.updateById(material));
    }

    /**
     * 新增或修改 物料表
     */
    @ApiLog("新增/修改")
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入material")
    public R submit(@Valid @RequestBody Material material) {
        return R.status(materialService.saveOrUpdate(material));
    }


    /**
     * 删除 物料表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(materialService.deleteLogic(Func.toLongList(ids)));
    }
    /**
     * 物料历史列表
     * @return  物料列表
     */
    @GetMapping("/materialHistoty")
    public R<List<MaterialVO>> materialHistoty() {
        List<MaterialVO> materialSimples = null;
        materialSimples = materialService.historyList();
        return R.data(materialSimples);
    }

}
