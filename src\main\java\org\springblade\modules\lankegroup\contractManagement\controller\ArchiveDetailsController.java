/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.AesUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveSystemNameMap;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveInventoryInformationVO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 归档详情 控制器
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/archive/archivedetails")
@Api(value = "归档详情", tags = "归档详情接口")
public class ArchiveDetailsController extends BladeController {

    private final IArchiveDetailsService archiveDetailsService;
    private final FlowEngineService flowEngineService;
    private final RedisTemplate redisTemplate;
    private final IContractService contractService;
    private final FlowBusinessService flowBusinessService;

    /**
     * 详情
     */
    @ApiLog("详情 归档详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入archiveDetails")
    public R<ArchiveDetailsVO> detail(Long contractId) {
        Map map = new HashMap();
        map.put("contractId", contractId);
        ArchiveDetailsVO archiveDetailsVO = archiveDetailsService.selectArchiveDetails(map);
        return R.data(archiveDetailsVO);
    }

    /**
     * 分页 归档详情
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入archiveDetails")
    public R<IPage<ArchiveDetails>> list(ArchiveDetails archiveDetails, Query query) {
        IPage<ArchiveDetails> pages = archiveDetailsService.page(Condition.getPage(query), Condition.getQueryWrapper(archiveDetails));
        return R.data(pages);
    }

    /**
     * 自定义分页 归档详情
     */
    @ApiLog("分页 归档详情")
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入archiveDetails")
    public R<IPage<ArchiveDetailsVO>> page(ArchiveDetailsVO archiveDetails, Query query) {
        IPage<ArchiveDetailsVO> pages = archiveDetailsService.selectArchiveDetailsPage(Condition.getPage(query), archiveDetails);
        return R.data(pages);
    }

    /**
     * 新增 归档详情
     */
    @ApiLog("新增 归档详情")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入archiveDetails")
    public R save(@Valid @RequestBody ArchiveDetailsVO archiveDetailsVO) {
        if (archiveDetailsVO.getName() == null || archiveDetailsVO.getName().length() > 50 || archiveDetailsVO.getName().isEmpty()) {
            return R.fail("合同名称为必填或名称过长");
        }
        if (archiveDetailsVO.getContractSigningDate() == null
                || archiveDetailsVO.getContractSigningDate().isEmpty()
                || archiveDetailsVO.getBid() == null) {
            return R.fail("请补全必填项");
        }

        // 合同清单信息，等保二级、三级系统名称校验
        boolean checkSaveSystemListName = this.checkSaveSystemListName(archiveDetailsVO);
        if (!checkSaveSystemListName) {
            return R.fail("填写的系统名称存在重复，请核对");
        }


        if (archiveDetailsVO.getNumber() == null || archiveDetailsVO.getNumber() <= 0) {
            return R.fail("留存份数为必填或不能小于0");
        }

        //        合同签订日期
        if (archiveDetailsVO.getContractSigningDate() != null) {
            if (archiveDetailsVO.getContractSigningDate().isEmpty()) {
                archiveDetailsVO.setContractSigningDate(null);
            }
        }
        //      服务起始日期
        if (archiveDetailsVO.getServiceStartDate() != null) {
            if (archiveDetailsVO.getServiceStartDate().isEmpty()) {
                archiveDetailsVO.setServiceStartDate(null);
            }
        }
        //     服务终止日期
        if (archiveDetailsVO.getServiceEndDate() != null) {
            if (archiveDetailsVO.getServiceEndDate().isEmpty()) {
                archiveDetailsVO.setServiceEndDate(null);
            }
        }
        //        出报告日期
//        if (archiveDetailsVO.getReportDate()!=null){
//            if(archiveDetailsVO.getReportDate().isEmpty()){
//                archiveDetailsVO.setReportDate(null);
//            }
//        }
        //        开标日期
        if (archiveDetailsVO.getBidOpeningDate() != null) {
            if (archiveDetailsVO.getBidOpeningDate().isEmpty()) {
                archiveDetailsVO.setBidOpeningDate(null);
            }
        }
        //        中标日期
        if (archiveDetailsVO.getBidWinningDate() != null) {
            if (archiveDetailsVO.getBidWinningDate().isEmpty()) {
                archiveDetailsVO.setBidWinningDate(null);
            }
        }
        //        领取中标通知书日期
        if (archiveDetailsVO.getNoticeDate() != null) {
            if (archiveDetailsVO.getNoticeDate().isEmpty()) {
                archiveDetailsVO.setNoticeDate(null);
            }
        }
        //        一次性预计回款 日期
        if (archiveDetailsVO.getOnceCollectionDate() != null) {
            if (archiveDetailsVO.getOnceCollectionDate().isEmpty()) {
                archiveDetailsVO.setOnceCollectionDate(null);
            }
        }
        //        渠道商id
        if (archiveDetailsVO.getChannelSupplierId() != null) {
            if (archiveDetailsVO.getChannelSupplierId().isEmpty()) {
                archiveDetailsVO.setChannelSupplierId(null);
            }
        }

        //  测试流程模板id
//        archiveDetailsVO.setProcessDefinitionId("filing:11:984a6d00-c206-11ed-bf7e-02004c4f4f50");
//        正式流程id
//        archiveDetailsVO.setProcessDefinitionId("filing:1:0fff5296-ca62-11ed-ad06-02004c4f4f50");
        // TODO 刘源兴工作流
//        String processId = flowEngineService.getProcessId("归档审批", "filing");
//        archiveDetailsVO.setProcessDefinitionId(processId);
//        List<ContractChange> contractChanges = archiveDetailsService.contractChange(archiveDetailsVO.getContractId());
//        if (contractChanges.size() != 0) {
//            for (ContractChange contractChange : contractChanges) {
//                if (contractChange.getStatus().toString().equals("0")) {
//                    return R.fail(500, "合同处于变更中无法进行归档");
//                }
//            }
//        }
//        Map map = archiveDetailsService.saveArchive(archiveDetailsVO);
//        if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().equals(""))) {
//            return R.data(map);
////            map = archiveDetailsService.approvalInPersonArchiveDetails(map);
////            if (map.get("status").toString().equals("true")) {
////                return R.data(map);
////            } else {
////                return R.fail(500, "流程无法提交，请联系管理员");
////            }
//        } else {
//            return R.fail(500, "流程保存失败");
//        }
        return R.fail(500, "流程保存失败");
    }

    /**
     * 修改 归档详情
     */
    @ApiLog("修改 归档详情")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入archiveDetails")
    public R update(@Valid @RequestBody ArchiveDetailsVO archiveDetailsVO) {
        if (archiveDetailsVO.getName() == null || archiveDetailsVO.getName().length() > 50 || archiveDetailsVO.getName().isEmpty()) {
            return R.fail("合同名称为必填或名称过长");
        }
        if (archiveDetailsVO.getContractSigningDate() == null
                || archiveDetailsVO.getContractSigningDate().isEmpty()
                || archiveDetailsVO.getBid() == null) {
            return R.fail("请补全必填项");
        }
        // 合同清单信息，等保二级、三级系统名称校验
        boolean checkSaveSystemListName = this.checkSaveSystemListName(archiveDetailsVO);
        if (!checkSaveSystemListName) {
            return R.fail("填写的系统名称存在重复，请核对");
        }

        if (archiveDetailsVO.getContractSigningDate() != null) {
            if (archiveDetailsVO.getContractSigningDate().isEmpty()) {
                archiveDetailsVO.setContractSigningDate(null);
            }
        }
        //      服务起始日期
        if (archiveDetailsVO.getServiceStartDate() != null) {
            if (archiveDetailsVO.getServiceStartDate().isEmpty()) {
                archiveDetailsVO.setServiceStartDate(null);
            }
        }
        //     服务终止日期
        if (archiveDetailsVO.getServiceEndDate() != null) {
            if (archiveDetailsVO.getServiceEndDate().isEmpty()) {
                archiveDetailsVO.setServiceEndDate(null);
            }
        }
        //        出报告日期
//        if (archiveDetailsVO.getReportDate()!=null){
//            if(archiveDetailsVO.getReportDate().isEmpty()){
//                archiveDetailsVO.setReportDate(null);
//            }
//        }
        //        开标日期
        if (archiveDetailsVO.getBidOpeningDate() != null) {
            if (archiveDetailsVO.getBidOpeningDate().isEmpty()) {
                archiveDetailsVO.setBidOpeningDate(null);
            }
        }
        //        中标日期
        if (archiveDetailsVO.getBidWinningDate() != null) {
            if (archiveDetailsVO.getBidWinningDate().isEmpty()) {
                archiveDetailsVO.setBidWinningDate(null);
            }
        }
        //        领取中标通知书日期
        if (archiveDetailsVO.getNoticeDate() != null) {
            if (archiveDetailsVO.getNoticeDate().isEmpty()) {
                archiveDetailsVO.setNoticeDate(null);
            }
        }
        //        渠道商id
        if (archiveDetailsVO.getChannelSupplierId() != null) {
            if (archiveDetailsVO.getChannelSupplierId().isEmpty()) {
                archiveDetailsVO.setChannelSupplierId(null);
            }
        }
        //        一次性预计回款 日期
        if (archiveDetailsVO.getOnceCollectionDate() != null) {
            if (archiveDetailsVO.getOnceCollectionDate().isEmpty()) {
                archiveDetailsVO.setOnceCollectionDate(null);
            }
        }
        boolean b = archiveDetailsService.updateArchive(archiveDetailsVO);
        return R.status(b);
    }

    /**
     * 新增或修改 归档详情
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入archiveDetails")
    public R submit(@Valid @RequestBody ArchiveDetails archiveDetails) {
        return R.status(archiveDetailsService.saveOrUpdate(archiveDetails));
    }


    /**
     * 删除 归档详情
     */
    @ApiLog("删除 归档详情")
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(archiveDetailsService.deleteLogic(Func.toLongList(ids)));
    }

    @ApiLog("完成任务 流程审批接口")
    @PostMapping("/completeTaskee")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "完成任务", notes = "传入流程信息")
    public R completeTaskee(@ApiParam("任务信息") @RequestBody BladeFlow flow) {
        //如果是 2 的话就是 走流程进行写入的
        Long userId = AuthUtil.getUserId();
        String vlaue = (userId.toString() + flow.getProcessInstanceId());
        Object key = redisTemplate.opsForValue().get(vlaue);
        if (key == null) {
            try {
                redisTemplate.opsForValue().set(vlaue, vlaue, 3, TimeUnit.HOURS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return R.status(false);
        }
        return R.data(key);
    }

    /**
     * 初始化合同页面
     *
     * @return 返回初始化的流程的人员审批结构
     */
    @ApiLog("初始化合同页面")
    @GetMapping("/initContractArchiveDetails")
    public R initContractArchiveDetails() {
        return R.data(archiveDetailsService.initContractArchiveDetails());
    }


    /**
     * 把之前的归档表中的 等保二级 等保三级信息 备份到 新 项目清单表中
     *
     * @param
     * @return
     */
    @GetMapping("/backups")
    public R backups() {
        return R.data(archiveDetailsService.backups());
    }

    /**
     * 提交归档时，校验合同清单中的系统名称
     *
     * @return false不通过
     */
    private boolean checkSaveSystemListName(ArchiveDetailsVO archiveDetailsVO) {
        // 别问我为啥这么多层,因为实体就是这么多层
        if (archiveDetailsVO != null) {
            List<ArchiveInventoryInformationVO> aiiList = archiveDetailsVO.getArchiveInventoryInformation();
            if (CollectionUtil.isNotEmpty(aiiList)) {
                Set<String> systemNames = new HashSet<>();
                for (ArchiveInventoryInformationVO aiiListItem : aiiList) {
                    String grade = aiiListItem.getGrade();
                    // 3是等保二级,4是等保三级
                    if (Func.isNotBlank(grade) && (grade.equals("3") || grade.equals("4"))) {
                        List<ArchiveSystemNameMap> systemNameList = aiiListItem.getSystemNameList();
                        if (CollectionUtil.isNotEmpty(systemNameList)) {
                            for (ArchiveSystemNameMap systemNameListItem : systemNameList) {
                                String systemName = systemNameListItem.getSystemName();
                                if (Func.isNotBlank(systemName)) {
                                    if (systemNames.contains(systemName)) {
                                        return false;
                                    }
                                    systemNames.add(systemName);
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    @PutMapping("rollBack")
    public R rollBack(Long id) {
        return archiveDetailsService.rollBack(id);
    }

    @PostMapping("complete-task")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "完成任务", notes = "传入流程信息")
    public R completeTask(@ApiParam("任务信息") @RequestBody BladeFlow flow) {

        // TODO 刘源兴工作流
//        Boolean task = flowBusinessService.completeTask_V2(flow);
//        if (task) {
//            if (flow.getArchiveAnnex() != null && !flow.getArchiveAnnex().equals("")) {
//                try {
//                    flow.setArchiveAnnex(AesUtil.decryptBase64(Base64Util.decode(flow.getArchiveAnnex())));
//                } catch (Exception e) {
//
//                }
//                archiveDetailsService.update(Wrappers.<ArchiveDetails>update().lambda()
//                        .set(ArchiveDetails::getArchiveAnnex, flow.getArchiveAnnex())
//                        .set(ArchiveDetails::getArchiveAnnexName, flow.getArchiveAnnexName())
//                        .eq(ArchiveDetails::getProcessInstanceId, flow.getProcessInstanceId()));
//            }
//        }
//        return R.status(task);
        return R.status(false);
    }
}
