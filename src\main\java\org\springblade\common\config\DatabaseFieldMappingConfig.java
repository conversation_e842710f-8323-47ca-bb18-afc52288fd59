package org.springblade.common.config;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库字段映射配置
 * 用于将数据库表名、字段名、索引名映射为中文描述
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 */
@Component
public class DatabaseFieldMappingConfig {
    
    /**
     * 表名中文映射
     */
    private static final Map<String, String> TABLE_NAME_MAP = new HashMap<>();
    
    /**
     * 索引名中文映射 (格式: 表名.索引名)
     */
    private static final Map<String, String> INDEX_NAME_MAP = new HashMap<>();
    
    /**
     * 字段名中文映射 (格式: 表名.字段名)
     */
    private static final Map<String, String> FIELD_NAME_MAP = new HashMap<>();
    
    static {
        initTableNameMap();
        initIndexNameMap();
        initFieldNameMap();
    }
    
    /**
     * 初始化表名映射
     */
    private static void initTableNameMap() {
        // CRM 模块
        TABLE_NAME_MAP.put("crm_expense_report_skill", "报账技巧");
        TABLE_NAME_MAP.put("crm_customer", "客户信息");
        TABLE_NAME_MAP.put("crm_customer_contact", "客户联系人");
        TABLE_NAME_MAP.put("crm_customer_care", "客户关怀");
        TABLE_NAME_MAP.put("crm_customer_visit", "客户拜访");
        
        // Blade 模块
        TABLE_NAME_MAP.put("blade_customer", "客户信息");
        TABLE_NAME_MAP.put("blade_user", "用户信息");
        TABLE_NAME_MAP.put("blade_role", "角色信息");
        TABLE_NAME_MAP.put("blade_menu", "菜单信息");
        
        // 系统模块
        TABLE_NAME_MAP.put("sys_user", "用户");
        TABLE_NAME_MAP.put("sys_role", "角色");
        TABLE_NAME_MAP.put("sys_dept", "部门");
        TABLE_NAME_MAP.put("sys_menu", "菜单");
        TABLE_NAME_MAP.put("blade_dict", "字典");
        TABLE_NAME_MAP.put("blade_dict_biz", "业务字典");
        
        // 项目模块
        TABLE_NAME_MAP.put("project_basic", "项目基本信息");
        TABLE_NAME_MAP.put("project_team", "项目团队");
        TABLE_NAME_MAP.put("project_progress", "项目进度");
        
        // 采购模块
        TABLE_NAME_MAP.put("purchase_requisition", "采购申请");
        
        // 等保模块
        TABLE_NAME_MAP.put("equal_protection_system_task", "等保系统任务");
        TABLE_NAME_MAP.put("equal_protection_project_task", "等保项目任务");
    }
    
    /**
     * 初始化索引名映射
     */
    private static void initIndexNameMap() {
        // crm_expense_report_skill 表索引
        INDEX_NAME_MAP.put("crm_expense_report_skill.uniq", "唯一标识");
        INDEX_NAME_MAP.put("crm_expense_report_skill.idx_customer_contact", "客户联系人组合");
        
        // crm_customer 表索引
        INDEX_NAME_MAP.put("crm_customer.idx_customer_name", "客户名称");
        INDEX_NAME_MAP.put("crm_customer.idx_tax_number", "税号");
        INDEX_NAME_MAP.put("crm_customer.idx_customer_code", "客户编码");
        
        // sys_user 表索引
        INDEX_NAME_MAP.put("sys_user.idx_account", "账号");
        INDEX_NAME_MAP.put("sys_user.idx_phone", "手机号");
        INDEX_NAME_MAP.put("sys_user.idx_email", "邮箱");
        
        // sys_role 表索引
        INDEX_NAME_MAP.put("sys_role.idx_role_name", "角色名称");
        INDEX_NAME_MAP.put("sys_role.idx_role_alias", "角色别名");
        
        // project_basic 表索引
        INDEX_NAME_MAP.put("project_basic.idx_project_name", "项目名称");
        INDEX_NAME_MAP.put("project_basic.idx_project_code", "项目编码");
    }
    
    /**
     * 初始化字段名映射
     */
    private static void initFieldNameMap() {
        // 通用字段
        FIELD_NAME_MAP.put("*.id", "主键ID");
        FIELD_NAME_MAP.put("*.name", "名称");
        FIELD_NAME_MAP.put("*.code", "编码");
        FIELD_NAME_MAP.put("*.account", "账号");
        FIELD_NAME_MAP.put("*.phone", "手机号");
        FIELD_NAME_MAP.put("*.email", "邮箱");
        
        // crm_expense_report_skill 表字段
        FIELD_NAME_MAP.put("crm_expense_report_skill.customer_id", "客户ID");
        FIELD_NAME_MAP.put("crm_expense_report_skill.contact_id", "联系人ID");
        FIELD_NAME_MAP.put("crm_expense_report_skill.content", "技巧内容");
        
        // crm_customer 表字段
        FIELD_NAME_MAP.put("crm_customer.customer_name", "客户名称");
        FIELD_NAME_MAP.put("crm_customer.customer_tax_number", "客户税号");
        FIELD_NAME_MAP.put("crm_customer.customer_code", "客户编码");
        
        // sys_user 表字段
        FIELD_NAME_MAP.put("sys_user.account", "用户账号");
        FIELD_NAME_MAP.put("sys_user.phone", "手机号码");
        FIELD_NAME_MAP.put("sys_user.email", "邮箱地址");
        FIELD_NAME_MAP.put("sys_user.real_name", "真实姓名");
    }
    
    /**
     * 获取表名的中文描述
     */
    public String getTableChineseName(String tableName) {
        if (tableName == null) {
            return "数据表";
        }
        
        String chineseName = TABLE_NAME_MAP.get(tableName);
        if (chineseName != null) {
            return chineseName;
        }
        
        // 根据前缀推测模块
        if (tableName.startsWith("crm_")) {
            return "CRM模块";
        } else if (tableName.startsWith("sys_")) {
            return "系统模块";
        } else if (tableName.startsWith("project_")) {
            return "项目模块";
        } else if (tableName.startsWith("purchase_")) {
            return "采购模块";
        } else if (tableName.startsWith("equal_protection_")) {
            return "等保模块";
        }
        
        return "数据表";
    }
    
    /**
     * 获取索引的中文描述
     */
    public String getIndexChineseName(String tableName, String indexName) {
        if (tableName == null || indexName == null) {
            return "相关字段";
        }
        
        String key = tableName + "." + indexName;
        String chineseName = INDEX_NAME_MAP.get(key);
        if (chineseName != null) {
            return chineseName;
        }
        
        // 通用索引名称映射
        if (indexName.contains("uniq") || indexName.contains("unique")) {
            return "唯一字段";
        } else if (indexName.contains("name")) {
            return "名称";
        } else if (indexName.contains("code")) {
            return "编码";
        } else if (indexName.contains("account")) {
            return "账号";
        } else if (indexName.contains("phone")) {
            return "手机号";
        } else if (indexName.contains("email")) {
            return "邮箱";
        } else if (indexName.contains("customer")) {
            return "客户";
        } else if (indexName.contains("contact")) {
            return "联系人";
        } else if (indexName.contains("project")) {
            return "项目";
        }
        
        return "相关字段";
    }
    
    /**
     * 获取字段的中文描述
     */
    public String getFieldChineseName(String tableName, String fieldName) {
        if (fieldName == null) {
            return "字段";
        }
        
        // 先查找具体的表.字段映射
        if (tableName != null) {
            String key = tableName + "." + fieldName;
            String chineseName = FIELD_NAME_MAP.get(key);
            if (chineseName != null) {
                return chineseName;
            }
        }
        
        // 再查找通用字段映射
        String universalKey = "*." + fieldName;
        String chineseName = FIELD_NAME_MAP.get(universalKey);
        if (chineseName != null) {
            return chineseName;
        }
        
        // 根据字段名推测
        if (fieldName.contains("name")) {
            return "名称";
        } else if (fieldName.contains("code")) {
            return "编码";
        } else if (fieldName.contains("account")) {
            return "账号";
        } else if (fieldName.contains("phone")) {
            return "手机号";
        } else if (fieldName.contains("email")) {
            return "邮箱";
        } else if (fieldName.contains("id")) {
            return "ID";
        }
        
        return "字段";
    }
    
    /**
     * 添加表名映射
     */
    public void addTableMapping(String tableName, String chineseName) {
        TABLE_NAME_MAP.put(tableName, chineseName);
    }
    
    /**
     * 添加索引映射
     */
    public void addIndexMapping(String tableName, String indexName, String chineseName) {
        INDEX_NAME_MAP.put(tableName + "." + indexName, chineseName);
    }
    
    /**
     * 添加字段映射
     */
    public void addFieldMapping(String tableName, String fieldName, String chineseName) {
        FIELD_NAME_MAP.put(tableName + "." + fieldName, chineseName);
    }
} 