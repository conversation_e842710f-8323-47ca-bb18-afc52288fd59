/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 合同管理表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface IChannelContractService extends BaseService<Contract> {

	/**
	 * 详情
	 * @param contract
	 * @return
	 */
	ContractVO tidyUpContract(Contract contract);
	/**
	 * 合同开启流程
	 * @param contract  合同实体
	 * @return
	 */
	Map startProcess(ContractVO contract);


	/**
	 * 根据id查询合同详细的基础信息
	 * @param map
	 * @return
	 */
	ContractVO selectDetail(Map map);

	/**
	 * 查询当前登录人的草稿
	 * @return
	 */
	ContractVO selectDraft(Map map);

	/**
	 * 初始化草稿
	 * @return
	 */
	R initContract();


	/**
	 * 合同草稿存储
	 * @param contract
	 * @return
	 */
	Map saveDraft(ContractVO contract);

}
