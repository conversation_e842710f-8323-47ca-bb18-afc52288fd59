package org.springblade.common.excel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.ExcelValidationUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.lankegroup.crm.excel.BaseExcelImportListener;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 通用Excel导入监听器
 * 使用泛型和BeanUtil.copyProperties实现通用的Excel导入功能
 * 支持自定义校验规则和保存逻辑
 *
 * @param <T> Excel数据模型类型
 * @param <E> 实体类型
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class GenericExcelImportListener<T, E extends BaseEntity> extends BaseExcelImportListener<T, E> {

    /**
     * 业务服务接口
     */
    private final BaseService<E> service;
    
    /**
     * 实体类型
     */
    private final Class<E> entityClass;
    
    /**
     * 自定义校验函数（可选）
     */
    private final Function<T, List<String>> customValidator;
    
    /**
     * 自定义转换函数（可选）
     */
    private final Function<T, E> customConverter;
    
    /**
     * 自定义保存函数（可选）
     */
    private final Function<E, Boolean> customSaver;

    /**
     * 构造函数 - 基础版本
     *
     * @param service 业务服务接口
     * @param entityClass 实体类型
     * @param excelModelClass Excel模型类型
     * @param response HTTP响应对象
     * @param errorFileName 错误文件名
     * @param errorSheetName 错误Sheet名
     */
    public GenericExcelImportListener(BaseService<E> service,
                                    Class<E> entityClass,
                                    Class<T> excelModelClass,
                                    HttpServletResponse response,
                                    String errorFileName,
                                    String errorSheetName) {
        super(response, errorFileName, errorSheetName, excelModelClass, false);
        this.service = service;
        this.entityClass = entityClass;
        this.customValidator = null;
        this.customConverter = null;
        this.customSaver = null;
    }

    /**
     * 构造函数 - 完整版本
     *
     * @param service 业务服务接口
     * @param entityClass 实体类型
     * @param excelModelClass Excel模型类型
     * @param response HTTP响应对象
     * @param errorFileName 错误文件名
     * @param errorSheetName 错误Sheet名
     * @param customValidator 自定义校验函数
     * @param customConverter 自定义转换函数
     * @param customSaver 自定义保存函数
     */
    public GenericExcelImportListener(BaseService<E> service,
                                    Class<E> entityClass,
                                    Class<T> excelModelClass,
                                    HttpServletResponse response,
                                    String errorFileName,
                                    String errorSheetName,
                                    Function<T, List<String>> customValidator,
                                    Function<T, E> customConverter,
                                    Function<E, Boolean> customSaver) {
        super(response, errorFileName, errorSheetName, excelModelClass, false);
        this.service = service;
        this.entityClass = entityClass;
        this.customValidator = customValidator;
        this.customConverter = customConverter;
        this.customSaver = customSaver;
    }

    @Override
    protected List<String> validateData(T data, Integer rowIndex) {
        List<String> errors = new ArrayList<>();

        // 1. 基础必填字段校验
        errors.addAll(ExcelValidationUtil.validateRequiredFields(data, rowIndex));

        // 2. 自定义校验
        if (customValidator != null) {
            try {
                List<String> customErrors = customValidator.apply(data);
                if (customErrors != null) {
                    errors.addAll(customErrors);
                }
            } catch (Exception e) {
                log.error("自定义校验异常，行号：{}", rowIndex, e);
                errors.add("自定义校验异常：" + e.getMessage());
            }
        }

        return errors;
    }

    @Override
    protected E convertToEntity(T data, Integer rowIndex) {
        try {
            E entity;
            
            // 1. 使用自定义转换函数
            if (customConverter != null) {
                entity = customConverter.apply(data);
            } else {
                // 2. 使用通用转换逻辑
                entity = entityClass.newInstance();
                
                // 使用BeanUtil.copyProperties进行属性复制
                BeanUtil.copyProperties(data, entity);
                
                // 设置系统字段
                ExcelValidationUtil.setCreateSystemFields(entity);
            }
            
            return entity;
            
        } catch (Exception e) {
            log.error("实体转换异常，行号：{}", rowIndex, e);
            return null;
        }
    }

    @Override
    protected boolean saveEntity(E entity) {
        log.debug("开始保存实体：{}", getEntityDisplayName(entity));
        
        try {
            boolean result;
            
            // 1. 使用自定义保存函数
            if (customSaver != null) {
                result = customSaver.apply(entity);
            } else {
                // 2. 使用通用保存逻辑
                result = service.save(entity);
            }
            
            if (result) {
                log.debug("保存实体成功：{}", getEntityDisplayName(entity));
            } else {
                log.warn("保存实体失败：{}", getEntityDisplayName(entity));
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("保存实体异常：{}", getEntityDisplayName(entity), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<E> batchSave(List<E> dataList) {
        log.debug("开始批量保存实体，数量：{}", dataList.size());
        
        List<E> successList = new ArrayList<>();
        
        for (E entity : dataList) {
            try {
                boolean result;
                
                // 1. 使用自定义保存函数
                if (customSaver != null) {
                    result = customSaver.apply(entity);
                } else {
                    // 2. 使用通用保存逻辑
                    result = service.save(entity);
                }
                
                if (result) {
                    successList.add(entity);
                } else {
                    log.warn("批量保存实体失败：{}", getEntityDisplayName(entity));
                }
                
            } catch (Exception e) {
                log.error("批量保存实体异常：{}", getEntityDisplayName(entity), e);
            }
        }
        
        log.debug("批量保存完成，成功：{}，失败：{}", successList.size(), dataList.size() - successList.size());
        return successList;
    }

    @Override
    protected T convertEntityToExcel(E entity, String errorMessage) {
        try {
            // 创建Excel对象实例
            T excel = excelModelClass.newInstance();
            
            // 使用BeanUtil.copyProperties进行属性复制
            BeanUtil.copyProperties(entity, excel);
            
            // 如果Excel类有错误信息字段，设置错误信息
            try {
                Field errorField = ReflectUtil.getField(excelModelClass, "errorMessage");
                if (errorField != null) {
                    ReflectUtil.setFieldValue(excel, errorField, errorMessage);
                }
            } catch (Exception e) {
                // 忽略错误信息字段设置异常
                log.debug("设置错误信息字段失败，可能Excel类没有errorMessage字段");
            }
            
            return excel;
            
        } catch (Exception e) {
            log.error("实体转换为Excel异常", e);
            return null;
        }
    }
    
    /**
     * 获取实体显示名称（用于日志）
     */
    private String getEntityDisplayName(E entity) {
        if (entity == null) {
            return "null";
        }
        
        try {
            // 尝试获取常见的显示字段
            String[] displayFields = {"name", "title", "contacts", "customerName", "supplierName"};
            
            for (String fieldName : displayFields) {
                try {
                    Field field = ReflectUtil.getField(entity.getClass(), fieldName);
                    if (field != null) {
                        Object value = ReflectUtil.getFieldValue(entity, field);
                        if (value != null && StrUtil.isNotBlank(value.toString())) {
                            return value.toString();
                        }
                    }
                } catch (Exception e) {
                    // 忽略字段获取异常
                }
            }
            
            // 如果没有找到合适的显示字段，返回ID
            if (entity.getId() != null) {
                return "ID:" + entity.getId();
            }
            
            return entity.getClass().getSimpleName();
            
        } catch (Exception e) {
            return entity.getClass().getSimpleName();
        }
    }
}