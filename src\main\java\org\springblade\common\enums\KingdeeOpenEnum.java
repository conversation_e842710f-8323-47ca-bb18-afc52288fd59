package org.springblade.common.enums;

public enum KingdeeOpenEnum {
    ENABLE("开启", "A"),
    DISABLED("关闭", "B");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private KingdeeOpenEnum(String name, String index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(String index) {
        for (KingdeeOpenEnum c : KingdeeOpenEnum.values()) {
            if (index.equalsIgnoreCase(c.getIndex())) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public String getIndex() {
        return index;
    }
}
