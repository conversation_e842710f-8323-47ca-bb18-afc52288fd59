/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.service.impl;

import org.springblade.modules.lankegroup.common.entity.Attachment;
import org.springblade.modules.lankegroup.common.vo.AttachmentVO;
import org.springblade.modules.lankegroup.common.mapper.AttachmentMapper;
import org.springblade.modules.lankegroup.common.service.IAttachmentService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.tool.utils.Func;

import java.util.List;

/**
 * 通用附件 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class AttachmentServiceImpl extends BaseServiceImpl<AttachmentMapper, Attachment> implements IAttachmentService {

	@Override
	public IPage<AttachmentVO> selectAttachmentPage(IPage<AttachmentVO> page, AttachmentVO attachment) {
		return page.setRecords(baseMapper.selectAttachmentPage(page, attachment));
	}

	@Override
	public List<Attachment> getAttachmentsByBusiness(String businessType, Long businessId) {
		LambdaQueryWrapper<Attachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Attachment::getBusinessType, businessType)
					.eq(Attachment::getBusinessId, businessId);
		return this.list(queryWrapper);
	}

	@Override
	public boolean deleteByBusiness(String businessType, Long businessId) {
		LambdaQueryWrapper<Attachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Attachment::getBusinessType, businessType)
					.eq(Attachment::getBusinessId, businessId);
		return this.remove(queryWrapper);
	}

	@Override
	public boolean saveList(String businessType, Long businessId, List<Attachment> attachmentList) {
		// 如果附件列表为空，直接删除所有旧附件
		if (Func.isEmpty(attachmentList)) {
			return this.deleteByBusiness(businessType, businessId);
		}
		
		// 先删除旧的附件
		this.deleteByBusiness(businessType, businessId);
		
		// 为每个附件设置业务类型和业务ID，并清除ID（确保新增）
		for (Attachment attachment : attachmentList) {
			attachment.setBusinessType(businessType);
			attachment.setBusinessId(businessId);
			attachment.setId(null); // 清除ID，确保新增
		}
		
		// 批量保存新的附件
		return this.saveBatch(attachmentList);
	}

}
 