package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 合同看板统计
 */
@Data
public class ContractStatisticsGroupVO {

    private String statusId;

    private String statusName;

    private Integer statusCount;

    private BigDecimal statusTotal;

    public void accumulateStatusTotal(BigDecimal additionalTotal) {
        if (this.statusTotal == null) {
            this.statusTotal = additionalTotal;
        } else {
            this.statusTotal = this.statusTotal.add(additionalTotal);
        }
    }

}
