//package org.springblade.modules.lankegroup.contractManagement.listener;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import org.flowable.engine.TaskService;
//import org.flowable.engine.delegate.TaskListener;
//import org.flowable.task.service.delegate.DelegateTask;
//import org.springblade.core.tool.utils.DigestUtil;
//import org.springblade.flow.engine.utils.SpringContextUtils;
//import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
//import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
//import org.springblade.modules.lankegroup.pro_management.service.IProjectGroupService;
//import org.springblade.modules.system.entity.User;
//import org.springblade.modules.system.service.IUserService;
//
///**
// * 销售合同项目所属分组负责人审批，通过监听器设置审批人
// */
//public class ProjectGroupPrincipalListener implements TaskListener {
//
//    private IProjectGroupService projectGroupService = SpringContextUtils.getBean(IProjectGroupService.class);
//    private IUserService userService = SpringContextUtils.getBean(IUserService.class);
//    private TaskService taskService = SpringContextUtils.getBean(TaskService.class);
//
//    private IContractService contractService = SpringContextUtils.getBean(IContractService.class);
//
//    @Override
//    public void notify(DelegateTask delegateTask) {
//        Long groupId = Long.valueOf(delegateTask.getVariable("projectType").toString());
//        Long userId = Long.valueOf(delegateTask.getVariable("createUser").toString());
//        Long contractId = Long.valueOf(delegateTask.getVariable("contractId").toString());
//
//        Long projectGroupPrincipalId = projectGroupService.getProjectGroupPrincipalId(groupId);
//        delegateTask.setAssignee(String.valueOf(projectGroupPrincipalId));
//
//        //如果项目分组负责人和申请人的直接上级是同一个人则自动审批通过
//        User createUser = userService.getById(userId);
//        if (createUser.getParentId().equals(projectGroupPrincipalId)) {
//            String processInstanceId = delegateTask.getProcessInstanceId();
//            String taskId = delegateTask.getId();
//            // 添加处理意见
//            taskService.addComment(taskId, processInstanceId, "自动通过");
//            // 自动审批通过
//            taskService.complete(taskId);
//
//
//            // TODO: 2024/7/11 更新合同表审批人,这里下一级审批人是王丛丛着急上线先写死了，后期有时间需要改成动态获取
//            contractService.update((Wrappers.<Contract>update().lambda().set(Contract::getTaskUser,"10626332229065259")).eq(Contract::getId,contractId));
//
//        }
//    }
//}
