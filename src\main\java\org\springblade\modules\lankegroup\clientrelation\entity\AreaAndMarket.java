package org.springblade.modules.lankegroup.clientrelation.entity;

import lombok.Data;

import java.util.List;

/**
 * 区划
 */
@Data
public class AreaAndMarket {

    private String areaCode;

    private String marketCode;

    private String marketName;

    private String userId;

    private String areaStr;

    private List<String> areaList;

    private Integer count;

    private String joinId;
}
