package org.springblade.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 应用凭证配置
 */
@Data
@Configuration
public class AppConfig {
//    @Value("${dingtalk.app_key}")
//    private String appKey;
//
//    @Value("${dingtalk.app_secret}")
//    private String appSecret;
//
//    @Value("${dingtalk.agent_id}")
//    private String agentId;
//
    @Value("${dingtalk.corp_id}")
    private String corpId;
//
//    @Value("${dingtalk.aes_token}")
//    private String aesToken;
//
//    @Value("${dingtalk.aes_key}")
//    private String aesKey;

    @Value("${dingtalk.digitize_interface_secret}")
    private String digitizeInterfaceSecret;
//    @Value("${dingtalk.sjhd_aes_key}")
//    private String sjhdAesKey;
//    @Value("${dingtalk.sjhd_aes_token}")
//    private String sjhdAesToken;


//    @Value("${spring.data.mongodb.database}")
//    private String mongoDB;
//
//    @Bean
//    public GridFSBucket getGridFSBucket(MongoClient mongoClient) {
//        MongoDatabase database = mongoClient.getDatabase(mongoDB);
//        return GridFSBuckets.create(database);
//    }
}
