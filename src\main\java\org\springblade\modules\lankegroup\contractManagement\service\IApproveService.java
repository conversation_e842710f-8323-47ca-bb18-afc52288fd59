/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import org.springblade.modules.lankegroup.contractManagement.entity.Approve;
import org.springblade.modules.lankegroup.contractManagement.vo.ApproveVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 流程审批记录操作表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface IApproveService extends BaseService<Approve> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param approve
	 * @return
	 */
	IPage<ApproveVO> selectApprovePage(IPage<ApproveVO> page, ApproveVO approve);

}
