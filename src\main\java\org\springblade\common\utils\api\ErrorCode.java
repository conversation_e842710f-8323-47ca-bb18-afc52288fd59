package org.springblade.common.utils.api;

import org.springblade.core.tool.api.IResultCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 11:47
 */
public enum ErrorCode implements IResultCode {
    // ==================系统错误异常，业务逻辑错误，系统不够健壮=======================//
    SYSTEM_ERROR(1001, "服务器繁忙，请稍后再试!"),
    INTERNAL_SERVER_ERROR(1002, "服务器内部错误"),

    // ==================业务错误异常，用户错误，没权限等=======================//
    BusinessError(2001, "业务异常"),
    ;

    final int code;
    final String message;

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    private ErrorCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

}
