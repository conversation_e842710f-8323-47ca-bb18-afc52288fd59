package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ContractStatisticsListVO {

    private Long conId;

    private String conName;

    private String conTotal;

    private String conStatus;

    private String userId;

    private String userName;

    private String createDateTime;

    private String idStr;

    private String nameStr;

    private String startTime;

    private String endTime;

    private String contractType;

    /**
     * 归档时间
     */
    private String examineTime;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否有变更(0有变更/1无变更/2有变更且为审批完成的变更作废)
     */
    private Integer isChanges = 1;

    /**
     * 有变更中/作废的合同不允许归档
     * 有变更中的/作废的合同true===》不允许归档
     */
    private Boolean allowArchiving = false;

    /**
     * 当前登录人是否为合同发起人
     * true为是
     * false为不是
     * 前端根据此字段控制合同归档是否要出现
     */
    private Boolean isOwner = false;

}
