package org.springblade.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.lankegroup.crm.excel.ExcelImportResult;

/**
 * Excel导入数据本地缓存工具类
 * 缓存所有导入数据（成功+失败），支持导出所有数据并标红错误数据
 * 基于通用LocalCache实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class LocalExcelErrorDataCache {
    
    /**
     * 底层缓存实现
     */
    private static final LocalCache<ExcelImportResult<?>> CACHE = 
            new LocalCache<>("Excel导入数据缓存", 30 * 60 * 1000L, 5);
    
    /**
     * 缓存导入数据（30分钟过期）
     * 包含所有数据（成功+失败）
     *
     * @param errorKey   数据缓存键
     * @param importResult 导入结果对象
     * @param <T>        数据类型
     */
    public static <T> void putErrorData(String errorKey, ExcelImportResult<T> importResult) {
        CACHE.put(errorKey, importResult);
        log.debug("缓存Excel导入数据成功，errorKey: {}, 总数据数量: {}, 成功: {}, 失败: {}", 
                 errorKey, importResult.getTotalCount(), importResult.getSuccessCount(), importResult.getFailureCount());
    }
    
    /**
     * 缓存所有导入数据（新方法名，语义更清晰）
     *
     * @param dataKey   数据缓存键
     * @param importResult 导入结果对象
     * @param <T>        数据类型
     */
    public static <T> void putImportData(String dataKey, ExcelImportResult<T> importResult) {
        putErrorData(dataKey, importResult); // 复用现有逻辑
    }
    
    /**
     * 获取导入数据（包含所有数据）
     *
     * @param errorKey 数据缓存键
     * @param <T>      数据类型
     * @return 导入结果对象
     */
    @SuppressWarnings("unchecked")
    public static <T> ExcelImportResult<T> getErrorData(String errorKey) {
        ExcelImportResult<T> result = (ExcelImportResult<T>) CACHE.get(errorKey);
        if (result != null) {
            log.debug("获取Excel导入数据成功，errorKey: {}, 总数据数量: {}, 成功: {}, 失败: {}", 
                     errorKey, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
        } else {
            log.warn("未找到Excel导入数据或数据已过期，errorKey: {}", errorKey);
        }
        return result;
    }
    
    /**
     * 获取所有导入数据（新方法名，语义更清晰）
     *
     * @param dataKey 数据缓存键
     * @param <T>      数据类型
     * @return 导入结果对象
     */
    public static <T> ExcelImportResult<T> getImportData(String dataKey) {
        return getErrorData(dataKey); // 复用现有逻辑
    }
    
    /**
     * 移除导入数据
     *
     * @param errorKey 数据缓存键
     */
    public static void removeErrorData(String errorKey) {
        CACHE.remove(errorKey);
        log.debug("移除Excel导入数据成功，errorKey: {}", errorKey);
    }
    
    /**
     * 移除导入数据（新方法名）
     *
     * @param dataKey 数据缓存键
     */
    public static void removeImportData(String dataKey) {
        removeErrorData(dataKey); // 复用现有逻辑
    }
    
    /**
     * 检查导入数据是否存在
     *
     * @param errorKey 数据缓存键
     * @return 是否存在
     */
    public static boolean hasErrorData(String errorKey) {
        return CACHE.contains(errorKey);
    }
    
    /**
     * 检查导入数据是否存在（新方法名）
     *
     * @param dataKey 数据缓存键
     * @return 是否存在
     */
    public static boolean hasImportData(String dataKey) {
        return hasErrorData(dataKey); // 复用现有逻辑
    }
    
    /**
     * 清理所有导入数据缓存
     */
    public static void clearAllErrorData() {
        CACHE.clear();
        log.info("清理所有Excel导入数据缓存完成");
    }
    
    /**
     * 清理所有导入数据缓存（新方法名）
     */
    public static void clearAllImportData() {
        clearAllErrorData(); // 复用现有逻辑
    }
    
    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return CACHE.getStats();
    }
    
    /**
     * 获取缓存大小
     */
    public static int getCacheSize() {
        return CACHE.size();
    }
    
    /**
     * 获取活跃缓存数量
     */
    public static long getActiveCacheCount() {
        return CACHE.getActiveCount();
    }
} 