package org.springblade.modules.lankegroup.clientrelation.controller;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.*;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.ProfessionDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Controls;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionService;
import org.springframework.web.bind.annotation.*;
import org.springblade.core.mp.support.Query;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 市场
 */
@AllArgsConstructor
@RestController
@RequestMapping("profession")
public class ProfessionController {

    private final ProfessionService professionService;

    private final ElasticsearchClient elasticsearchClient;

    /**
     * 新增市场
     * @param professionDTO
     * @return
     */
    @PostMapping
    public R saveProfession(@RequestBody ProfessionDTO professionDTO){
        try {
            return professionService.saveProfession(professionDTO);
        }catch (Exception e){
            e.printStackTrace();
        }
        return R.fail("新增区域市场失败");
    }

    @GetMapping
    public R getProfessionList(ProfessionDTO professionDTO,Query query){
        return professionService.getProfessionList(professionDTO, Condition.getPage(query));
    }

    /**
     * 校验区域市场及行业是否能编辑
     * @return
     */
    @PostMapping("checkMarketAndArea")
    public R checkMarketAndArea(@RequestBody ProfessionDTO professionDTO){
        return professionService.checkMarketAndArea(professionDTO);
    }

    /**
     * 编辑区域市场
     * @param professionDTO
     * @return
     */
    @PutMapping
    public R updateProfession(@RequestBody ProfessionDTO professionDTO){
        try {
            return professionService.updateProfession(professionDTO);
        }catch (Exception e){
            e.printStackTrace();
        }
        return R.fail("修改区域市场失败");
    }

    /**
     * 删除区域市场
     * @param professionDTO
     * @return
     */
    @DeleteMapping
    public R deleteProfession(@RequestBody ProfessionDTO professionDTO){
        try {
            return professionService.deleteProfession(String.valueOf(professionDTO.getId()));
        }catch (Exception e){
            e.printStackTrace();
        }
        return R.fail("删除区域市场失败");
    }

    /**
     * 新增人员区域市场选择
     * @param deptId
     * @return
     */
    @GetMapping("personnelProfessionList")
    public R personnelProfessionList(String deptId){
        if(null == deptId || "".equals(deptId)){
            return R.fail("缺少部门信息");
        }
        return professionService.getProfessionListByDept(deptId);
    }

    /**
     * 区域市场详情
     * @param id
     * @return
     */
    @GetMapping("detail")
    public R detail(Long id){
        return professionService.detailProfession(id);
    }

    /**
     * 动态
     * @return
     */
    @GetMapping("dynamic")
    public R dynamic(Long id){
        List<Controls> list = new ArrayList<>();
        BoolQuery.Builder query = new BoolQuery.Builder();
        query.filter(f -> f.term(t -> t.field("targetId").value(id)));
        SearchRequest searchRequest = SearchRequest.of(a -> a.index("ding_talk_clientrelation_log").query(b ->
                b.bool(query.build())).from(0).size(10000).sort(c -> c.field(d ->
                d.field("createTime").order(SortOrder.Desc))).trackTotalHits(t -> t.enabled(Boolean.TRUE)));
        try {
            SearchResponse<Controls> search = elasticsearchClient.search(searchRequest, Controls.class);
            search.hits().hits().stream().forEach(controlsHit -> {
                list.add(controlsHit.source());
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.data(list);
    }

    /**
     * 行业信息
     * @return
     */
    @PostMapping("industryInformation")
    public R industryInformation(@RequestBody Map map){
        return professionService.industryInformation(map);
    }

    /**
     * 区域详情
     * @param map
     * @return
     */
    @PostMapping("getAreaList")
    public R getAreaList(@RequestBody Map map){
        return professionService.getAreaList(map);
    }

    /**
     * 获取某区域市场下区划列表
     * @param map
     * @return
     */
    @PostMapping("getRegionalizationList")
    public R getRegionalizationList(@RequestBody Map map){
        return professionService.getRegionalizationList(map);
    }

    /**
     * 获取某区域市场下行业列表
     * @param map
     * @return
     */
    @PostMapping("getProfessionList")
    public R getProfessionList(@RequestBody Map map){
        return professionService.getProfessionList(map);
    }

    /**
     * 判断当前部门是否创建区域市场
     * @return
     */
    @GetMapping("getProfessionData")
    public R getProfessionData(String deptId){
        return professionService.getProfessionData(deptId);
    }

    /**
     * 根据区域市场ID查询行业列表
     * @param professionId
     * @return
     */
    @GetMapping("getProfessionByMarketList")
    public R getProfessionByMarketList(String professionId,String joinId){
        return professionService.getProfessionByMarketList(professionId,joinId);
    }
}
