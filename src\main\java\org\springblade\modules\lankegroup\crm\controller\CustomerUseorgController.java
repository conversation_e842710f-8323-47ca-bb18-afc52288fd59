/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.clientrelation.entity.Controls;
import org.springblade.modules.lankegroup.clientrelation.entity.Profession;
import org.springblade.modules.lankegroup.clientrelation.service.AreaService;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionService;
import org.springblade.modules.lankegroup.crm.entity.BatchAllocateParam;
import org.springblade.modules.lankegroup.crm.entity.CustomerUseOrgResult;
import org.springblade.modules.lankegroup.crm.service.ICustomerService;
import org.springblade.modules.lankegroup.crm.vo.CustomerVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerUseorg;
import org.springblade.modules.lankegroup.crm.vo.CustomerUseorgVO;
import org.springblade.modules.lankegroup.crm.wrapper.CustomerUseorgWrapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerUseorgService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户分配使用组织关联表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/distribution/customeruseorg")
@Api(value = "客户分配使用组织关联表", tags = "客户分配使用组织关联表接口")
public class CustomerUseorgController extends BladeController {

	private final ICustomerUseorgService customerUseorgService;
	private final ICustomerService customerService;
	private final ProfessionService professionService;
	private final AreaService areaService;

	/**
	 * 详情
	 */
	@ApiLog("详情")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerUseorg")
	public R<CustomerUseorgVO> detail(CustomerUseorg customerUseorg) {
		CustomerUseorg detail = customerUseorgService.getOne(Condition.getQueryWrapper(customerUseorg));
		return R.data(CustomerUseorgWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 客户分配使用组织关联表
	 */
	@ApiLog("列表")
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerUseorg")
	public R<IPage<CustomerUseorgVO>> list(CustomerUseorg customerUseorg, Query query) {
		IPage<CustomerUseorg> pages = customerUseorgService.page(Condition.getPage(query), Condition.getQueryWrapper(customerUseorg));
		return R.data(CustomerUseorgWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 客户分配使用组织关联表
	 */
	@ApiLog("列表")
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerUseorg")
	public R<IPage<CustomerUseorgVO>> page(CustomerUseorgVO customerUseorg, Query query) {
		IPage<CustomerUseorgVO> pages = customerUseorgService.selectCustomerUseorgPage(Condition.getPage(query), customerUseorg);
		return R.data(pages);
	}

	/**
	 * 新增 客户分配使用组织关联表
	 * 先校验该组织是否分配了该客户
	 */
//	@ApiLog("新增")
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "新增", notes = "传入customerUseorg")
//	public R save(@Valid @RequestBody CustomerUseorg customerUseorg) throws Exception {
//		R r = customerUseorgService.checkDistribution(customerUseorg);
//		if(r.isSuccess()){
//			Boolean kd = customerUseorgService.KDAllocate(customerUseorg);
//				if(kd){
//					return R.success("客户分配成功！");
//				}
//				return R.fail(500,"金蝶客户分配失败！");
//		}
//		return R.fail(500,r.getMsg());
//
//
////		Boolean save;
////		if(r.isSuccess()){
////			save = customerUseorgService.save(customerUseorg);
////			if(save){
////				Boolean kd = customerUseorgService.KDAllocate(customerUseorg);
////				if(kd){
////					return R.success("客户分配成功！");
////				}
////				return R.fail(500,"金蝶客户分配失败！");
////
////			}else{
////				return R.status(save);
////			}
////
////		}
////		return R.fail(500,r.getMsg());
//	}
	/**
	 * 新增 客户分配使用组织关联表，单个客户对应多个组织
	 * 先校验组织列表中是否包含已经分配了该客户的，已经分配了的筛出去，未分配的组织列表进行分配
	 */
	@ApiLog("新增")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerUseorg")
	public R save(@Valid @RequestBody BatchAllocateParam customerUseorgs) throws Exception {
		if(customerUseorgs.getOrgIds()!=null && customerUseorgs.getOrgIds().size()>0) {
			for (CustomerUseorg org : customerUseorgs.getOrgIds()) {
				org.setCustomerId(customerUseorgs.getCustomerId());
			}
			CustomerUseOrgResult result = customerUseorgService.checkDistribution(customerUseorgs);
			List<CustomerUseorg> orgs = result.getCustomerUseorgs();
			if (orgs != null && orgs.size() > 0) {
				Boolean kd = customerUseorgService.KDAllocate(orgs);
				if (kd) {
					if (result.getMsg() != null && (!result.getMsg().equals(""))) {
						return R.success(result.getMsg());
					}
////					客户详情获取
//					CustomerVO customer = customerService.selectById(customerUseorgs.getCustomerId());
////        客户所属区域市场匹配
////        根据客户的所属行业id，所属区域省/市/县获取客户所属区域市场id名称
//					Map map=new HashMap();
////        客户所属行业id
//					map.put("industryInvolved", customer.getIndustryInvolved());
////        客户所属区域（省/市/县）
////        省id
//					map.put("province",customer.getProvince());
////        市id
//					map.put("urbanArea",customer.getUrbanArea());
////        县id
//					map.put("city",customer.getCity());
//					List<Profession>  professions = professionService.selectByAreAndMarketForProfession(map);
//					if(professions!=null&&professions.size()>0){
//						Controls controls = new Controls();
//						controls.setContent("分配了客户");
//						controls.setUserId(String.valueOf(AuthUtil.getUserId()));
//						controls.setUserName(AuthUtil.getNickName());
//						controls.setCreateTime(new Date());
////                    客户所属区域市场写入
//						for (Profession profession : professions) {
//							controls.setTargetId(String.valueOf(profession.getId()));
//							areaService.saveControlsLog(controls);
//						}
//					}
					return R.success("客户分配成功");
				}
				return R.fail(500,"金蝶客户分配与本地分配数据不同，请联系相关开发人员");

			}
			return R.fail(500,result.getMsg());

		}
		return R.fail(500,"请选择分配组织");

	}
	/**
	 * 修改 客户分配使用组织关联表
	 */
	@ApiLog("修改")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerUseorg")
	public R update(@Valid @RequestBody CustomerUseorg customerUseorg) {
		return R.status(customerUseorgService.updateById(customerUseorg));
	}

	/**
	 * 新增或修改 客户分配使用组织关联表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerUseorg")
	public R submit(@Valid @RequestBody CustomerUseorg customerUseorg) {
		return R.status(customerUseorgService.saveOrUpdate(customerUseorg));
	}

	
	/**
	 * 删除 客户分配使用组织关联表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerUseorgService.deleteLogic(Func.toLongList(ids)));
	}

	
}
