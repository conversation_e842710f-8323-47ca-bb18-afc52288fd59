/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.kingdee.bos.webapi.sdk.RepoResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.enums.OrgEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.clientrelation.service.AreaService;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionService;
import org.springblade.modules.lankegroup.common.service.IAttachmentService;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.crm.dto.CustomerDTO;
import org.springblade.modules.lankegroup.crm.entity.BatchEnableParams;
import org.springblade.modules.lankegroup.crm.entity.Customer;
import org.springblade.modules.lankegroup.crm.excel.CustomerExcel;
import org.springblade.modules.lankegroup.crm.excel.CustomerListener;
import org.springblade.modules.lankegroup.crm.service.*;
import org.springblade.modules.lankegroup.crm.vo.CustomerContactVO;
import org.springblade.modules.lankegroup.crm.vo.CustomerUpdateVO;
import org.springblade.modules.lankegroup.crm.vo.CustomerVO;
import org.springblade.modules.lankegroup.crm.vo.CustomerDeptStatisticsVO;
import org.springblade.modules.lankegroup.crm.vo.CustomerDeptGroupVO;
import org.springblade.modules.lankegroup.crm.config.CustomerTypeConfig;
import org.springblade.modules.lankegroup.crm.wrapper.CustomerWrapper;
import org.springblade.modules.system.entity.DictBiz;
import org.springblade.modules.system.service.IDictBizService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.jl.service.ICustomerDeptService;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.ChangedUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/basic/customer")
@Api(value = "客户表", tags = "客户表接口")
public class CustomerController extends BladeController {
    /**
     * 业务类型常量
     */
    public static final String BUSINESS_TYPE = "customer";
    private final static Logger log = LoggerFactory.getLogger(CustomerController.class);

    private final ICustomerService customerService;
    private final ICustomerContactService customerContactService;
    private final ICustomerUseorgService customerUseorgService;
    private final IContractService contractService;
    private final ICustomerConnectLogService customerConnectLogService;
    private  final ICustomerChargeUserService customerChargeUserService;
    private  final ProfessionService professionService;
    private final AreaService areaService;
    private final IDictBizService dictBizService;
    private final IAttachmentService attachmentService;
    private final ICustomerDeptService customerDeptService;
    private final IUserService userService;


    /**
     * 客户联系人列表
     */
    @ApiLog("客户联系人列表")
    @GetMapping("/contactList")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入customerId")
    public R<List<CustomerContactVO>> contactList(@RequestParam Long customerId) {
        return R.data(customerContactService.selectContactList(customerId));
    }


    /**
     * 客户列表
     * 项目立项中签约单位使用+客户联系人关联客户使用
     * 根据客户名称模糊查询blade表中符合条件的数据
     * 合同管理乙方
     * <p>
     * <p>
     * 收款单客户、供应商列表增加参数orgId，根据组织id区分基础资料的使用组织
     *
     * @param customerName 客户名称所含关键字
     * @return 相关项目列表
     */
    @ApiLog("客户列表")
    @PostMapping("/customerList")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表", notes = "传入customerName")
    public R<List<CustomerVO>> listCustomer(String customerName, String type, String customerType) {
        if (type != null && !type.isEmpty()) {
//        查询一下当前登录人的历史客户记录
            return R.data(customerService.customerHistoryList());
        } else {
            Map map = new HashMap();
            if ((!customerName.isBlank()) && (!customerName.isEmpty())) {
                map.put("customerName", customerName);
                if (Func.isNotBlank(customerType)) {
                    map.put("customerType", customerType);
                }
                return R.data(customerService.customerList(map));
            } else {
                return R.data(new ArrayList<>());
            }
        }
    }

    /**
     * 通过客户id/金蝶id查看客户详情
     * 客户信息拆分后，客户详情信息
     */
    @ApiLog("客户详情")
    @GetMapping("/detailById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入customerId")
    public R<CustomerVO> detailById(@RequestParam Long customerId) {
        log.info("/basic/customer/detailById  customerId = " + customerId);
        CustomerVO detail = customerService.selectById(customerId);
        //			税号/开户行/银行账户/地址/客户电话去空
        CustomerVO customerVO = CustomerWrapper.build().entityVO(detail);
        return R.data(customerVO);
    }

    /**
     * 查看客户列表
     * 模糊查询客户列表
     * 参数：
     * 客户创建时间【开始时间】：startTime，【结束时间】：endTime
     * 客户类型：customerType：企业客户1/政府客户2
     * 客户状态：forbidStatus：A未作废，B已作废【批量启用时传D，批量禁用时传C】
     * 客户所属行业：industryInvolved
     * 公司性质：companyNature
     * customerName 客户名称关键字
     * <p>
     * <p>
     * current：当前页数
     * size：每页数量
     * useOrgId:使用组织id(作废)
     * <p>
     * 刘亚军默认显示公司所有客户信息
     *
     * 2023/09/13
     * token登录时，fightGrain判断当前登录人是否为打粮队领导或高层，为true时则在列表出现分配按钮
     */
    @ApiLog("客户列表分页")
    @PostMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入customer")
    public R<IPage<CustomerVO>> page(@RequestBody CustomerVO customer, @RequestBody Query query) {
        IPage<CustomerVO> pages = customerService.selectCustomerPage(Condition.getPage(query), customer);

        // 获取当前页的客户数据
        List<CustomerVO> customerList = pages.getRecords();
        if (customerList != null && !customerList.isEmpty()) {
            // 提取客户ID列表
            List<Long> customerIds = customerList.stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());

            // 一次性获取所有客户的CustomerDept统计数据
            Map<Long, List<CustomerDeptStatisticsVO>> customerDeptMap = customerDeptService.statisticsByCustomerIdsGrouped(customerIds);

            // 获取当前登录人及其下属用户ID列表
            List<Long> lowUserIds = userService.getLowerAndSelf(AuthUtil.getUserId());

            // 统计负责客户数量
            Long responsibleCustomerCount = customerContactService.countResponsibleCustomers(lowUserIds, customerIds);

            // 为每个客户设置对应的统计结果
            for (CustomerVO customerVO : customerList) {
                Integer customerId = customerVO.getId().intValue();

                // 先根据customerType设置默认值为0
                List<CustomerDeptStatisticsVO> customerStatistics = getDefaultStatisticsByCustomerType(customerVO.getCustomerType());

                // 如果有实际统计数据，则用实际值覆盖对应的默认值
                List<CustomerDeptStatisticsVO> actualStatistics = customerDeptMap.get(customerId);
                if (actualStatistics != null && !actualStatistics.isEmpty()) {
                    mergeActualStatistics(customerStatistics, actualStatistics);
                }

                customerVO.setCustomerDeptStatistics(customerStatistics);

                // 设置负责客户数量（所有客户显示相同的统计值）
                customerVO.setResponsibleCustomerContactCount(responsibleCustomerCount);
            }
        }

        return R.data(pages);
    }

    /**
     * 根据客户类型生成默认的CustomerDept统计值
     *
     * @param customerType 客户类型
     * @return 默认统计结果列表
     */
    private List<CustomerDeptStatisticsVO> getDefaultStatisticsByCustomerType(String customerType) {
        List<CustomerDeptStatisticsVO> defaultStatistics = new ArrayList<>();

        // 使用配置类获取对应的部门类型列表
        List<String> deptTypes = CustomerTypeConfig.getDeptTypesByCustomerType(customerType);

        // 为每个部门类型创建默认统计值（值为0）
        for (String deptType : deptTypes) {
            defaultStatistics.add(new CustomerDeptStatisticsVO(deptType, 0L));
        }

        return defaultStatistics;
    }

    /**
     * 将实际统计数据合并到默认统计数据中
     *
     * @param defaultStatistics 默认统计数据（值为0）
     * @param actualStatistics 实际统计数据
     */
    private void mergeActualStatistics(List<CustomerDeptStatisticsVO> defaultStatistics,
                                     List<CustomerDeptStatisticsVO> actualStatistics) {
        // 创建实际统计数据的Map，便于查找
        Map<String, Long> actualMap = actualStatistics.stream()
            .collect(Collectors.toMap(
                CustomerDeptStatisticsVO::getKey,
                CustomerDeptStatisticsVO::getValue
            ));

        // 用实际值覆盖默认值中对应的类型
        for (CustomerDeptStatisticsVO defaultStat : defaultStatistics) {
            if (actualMap.containsKey(defaultStat.getKey())) {
                defaultStat.setValue(actualMap.get(defaultStat.getKey()));
            }
        }
    }

    /**
     * 2023-10-25  客户列表增加：1、客户总数   2、拜访客户数  3、立项客户数   4、基本盘客户数
     * 根据客户的筛选条件，对客户数进行筛选
     * @param customer
     * @return
     */
    @ApiLog("客户列表四个数")
    @PostMapping("/statics")
    @ApiOperationSupport(order = 3)
    public R statics (@RequestBody CustomerVO customer) {
        return R.data(customerService.statics(customer));
    }
    /**
     * 新增 客户表
     * 客户先进行校验
     * 客户校验：有税号对税号进行唯一性校验，没有税号对客户名称进行唯一性校验
     * 客户保存时，客户负责人==客户创建人，不进行另外录入
     */
    @ApiLog("新增客户")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入customer")
    @Transactional(rollbackFor = Exception.class)
    public R save(@Valid @RequestBody CustomerVO customer) throws Exception {
        if(Objects.equals(customer.getCustomerTaxNumber(),"")){
            customer.setCustomerTaxNumber(null);
        }

        // 查询纳税登记号是否重复
        R checkResult = customerService.checkTaxNumber(customer.getCustomerTaxNumber());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        R r1 = customerService.saveAll(customer);
        // 2. 保存附件列表（如果传了空列表，会删除所有旧附件）
        if (customer.getAttachmentList() != null) {
            attachmentService.saveList(BUSINESS_TYPE, customer.getId(), customer.getAttachmentList());
        }
        return r1;
    }

    /**
     * bladex 客户端
     * 新增 客户表
     * 客户先进行校验
     */
    @ApiLog("新增客户")
    @PostMapping("/saveBladex")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入customer")
    public R saveBladex(@Valid @RequestBody CustomerVO customer) throws Exception {
        R r = customerService.checkCustomerBladex(customer);
        if (r.isSuccess()) {
            boolean status = false;
//           金蝶添加客户
            R kd = customerService.saveBill(customer);
            if (kd.isSuccess()) {
//               把金蝶返回的客户id存到数据库
                RepoResult result = (RepoResult) kd.getData();
                customer.setKdCustomerId(result.getResponseStatus().getSuccessEntitys().get(0).getId());
                customer.setCustomerCode(result.getResponseStatus().getSuccessEntitys().get(0).getNumber());
                customer.setCreateOrgId(Long.parseLong(OrgEnum.JT.getId()));
                status = customerService.save(customer);
//               数据库保存成功后开始分配
                if (status) {
//                   金蝶分配
                    status = customerService.allocateBladex(customer).isSuccess();
//                   金蝶分配成功后blade分配表新增数据
                    if (status) {
                        status = customerUseorgService.customerOrgSaveBladex(customer);
                    }
                }
            }
            return R.status(status);
        } else {
            return R.fail(500, r.getMsg());
        }
    }

    /**
     * 修改 客户表
     * 更新的时候，可以修改客户的负责人【该chargeUser字段数据可改】
     * 详情页面也可修改客户负责人（仅财务）+（打粮队）
     * <p>
     * 更新的时候先对数据进行唯一性校验
     * 修改过税号的，对新税号进行唯一性校验，没修改过的就对客户名称进行唯一性校验
     * <p>
     * customer.getModificeWay用来标识是否为外部仅修改负责人的操作
     * 打粮队+财务人员可直接在详情页对客户负责人进行修改
     * 修改方式 为true表示外部直接修改负责人
     */
    @ApiLog("修改客户")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入customer")
    @Transactional(rollbackFor = Exception.class)
    public R update(@Valid @RequestBody CustomerUpdateVO customer) throws Exception {
        if(Objects.equals(customer.getCustomerTaxNumber(),"")){
            customer.setCustomerTaxNumber(null);
        }
        Customer oldCustomer = customerService.getById(customer.getId());
        if (Objects.equals(oldCustomer.getCustomerStatus(),"禁用")&&Objects.equals(customer.getCustomerStatus(),"禁用")) {
            return R.fail("已禁用机构不支持修改");
        }
        if (oldCustomer.getCustomerStatus().equals("审批中")) {
            return R.fail("审批中机构不支持修改");
        }
/*        if (oldCustomer.getForbidStatus().equals("B")) {
            return R.fail(500, "已禁用客户不支持修改");
        }*/
//        R r = customerService.checkModification(customer);
//        if (r.isSuccess()) {
            if (customer.getAttachmentList() != null) {
                attachmentService.saveList(BUSINESS_TYPE, customer.getId(), customer.getAttachmentList());
            }
//        }
        return customerService.updateAll(customer);

    }

    /**
     * 详情
     */
    @ApiLog("客户详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入customer")
    public R<CustomerVO> detail(Customer customer) {
//        Customer detail = customerService.getOne(Condition.getQueryWrapper(customer));
        CustomerVO detail = customerService.selectByIdBladex(customer.getId());
        return R.data(detail);
    }

    /**
     * Bladex 分页 客户表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入customer")
    public R<IPage<CustomerVO>> list(CustomerVO customerVO, Query query) {
//        IPage<Customer> pages = customerService.page(Condition.getPage(query), Condition.getQueryWrapper(customer));
        IPage<CustomerVO> pages = customerService.listCustomer(Condition.getPage(query), customerVO);
        return R.data(pages);
    }


    /**
     * 新增或修改 客户表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入customer")
    public R submit(@Valid @RequestBody Customer customer) {
        return R.status(customerService.saveOrUpdate(customer));
    }


    /**
     * 删除 客户表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam Long ids) throws Exception {
        R remove = customerService.remove(ids);
        if (remove.isSuccess()) {
            return R.status(customerService.deleteLogic(Collections.singletonList(ids)));
        }
        return remove;
    }

    /**
     * 客户详情区分为客户头与客户详情两个部分
     * 此处为合同头
     * 合同头的三项金额仅累计当前年份的
     *
     * @param customerId
     * @return
     */
    @GetMapping("/customerTop")
    public R customerTop(Long customerId) {
        return R.data(customerService.customerTop(customerId));
    }

    /**
     * enable==>A  为启用操作
     * enable==>B  为禁用操作
     * 客户启用A/禁用B
     * 金蝶反禁用成功后，将本地客户的禁用标志反写为正常A
     * 金蝶禁用成功后，将本地客户的禁用标志反写为禁用B
     * <p>
     * 根据金蝶id查询金蝶编码，根据金蝶编码调用金蝶禁用/启用接口
     *
     * @param params 金蝶客户id（主键）+启用/禁用标志位
     * @return
     */
    @PostMapping("/enable")
    public R enable(@RequestBody BatchEnableParams params) throws Exception {
        R r = new R();
        if (params.getEnable().equals("A")) {
            r = customerService.checkEnable(params.getKdCustomerIds());
        }
        if (r.isSuccess() || params.getEnable().equals("B")) {
            Map result = customerService.KdBatchEnable(params.getKdCustomerIds(), params.getEnable());
            if (result.get("status").toString().equals("true")) {
                return R.status(true);
            } else {
                return R.fail(500, result.get("error").toString());
            }

        }
        return r;

    }
//    @GetMapping("/enable")
//    public R enable(String kdCustomerId, String enable) throws Exception {
//        R r = null;
//        if(enable.equals("A")){
//             r=customerService.checkEnable(kdCustomerId);
//        }
//        if(r.isSuccess()||enable.equals("B")){
//            Customer old = customerService.selectByKdFid(kdCustomerId);
//            Map result = customerService.KdEnableByCode(old, enable);
//            if (result.get("status").toString().equals("true")) {
//                CustomerConnectLog contractChangeLog = new CustomerConnectLog();
//                Customer newCustomer = CustomerWrapper.build().entity(customerService.selectById(old.getId()));
//                switch (enable) {
//                    case "A":
//                        old.setForbidStatus("B");
//                        contractChangeLog.setAboutDescription("启用了客户");
//                        break;
//                    case "B":
//                        old.setForbidStatus("A");
//                        contractChangeLog.setAboutDescription("禁用了客户");
//                        break;
//
//                }
//                //                                    写入记录日志表
//                String changedFields = ChangedUtil.getUpdateFields(newCustomer, old);
//                if (changedFields != null && (!changedFields.equals(""))) {
//                    contractChangeLog.setDescription(changedFields);
//                    contractChangeLog.setCustomerId(newCustomer.getId());
//                    contractChangeLog.setContactId(newCustomer.getId());
//                    contractChangeLog.setChangeTable(TableIdEnum.客户.getTableId());
//                    contractChangeLog.setChargeUser(AuthUtil.getUserId());
//                    customerConnectLogService.save(contractChangeLog);
//                }
//                return R.status(true);
//            } else {
//                return R.fail(500, result.get("error").toString());
//            }
//        }else{
//            return r;
//        }
//
//    }

    /**
     * 客户关联的所有下推为正式的项目列表
     *
     * @param customerId 客户id
     * @param query
     * @return
     */
    @GetMapping("/relevancyFormalProject")
    public R<IPage<Map>> relevancyFormalProject(Long customerId, Query query) {
        return R.data(customerService.relevancyFormalProject(Condition.getPage(query), customerId));
    }

    /**
     * 根据客户id，查寻客户关联的所有的合同
     *
     * @param customerId
     * @return
     */
    @GetMapping("/customerContractList")
    public R<IPage<ContractListResult>> customerContractList(Long customerId, Query query) {
        return R.data(contractService.customerContractList(Condition.getPage(query), customerId));
    }

    @GetMapping("/pageDetails")
    public R<CustomerVO> pageDetails(@RequestParam Long id) {
        return R.data(customerService.pageDetails(id));
    }

    /**
     * 导入客户数据
     * @param file
     * @param response
     * @throws IOException
     */
    @PostMapping("/import")
    public void importData(MultipartFile file, HttpServletResponse response) throws IOException {
        log.info("开始导入数据");
        DictBiz dictBiz = dictBizService.getOne(new LambdaQueryWrapper<DictBiz>().eq(DictBiz::getCode, "importance").eq(DictBiz::getDictValue, "普通"));
        DictBiz productType = dictBizService.getOne(new LambdaQueryWrapper<DictBiz>()
                .eq(DictBiz::getCode, "product_type").eq(DictBiz::getParentId, 0).eq(DictBiz::getDictValue, "客户产品类型"));
        CustomerListener questionImportListener = new CustomerListener(customerService, response, dictBiz.getDictKey(),productType.getId());
        EasyExcel.read(file.getInputStream(), CustomerExcel.class,questionImportListener).sheet().doRead();
    }

    /**
     * 第三方保存客户数据
     * @param customers
     * @return
     */
    @PostMapping("/saveForThird")
    public R saveForThird(@RequestBody List<CustomerExcel> customers) {
        return customerService.saveForThird(customers);
    }

    /**
     * 发起审批
     */
    @PostMapping("/initiateApproval")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "发起审批", notes = "传入customerDTO")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "机构主键" , dataType = "string", required = true),
            @ApiImplicitParam(name = "remark", value = "备注" , dataType = "string", required = false),
    })
    public R initiateApproval(@RequestBody CustomerDTO customerDTO) {
        return customerService.initiateApproval(customerDTO);
    }

    /**
     * 客户状态切换（启用/禁用）
     * 根据当前customerStatus状态自动切换：正常 <-> 禁用
     * @param customerId 客户ID
     * @return
     */
    @ApiLog("客户状态切换")
    @PostMapping("/toggleStatus")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "客户状态切换", notes = "传入customerId")
    @Transactional(rollbackFor = Exception.class)
    public R toggleStatus(@RequestParam Long customerId) {
        try {
            // 获取客户信息
            Customer customer = customerService.getById(customerId);
            if (customer == null) {
                return R.fail("客户不存在");
            }

            // 记录修改前的状态
            String oldStatus = customer.getCustomerStatus();
            Customer oldCustomer = new Customer();
            oldCustomer.setCustomerStatus(oldStatus);

            // 根据当前状态切换
            String newStatus;
            String operationDesc;
            if ("正常".equals(oldStatus)) {
                newStatus = "禁用";
                operationDesc = "禁用了客户";
            } else if ("禁用".equals(oldStatus)) {
                newStatus = "正常";
                operationDesc = "启用了客户";
            } else {
                return R.fail("当前客户状态不支持切换操作，只有'正常'和'禁用'状态的客户可以切换");
            }

            // 更新客户状态
            customer.setCustomerStatus(newStatus);
            boolean updateResult = customerService.updateById(customer);

            if (updateResult) {
                // 记录日志
                Customer newCustomer = new Customer();
                newCustomer.setCustomerStatus(newStatus);

                String changedFields = ChangedUtil.getUpdateFields(newCustomer, oldCustomer);
                if (changedFields != null && !changedFields.equals("")) {
                    CustomerConnectLog customerConnectLog = new CustomerConnectLog();
                    customerConnectLog.setDescription(changedFields);
                    customerConnectLog.setCustomerId(customerId);
                    customerConnectLog.setContactId(customerId);
                    customerConnectLog.setChangeTable(TableIdEnum.客户.getTableId());
                    customerConnectLog.setAboutDescription(operationDesc);
                    customerConnectLog.setChargeUser(AuthUtil.getUserId());
                    customerConnectLogService.save(customerConnectLog);
                }

                return R.success(operationDesc + "成功");
            } else {
                return R.fail("操作失败");
            }

        } catch (Exception e) {
            log.error("客户状态切换失败", e);
            return R.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据客户ID查询客户部门，按照客户类型进行分组
     * @param customerId 客户ID
     * @return 按类型分组的客户部门列表
     */
    @ApiLog("查询客户部门分组")
    @GetMapping("/customerDeptGroup")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "查询客户部门分组", notes = "传入customerId")
    public R<List<CustomerDeptGroupVO>> getCustomerDeptGroup(@RequestParam Long customerId) {
        try {
            // 获取客户信息以确定客户类型
            Customer customer = customerService.getById(customerId);
            if (customer == null) {
                return R.fail("客户不存在");
            }

            // 根据客户ID和客户类型查询分组的部门信息
            List<CustomerDeptGroupVO> result = customerDeptService.getCustomerDeptGroupedByType(customerId, customer.getCustomerType());

            return R.data(result);
        } catch (Exception e) {
            log.error("查询客户部门分组失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

}
