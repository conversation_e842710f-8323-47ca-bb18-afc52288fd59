/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.vo.CustomerConnectLogVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 客户关联的变更记录 控制器
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/connectlog/customerconnectlog")
@Api(value = "客户关联的变更记录", tags = "客户关联的变更记录接口")
public class CustomerConnectLogController extends BladeController {

	private final ICustomerConnectLogService customerConnectLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerConnectLog")
	public R<CustomerConnectLog> detail(CustomerConnectLog customerConnectLog) {
		CustomerConnectLog detail = customerConnectLogService.getOne(Condition.getQueryWrapper(customerConnectLog));
		return R.data(detail);
	}

	/**
	 * 分页 客户关联的变更记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerConnectLog")
	public R<IPage<CustomerConnectLog>> list(CustomerConnectLog customerConnectLog, Query query) {
		IPage<CustomerConnectLog> pages = customerConnectLogService.page(Condition.getPage(query), Condition.getQueryWrapper(customerConnectLog));
		return R.data(pages);
	}

	/**
	 * 自定义分页 客户关联的变更记录
	 * 根据客户id：customerId
	 * 查询与该客户有关的所有动态记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerConnectLog")
	public R<IPage<CustomerConnectLogVO>> page(CustomerConnectLogVO customerConnectLog, Query query) {
		IPage<CustomerConnectLogVO> pages = customerConnectLogService.selectCustomerConnectLogPage(Condition.getPage(query), customerConnectLog);
		return R.data(pages);
	}

	/**
	 * 新增 客户关联的变更记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerConnectLog")
	public R save(@Valid @RequestBody CustomerConnectLog customerConnectLog) {
		return R.status(customerConnectLogService.save(customerConnectLog));
	}

	/**
	 * 修改 客户关联的变更记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerConnectLog")
	public R update(@Valid @RequestBody CustomerConnectLog customerConnectLog) {
		return R.status(customerConnectLogService.updateById(customerConnectLog));
	}

	/**
	 * 新增或修改 客户关联的变更记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerConnectLog")
	public R submit(@Valid @RequestBody CustomerConnectLog customerConnectLog) {
		return R.status(customerConnectLogService.saveOrUpdate(customerConnectLog));
	}

	
	/**
	 * 删除 客户关联的变更记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerConnectLogService.deleteLogic(Func.toLongList(ids)));
	}

	
}
