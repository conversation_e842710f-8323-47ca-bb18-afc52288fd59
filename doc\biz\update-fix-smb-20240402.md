# fix-smb-0402分支更新内容

## mysql
### 项目完成-进行中，改为项目结项
#### 1. 本次更改的项目ID，用于以后追踪
```sql
SET SESSION group_concat_max_len = 102400;
select GROUP_CONCAT(kd_project_fid) from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0;
```
> 104863,107897,204447,226932,232816,233764,233766,235700,239079,271346,280905
#### 2. 更改金蝶SQLServer
```sql
update T_BAS_PREBDONE set F_RWSQ_XMJD = '6594b1f6218d78' where F_RWSQ_XMJD = '60ffe03d982b50' and FID in (↑);
```
#### 3. 删除项目阶段，项目完成阶段的数据MySQL
```sql
select * from blade_project_progress where project_id in (select id from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0) and is_deleted = 0 and current_progress_key = 9;

select * from blade_project_team where project_id in (select id from blade_project_basic where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0) and is_deleted = 0 and node_id = 9;
```
#### 4. 更新项目阶段
```sql
update blade_project_basic set schedule_id = '11' where is_deleted = 0 and forbid_status = 'A' and schedule_id = '9' and project_closed = 0;
```