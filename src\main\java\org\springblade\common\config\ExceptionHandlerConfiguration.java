package org.springblade.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.exception.GlobalExceptionHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

/**
 * 异常处理器配置类
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Configuration
@Slf4j
public class ExceptionHandlerConfiguration {
    
    /**
     * 注册全局异常处理器
     */
    @Bean
    @Primary  // 设置为主要的异常处理器
    @Order(0) // 设置最高优先级，确保优先于其他异常处理器
    public GlobalExceptionHandler globalExceptionHandler() {
        log.info("=================================================");
        log.info("正在加载新版全局异常处理器...");
        log.info("- 支持异常根因分析");
        log.info("- 支持租户数据源异常解包");
        log.info("- 支持详细的异常链路跟踪");
        log.info("- 优先级: 0 (最高)");
        log.info("=================================================");
        return new GlobalExceptionHandler();
    }
    
    /**
     * 配置异常处理相关属性
     */
    @Bean
    public ExceptionHandlerProperties exceptionHandlerProperties() {
        ExceptionHandlerProperties properties = new ExceptionHandlerProperties();
        properties.setEnableDetailedStackTrace(true);
        properties.setEnableSuggestions(true);
        properties.setMaxStackTraceLines(10);
        
        log.info("异常处理器配置完成 - 详细堆栈跟踪: {}, 修复建议: {}, 最大堆栈行数: {}", 
                properties.isEnableDetailedStackTrace(),
                properties.isEnableSuggestions(),
                properties.getMaxStackTraceLines());
        
        return properties;
    }
    
    /**
     * 异常处理器配置属性类
     */
    public static class ExceptionHandlerProperties {
        private boolean enableDetailedStackTrace = true;
        private boolean enableSuggestions = true;
        private int maxStackTraceLines = 10;
        
        public boolean isEnableDetailedStackTrace() {
            return enableDetailedStackTrace;
        }
        
        public void setEnableDetailedStackTrace(boolean enableDetailedStackTrace) {
            this.enableDetailedStackTrace = enableDetailedStackTrace;
        }
        
        public boolean isEnableSuggestions() {
            return enableSuggestions;
        }
        
        public void setEnableSuggestions(boolean enableSuggestions) {
            this.enableSuggestions = enableSuggestions;
        }
        
        public int getMaxStackTraceLines() {
            return maxStackTraceLines;
        }
        
        public void setMaxStackTraceLines(int maxStackTraceLines) {
            this.maxStackTraceLines = maxStackTraceLines;
        }
    }
} 