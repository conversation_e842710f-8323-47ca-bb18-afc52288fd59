package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 渠道商列表实体类
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Data
@TableName("blade_channel_supplier")
@EqualsAndHashCode(callSuper = true)
public class ChannelSupplier extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 供应商表id
     */
    @ApiModelProperty("供应商表id")
    private Long supplierId;
    /**
     * 金蝶供应商id
     */
    @ApiModelProperty("金蝶供应商id")
    private Long kdSupplierId;
    /**
     * 渠道商名称
     */
    @ApiModelProperty("渠道商名称")
    private String channelName;
    /**
     * 纳税登记号
     */
    @ApiModelProperty("纳税登记号")
    private String taxRegistrationNo;
    /**
     * 公司地址
     */
    @ApiModelProperty("公司地址")
    private String companyAddress;
    /**
     * 公司注册资本
     */
    @ApiModelProperty("公司注册资本")
    private String companyRegistCapi;
    /**
     * 公司规模
     */
    @ApiModelProperty("公司规模")
    private String companyScale;
    /**
     * 公司规模名称
     */
    @ApiModelProperty("公司规模名称")
    private String companyScaleName;
    /**
     * 法人姓名
     */
    @ApiModelProperty("法人姓名")
    private String legalPersonName;
    /**
     * 董事长/总经理姓名
     */
    @ApiModelProperty("董事长/总经理姓名")
    private String chairmanName;
    /**
     * 公司官网地址
     */
    @ApiModelProperty("公司官网地址")
    private String officialWebsite;
    /**
     * 公众号名称
     */
    @ApiModelProperty("公众号名称")
    private String officialAccount;
    /**
     * 公司简介
     */
    @ApiModelProperty("公司简介")
    private String companyIntroduction;
    /**
     * 经营范围
     */
    @ApiModelProperty("经营范围")
    private String businessScope;
    /**
     * 业务承诺
     */
    @ApiModelProperty("业务承诺")
    private String businessCommitment;
    /**
     * 添加理由
     */
    @ApiModelProperty("添加理由")
    private String addReason;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 流程定义主键
     */
    @ApiModelProperty("流程定义主键")
    private String processDefinitionId;
    /**
     * 流程实例主键
     */
    @ApiModelProperty("流程实例主键")
    private String processInstanceId;
    /**
     * 当前审批人
     */
    @ApiModelProperty("当前审批人")
    private String taskUser;
    /**
     * 金蝶作废标志（A否/B是）钉钉上（A正常/B禁用）
     */
    @ApiModelProperty("金蝶作废标志（A否/B是）钉钉上（A正常/B禁用）")
    private String forbidStatus;
    /**
     * 渠道负责人id
     */
    @ApiModelProperty("渠道负责人id")
    private Long channelManager;

}
