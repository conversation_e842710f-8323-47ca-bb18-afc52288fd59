<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractReagentQuotationMaterialMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractReagentQuotationMaterialResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractReagentQuotationMaterial">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="main_id" property="mainId"/>
        <result column="material_id" property="materialId"/>
        <result column="material_name" property="materialName"/>
        <result column="material_spec" property="materialSpec"/>
        <result column="quantity" property="quantity"/>
        <result column="final_amount" property="finalAmount"/>
        <result column="item_number" property="itemNumber"/>
    </resultMap>


    <select id="selectContractReagentQuotationMaterialPage" resultMap="contractReagentQuotationMaterialResultMap">
        select * from cm_contract_reagent_quotation_material where is_deleted = 0
    </select>

</mapper>
