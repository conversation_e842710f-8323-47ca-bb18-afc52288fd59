<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractExperimentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractExperimentResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ContractExperiment">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="project_name" property="projectName"/>
        <result column="materials_provided" property="materialsProvided"/>
        <result column="experiment_cycle" property="experimentCycle"/>
        <result column="materials_required" property="materialsRequired"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_terms" property="paymentTerms"/>
    </resultMap>


    <select id="selectContractExperimentPage" resultMap="contractExperimentResultMap">
        select *
        from cm_contract_experiment
        where is_deleted = 0
    </select>

</mapper>
