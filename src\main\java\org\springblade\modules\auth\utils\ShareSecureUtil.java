package org.springblade.modules.auth.utils;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.security.Key;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import javax.crypto.spec.SecretKeySpec;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.secure.TokenInfo;
import org.springblade.core.secure.provider.IClientDetailsService;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tool.utils.SpringUtil;

public class ShareSecureUtil extends SecureUtil {
   private static final String CLIENT_ID = "client_id";
   private static IClientDetailsService clientDetailsService;
   private static JwtProperties jwtProperties;

   public ShareSecureUtil() {
   }

   private static IClientDetailsService getClientDetailsService() {
      if (clientDetailsService == null) {
         clientDetailsService = (IClientDetailsService) SpringUtil.getBean(IClientDetailsService.class);
      }

      return clientDetailsService;
   }

   private static JwtProperties getJwtProperties() {
      if (jwtProperties == null) {
         jwtProperties = (JwtProperties) SpringUtil.getBean(JwtProperties.class);
      }

      return jwtProperties;
   }

   public static TokenInfo createJWTWiothTemp(Map<String, Object> user, String audience, String issuer, String tokenType,
         long accessTokenValidity) {
      // String[] tokens = extractAndDecodeHeader();
      // String clientId = tokens[0];
      // String clientSecret = tokens[1];
      // IClientDetails clientDetails = clientDetails(clientId);
      // if (!validateClient(clientDetails, clientId, clientSecret)) {
      // throw new SecureException("客户端认证失败, 请检查请求头 [Authorization] 信息");
      // } else {
      SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
      long nowMillis = System.currentTimeMillis();
      Date now = new Date(nowMillis);
      byte[] apiKeySecretBytes = Base64.getDecoder().decode(JwtUtil.getBase64Security());
      Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
      JwtBuilder builder = Jwts.builder().setHeaderParam("typ", "JWT").setIssuer(issuer).setAudience(audience)
            .signWith(signingKey);
      Objects.requireNonNull(builder);
      user.forEach(builder::claim);
      builder.claim("client_id", "share_client");
      long expireMillis;
      if (tokenType.equals("access_token")) {
         expireMillis = accessTokenValidity * 1000L;
      } else if (tokenType.equals("refresh_token")) {
         // expireMillis = (long)clientDetails.getRefreshTokenValidity() * 1000L;
         expireMillis = 1L * 1000L;
      } else {
         // expireMillis = getExpire();
         expireMillis = 1L * 1000L;
      }

      long expMillis = nowMillis + expireMillis;
      Date exp = new Date(expMillis);
      builder.setExpiration(exp).setNotBefore(now);
      TokenInfo tokenInfo = new TokenInfo();
      tokenInfo.setToken(builder.compact());
      tokenInfo.setExpire((int) (expireMillis / 1000L));
      String tenantId;
      String userId;
      if (getJwtProperties().getState() && "access_token".equals(tokenType)) {
         tenantId = String.valueOf(user.get("tenant_id"));
         userId = String.valueOf(user.get("user_id"));
         JwtUtil.addAccessToken(tenantId, userId, tokenInfo.getToken(), tokenInfo.getExpire());
      }

      if (getJwtProperties().getState() && getJwtProperties().getSingle() && "refresh_token".equals(tokenType)) {
         tenantId = String.valueOf(user.get("tenant_id"));
         userId = String.valueOf(user.get("user_id"));
         JwtUtil.addRefreshToken(tenantId, userId, tokenInfo.getToken(), tokenInfo.getExpire());
      }

      return tokenInfo;
      // }
   }

   // public static long getExpire() {
   // Calendar cal = Calendar.getInstance();
   // cal.add(6, 1);
   // cal.set(11, 3);
   // cal.set(13, 0);
   // cal.set(12, 0);
   // cal.set(14, 0);
   // return cal.getTimeInMillis() - System.currentTimeMillis();
   // }

   // public static String[] extractAndDecodeHeader() {
   // try {
   // String header =
   // ((HttpServletRequest)Objects.requireNonNull(WebUtil.getRequest())).getHeader("Authorization");
   // header = Func.toStr(header).replace("Basic%20", "Basic ");
   // if (!header.startsWith("Basic ")) {
   // throw new SecureException("未获取到请求头[Authorization]的信息");
   // } else {
   // byte[] base64Token = header.substring(6).getBytes(Charsets.UTF_8_NAME);

   // byte[] decoded;
   // try {
   // decoded = Base64.getDecoder().decode(base64Token);
   // } catch (IllegalArgumentException var5) {
   // throw new RuntimeException("客户端令牌解析失败");
   // }

   // String token = new String(decoded, Charsets.UTF_8_NAME);
   // int index = token.indexOf(":");
   // if (index == -1) {
   // throw new RuntimeException("客户端令牌不合法");
   // } else {
   // return new String[]{token.substring(0, index), token.substring(index + 1)};
   // }
   // }
   // } catch (Throwable var6) {
   // throw var6;
   // }
   // }

   // public static String getClientIdFromHeader() {
   // String[] tokens = extractAndDecodeHeader();

   // assert tokens.length == 2;

   // return tokens[0];
   // }

   // private static IClientDetails clientDetails(String clientId) {
   // return getClientDetailsService().loadClientByClientId(clientId);
   // }

   // private static boolean validateClient(IClientDetails clientDetails, String
   // clientId, String clientSecret) {
   // if (clientDetails == null) {
   // return false;
   // } else {
   // return StringUtil.equals(clientId, clientDetails.getClientId()) &&
   // StringUtil.equals(clientSecret, clientDetails.getClientSecret());
   // }
   // }
}
