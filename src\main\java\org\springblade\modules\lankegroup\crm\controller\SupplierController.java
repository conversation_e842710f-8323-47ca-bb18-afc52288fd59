/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.crm.entity.Supplier;
import org.springblade.modules.lankegroup.crm.service.ISupplierService;
import org.springblade.modules.lankegroup.crm.service.ISupplierUseorgService;
import org.springblade.modules.lankegroup.crm.vo.SupplierVO;
import org.springblade.modules.lankegroup.crm.wrapper.SupplierWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/basic/supplier")
@Api(value = "供应商表", tags = "供应商表接口")
public class SupplierController extends BladeController {
    private final static Logger log = LoggerFactory.getLogger(SupplierController.class);
    private final ISupplierService supplierService;

    private final ISupplierUseorgService supplierUseorgService;


    /**
     * 通过供应商id查看供应商详情
     */
    @ApiLog("供应商详情")
    @GetMapping("/detailById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入supplierId")
    public R<SupplierVO> detailById(@RequestParam Long supplierId) {
        SupplierVO detail = supplierService.selectById(supplierId);
        return R.data(detail);
    }

    /**
     * 详情
     */
    @ApiLog("详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入supplier")
    public R<SupplierVO> detail(Supplier supplier) {
//		Supplier detail = supplierService.getOne(Condition.getQueryWrapper(supplier));

        SupplierVO detail = supplierService.selectByIdBladex(supplier.getId());

        return R.data(SupplierWrapper.build().entityVO(detail));
    }

    /**
     * 分页 供应商表
     */
    @ApiLog("分页")
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入supplier")
    public R<IPage<SupplierVO>> list(SupplierVO supplierVO, Query query) {
//		IPage<Supplier> pages = supplierService.page(Condition.getPage(query), Condition.getQueryWrapper(supplier));
        IPage<SupplierVO> pages = supplierService.listSupplier(Condition.getPage(query), supplierVO);
        return R.data(pages);
    }


    /**
     * 自定义分页 供应商表
     * 参数：
     * supplierName:客户名称关键字
     * current:当前页数
     * size:每页数量
     */
    @ApiLog("分页")
    @PostMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入supplier")
    public R<IPage<SupplierVO>> page(@RequestBody SupplierVO supplier, @RequestBody Query query) {
        IPage<SupplierVO> pages = supplierService.selectSupplierPage(Condition.getPage(query), supplier);
        return R.data(pages);
    }

    /**
     * 新增/更新 供应商表
     * 供应商根据名称来确定是否为同一个供应商
     * 参数中金蝶主键id不为空为更新，否则直接保存
     * supplierVO.getModificeWay用来标识是否为外部仅修改负责人的操作
     * 打粮队+财务人员可直接在详情页对客户负责人进行修改
     * 修改方式 为true表示外部直接修改负责人
     */
    @ApiLog("新增")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入supplier")
    public R save(@Valid @RequestBody SupplierVO supplierVO) throws Exception {
        return supplierService.saveSupplier(supplierVO);
    }

    /**
     * 修改 供应商表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入supplier")
    public R update(@Valid @RequestBody SupplierVO supplierVO) throws Exception {
        return R.status(supplierService.updateById(supplierVO));
    }

    /**
     * 新增或修改 供应商表
     */
    @ApiLog("新增/修改")
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入supplier")
    public R submit(@Valid @RequestBody Supplier supplier) {
        return R.status(supplierService.saveOrUpdate(supplier));
    }


    /**
     * 删除 供应商表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String id) throws Exception {
//		return R.status(supplierService.deleteLogic(Func.toLongList(ids)));
        R remove = supplierService.remove(Long.valueOf(id));
        if (remove.isSuccess()) {
            return R.status(supplierService.deleteLogic(Collections.singletonList(Long.valueOf(id))));
        }
        return remove;
    }

    /**
     * 供应商列表，单据搜索供应商也会使用
     * 项目立项中渠道商使用
     * 根据渠道商名称模糊查询blade表中符合条件的数据
     * <p>
     * <p>
     * 收款单客户、供应商列表增加参数orgId，根据组织id区分基础资料的使用组织
     *
     * @param supplierName 供应商名称所含关键字
     * @return 相关项目列表
     */
    @ApiLog("供应商列表")
    @PostMapping("/supplierList")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表", notes = "传入supplierName")
    public R<List<Supplier>> listSupplier(String supplierName) {
        Map map = new HashMap();
        map.put("supplierName", "%" + supplierName + "%");
//        map.put("orgId",orgId);
        return R.data(supplierService.supplierList(map));
    }

    @ApiLog("供应商详情--各财务单据选择供应商使用")
    @GetMapping("/supplierDetail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入supplierId")
    public R<Supplier> supplierDetails(Long supplierId) {
        return R.data(supplierService.getById(supplierId));
    }


    /**
     * 启用/禁用
     */
    @GetMapping("updateForbidStatus")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "启用/禁用", notes = "传入id和标识")
    public R updateForbidStatus(Supplier supplier) {
        return supplierService.updateForbidStatus(supplier);
    }
}
