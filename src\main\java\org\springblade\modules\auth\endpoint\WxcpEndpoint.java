package org.springblade.modules.auth.endpoint;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springblade.modules.auth.service.WxcpCallbackService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("wxcp")
@Slf4j
public class WxcpEndpoint {

	private final WxCpService wxcpService;
	private final WxcpCallbackService callbackService;

	@GetMapping("login")
	public Object login(
		@RequestParam("code") String code
	) {
		throw new RuntimeException();
	}

	@PostMapping("refresh-token")
	public Object refreshToken() {
		throw new RuntimeException();
	}


	@GetMapping("callback")
	public String echoStr(
		@RequestParam("msg_signature") String signature,
		@RequestParam("timestamp") Integer timestamp,
		@RequestParam("nonce") String nonce,
		@RequestParam("echostr") String echoStr
	) {
		if (!wxcpService.checkSignature(signature, timestamp.toString(), nonce, echoStr)) {
			log.error("消息不合法");
			return "error";
		}
		return callbackService.handleEchoStr(echoStr);
	}

	@PostMapping("callback")
	public String wxcpCallback(
		@RequestParam("msg_signature") String signature,
		@RequestParam("timestamp") String timestamp,
		@RequestParam("nonce") String nonce,
		@RequestBody String xml
	) {
		val inMessage = WxCpXmlMessage.fromEncryptedXml(xml, wxcpService.getWxCpConfigStorage(), timestamp, nonce, signature);
		log.info(inMessage.toString());
		return "";
	}
}
