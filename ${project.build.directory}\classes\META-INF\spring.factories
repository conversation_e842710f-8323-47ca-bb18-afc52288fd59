org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  org.springblade.modules.lankegroup.disposition.service.impl.BusinessGroupDispositionServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.service.impl.ProjectOpportunityServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.MaterialServiceImpl,\
  org.springblade.modules.lankegroup.crm.kingdee.KDSupplierService,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeQuestionTypeController,\
  org.springblade.modules.lankegroup.invoiceCollectionPlan.service.impl.InvoiceCollectionPlanServiceImpl,\
  org.springblade.common.manager.UserLevelManager,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.BasicEmployeeController,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeProductController,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.BasicCustomerController,\
  org.springblade.modules.lankegroup.friends.service.impl.FriendlyBusinessServiceImpl,\
  org.springblade.modules.lankegroup.jl.controller.ProductCateController,\
  org.springblade.modules.system.controller.LogErrorController,\
  org.springblade.modules.lankegroup.common.service.impl.AttachmentServiceImpl,\
  org.springblade.modules.lankegroup.outbound.controller.OutboundController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerChargeUserServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.utils.CreateWordUtil,\
  org.springblade.modules.lankegroup.otherpayables.controller.OtherPayablesController,\
  org.springblade.modules.auth.service.impl.RoleDeptServiceImpl,\
  org.springblade.modules.develop.service.impl.DatasourceServiceImpl,\
  org.springblade.modules.lankegroup.purchase.service.impl.PurchaseMaterialsServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.service.impl.ProjectOpportunityFbServiceImpl,\
  org.springblade.modules.lankegroup.reim.service.impl.ReimProjectServiceImpl,\
  org.springblade.modules.lankegroup.message.controller.SystemMessageController,\
  org.springblade.flow.business.controller.BusinessProcessController,\
  org.springblade.modules.system.service.impl.MenuServiceImpl,\
  org.springblade.modules.lankegroup.payback.controller.PaybackPanelController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitCommentLikeController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionProjectTaskServiceImpl,\
  org.springblade.modules.lankegroup.ticket.service.impl.TicketApprovalFlowServiceImpl,\
  org.springblade.modules.system.controller.TenantController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitLookController,\
  org.springblade.modules.lankegroup.invoiceStatistics.util.CalendarUtil,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackReceiptManageServiceImpl,\
  org.springblade.modules.lankegroup.disposition.controller.BusinessGroupDispositionController,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressController,\
  org.springblade.common.annotation.log.aspect.LogAspect,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractTemplateServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.AccountBalanceServiceImpl,\
  org.springblade.modules.lankegroup.jl.controller.quotation.QuotationCommunicateController,\
  org.springblade.modules.system.service.impl.InitWxcpDataImpl,\
  org.springblade.modules.lankegroup.projectKanBan.controller.ProjectFollowUpPlanController,\
  org.springblade.common.service.impl.FormImpl,\
  org.springblade.modules.lankegroup.jl.controller.ProductGroupItemController,\
  org.springblade.modules.wxcp.controller.WxcpController,\
  org.springblade.modules.system.service.impl.LogServiceImpl,\
  org.springblade.modules.auth.controller.AuthCodeController,\
  org.springblade.modules.lankegroup.jl.service.impl.QuotationServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.mongo.ProjectApprovalDao,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.task.EqualProtectionPerformanceEvaluationTask,\
  org.springblade.common.utils.SendLogTask,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressContractDisclosureController,\
  org.springblade.modules.xxljob.manager.XXLJobManager,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitLikeServiceImpl,\
  org.springblade.modules.lankegroup.meetManage.controller.MeetingSignageController,\
  org.springblade.modules.lankegroup.jl.controller.ProductController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerProductTypeServiceImpl,\
  org.springblade.modules.lankegroup.target.controller.TargetSettingsController,\
  org.springblade.flow.engine.config.FlowableConfiguration,\
  org.springblade.modules.lankegroup.crm.controller.MessageTimingController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractExperimentServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.BasicProjectController,\
  org.springblade.modules.system.service.impl.ApiScopeServiceImpl,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeCaseController,\
  org.springblade.modules.xxljob.manager.ToXxlJobInfoManager,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeQuestionController,\
  org.springblade.modules.lankegroup.pro_management.service.impl.ProjectCollaborationTaskServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionSystemTaskController,\
  org.springblade.common.schedule.ScheduleUtil,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.ProfessionJoinServiceImpl,\
  org.springblade.modules.lankegroup.index.service.impl.CommonlyUsedMenuServiceImpl,\
  org.springblade.modules.system.controller.ApiScopeController,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeDocumentTypeServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.CustomerContactController,\
  org.springblade.flow.business.service.impl.BusinessProcessTransferServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeApi.KingDeeAPI,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ChannelContractServiceImpl,\
  org.springblade.common.utils.TenantDiagnosticUtil,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressContractSigningController,\
  org.springblade.modules.lankegroup.jl.controller.quotation.QuotationMaterialController,\
  org.springblade.modules.auth.endpoint.WxcpEndpoint,\
  org.springblade.modules.lankegroup.contractManagement.controller.SignatoryCompanyController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressContractDisclosureServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.CustomerConnectLogController,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeCaseServiceImpl,\
  org.springblade.modules.lankegroup.homePage.controller.MenuUserController,\
  org.springblade.modules.system.controller.RegionController,\
  org.springblade.modules.lankegroup.crm.service.impl.RequestShareContactServiceImpl,\
  org.springblade.modules.system.controller.LogUsualController,\
  org.springblade.modules.lankegroup.homePage.service.impl.MenuUserServiceImpl,\
  org.springblade.common.utils.SelectDeptOrUserUtil,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskCqtjServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractServiceImpl,\
  org.springblade.flow.engine.controller.FlowFollowController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerIndustryInvolvedServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressClosureStageController,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeQuestionTypeServiceImpl,\
  org.springblade.common.config.SwaggerConfiguration,\
  org.springblade.modules.auth.endpoint.BladeSocialEndpoint,\
  org.springblade.modules.lankegroup.salesFunnel.controller.SalesFunnelController,\
  org.springblade.modules.auth.service.WxcpCallbackService,\
  org.springblade.modules.desk.service.impl.NoticeServiceImpl,\
  org.springblade.common.utils.SendBillMsgUtil,\
  org.springblade.modules.system.service.impl.RoleServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitContactServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.ProjectApprovalController,\
  org.springblade.modules.lankegroup.message.service.impl.SystemMessageServiceImpl,\
  org.springblade.common.utils.DatabaseExceptionFormatter,\
  org.springblade.modules.lankegroup.finance.controller.FinanceKanbanController,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.TeamManagementController,\
  org.springblade.modules.lankegroup.crm.controller.ContactPersonController,\
  org.springblade.modules.lankegroup.contractManagement.task.ContractUnionInfoTask,\
  org.springblade.modules.lankegroup.log.service.impl.ControlsLogServiceImpl,\
  org.springblade.modules.lankegroup.payback.controller.ReceivingAccountManageController,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitLookServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.ExpenseReportSkillServiceImpl,\
  org.springblade.common.config.AppConfig,\
  org.springblade.modules.lankegroup.kingdeeApproval.controller.ReceiptController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerContactServiceImpl,\
  org.springblade.modules.system.controller.DataScopeController,\
  org.springblade.common.utils.ProjectCooperationTaskTimer,\
  org.springblade.modules.lankegroup.meetManage.service.impl.MeetingSignageServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionGroupMemberController,\
  org.springblade.modules.lankegroup.crm.service.impl.UnassignedCustomerServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.service.impl.ProjectOpportunityIndustryInvolvedServiceImpl,\
  org.springblade.common.config.BladeConfiguration,\
  org.springblade.modules.system.service.impl.AsyncOperationUserServiceImpl,\
  org.springblade.modules.wxcp.advice.WxcpBizAdvice,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerUseorgServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.controller.PersonnelController,\
  org.springblade.modules.resource.endpoint.OssEndpoint,\
  org.springblade.modules.lankegroup.projectKanBan.controller.ProKanBanController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractMainController,\
  org.springblade.modules.lankegroup.invoiceVoid.controller.InvoiceVoidController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CommonPhrasesController,\
  org.springblade.modules.wxcp.service.impl.WxRoleDeptServiceImpl,\
  org.springblade.modules.lankegroup.saleKanBan.controller.SaleKanBanController,\
  org.springblade.modules.lankegroup.crm.controller.CustomerCareMaterialController,\
  org.springblade.modules.desk.controller.DashBoardController,\
  org.springblade.modules.system.controller.TopMenuController,\
  org.springblade.modules.wxcp.manager.WxcpManager,\
  org.springblade.modules.lankegroup.jlopportunity.service.impl.BusinessOpportunityPhaseStatusServiceImpl,\
  org.springblade.modules.system.service.impl.RegionServiceImpl,\
  org.springblade.modules.lankegroup.jl.service.impl.ProductGroupItemServiceImpl,\
  org.springblade.modules.lankegroup.reim.controller.ReimbursedRobotPrint,\
  org.springblade.modules.lankegroup.paybill.controller.PaybillAnnexController,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectKanBanMsgServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ProjectCollectionArchiveServiceImpl,\
  org.springblade.flow.engine.listener.flowable.MultiInstanceCompleteTask,\
  org.springblade.modules.lankegroup.crm.controller.CustomerController,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicMaterialServiceImpl,\
  org.springblade.modules.lankegroup.seal.controller.SealApplicationController,\
  org.springblade.common.utils.ProjectCollaborationTaskTimer,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityKeypointsController,\
  org.springblade.modules.lankegroup.kingdeeproject.kingdeeApi.ProjectApprovalService,\
  org.springblade.modules.lankegroup.invoiceVoid.service.impl.InvoiceVoidServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.StockServiceImpl,\
  org.springblade.flow.business.controller.WorkController,\
  org.springblade.modules.lankegroup.jl.service.impl.ProductServiceImpl,\
  org.springblade.modules.system.service.impl.TenantPackageServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectKanBanServiceImpl,\
  org.springblade.modules.wxcp.service.impl.WxcpServiceImpl,\
  org.springblade.modules.lankegroup.pro_management.controller.ProjectBasicController,\
  org.springblade.modules.resource.service.impl.OssServiceImpl,\
  org.springblade.modules.lankegroup.dingrobot.service.impl.DingTaskRobotServiceImpl,\
  org.springblade.modules.system.service.impl.DeptServiceImpl,\
  org.springblade.modules.resource.controller.OssController,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitServiceImpl,\
  org.springblade.modules.lankegroup.jl.controller.quotation.QuotationChargeItemController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractChangeLogController,\
  org.springblade.common.exception.GlobalExceptionHandler,\
  org.springblade.flow.engine.service.impl.CustomProcessConfigServiceImpl,\
  org.springblade.modules.lankegroup.jl.service.impl.QuotationChargeCategoryServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.PersonnelMarketServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerServiceImpl,\
  org.springblade.modules.resource.controller.AttachController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionStageServiceImpl,\
  org.springblade.workflow.event.listener.WorkFlowListener,\
  org.springblade.modules.lankegroup.invoiceVoid.dao.InvoiceBillDao,\
  org.springblade.modules.lankegroup.test.SaveCustController,\
  org.springblade.modules.lankegroup.invoice.controller.InvoiceController,\
  org.springblade.flow.business.feign.FlowClient,\
  org.springblade.modules.wxcp.config.WxcpConfiguration,\
  org.springblade.modules.system.controller.UserDeptController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionProjectTaskController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractChangeLogServiceImpl,\
  org.springblade.modules.lankegroup.ImportantList.service.impl.TodoServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicProjectServiceImpl,\
  org.springblade.modules.lankegroup.paybill.controller.PaybillController,\
  org.springblade.modules.lankegroup.crm.controller.CustomerIndustryInvolvedController,\
  org.springblade.common.config.WebSocketConfig,\
  org.springblade.modules.lankegroup.contractManagement.controller.ArchiveDetailsController,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityTransformationSuggestController,\
  org.springblade.modules.lankegroup.invoiceStatistics.task.ReceivebillTask,\
  org.springblade.modules.lankegroup.invoiceStatistics.controller.InvoiceStaticsController,\
  org.springblade.common.utils.ExceptionLogFormatter,\
  org.springblade.modules.lankegroup.digitizeskip.controller.DigitizeInterfaceSkipController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ChannelArchiveDetailsServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CompletePlanLogTempisticaServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.ProfessionAreaServiceImpl,\
  org.springblade.modules.lankegroup.dept.service.impl.DeptByUserServiceImpl,\
  org.springblade.common.config.ButtonPermissionConfig,\
  org.springblade.modules.wxcp.controller.SyncContactController,\
  org.springblade.modules.lankegroup.crm.service.impl.ConcentUserServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ArchiveInventoryInformationServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.controller.CompletePlanLogTempisticaController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ArchiveDetailsServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.BasicMaterialController,\
  org.springblade.modules.lankegroup.receivable.service.impl.ReceivableServiceImpl,\
  org.springblade.modules.system.service.impl.DataScopeServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.UserProjectNodeController,\
  org.springblade.modules.lankegroup.jl.controller.PlanController,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressAcceptanceController,\
  org.springblade.modules.lankegroup.jl.service.impl.QuotationChargeItemServiceImpl,\
  org.springblade.modules.auth.endpoint.BladeTokenEndPoint,\
  org.springblade.modules.lankegroup.payback.event.listener.PaybackPlanEventListener,\
  org.springblade.modules.lankegroup.crm.controller.ChannelSupplierContactController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerCareServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressPaymentController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerGroupServiceImpl,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressServiceImpl,\
  org.springblade.modules.auth.granter.WxcpLocalTokenGranter,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CommonPhrasesServiceImpl,\
  org.springblade.common.config.BladeLogConfiguration,\
  org.springblade.common.config.DataPermissionFieldConfig,\
  org.springblade.common.controller.ButtonPermissionController,\
  org.springblade.modules.lankegroup.common.service.impl.SimpleSerialNumberServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectFollowUpRecordServiceImpl,\
  org.springblade.modules.lankegroup.jlopportunity.service.impl.BusinessOpportunityCompetitorServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.controller.ApproveController,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitCommentServiceImpl,\
  org.springblade.modules.lankegroup.ticket.service.impl.TicketServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionKanbanServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CompletePlanLogServiceImpl,\
  org.springblade.modules.system.service.impl.TopMenuServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitFeeBreakdownPaymentVoucherServiceImpl,\
  org.springblade.common.utils.SpringContextUtils,\
  org.springblade.flow.engine.service.impl.FlowEngineServiceImpl,\
  org.springblade.modules.lankegroup.reim.controller.ReimProjectController,\
  org.springblade.modules.lankegroup.crm.service.impl.ChannelSupplierContactServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.ProfessionMarketServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractMainServiceImpl,\
  org.springblade.modules.lankegroup.saleKanBan.service.impl.SaleKanBanServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractStatisticsServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.ChannelSupplierServiceImpl,\
  org.springblade.modules.lankegroup.outboundKanban.controller.OutboundKanbanController,\
  org.springblade.modules.system.controller.UserController,\
  org.springblade.modules.lankegroup.disposition.kingdee.KingdeeService,\
  org.springblade.modules.lankegroup.crm.service.impl.SupplierContactServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.PersonnelServiceImpl,\
  org.springblade.common.manager.ButtonPermissionManager,\
  org.springblade.modules.lankegroup.crm.controller.UnassignedCustomerController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitFeeBreakdownController,\
  org.springblade.modules.system.controller.DictController,\
  org.springblade.modules.wxcp.service.impl.WxcpSyncServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.service.impl.ProjectOpportunityKeypointsServiceImpl,\
  org.springblade.modules.lankegroup.project.service.impl.MinioServiceImpl,\
  org.springblade.modules.lankegroup.friends.controller.FriendlyBusinessController,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitCommentLikeServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.controller.CompletePlanLogController,\
  org.springblade.modules.resource.service.impl.SmsServiceImpl,\
  org.springblade.flow.engine.listener.flowable.UserTaskCompleteListener,\
  org.springblade.flow.engine.controller.FlowProcessController,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.AreaServiceImpl,\
  org.springblade.modules.lankegroup.version.service.impl.VersionServiceImpl,\
  org.springblade.modules.lankegroup.receivable.service.impl.ReceivableDetailServiceImpl,\
  org.springblade.common.config.BladeReportConfiguration,\
  org.springblade.common.config.DigitizeSkipConfig,\
  org.springblade.modules.lankegroup.crm.service.impl.AttachmentsServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.SupplierUseorgController,\
  org.springblade.modules.lankegroup.crm.service.impl.SupplierFollowUpRecordServiceImpl,\
  org.springblade.modules.lankegroup.jlopportunity.service.impl.BusinessOpportunityServiceImpl,\
  org.springblade.common.utils.DatabaseMetadataUtil,\
  org.springblade.modules.xxljob.service.jobhandler.SampleXxlJob,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityCommunicateController,\
  org.springblade.modules.lankegroup.crm.controller.ReminderTimerController,\
  org.springblade.modules.system.service.impl.PostServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityDecisionChainController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ProjectCollectionArchiveController,\
  org.springblade.modules.lankegroup.kingdeeApproval.service.impl.ReceiptServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.PerformanceEvaluationServiceImpl,\
  org.springblade.modules.lankegroup.message.task.BadjMessageTask,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractStatisticsController,\
  org.springblade.modules.lankegroup.crm.controller.CustomerContactCollectController,\
  org.springblade.flow.demo.service.impl.FlowTestBusinessServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.MaterialController,\
  org.springblade.modules.lankegroup.crm.kingdee.KDCustomerService,\
  org.springblade.workflow.service.WorkFlowService,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionKanbanController,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.ProfessionServiceImpl,\
  org.springblade.modules.system.service.impl.AuthClientServiceImpl,\
  org.springblade.modules.lankegroup.invoice.service.impl.InvoicePictureFileServiceImpl,\
  org.springblade.modules.lankegroup.invoiceStatistics.service.impl.InvoiceKanbanProjectProcessServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.controller.ProjectFollowUpRecordController,\
  org.springblade.modules.lankegroup.crm.controller.SupplierController,\
  org.springblade.modules.system.service.impl.TopMenuSettingServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicSupplierServiceImpl,\
  org.springblade.modules.lankegroup.receivable.controller.ReceivableController,\
  org.springblade.modules.lankegroup.historicprocess.controller.HistoricProcessController,\
  org.springblade.common.config.BladePreviewConfiguration,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitFeeBreakdownPaymentVoucherController,\
  org.springblade.flow.engine.listener.flowable.OneSignMultiInstanceCompleteTask,\
  org.springblade.modules.lankegroup.outboundKanban.service.impl.OutboundKanbanServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressWarrantyStageController,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicCustomerServiceImpl,\
  org.springblade.modules.auth.granter.RefreshTokenGranter,\
  org.springblade.flow.demo.controller.FlowTestBusinessController,\
  org.springblade.modules.lankegroup.opportunity.service.impl.ProjectOpportunityDecisionChainServiceImpl,\
  org.springblade.modules.resource.config.BladeOssConfiguration,\
  org.springblade.modules.system.controller.SearchController,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerConnectLogServiceImpl,\
  org.springblade.modules.wxcp.controller.WxcpInitConfigController,\
  org.springblade.modules.lankegroup.digitizeskip.controller.WorkbenchSkipController,\
  org.springblade.modules.auth.granter.WxcpTokenGranter,\
  org.springblade.flow.engine.listener.flowable.MultiInstanceExecutionListener,\
  org.springblade.modules.lankegroup.otherpayables.service.impl.OtherPayablesServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionStageMemberController,\
  org.springblade.flow.engine.listener.flowable.UserTaskCreateListener,\
  org.springblade.modules.lankegroup.receivable.controller.ReceivableDetailController,\
  org.springblade.modules.lankegroup.projectKanBan.controller.ProjectCollectionEstimateAmountController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskCustomerServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskStageServiceImpl,\
  org.springblade.modules.lankegroup.reim.controller.ReimbursedPrint,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.CustomerKanBanServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerContactCollectServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskServiceImpl,\
  org.springblade.modules.lankegroup.pro_management.service.impl.ProjectExpectedServiceImpl,\
  org.springblade.modules.lankegroup.finance.entry.AccountingDimension,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeQuestionServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.SupplierContactController,\
  org.springblade.modules.lankegroup.crm.controller.RequestShareContactController,\
  org.springblade.modules.lankegroup.crm.wrapper.CustomerWrapper,\
  org.springblade.modules.lankegroup.ImportantList.service.impl.TodoFollowUserServiceImpl,\
  org.springblade.modules.lankegroup.invoiceStatistics.util.JDBCUtil,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ApproveServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.controller.ArchiveInventoryInformationController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.PerformanceEvaluationController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractTemplateController,\
  org.springblade.modules.lankegroup.pro_management.dao.ProjectDao,\
  org.springblade.modules.lankegroup.purchase.controller.PurchaseMaterialsController,\
  org.springblade.modules.lankegroup.reim.service.impl.ReimServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerLogManagementController,\
  org.springblade.modules.lankegroup.project.controller.ProjectTeamController,\
  org.springblade.modules.lankegroup.disposition.service.impl.ProjectGroupDispositionServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitLikeController,\
  org.springblade.modules.wxcp.component.WxcpMessageSender,\
  org.springblade.modules.system.service.impl.RoleMenuServiceImpl,\
  org.springblade.modules.system.service.impl.DictServiceImpl,\
  org.springblade.modules.system.service.impl.TenantServiceImpl,\
  org.springblade.modules.lankegroup.jl.service.impl.QuotationMaterialServiceImpl,\
  org.springblade.common.utils.FlowButtonTypeUtil,\
  org.springblade.modules.lankegroup.disposition.controller.ProjectGroupDispositionController,\
  org.springblade.modules.lankegroup.finance.entry.FinanceReceivable,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeDocumentServiceImpl,\
  org.springblade.modules.lankegroup.payback.service.impl.ReceivingAccountManageServiceImpl,\
  org.springblade.modules.system.service.impl.ParamServiceImpl,\
  org.springblade.modules.resource.service.impl.AttachServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressProjectCloseController,\
  org.springblade.modules.lankegroup.version.controller.VersionController,\
  org.springblade.modules.lankegroup.jl.controller.quotation.QuotationController,\
  org.springblade.modules.lankegroup.crm.service.impl.NotifyRecordServiceImpl,\
  org.springblade.modules.lankegroup.taskdelivery.controller.DeliverySpecificationsController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractChangeController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressContractSigningServiceImpl,\
  org.springblade.modules.auth.granter.CaptchaTokenGranter,\
  org.springblade.modules.system.controller.TenantPackageController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.task.EqualProtectionProjectSystemTask,\
  org.springblade.modules.lankegroup.pro_management.service.impl.BiddingCooperationServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectFollowUpPlanServiceImpl,\
  org.springblade.modules.resource.endpoint.SmsEndpoint,\
  org.springblade.modules.lankegroup.finance.service.impl.FinanceServiceImpl,\
  org.springblade.modules.lankegroup.pro_management.controller.ProjectGroupController,\
  org.springblade.modules.lankegroup.digitizeskip.service.impl.WorkbenchSkipServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressSaleStageController,\
  org.springblade.modules.lankegroup.projectClaim.controller.claimController,\
  org.springblade.modules.system.controller.InitWxcpDataController,\
  org.springblade.modules.lankegroup.crm.service.impl.SupplierUseorgServiceImpl,\
  org.springblade.modules.system.service.impl.UserOauthServiceImpl,\
  org.springblade.modules.lankegroup.payback.controller.PaybackPlanManageController,\
  org.springblade.modules.develop.controller.DatasourceController,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressProjectDeliveryController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressAcceptanceServiceImpl,\
  org.springblade.modules.lankegroup.taskdelivery.service.impl.TaskDeliveryServiceImpl,\
  org.springblade.modules.system.service.impl.UserSearchServiceImpl,\
  org.springblade.modules.system.service.impl.LogUsualServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskStageFileServiceImpl,\
  org.springblade.modules.lankegroup.knowledge.service.impl.KnowledgeProductServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.ClientController,\
  org.springblade.modules.lankegroup.jl.service.impl.PlanServiceImpl,\
  org.springblade.modules.lankegroup.taskdelivery.service.impl.DeliverySpecificationsServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.controller.ChannelContractController,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeDocumentTypeController,\
  org.springblade.modules.lankegroup.index.controller.CommonlyUsedMenuController,\
  org.springblade.flow.engine.controller.FlowModelController,\
  org.springblade.modules.lankegroup.crm.controller.ConcentUserController,\
  org.springblade.modules.xxljob.config.XxlJobExecutorConfig,\
  org.springblade.modules.system.controller.PostController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionTaskStatisticsServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.CustomerProductTypeController,\
  org.springblade.modules.lankegroup.ImportantList.controller.TodoController,\
  org.springblade.modules.system.service.impl.DictBizServiceImpl,\
  org.springblade.modules.lankegroup.paybill.service.impl.PaybillServiceImpl,\
  org.springblade.modules.lankegroup.mongobilltask.controller.MongoBillTaskController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitController,\
  org.springblade.common.service.impl.SaveToKingDeeImpl,\
  org.springblade.modules.lankegroup.seal.service.impl.SealApplicationServiceImpl,\
  org.springblade.modules.lankegroup.mongobilltask.service.impl.MongoBillTaskServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.SupplierServiceImpl,\
  org.springblade.modules.system.service.impl.RoleScopeServiceImpl,\
  org.springblade.common.utils.SendSocketMessageUtil,\
  org.springblade.modules.lankegroup.crm.controller.CustomerChargeUserController,\
  org.springblade.modules.lankegroup.message.task.MessageTask,\
  org.springblade.modules.lankegroup.invoice.controller.InvoicePictureFileController,\
  org.springblade.modules.resource.config.BladeSmsConfiguration,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackBadDebtApprovalFlowServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityController,\
  org.springblade.modules.lankegroup.payback.controller.PaybackReceiptManageController,\
  org.springblade.modules.lankegroup.invoiceStatistics.controller.InvoiceKanbanProjectProcessController,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicProjectTeamServiceImpl,\
  org.springblade.common.utils.VisitTimer,\
  org.springblade.modules.lankegroup.dingrobot.service.DingTalkLoginService,\
  org.springblade.modules.lankegroup.crm.controller.MaterialUnitController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.SignatoryCompanyServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitContactController,\
  org.springblade.modules.lankegroup.crm.controller.ChannelSupplierController,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContracTransferTaskController,\
  org.springblade.common.config.DatabaseFieldMappingConfig,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressClosureStageServiceImpl,\
  org.springblade.modules.lankegroup.pro_management.controller.ProjectExpectedController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionStageController,\
  org.springblade.common.manager.DataPermissionManager,\
  org.springblade.modules.system.controller.MenuController,\
  org.springblade.modules.lankegroup.reim.controller.ReimFeeBreakdownController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressSaleStageServiceImpl,\
  org.springblade.modules.lankegroup.historicprocess.service.impl.HistoricProcessServiceImpl,\
  org.springblade.common.config.ElasticSearchConfig,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackPlanManageServiceImpl,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackBadDebtServiceImpl,\
  org.springblade.modules.lankegroup.invoiceStatistics.service.impl.InvoiceStaticsServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.MaterialUnitServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.controller.EqualProtectionTaskStatisticsController,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackPlanReceiptRelServiceImpl,\
  org.springblade.modules.xxljob.config.XXLJobAdminConfiguration,\
  org.springblade.flow.business.service.impl.FlowServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.controller.ProfessionController,\
  org.springblade.modules.lankegroup.ImportantList.controller.TodoFollowUserController,\
  org.springblade.common.manager.BusinessAuthManager,\
  org.springblade.modules.lankegroup.jlopportunity.controller.BusinessOpportunityController,\
  org.springblade.modules.wxcp.config.WxcpConfig,\
  org.springblade.modules.lankegroup.taskdelivery.controller.TaskDeliveryController,\
  org.springblade.modules.lankegroup.invoice.service.impl.InvoiceServiceImpl,\
  org.springblade.modules.system.service.impl.UserServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.service.impl.MarketplaceServiceImpl,\
  org.springblade.modules.lankegroup.knowledge.controller.KnowledgeDocumentController,\
  org.springblade.common.utils.ExcelImportHelper,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerContactTagsServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.MessageTimingServiceImpl,\
  org.springblade.Application,\
  org.springblade.modules.lankegroup.project.service.impl.UserProjectNodeServiceImpl,\
  org.springblade.modules.lankegroup.jlopportunity.service.impl.BusinessOpportunityAssessmentServiceImpl,\
  org.springblade.modules.lankegroup.file.controller.ProjectFileController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionStageMemberServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.task.CustomerTask,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.manager.EqualProtectionTaskManager,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionGroupMemberServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.CustomerCareController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressPaymentServiceImpl,\
  org.springblade.modules.system.controller.ParamController,\
  org.springblade.flow.business.feign.WorkFlowClient,\
  org.springblade.modules.lankegroup.jlopportunity.controller.BusinessOpportunityAssessmentController,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressBiddingDecisionController,\
  org.springblade.modules.lankegroup.invoiceStatistics.service.impl.InvoiceLogServiceImpl,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectTeamServiceImpl,\
  org.springblade.modules.lankegroup.pro_management.controller.BiddingCooperationController,\
  org.springblade.modules.lankegroup.crm.controller.ProjectController,\
  org.springblade.modules.system.controller.DictBizController,\
  org.springblade.flow.business.service.impl.FlowBusinessServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.SupplierFollowUpRecordController,\
  org.springblade.modules.lankegroup.outbound.service.impl.OutboundServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.ExpenseReportSkillController,\
  org.springblade.modules.lankegroup.projectClaim.service.impl.claimServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.CustomerGroupController,\
  org.springblade.modules.lankegroup.pro_management.controller.ProjectCollaborationTaskController,\
  org.springblade.modules.wxcp.service.SyncContactService,\
  org.springblade.common.controller.UtilStoreController,\
  org.springblade.flow.engine.utils.ProcessMessageUtils,\
  org.springblade.modules.lankegroup.jl.service.impl.ProductCateServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.CustomerCareMaterialServiceImpl,\
  org.springblade.modules.lankegroup.invoiceStatistics.controller.InvoiceLogController,\
  org.springblade.modules.lankegroup.pro_management.service.impl.ProjectGroupServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.ContactPersonServiceImpl,\
  org.springblade.modules.lankegroup.log.controller.LogController,\
  org.springblade.modules.lankegroup.outbound.service.impl.OutboundMaterialsServiceImpl,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.service.impl.EqualProtectionSystemTaskStageMemberServiceImpl,\
  org.springblade.modules.system.service.impl.UserDeptServiceImpl,\
  org.springblade.workflow.feign.BusinessFormClient,\
  org.springblade.modules.resource.controller.SmsController,\
  org.springblade.modules.develop.service.impl.CodeServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.StockController,\
  org.springblade.modules.lankegroup.target.service.impl.TargetSettingsServiceImpl,\
  org.springblade.modules.lankegroup.jl.controller.quotation.QuotationChargeCategoryController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressPreBidGuidanceServiceImpl,\
  org.springblade.common.config.ExceptionHandlerConfiguration,\
  org.springblade.modules.lankegroup.reim.service.impl.ReimFeeBreakdownServiceImpl,\
  org.springblade.common.utils.UtilStoreServiceImpl,\
  org.springblade.modules.auth.granter.PasswordTokenGranter,\
  org.springblade.modules.lankegroup.purchase.service.impl.PurchaseRequisitionServiceImpl,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressProjectDeliveryServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.ProjectProgressPreBidGuidanceController,\
  org.springblade.modules.lankegroup.contractManagement.manager.ContractWordManager,\
  org.springblade.modules.lankegroup.file.service.impl.ProjectFileServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.SupplierGroupController,\
  org.springblade.modules.lankegroup.ticket.service.impl.TicketPaybackReceiptServiceImpl,\
  org.springblade.modules.lankegroup.project.controller.MInioController,\
  org.springblade.modules.lankegroup.ticket.controller.TicketController,\
  org.springblade.modules.lankegroup.crm.controller.CustomerUseorgController,\
  org.springblade.modules.lankegroup.salesFunnel.service.impl.SalesFunnelServiceImpl,\
  org.springblade.modules.system.controller.RoleController,\
  org.springblade.modules.auth.service.KdEmployeeService,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressBiddingDecisionServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.controller.MarketplaceController,\
  org.springblade.modules.lankegroup.message.controller.SendLogDateController,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectCollectionServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.controller.AreaController,\
  org.springblade.modules.lankegroup.contractManagement.service.impl.ContractChangeServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.controller.ChannelArchiveDetailsController,\
  org.springblade.modules.lankegroup.equalProtectionKanBan.utils.HighRisePermissionsUtil,\
  org.springblade.modules.lankegroup.projectKanBan.config.ProjectKanBanExecutor,\
  org.springblade.modules.system.controller.DeptController,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerLogManagementServiceImpl,\
  org.springblade.common.utils.previousProjects,\
  org.springblade.modules.lankegroup.pro_management.service.impl.ProjectBasicServiceImpl,\
  org.springblade.modules.lankegroup.clientrelation.task.CustomerVisitTask,\
  org.springblade.modules.lankegroup.projectKanBan.controller.FormalProjectController,\
  org.springblade.modules.lankegroup.crm.service.impl.ProjectServiceImpl,\
  org.springblade.modules.lankegroup.target.service.impl.TargetMonthSettingsServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityFbController,\
  org.springblade.modules.system.controller.LogApiController,\
  org.springblade.common.interceptor.AuthInterceptor,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.TeamServiceImpl,\
  org.springblade.flow.engine.controller.FlowManagerController,\
  org.springblade.modules.lankegroup.customerVisit.controller.CustomerVisitCommentController,\
  org.springblade.modules.system.service.impl.LogErrorServiceImpl,\
  org.springblade.modules.lankegroup.crm.service.impl.OrgServiceImpl,\
  org.springblade.modules.lankegroup.contractManagement.controller.ContractController,\
  org.springblade.modules.lankegroup.paybill.service.impl.PaybillAnnexServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.controller.ProjectCollectionController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressProjectCloseServiceImpl,\
  org.springblade.modules.lankegroup.opportunity.controller.ProjectOpportunityIndustryInvolvedController,\
  org.springblade.modules.lankegroup.payback.service.impl.PaybackReceiptApprovalFlowServiceImpl,\
  org.springblade.modules.lankegroup.purchase.controller.PurchaseRequisitionController,\
  org.springblade.modules.lankegroup.reim.controller.ReimController,\
  org.springblade.modules.auth.controller.RoleDeptController,\
  org.springblade.modules.desk.controller.NoticeController,\
  org.springblade.modules.lankegroup.kingdeeproject.controller.BasicSupplierController,\
  org.springblade.modules.lankegroup.common.controller.AttachmentController,\
  org.springblade.modules.lankegroup.ticket.service.impl.TicketPaybackPlanServiceImpl,\
  org.springblade.modules.lankegroup.invoiceCollectionPlan.controller.InvoiceCollectionPlanController,\
  org.springblade.flow.business.service.impl.BusinessProcessServiceImpl,\
  org.springblade.modules.lankegroup.customerVisit.service.impl.CustomerVisitFeeBreakdownServiceImpl,\
  org.springblade.modules.auth.granter.SocialTokenGranter,\
  org.springblade.modules.lankegroup.clientrelation.controller.CustomerKanBanController,\
  org.springblade.modules.lankegroup.crm.service.impl.SupplierGroupServiceImpl,\
  org.springblade.modules.lankegroup.outbound.controller.OutboundMaterialsController,\
  org.springblade.modules.lankegroup.payback.event.listener.PaybackReceiptEventListener,\
  org.springblade.modules.lankegroup.crm.controller.OrgController,\
  org.springblade.modules.lankegroup.pro_management.service.impl.IApproverImpl,\
  org.springblade.modules.system.service.impl.LogApiServiceImpl,\
  org.springblade.modules.lankegroup.crm.controller.NotifyRecordController,\
  org.springblade.modules.lankegroup.project.service.impl.ProjectProgressWarrantyStageServiceImpl,\
  org.springblade.modules.lankegroup.kingdeeproject.service.impl.BasicEmployeeServiceImpl,\
  org.springblade.modules.lankegroup.projectKanBan.service.impl.ProjectCollectionEstimateAmountServiceImpl,\
  org.springblade.modules.system.controller.AuthClientController,\
  org.springblade.modules.wxcp.config.WxcpRobotConfiguration,\
  org.springblade.modules.develop.controller.CodeController
