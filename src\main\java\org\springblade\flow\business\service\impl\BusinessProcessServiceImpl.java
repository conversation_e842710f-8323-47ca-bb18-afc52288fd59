/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.dto.BusinessProcessDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.mapper.BusinessProcessMapper;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.business.util.DateRangeUtil;
import org.springblade.flow.business.vo.BusinessProcessVO;
import org.springblade.flow.business.wrapper.BusinessProcessWrapper;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.workflow.feign.IBusinessFormClient;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 自定义流程审批中的所有业务单据任务 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
@RequiredArgsConstructor
public class BusinessProcessServiceImpl extends BaseServiceImpl<BusinessProcessMapper, BusinessProcessEntity> implements IBusinessProcessService {

	private final IBusinessFormClient businessFormClient;

	@Override
	public IPage<BusinessProcessVO> selectBusinessProcessPage(IPage<BusinessProcessVO> page, BusinessProcessDTO businessProcessTaskDTO) {
		DateRangeUtil.invoke(businessProcessTaskDTO);
		businessProcessTaskDTO.setUserId(AuthUtil.getUserId());
		List<BusinessProcessVO> record;
		if (businessProcessTaskDTO.getFlowTypeCode() == 4) {
			record = baseMapper.selectBusinessProcessCCTaskPage(page, businessProcessTaskDTO);
		} else {
			record = baseMapper.selectBusinessProcessTaskPage(page, businessProcessTaskDTO);
		}
		record = record.stream().map(e -> BusinessProcessWrapper.build().entityVO(e)).collect(Collectors.toList());
		return page.setRecords(record);
	}

	@Override
	public IPage<BusinessProcessVO> selectBusinessProcessTransferPage(IPage<BusinessProcessVO> page, BusinessProcessDTO BusinessProcessTask) {
		List<BusinessProcessVO> record= baseMapper.selectBusinessProcessTransferPage(page, BusinessProcessTask);
		record = record.stream().map(e -> BusinessProcessWrapper.build().entityVO(e)).collect(Collectors.toList());
		return page.setRecords(record);
	}

	@Override
	public boolean updateBusinessProcessUserCompleteTask(String processInstanceId, String assigneeId, boolean isPass) {
		BusinessProcessEntity businessProcessEntity = this.getOne(Wrappers.<BusinessProcessEntity>lambdaQuery()
			.eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId));
		if (businessProcessEntity == null) {
			return false;
		}
		//更新历史审批人
		setHistoryAssignee(businessProcessEntity);
		businessProcessEntity.setStatus(isPass ? ProcessStatusEnum.审批中.getCode() : ProcessStatusEnum.已驳回.getCode());
		updateById(businessProcessEntity);

		businessFormClient.updateBusinessFormStatus(businessProcessEntity.getFormKey(), isPass ? ProcessStatusEnum.审批中 : ProcessStatusEnum.已驳回, String.valueOf(businessProcessEntity.getFormDataId()));

		return true;
	}

	@Override
	public boolean updateBusinessProcessRejectTask(String tableName, String formDataId, String processInstanceId) {
		BusinessProcessEntity businessProcessEntity = this.getOne(Wrappers.<BusinessProcessEntity>lambdaQuery()
			.eq(BusinessProcessEntity::getFormKey, tableName).eq(BusinessProcessEntity::getFormDataId, formDataId).eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId));
		if (businessProcessEntity == null) {
			return false;
		}
		//更新历史审批人
		setHistoryAssignee(businessProcessEntity);
		businessProcessEntity.setStatus(ProcessStatusEnum.已驳回.getCode());
		updateById(businessProcessEntity);

		//更新业务单据
		businessFormClient.updateBusinessFormStatus(tableName, ProcessStatusEnum.已驳回, formDataId);

		return true;
	}

	@Override
	public boolean updateBusinessProcessRevokeTask(String tableName, String formDataId, String processInstanceId) {
		//更新新业务单据流程记录
		this.update(Wrappers.<BusinessProcessEntity>update().lambda()
			.set(BusinessProcessEntity::getStatus, ProcessStatusEnum.已撤回.getCode())
			.eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId)
			.eq(BusinessProcessEntity::getFormKey, tableName)
			.eq(BusinessProcessEntity::getFormDataId, formDataId));

		//更新业务单据
		businessFormClient.updateBusinessFormStatus(tableName, ProcessStatusEnum.已撤回, formDataId);

		return true;
	}

	@Override
	public boolean updateBusinessProcessDeleteFlow(String tableName, String formDataId, String processInstanceId) {
		// 逻辑删除审批待办
		this.update(Wrappers.<BusinessProcessEntity>update().lambda()
			.set(BusinessProcessEntity::getIsDeleted, BladeConstant.DB_IS_DELETED)
			.eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId)
			.eq(BusinessProcessEntity::getFormKey, tableName)
			.eq(BusinessProcessEntity::getFormDataId, formDataId));

		//更新业务单据
		businessFormClient.updateBusinessFormDeleted(tableName, formDataId);

		return true;
	}

	@Override
	public void businessProcessFinshed(String tableName, String formDataId, String processInstanceId) {
		//更新新业务单据流程记录
		this.update(Wrappers.<BusinessProcessEntity>update().lambda()
			.set(BusinessProcessEntity::getApproveTime, new Date())
			.set(BusinessProcessEntity::getStatus, ProcessStatusEnum.已完成.getCode())
			.eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId)
			.eq(BusinessProcessEntity::getFormKey, tableName)
			.eq(BusinessProcessEntity::getFormDataId, formDataId));
		//更新业务单据
		businessFormClient.updateBusinessFormFinshed(tableName, formDataId);
	}

	@Override
	public void businessProcessCcDelegate(String tableName, String formDataId,String ccUserIds, String processInstanceId) {
		//更新新业务单据流程记录
		this.update(Wrappers.<BusinessProcessEntity>update().lambda()
			.set(BusinessProcessEntity::getProcessCcDelegate, ccUserIds)
			.eq(BusinessProcessEntity::getProcessInstanceId, processInstanceId)
			.eq(BusinessProcessEntity::getFormKey, tableName)
			.eq(BusinessProcessEntity::getFormDataId, formDataId));
	}

	/**
	 * 更新历史审批人
	 *
	 * @param businessProcessEntity
	 * @return
	 */
	private BusinessProcessEntity setHistoryAssignee(BusinessProcessEntity businessProcessEntity) {
		StringBuffer historyAssignee = new StringBuffer();
		if (businessProcessEntity != null) {
			if (!StringUtil.isEmpty(businessProcessEntity.getHistoryAssignee())) {
				historyAssignee.append(businessProcessEntity.getHistoryAssignee());
				historyAssignee.append("," + AuthUtil.getUserId());
			} else {
				historyAssignee.append(AuthUtil.getUserId());
			}
		}
		businessProcessEntity.setHistoryAssignee(String.valueOf(historyAssignee));
		return businessProcessEntity;
	}
}
