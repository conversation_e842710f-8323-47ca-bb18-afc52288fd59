package org.springblade.modules.lankegroup.contractManagement.manager;

import cn.hutool.core.util.StrUtil;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.AllArgsConstructor;
import org.springblade.core.oss.MinioTemplate;
import org.springblade.core.oss.rule.OssRule;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractMainDTO;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;
import org.springblade.modules.lankegroup.contractManagement.enums.ContractCategoryEnum;
import org.springblade.modules.lankegroup.contractManagement.enums.SignMethodEnum;
import org.springblade.modules.lankegroup.contractManagement.utils.CreateWordUtil;
import org.springblade.modules.system.mapper.UserMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

@Service
@AllArgsConstructor
public class ContractWordManager {

    private final MinioTemplate minioTemplate;
    private final MinioClient minioClient;
    private final OssRule ossRule;
    private final UserMapper userMapper;
    private final CreateWordUtil createWordUtil;

    /**
     * 导出word并上传到oss
     */
    public Kv exportWordAndPutOss(ContractMainDTO dto) {
        String fileName = StrUtil.format(CreateWordUtil.EXPORT_WORD_FILE_NAME,
                SignMethodEnum.CUSTOMER_CONTACT_SIGN.getDesc().equals(dto.getSignMethod()) ? dto.getCustomerContactName() : dto.getCustomerName(),
                ContractCategoryEnum.getTemplateName(dto.getCategory()),
                dto.getSignDate()
        );
        byte[] bytes = createWordUtil.export(dto);
        String filePath = this.putOss(fileName, bytes);
        return Kv.create().set("fileName", fileName).set("filePath", filePath);
    }


    /**
     * 上传到oss
     *
     * @param filename 文件名，重复也没关系，主要是为了获得拓展名
     * @param bytes    字节数组
     * @return oss的uri地址
     */
    private String putOss(String filename, byte[] bytes) {
        if (bytes.length == 0) {
            return null;
        }
        InputStream is = new ByteArrayInputStream(bytes);
        // 加了个文件夹，以后删除没用的数据容易删
        String path = "contractpreview/" + this.ossRule.fileName(filename);

        try {
            this.minioClient.putObject(PutObjectArgs.builder().bucket(this.ossRule.bucketName(this.minioTemplate.getBucket().name())).object(path).stream(is, is.available(), -1L).contentType("application/octet-stream").build());
        } catch (Exception ignored) {
            return null;
        }
        return path;
    }

    /**
     * 校验数据
     *
     * @param entity 销售合同
     * @return true校验成功, false校验失败
     */
    public boolean checkData(ContractMain entity) {
        if (entity == null) {
            return false;
        }
        boolean isNullField = Func.isAnyBlank(
                // TODO 待做校验
        );
        return !isNullField;
    }

}
