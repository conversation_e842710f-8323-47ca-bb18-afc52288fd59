package org.springblade.flow.engine.listener.flowable;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> mtf
 * @description : 自定义 flowable事件接口通知
 * 当任务完成或者被拒绝时像配置接口推送数据
 **/
@Slf4j
@Component(value = "userTaskCompleteListener")
@AllArgsConstructor
public class UserTaskCompleteListener implements TaskListener {

	private final IBusinessProcessService businessProcessClient;

	@Override
	public void notify(DelegateTask delegateTask) {
		// 审核状态 通过拒绝
		Object approveStatus = delegateTask.getVariable(ProcessConstant.PASS_KEY);
		if (ObjectUtil.isNull(approveStatus)) {
			return;
		}
		//更新业务单据审批人和审批状态
//		businessProcessClient.updateBusinessProcessUserCompleteTask(delegateTask.getId(), delegateTask.getProcessInstanceId(), String.valueOf(AuthUtil.getUserId()), (Boolean) approveStatus);
		businessProcessClient.updateBusinessProcessUserCompleteTask(delegateTask.getProcessInstanceId(), String.valueOf(AuthUtil.getUserId()), (Boolean) approveStatus);
	}
}
