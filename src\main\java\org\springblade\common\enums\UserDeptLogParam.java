package org.springblade.common.enums;

import lombok.Data;

@Data
public class UserDeptLogParam {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 联系人统计
     */
    private Integer contactCount;

    /**
     * 项目统计
     */
    private Integer projectCount;

    /**
     * 合同统计
     */
    private Integer contractCount;

    /**
     * 拜访统计
     */
    private Integer callCount;

    /**
     * 客户统计
     */
    private Integer clientCount;

    /**
     * 项目相关列表
     */
    private String projectCorrelationList;

    private String type;

    private String deptType;

    private String status;

    private String groupId;

    private String deptId;

    private String createName;

    private String parentId;

}
