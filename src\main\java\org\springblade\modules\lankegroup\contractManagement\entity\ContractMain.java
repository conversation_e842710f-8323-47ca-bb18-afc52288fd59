/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 合同主表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@TableName("cm_contract_main")
@EqualsAndHashCode(callSuper = true)
public class ContractMain extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 合同类型(1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)
     */
    @ApiModelProperty(value = "合同类型(1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养)")
    private String category;
    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String name;
    /**
     * 合同编号 【后端生成】
     */
    @ApiModelProperty(value = "合同编号 【后端生成】")
    private String code;
    /**
     * 商机ID
     */
    @ApiModelProperty(value = "商机ID")
    private Long businessOpportunityId;
    /**
     * 商机名称
     */
    @ApiModelProperty(value = "商机名称")
    private String businessOpportunityName;
    /**
     * 商机编号
     */
    @ApiModelProperty(value = "商机编号")
    private String businessOpportunityCode;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    /**
     * 签约方式 客户签约，机构签约
     */
    @ApiModelProperty(value = "签约方式 客户签约，机构签约")
    private String signMethod;
    /**
     * 签约日期
     */
    @ApiModelProperty(value = "签约日期")
    private LocalDate signDate;
    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal amount;
    /**
     * 合同金额大写
     */
    @ApiModelProperty(value = "合同金额大写")
    private String amountText;
    /**
     * 截止日期、交货日期
     */
    @ApiModelProperty(value = "截止日期、交货日期")
    private LocalDate endDate;
    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private Long customerContactId;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerContactName;
    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话")
    private String customerContactPhone;
    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long customerId;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String customerName;
    /**
     * 机构通讯地址
     */
    @ApiModelProperty(value = "机构通讯地址")
    private String customerAddress;
    /**
     * 乙方ID
     */
    @ApiModelProperty(value = "乙方ID")
    private Long partyBId;
    /**
     * 乙方名称
     */
    @ApiModelProperty(value = "乙方名称")
    private String partyBName;
    /**
     * 乙方通讯地址
     */
    @ApiModelProperty(value = "乙方通讯地址")
    private String partyBAddress;
    /**
     * 乙方 【签约公司属性-银行卡号】需要后端静默获取并保存
     */
    @ApiModelProperty(value = "乙方 【签约公司属性-银行卡号】需要后端静默获取并保存")
    private String partyBScBankCode;
    /**
     * 乙方 【签约公司属性-开户行名称】需要后端静默获取并保存
     */
    @ApiModelProperty(value = "乙方 【签约公司属性-开户行名称】需要后端静默获取并保存")
    private String partyBScAccountName;
    /**
     * 乙方 【签约公司属性-纳税登记号】需要后端静默获取并保存
     */
    @ApiModelProperty(value = "乙方 【签约公司属性-纳税登记号】需要后端静默获取并保存")
    private String partyBScTaxNumber;
    /**
     * 乙方 【签约公司属性-联系电话】需要后端静默获取并保存
     */
    @ApiModelProperty(value = "乙方 【签约公司属性-联系电话】需要后端静默获取并保存")
    private String partyBScPhone;
    /**
     * 乙方联系人【销售】
    @ApiModelProperty(value = "乙方联系人【销售】")
     */
    private String partyBPerson;
    /**
     * 乙方电话【销售电话】
     */
    @ApiModelProperty(value = "乙方电话【销售电话】")
    private String partyBPhone;
    /**
     * 用章类型：合同章、公章、法人章、财务章
     */
    @ApiModelProperty(value = "用章类型：合同章、公章、法人章、财务章")
    private String sealType;
    /**
     * 草稿状态(0:草稿,1:非草稿)
     */
    @ApiModelProperty(value = "草稿状态(0:草稿,1:非草稿)")
    private Integer draftStatus;
    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;
    /**
     * 模板路径
     */
    @ApiModelProperty(value = "模板路径")
    private String templateUrl;
    /**
     * 预览路径
     */
    @ApiModelProperty(value = "预览路径")
    private String previewUrl;
    /**
     * 预览名称
     */
    @ApiModelProperty(value = "预览名称")
    private String previewName;


}
