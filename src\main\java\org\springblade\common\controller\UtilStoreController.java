package org.springblade.common.controller;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiOperation;
import org.springblade.common.controller.vo.UtilStoreGetReqVO;
import org.springblade.common.controller.vo.UtilStoreReqVO;
import org.springblade.common.utils.UtilStoreServiceImpl;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Validated
@RequestMapping("/common")
public class UtilStoreController {

    @Resource
    private UtilStoreServiceImpl utilService;

    @PostMapping("/store")
    @ApiOperation(value = "公共存储", notes = "公共存储")
    public R<String> store(@RequestBody @Valid UtilStoreReqVO reqVO) {
        String s = utilService.generateIdAndSet(reqVO.getJsonStr(), reqVO.getPwd(), reqVO.getExpireTime());
        return R.data(s);
    }

    @PostMapping("/store/get")
    @ApiOperation(value = "获取公共存储", notes = "根据页面业务标识和场景获取按钮权限配置")
    public R<JSONObject> get(@RequestBody @Valid UtilStoreGetReqVO reqVO) {
        return R.data(utilService.getStore(reqVO));
    }

}
