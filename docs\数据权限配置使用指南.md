# 数据权限配置使用指南（改进版）

## 概述

这是一个改进版的通用数据权限解决方案，支持：
- **默认权限配置**：部门领导看部门、普通员工看自己、财务领导看所有
- **业务类型配置**：通过`DataPermissionFieldConfig`自动配置负责人字段
- **极简使用**：一行代码应用权限，一行XML引用SQL片段

## 核心特性

### ✅ 默认权限规则
- **高层(1)**：查看所有数据
- **财务(2)**：查看所有数据
- **部门领导(3)**：查看本部门及下属部门数据
- **普通员工(4)**：查看自己负责的数据（可选择看下级）

### ✅ 自动字段配置
- 系统自动根据业务类型应用对应的负责人字段
- 支持多个负责人字段的OR关系权限控制
- 可扩展的配置管理

### ✅ 三种使用模式
1. **兼容模式**：保持原有API不变
2. **配置模式**：指定业务类型，自动应用字段配置
3. **智能模式**：根据VO类名自动推断业务类型

## 使用步骤

### 步骤1：在VO中添加权限字段

```java
public class YourBusinessVO {
    // 业务字段...
    
    // ========== 数据权限字段（必需）==========
    /**
     * 当前用户级别 1.高层；2.财务；3.部门领导；4.普通员工
     */
    @ApiModelProperty(value = "当前用户级别")
    private Integer targetUserLevel;

    /**
     * 允许查看的用户ID列表（null=无限制）
     */
    @ApiModelProperty(value = "允许查看的用户ID列表")
    private List<Long> allowedUserIds;

    /**
     * 数据权限字段列表（自动配置）
     */
    @ApiModelProperty(value = "数据权限字段列表")
    private List<String> dataPermissionFields;

    /**
     * 数据属性（如"sub"表示查看下级数据）
     */
    @ApiModelProperty(value = "数据属性")
    private String dataAttribute;
}
```

### 步骤2：配置业务类型字段（可选）

在`DataPermissionFieldConfig`中配置业务类型的负责人字段：

```java
// 已预配置的业务类型
static {
    // 报价单模块
    BUSINESS_OWNER_FIELDS.put("quotation", Arrays.asList("quotation_user_id", "create_user"));
    
    // 商机模块
    BUSINESS_OWNER_FIELDS.put("business_opportunity", Arrays.asList("responsible_user_id", "create_user"));
    
    // 客户模块
    BUSINESS_OWNER_FIELDS.put("customer", Arrays.asList("responsible_user_id", "create_user"));
    
    // 你可以添加更多业务类型...
}
```

### 步骤3：在Service中应用权限

有三种使用方式：

#### 方式1：兼容模式（保持原有用法）
```java
@Override
public IPage<YourBusinessVO> selectPage(IPage<YourBusinessVO> page, YourBusinessVO query) {
    // 使用默认权限配置（向下兼容）
    dataPermissionManager.applyDataPermission(query, query.getDataAttribute());
    
    List<YourBusinessVO> records = baseMapper.selectPage(page, query);
    return page.setRecords(records);
}
```

#### 方式2：配置模式（推荐）
```java
@Override
public IPage<QuotationVO> selectQuotationPage(IPage<QuotationVO> page, QuotationVO quotation) {
    // 指定业务类型，自动应用字段配置
    dataPermissionManager.applyDataPermissionWithBusinessType(quotation, quotation.getDataAttribute(), "quotation");
    
    List<QuotationVO> records = baseMapper.selectQuotationPage(page, quotation);
    return page.setRecords(records);
}
```

#### 方式3：智能模式
```java
@Override
public IPage<QuotationVO> selectQuotationPage(IPage<QuotationVO> page, QuotationVO quotation) {
    // 根据VO类名自动推断业务类型
    dataPermissionManager.applyDataPermissionAuto(quotation, quotation.getDataAttribute());
    
    List<QuotationVO> records = baseMapper.selectQuotationPage(page, quotation);
    return page.setRecords(records);
}
```

### 步骤4：在Mapper.xml中使用SQL片段

有三种SQL片段可选：

#### 选项1：自动配置（推荐）
```xml
<select id="selectPage" resultType="com.example.vo.YourBusinessVO">
    SELECT * FROM your_table 
    WHERE is_deleted = 0
    
    <!-- 自动使用业务类型配置的字段 -->
    <include refid="DataPermissionSql.autoDataPermissionCondition" />
    
    ORDER BY create_time DESC
</select>
```

#### 选项2：单个字段
```xml
<select id="selectPage" resultType="com.example.vo.YourBusinessVO">
    SELECT * FROM your_table 
    WHERE is_deleted = 0
    
    <!-- 指定单个负责人字段 -->
    <include refid="DataPermissionSql.dataPermissionCondition">
        <property name="ownerField" value="responsible_user_id"/>
    </include>
    
    ORDER BY create_time DESC
</select>
```

#### 选项3：多个字段
```xml
<select id="selectPage" resultType="com.example.vo.YourBusinessVO">
    SELECT * FROM your_table 
    WHERE is_deleted = 0
    
    <!-- 指定多个负责人字段（OR关系） -->
    <include refid="DataPermissionSql.multiFieldDataPermissionCondition">
        <property name="ownerFields" value="responsible_user_id,create_user,quotation_user_id"/>
    </include>
    
    ORDER BY create_time DESC
</select>
```

## 完整使用示例

### 报价单模块示例

#### 1. VO定义
```java
@Data
@ApiModel(value = "QuotationVO", description = "报价单视图对象")
public class QuotationVO extends QuotationBaseVO {
    // ... 业务字段
    
    // 数据权限字段
    private Integer targetUserLevel;
    private List<Long> allowedUserIds;
    private List<String> dataPermissionFields;
    private String dataAttribute;
}
```

#### 2. Service实现
```java
@Service
@AllArgsConstructor
public class QuotationServiceImpl implements IQuotationService {
    
    private final DataPermissionManager dataPermissionManager;
    
    @Override
    public IPage<QuotationVO> selectQuotationPage(IPage<QuotationVO> page, QuotationVO quotation) {
        // 一行代码应用权限
        dataPermissionManager.applyDataPermissionWithBusinessType(quotation, quotation.getDataAttribute(), "quotation");
        
        List<QuotationVO> records = baseMapper.selectQuotationPage(page, quotation);
        return page.setRecords(records);
    }
}
```

#### 3. Mapper.xml配置
```xml
<mapper namespace="org.example.mapper.QuotationMapper">
    <select id="selectQuotationPage" resultType="org.example.vo.QuotationVO">
        SELECT * FROM jl_quotation 
        WHERE is_deleted = 0
        
        <!-- 其他查询条件 -->
        <if test="query.quotationCode != null and query.quotationCode != ''">
            AND quotation_code LIKE CONCAT('%', #{query.quotationCode}, '%')
        </if>
        
        <!-- 一行代码应用数据权限 -->
        <include refid="DataPermissionSql.autoDataPermissionCondition" />
        
        ORDER BY create_time DESC
    </select>
</mapper>
```

## 权限逻辑说明

### 自动权限计算

| 用户级别 | allowedUserIds | dataPermissionFields | SQL效果 |
|---------|---------------|---------------------|---------|
| 高层(1) | null | N/A | 无WHERE限制，查看所有数据 |
| 财务(2) | null | N/A | 无WHERE限制，查看所有数据 |
| 部门领导(3) | [部门用户ID列表] | ["quotation_user_id", "create_user"] | WHERE (quotation_user_id IN (...) OR create_user IN (...)) |
| 普通员工(4) | [自己ID, 下级ID...] | ["quotation_user_id", "create_user"] | WHERE (quotation_user_id IN (...) OR create_user IN (...)) |

### 字段配置优先级

1. **有dataPermissionFields**：使用配置的字段列表
2. **无dataPermissionFields**：使用默认的create_user字段
3. **高层/财务**：跳过所有权限检查

## 扩展功能

### 查看下级数据
```java
// 普通员工查看下级数据
quotationVO.setDataAttribute("sub");
dataPermissionManager.applyDataPermissionWithBusinessType(quotationVO, "sub", "quotation");
```

### 动态添加业务配置
```java
// 运行时动态添加新的业务类型配置
dataPermissionFieldConfig.addBusinessConfig("new_business", 
    Arrays.asList("owner_user_id", "responsible_user_id", "create_user"));
```

### 自定义字段配置
```java
// 获取现有配置
List<String> fields = dataPermissionFieldConfig.getOwnerFields("quotation");

// 检查是否已配置
boolean configured = dataPermissionFieldConfig.isConfigured("quotation");
```

## 性能优化建议

### 1. 数据库索引
为负责人字段建立索引：
```sql
-- 为配置的负责人字段建立索引
CREATE INDEX idx_quotation_user_id ON jl_quotation(quotation_user_id);
CREATE INDEX idx_create_user ON jl_quotation(create_user);
```

### 2. 批量查询优化
对于复杂查询，考虑在应用层进行权限过滤：
```java
// 先查询数据，再在应用层过滤
List<QuotationVO> allRecords = baseMapper.selectAll();
List<QuotationVO> filteredRecords = allRecords.stream()
    .filter(record -> hasPermission(record))
    .collect(Collectors.toList());
```

## 迁移指南

### 从原版本迁移

1. **VO字段更新**：添加`dataPermissionFields`字段
2. **Service调用更新**：将`applyDataPermission`改为`applyDataPermissionWithBusinessType`
3. **Mapper.xml更新**：使用通用SQL片段替换硬编码权限条件

### 兼容性保证

- 原有的`applyDataPermission`方法仍然可用
- 原有的权限字段保持不变
- 渐进式迁移，可以逐步更新各个模块

## 总结

改进版数据权限系统的优势：

### 🎯 更灵活的配置
- **业务类型配置**：每个业务模块可以配置不同的负责人字段
- **多字段支持**：支持多个负责人字段的OR关系权限控制
- **动态配置**：支持运行时添加新的业务类型配置

### 🎯 更简单的使用
- **一行代码**：Service中一行代码应用权限
- **一行XML**：Mapper.xml中一行代码引用权限条件
- **自动推断**：支持根据VO类名自动推断业务类型

### 🎯 更好的维护性
- **统一管理**：所有权限逻辑集中在DataPermissionManager中
- **配置分离**：权限字段配置与业务逻辑分离
- **向下兼容**：保持原有API的兼容性

这个改进版系统既保持了原有的简单性，又增加了灵活性和可扩展性，满足了你对默认权限配置和业务类型配置的需求。 