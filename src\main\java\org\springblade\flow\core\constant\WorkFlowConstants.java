package org.springblade.flow.core.constant;

/**
 * @description: 工作流常量
 * @author: smalljop
 * @create: 2020-02-12 22:34
 **/
public interface WorkFlowConstants {


    /**
     * 审批是否通过常量
     */
    String APPROVE_KEY = "approveStatus";


    /**
     * 任务分配岗位前缀
     */
    String TASK_CANDIDATE_GROUP_POST = "POST_";


    /**
     * 任务分配角色前缀
     */
    String TASK_CANDIDATE_GROUP_ROLE = "ROLE_";

    /**
     * 同意事件属性
     */
    String BPMN_AGREE_EVENT = "flowable:agreeEvent";


    /**
     * 同意事件属性
     */
    String BPMN_REJECT_EVENT = "flowable:rejectEvent";


    /**
     * 匿名用户名
     */
    String ANONYMOUS_USER_NAME = "anonymous";

    String ANONYMOUS_SHOW_USER_NAME = "匿名用户";


    /**
     * 部门主管变量名
     */
    String DEPT_LEVEL_LEADER = "deptLevelLeader";

	/**
	 * 创建人直属领导变量名称
	 */
	String USER_IMMEDIATE_SUPERVISOR = "userImmediateSupervisor";

	// 动态审批人 表单字段
    String APPROVE_FORM_VAR = "approveFormVar";

    /**
     * 会签列表流程字段名
     */
    String MULTI_INSTANCE_ASSIGNEE_LIST_VAR = "assigneeList";

    /**
     * 发起人流程变量名
     */
    String PROC_INSTANCE_START_USER_NAME_VAR = "initiator";

	/**
	 * 发起人部门流程变量名
	 */
    String PROC_INSTANCE_START_USER_DEPT_VAR = "initiatorDept";

    /**
     * 流程名
     */
    String PROC_INSTANCE_NAME = "processName";

    /**
     * 流程关联表单Id
     */
    String PROC_INSTANCE_FORM_KEY = "formKey";

    /**
     * 流程关联表单Code
     */
    String PROC_INSTANCE_FORM_CODE = "formCode";

    /**
     * 表单数据ID
     */
    String PROC_INSTANCE_FORM_DATA_ID = "formDataId";

    /**
     * 转交给管理员的原始审批人
     */
    String ORIGINAL_APPROVER = "originalApprover";
}
