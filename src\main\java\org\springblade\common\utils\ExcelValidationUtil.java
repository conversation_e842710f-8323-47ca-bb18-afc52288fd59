package org.springblade.common.utils;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Excel校验工具类
 * 提供通用的Excel导入校验方法
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class ExcelValidationUtil {


    /**
     * 校验必填字段
     * 根据@ExcelProperty注解中value值末尾是否带星号(*)来判断是否为必填字段
     *
     * @param data     Excel数据对象
     * @param rowIndex 行号
     * @param <T>      Excel数据类型
     * @return 错误信息列表
     */
    public static <T> List<String> validateRequiredFields(T data, Integer rowIndex) {
        List<String> errors = new ArrayList<>();
        
        if (data == null) {
            errors.add("数据不能为空");
            return errors;
        }

        Class<?> clazz = data.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                String[] values = excelProperty.value();
                if (values.length > 0) {
                    String fieldName = values[0];
                    
                    // 检查字段名是否以星号结尾，表示必填
                    if (fieldName.startsWith("*")) {
                        String displayName = fieldName.substring(1);
                        
                        try {
                            field.setAccessible(true);
                            Object fieldValue = field.get(data);
                            
                            if (isEmpty(fieldValue)) {
                                errors.add(displayName + "不能为空");
                            }
                        } catch (IllegalAccessException e) {
                            log.error("获取字段值失败：{}", field.getName(), e);
                        }
                    }
                }
            }
        }

        return errors;
    }

    /**
     * 设置系统字段（创建时）
     *
     * @param entity 实体对象
     * @param <T>    实体类型
     */
    public static <T> void setCreateSystemFields(T entity) {
        try {
            // 设置创建用户
            Field createUserField = ReflectUtil.getField(entity.getClass(), "createUser");
            if (createUserField != null) {
                createUserField.setAccessible(true);
                createUserField.set(entity, AuthUtil.getUserId());
            }

            // 设置创建时间
            Field createTimeField = ReflectUtil.getField(entity.getClass(), "createTime");
            if (createTimeField != null) {
                createTimeField.setAccessible(true);
                createTimeField.set(entity, new Date());
            }

            // 设置更新用户
            Field updateUserField = ReflectUtil.getField(entity.getClass(), "updateUser");
            if (updateUserField != null) {
                updateUserField.setAccessible(true);
                updateUserField.set(entity, AuthUtil.getUserId());
            }

            // 设置更新时间
            Field updateTimeField = ReflectUtil.getField(entity.getClass(), "updateTime");
            if (updateTimeField != null) {
                updateTimeField.setAccessible(true);
                updateTimeField.set(entity, new Date());
            }

            // 设置状态
            Field statusField = ReflectUtil.getField(entity.getClass(), "status");
            if (statusField != null) {
                statusField.setAccessible(true);
                statusField.set(entity, 1);
            }

            // 设置删除标记
            Field isDeletedField = ReflectUtil.getField(entity.getClass(), "isDeleted");
            if (isDeletedField != null) {
                isDeletedField.setAccessible(true);
                isDeletedField.set(entity, 0);
            }

        } catch (Exception e) {
            log.error("设置系统字段失败", e);
        }
    }

    /**
     * 判断值是否为空
     *
     * @param value 值
     * @return 是否为空
     */
    private static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StrUtil.isBlank((String) value);
        }
        if (value instanceof Number) {
            return false; // 数字类型0也是有效值
        }
        return false;
    }

}