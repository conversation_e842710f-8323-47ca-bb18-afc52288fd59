/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.Org;
import org.springblade.modules.lankegroup.crm.vo.OrgVO;
import org.springblade.modules.lankegroup.crm.service.IOrgService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 组织表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/org/org")
@Api(value = "组织表", tags = "组织表接口")
public class OrgController extends BladeController {

	private final IOrgService orgService;

	/**
	 * 详情
	 */
	@ApiLog("详情")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入org")
	public R<Org> detail(Org org) {
		Org detail = orgService.getOne(Condition.getQueryWrapper(org));
		return R.data(detail);
	}

	/**
	 * 分页 组织表
	 */
	@ApiLog("分页")
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入org")
	public R<IPage<Org>> list(Org org, Query query) {
		IPage<Org> pages = orgService.page(Condition.getPage(query), Condition.getQueryWrapper(org));
		return R.data(pages);
	}

	/**
	 * 自定义分页 组织表
	 * 每页默认条数改为100条
	 *  过滤条件为：小于900的且id！=1（兰科集团为虚拟组织，无需展示）
	 *  合同管理乙方
	 */
	@ApiLog("分页")
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入org")
	public R<IPage<OrgVO>> page(OrgVO org, Query query) {
		query.setSize(100);
		IPage<OrgVO> pages = orgService.selectOrgPage(Condition.getPage(query), org);
		return R.data(pages);
	}

	/**
	 * 新增 组织表
	 */
	@ApiLog("新增")
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入org")
	public R save(@Valid @RequestBody Org org) {
		return R.status(orgService.save(org));
	}

	/**
	 * 修改 组织表
	 */
	@ApiLog("修改")
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入org")
	public R update(@Valid @RequestBody Org org) {
		return R.status(orgService.updateById(org));
	}

	/**
	 * 新增或修改 组织表
	 */
	@ApiLog("新增/修改")
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入org")
	public R submit(@Valid @RequestBody Org org) {
		return R.status(orgService.saveOrUpdate(org));
	}

	
	/**
	 * 删除 组织表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(orgService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 供应商、客户在分配的时候，需要根据金蝶的【基础资料控制策略】来规范哪些组织可以被分配
	 * @param basicId  基础资料id
	 * @return  该基础资料可被使用/分配的组织
	 */
	@ApiLog("部分组织")
	@GetMapping("/basicControlStrategy")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "部分组织", notes = "传入基础资料id")
	public R basicControlStrategy(String basicId) {
		return R.data(orgService.basicControlStrategy(basicId));
	}

	/**
	 * 收款单结算组织使用
	 * 云星空组织有很多，并非所有组织都有结算职能，需要多组织的业务职能进行筛选
	 *
	 * @return  云星空所有有业务职能的组织
	 */
	@ApiLog("结算组织")
	@GetMapping("/businessOrganization")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "部分组织", notes = "展示所有有业务职能的组织")
	public R businessOrganization() {
		return R.data(orgService.businessOrganization());
	}
	
}
