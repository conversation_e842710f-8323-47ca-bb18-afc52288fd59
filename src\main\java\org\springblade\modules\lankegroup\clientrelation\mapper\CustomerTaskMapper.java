package org.springblade.modules.lankegroup.clientrelation.mapper;

import org.springblade.modules.lankegroup.clientrelation.entity.CustomerTaskEntity;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerizationNew;

import java.util.List;

/**
 * 客户统计定时相关
 * <AUTHOR>
 */
public interface CustomerTaskMapper {


    /**
     * 查询各客户属性
     * @return
     */
    List<CustomerTaskEntity> getCustomerData();

    /**
     * 保存各客户属性
     */
    void saveCustomerData(List<CustomerTaskEntity> list);

    /**
     * 删除各客户属性
     */
    void deleteCustomerData();

    /**
     * 查询所有区域市场ID
     * @return
     */
    List<String> getProfessionListId();

    /**
     * 查询所有区域市场下人员ID
     * @return
     */
    List<String> getProfessionByUserId(String professionId);

    /**
     * 查询当前区域市场所负责客户列表
     * @param professionId
     * @param userList
     * @return
     */
    List<CustomerTaskEntity> getProfessionByCustomerList(String professionId,List<String> userList);

    /**
     * 保存各区域市场负责的客户信息
     */
    void saveProfessionByCustomerList(List<CustomerTaskEntity> list);

    /**
     * 删除各区域市场负责的客户信息
     */
    void deleteProfessionByCustomerList(String professionId);

    /**
     * 查询当前人员负责客户信息
     * @param userId
     * @return
     */
    List<CustomerTaskEntity> getUserByCustomerList(String userId);

    /**
     * 保存各人员客户信息
     * @param list
     */
    void saveUserByCustomerList(List<CustomerTaskEntity> list);

    /**
     * 删除各负责人客户信息
     */
    void deleteUserByCustomerList(String userId);

    /**
     * 删除各区域市场区域统计
     */
    void deleteProfessionAreaGroup(String professionId);

    /**
     * 查询各区域市场各区域客户统计
     * @return
     */
    List<CustomerTaskEntity> getProfessionAreaGroup(String professionId,List<String> userList);

    /**
     * 新增各区域市场各区域客户统计
     * @param list
     */
    void saveProfessionAreaGroup(List<CustomerTaskEntity> list);

    /**
     * 删除各区域市场各区县区域统计
     */
    void deleteProfessionAreaCityGroup(String professionId,String areaCode);

    /**
     * 查询各区域市场各区县区域客户统计
     * @return
     */
    List<CustomerTaskEntity> getProfessionAreaCityGroup(String professionId,List<String> userList,String areaCode);

    /**
     * 新增各区域市场各区县区域客户统计
     * @param list
     */
    void saveProfessionAreaCityGroup(List<CustomerTaskEntity> list,String professionId,String areaCode);

    /**
     * 删除各区域市场各区县区域统计
     */
    void deleteProfessionCustomerCount(String professionId);

    /**
     * 查询各区域市场各区县区域客户统计
     * @return
     */
    List<CustomerTaskEntity> getProfessionCustomerCount(String professionId,List<String> userList);

    /**
     * 新增各区域市场各区县区域客户统计
     * @param list
     */
    void saveProfessionCustomerCount(List<CustomerTaskEntity> list);

    /**
     * 查询所有负责人信息
     * @return
     */
    List<String> getPersonnelList();

    /**
     * 查询所有负责人应有实有客户信息
     * @return
     */
    List<CustomerTaskEntity> getPersonnelCustomer();

    /**
     * 删除所有负责人应有是有客户信息
     */
    void deletePersonnelCustomer();

    /**
     * 新增所有负责人应有是有客户信息
     */
    void savePersonnelCustomer(List<CustomerTaskEntity> list);

    /**
     * 查询各人员市区应有实有客户
     * @param personnelId
     * @return
     */
    List<CustomerTaskEntity> getPersonnelUrbanCustomer(String personnelId);

    /**
     * 删除各人员市区应有实有客户
     * @param personnelId
     * @return
     */
    void deletePersonnelUrbanCustomer(String personnelId);

    /**
     * 新增各人员市区应有实有客户
     * @param
     * @return
     */
    void savePersonnelUrbanCustomer(List<CustomerTaskEntity> list);

    /**
     * 查询各人员区县应有实有客户
     * @param personnelId
     * @return
     */
    List<CustomerTaskEntity> getPersonnelCityCustomer(String personnelId,String areaCode);

    /**
     * 删除各人员区县应有实有客户
     * @param personnelId
     * @return
     */
    void deletePersonnelCityCustomer(String personnelId,String areaCode);

    /**
     * 新增各人员区县应有实有客户
     * @param
     * @return
     */
    void savePersonnelCityCustomer(List<CustomerTaskEntity> list,String personnelId,String areaCode);

}
