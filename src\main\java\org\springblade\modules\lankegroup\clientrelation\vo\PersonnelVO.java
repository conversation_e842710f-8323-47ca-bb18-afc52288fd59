package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

import java.util.List;

/**
 * 人员
 */
@Data
public class PersonnelVO {

    private Long id;

    private String userId;

    private String userName;

    private String deptId;

    private String deptName;

    /**
     * 市场ID
     */
    private String professionId;

    /**
     * 市场名称
     */
    private String professionName;

    /**
     * 区县行业列表
     */
    private List<PersonnelMarketResultVO> makerList;

    private boolean parent;
}
