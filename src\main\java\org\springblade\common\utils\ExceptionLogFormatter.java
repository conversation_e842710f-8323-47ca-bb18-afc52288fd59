package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 异常日志格式化工具类
 * 统一处理异常日志的格式化和输出，包含控制器、方法等关键信息
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Component
@Slf4j
public class ExceptionLogFormatter {
    
    @Value("${spring.profiles.active:prod}")
    private String activeProfile;
    
    /**
     * 格式化并输出异常日志（统一入口）
     * 
     * @param exception 异常对象
     * @param logType 日志类型（ERROR、WARN、INFO）
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logException(Exception exception, String logType, String userMessage, String technicalMessage) {
        // 构建完整的异常信息
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        
        // 根据日志类型输出
        switch (logType.toUpperCase()) {
            case "ERROR":
                log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
                break;
            case "WARN":
                log.warn(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
                break;
            case "INFO":
                log.info(logInfo.getFormattedMessage());
                break;
            default:
                log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
        }
    }
    
    /**
     * 格式化数据库异常日志（专门用于数据库异常）
     * 
     * @param exception 数据库异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logDatabaseException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("数据库异常");
        
        // 输出单行ERROR日志，包含过滤后的堆栈信息
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化唯一索引冲突异常日志
     * 
     * @param exception 唯一索引冲突异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logUniqueConstraintException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("唯一索引冲突");
        
        // 输出单行ERROR日志，包含过滤后的堆栈信息
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化业务异常日志
     * 
     * @param exception 业务异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logBusinessException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("业务异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化系统异常日志
     * 
     * @param exception 系统异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logSystemException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("系统异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化参数校验异常日志
     * 
     * @param exception 参数校验异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logValidationException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("参数校验异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化网络异常日志
     * 
     * @param exception 网络异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logNetworkException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("网络异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化空指针异常日志
     * 
     * @param exception 空指针异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logNullPointerException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("空指针异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化请求异常日志
     * 
     * @param exception 请求异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logRequestException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("请求异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 格式化未知异常日志
     * 
     * @param exception 未知异常
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     */
    public void logUnknownException(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = buildExceptionLogInfo(exception, userMessage, technicalMessage);
        logInfo.setExceptionCategory("未知异常");
        
        log.error(logInfo.getFormattedMessageWithFilteredStackTrace(exception));
    }
    
    /**
     * 判断是否为开发环境
     */
    private boolean isDevelopmentEnvironment() {
        return "dev".equals(activeProfile) || "test".equals(activeProfile);
    }
    
    /**
     * 构建异常日志信息
     * 
     * @param exception 异常对象
     * @param userMessage 用户友好的错误消息
     * @param technicalMessage 技术详细信息
     * @return 异常日志信息对象
     */
    private ExceptionLogInfo buildExceptionLogInfo(Exception exception, String userMessage, String technicalMessage) {
        ExceptionLogInfo logInfo = new ExceptionLogInfo();
        
        // 设置异常基本信息
        logInfo.setExceptionType(exception.getClass().getSimpleName());
        logInfo.setUserMessage(userMessage);
        logInfo.setTechnicalMessage(technicalMessage);
        logInfo.setExceptionCategory("系统异常");
        
        // 获取调用栈信息
        setStackTraceInfo(logInfo, exception);
        
        // 获取请求信息
        setRequestInfo(logInfo);
        
        // 获取用户信息
        setUserInfo(logInfo);
        
        return logInfo;
    }
    
    /**
     * 设置调用栈信息
     */
    private void setStackTraceInfo(ExceptionLogInfo logInfo, Exception exception) {
        if (exception.getStackTrace() != null && exception.getStackTrace().length > 0) {
            // 查找第一个业务代码调用点（排除框架代码）
            for (StackTraceElement element : exception.getStackTrace()) {
                String className = element.getClassName();
                
                // 排除框架和第三方类
                if (isBusinessCode(className)) {
                    logInfo.setSourceClass(getSimpleClassName(className));
                    logInfo.setSourceMethod(element.getMethodName());
                    logInfo.setSourceLine(element.getLineNumber());
                    logInfo.setSourceFile(element.getFileName());
                    
                    // 构建可点击跳转的位置信息
                    logInfo.setClickableLocation(String.format("%s.%s(%s:%d)", 
                            getSimpleClassName(className), 
                            element.getMethodName(), 
                            element.getFileName(), 
                            element.getLineNumber()));
                    break;
                }
            }
        }
    }
    
    /**
     * 判断是否为业务代码
     */
    private boolean isBusinessCode(String className) {
        // 包含业务包名的类
        if (className.contains("org.springblade.modules") || 
            className.contains("org.springblade.common") ||
            className.contains("org.springblade.core") ||
            className.contains("com.yourcompany")) {
            return true;
        }
        
        // 排除框架类
        return !className.startsWith("org.springframework") &&
               !className.startsWith("org.mybatis") &&
               !className.startsWith("com.baomidou") &&
               !className.startsWith("java.") &&
               !className.startsWith("javax.") &&
               !className.startsWith("org.apache") &&
               !className.startsWith("net.sf.cglib") &&
               !className.startsWith("org.aspectj") &&
               !className.startsWith("com.sun.proxy") &&
               !className.startsWith("org.hibernate") &&
               !className.startsWith("org.jboss") &&
               !className.startsWith("io.undertow") &&
               !className.startsWith("org.xnio");
    }
    
    /**
     * 获取简单类名
     */
    private String getSimpleClassName(String fullClassName) {
        return fullClassName.substring(fullClassName.lastIndexOf('.') + 1);
    }
    
    /**
     * 设置请求信息
     */
    private void setRequestInfo(ExceptionLogInfo logInfo) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                logInfo.setRequestMethod(request.getMethod());
                logInfo.setRequestUrl(request.getRequestURI());
                logInfo.setRequestParams(request.getQueryString());
                
                // 尝试获取控制器信息
                String controllerInfo = extractControllerInfo(request);
                if (StringUtil.isNotBlank(controllerInfo)) {
                    logInfo.setControllerInfo(controllerInfo);
                }
            }
        } catch (Exception e) {
            log.debug("获取请求信息失败", e);
        }
    }
    
    /**
     * 提取控制器信息
     */
    private String extractControllerInfo(HttpServletRequest request) {
        try {
            // 尝试从请求属性中获取Handler信息
            Object handler = request.getAttribute("org.springframework.web.servlet.HandlerMapping.bestMatchingHandler");
            if (handler != null) {
                return handler.toString();
            }
            
            // 根据URL路径推测控制器
            String uri = request.getRequestURI();
            if (uri.contains("/api/")) {
                String[] parts = uri.split("/");
                for (int i = 0; i < parts.length; i++) {
                    if ("api".equals(parts[i]) && i + 1 < parts.length) {
                        return parts[i + 1] + "Controller";
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取控制器信息失败", e);
        }
        return null;
    }
    
    /**
     * 设置用户信息
     */
    private void setUserInfo(ExceptionLogInfo logInfo) {
        try {
            BladeUser user = AuthUtil.getUser();
            if (user != null) {
                logInfo.setUserId(user.getUserId());
                logInfo.setUserName(user.getUserName());
                logInfo.setTenantId(user.getTenantId());
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败", e);
        }
    }
    

    
    /**
     * 异常日志信息内部类
     */
    private class ExceptionLogInfo {
        private String exceptionType;
        private String exceptionCategory;
        private String userMessage;
        private String technicalMessage;
        private String sourceClass;
        private String sourceMethod;
        private String sourceFile;
        private Integer sourceLine;
        private String clickableLocation;
        private String requestMethod;
        private String requestUrl;
        private String requestParams;
        private String controllerInfo;
        private Long userId;
        private String userName;
        private String tenantId;
        
        /**
         * 生成格式化的日志消息
         */
        public String getFormattedMessage() {
            StringBuilder message = new StringBuilder();
            
            // 异常类别和类型
            message.append("[").append(exceptionCategory).append("] ");
            message.append(exceptionType).append(" - ");
            
            // 用户友好消息
            if (StringUtil.isNotBlank(userMessage)) {
                message.append("用户提示: ").append(userMessage);
            }
            
            // 技术详情
            if (StringUtil.isNotBlank(technicalMessage)) {
                message.append(" | 技术详情: ").append(technicalMessage);
            }
            
            // 请求信息
            if (StringUtil.isNotBlank(requestMethod) && StringUtil.isNotBlank(requestUrl)) {
                message.append(" | 请求: ").append(requestMethod).append(" ").append(requestUrl);
            }
            
            // 控制器信息
            if (StringUtil.isNotBlank(controllerInfo)) {
                message.append(" | 控制器: ").append(controllerInfo);
            }
            
            // 用户信息
            if (userId != null) {
                message.append(" | 用户: ").append(userName).append("(").append(userId).append(")");
                if (StringUtil.isNotBlank(tenantId)) {
                    message.append(" | 租户: ").append(tenantId);
                }
            }
            
            return message.toString();
        }
        
        /**
         * 生成包含过滤后堆栈跟踪的格式化日志消息
         */
        public String getFormattedMessageWithFilteredStackTrace(Exception exception) {
            StringBuilder message = new StringBuilder();
            message.append(getFormattedMessage());
            
            // 添加单独的业务代码堆栈信息
            if (exception != null && exception.getStackTrace() != null) {
                List<String> businessStackLines = new ArrayList<>();
                
                // 遍历堆栈跟踪，只保留业务代码相关的行
                for (StackTraceElement element : exception.getStackTrace()) {
                    String className = element.getClassName();
                    
                    // 只保留业务代码
                    if (isBusinessCode(className)) {
                        // 保持可点击的堆栈格式：完整类名.方法名(文件名:行号)
                        String stackLine = String.format("\tat %s.%s(%s:%d)", 
                            className, 
                            element.getMethodName(), 
                            element.getFileName() != null ? element.getFileName() : "Unknown", 
                            element.getLineNumber());
                        businessStackLines.add(stackLine);
                    }
                }
                
                // 如果有业务代码堆栈，单独打印
                if (!businessStackLines.isEmpty()) {
                    message.append("\n业务代码调用栈：");
                    for (String line : businessStackLines) {
                        message.append("\n").append(line);
                    }
                }
            }
            
            return message.toString();
        }
        
        // Getter and Setter methods
        public String getExceptionType() { return exceptionType; }
        public void setExceptionType(String exceptionType) { this.exceptionType = exceptionType; }
        
        public String getExceptionCategory() { return exceptionCategory; }
        public void setExceptionCategory(String exceptionCategory) { this.exceptionCategory = exceptionCategory; }
        
        public String getUserMessage() { return userMessage; }
        public void setUserMessage(String userMessage) { this.userMessage = userMessage; }
        
        public String getTechnicalMessage() { return technicalMessage; }
        public void setTechnicalMessage(String technicalMessage) { this.technicalMessage = technicalMessage; }
        
        public String getSourceClass() { return sourceClass; }
        public void setSourceClass(String sourceClass) { this.sourceClass = sourceClass; }
        
        public String getSourceMethod() { return sourceMethod; }
        public void setSourceMethod(String sourceMethod) { this.sourceMethod = sourceMethod; }
        
        public String getSourceFile() { return sourceFile; }
        public void setSourceFile(String sourceFile) { this.sourceFile = sourceFile; }
        
        public Integer getSourceLine() { return sourceLine; }
        public void setSourceLine(Integer sourceLine) { this.sourceLine = sourceLine; }
        
        public String getClickableLocation() { return clickableLocation; }
        public void setClickableLocation(String clickableLocation) { this.clickableLocation = clickableLocation; }
        
        public String getRequestMethod() { return requestMethod; }
        public void setRequestMethod(String requestMethod) { this.requestMethod = requestMethod; }
        
        public String getRequestUrl() { return requestUrl; }
        public void setRequestUrl(String requestUrl) { this.requestUrl = requestUrl; }
        
        public String getRequestParams() { return requestParams; }
        public void setRequestParams(String requestParams) { this.requestParams = requestParams; }
        
        public String getControllerInfo() { return controllerInfo; }
        public void setControllerInfo(String controllerInfo) { this.controllerInfo = controllerInfo; }
        
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    }
} 