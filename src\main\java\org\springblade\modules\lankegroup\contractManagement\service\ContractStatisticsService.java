package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.dto.ContractStatisticsDTO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsListVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractStatisticsVO;


/**
 * <AUTHOR>
 */
public interface ContractStatisticsService extends BaseService<ContractStatisticsVO> {

    /**
     * 状态统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatus(ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 情况统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatistics(ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 人员统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatisticsByUser(ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 合同类型统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatisticsType(ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 履行情况列表
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatisticsList(IPage<ContractStatisticsListVO> page, ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 状态统计列表
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractStatisticsStatusList(IPage<ContractStatisticsListVO> page, ContractStatisticsDTO contractStatisticsDTO);

    /**
     * 履行情况详情
     *
     * @param id
     * @return
     */
    R getContractStatisticsDetail(Long id);

    /**
     * 归档统计
     *
     * @param contractStatisticsDTO
     * @return
     */
    R getContractHaveBeenFiled(ContractStatisticsDTO contractStatisticsDTO);
}
