/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.entity.ProjectCollectionArchive;
import org.springblade.modules.lankegroup.contractManagement.vo.ProjectCollectionArchiveVO;
import org.springblade.modules.lankegroup.contractManagement.service.IProjectCollectionArchiveService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 归档信息--回款计划--临时表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/archive/projectcollectionarchive")
@Api(value = "归档信息--回款计划--临时表", tags = "归档信息--回款计划--临时表接口")
public class ProjectCollectionArchiveController extends BladeController {

	private final IProjectCollectionArchiveService projectCollectionArchiveService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入projectCollectionArchive")
	public R<ProjectCollectionArchive> detail(ProjectCollectionArchive projectCollectionArchive) {
		ProjectCollectionArchive detail = projectCollectionArchiveService.getOne(Condition.getQueryWrapper(projectCollectionArchive));
		return R.data(detail);
	}

	/**
	 * 分页 归档信息--回款计划--临时表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入projectCollectionArchive")
	public R<IPage<ProjectCollectionArchive>> list(ProjectCollectionArchive projectCollectionArchive, Query query) {
		IPage<ProjectCollectionArchive> pages = projectCollectionArchiveService.page(Condition.getPage(query), Condition.getQueryWrapper(projectCollectionArchive));
		return R.data(pages);
	}

	/**
	 * 自定义分页 归档信息--回款计划--临时表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入projectCollectionArchive")
	public R<IPage<ProjectCollectionArchiveVO>> page(ProjectCollectionArchiveVO projectCollectionArchive, Query query) {
		IPage<ProjectCollectionArchiveVO> pages = projectCollectionArchiveService.selectProjectCollectionArchivePage(Condition.getPage(query), projectCollectionArchive);
		return R.data(pages);
	}

	/**
	 * 新增 归档信息--回款计划--临时表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入projectCollectionArchive")
	public R save(@Valid @RequestBody ProjectCollectionArchive projectCollectionArchive) {
		return R.status(projectCollectionArchiveService.save(projectCollectionArchive));
	}

	/**
	 * 修改 归档信息--回款计划--临时表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入projectCollectionArchive")
	public R update(@Valid @RequestBody ProjectCollectionArchive projectCollectionArchive) {
		return R.status(projectCollectionArchiveService.updateById(projectCollectionArchive));
	}

	/**
	 * 新增或修改 归档信息--回款计划--临时表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入projectCollectionArchive")
	public R submit(@Valid @RequestBody ProjectCollectionArchive projectCollectionArchive) {
		return R.status(projectCollectionArchiveService.saveOrUpdate(projectCollectionArchive));
	}

	
	/**
	 * 删除 归档信息--回款计划--临时表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(projectCollectionArchiveService.deleteLogic(Func.toLongList(ids)));
	}

	
}
