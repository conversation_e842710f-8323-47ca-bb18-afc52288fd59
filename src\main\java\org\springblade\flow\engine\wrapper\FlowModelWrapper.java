/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.engine.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.engine.vo.FlowModelVO;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.UserCache;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户管理表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
public class FlowModelWrapper extends BaseEntityWrapper<FlowModel, FlowModelVO> {

	public static FlowModelWrapper build() {
		return new FlowModelWrapper();
	}

	@Override
	public FlowModelVO entityVO(FlowModel flowModel) {

		FlowModelVO flowModelVO = Objects.requireNonNull(BeanUtil.copy(flowModel, FlowModelVO.class));
		if (Func.isNotEmpty(flowModel.getCategory())) {
			flowModelVO.setCategoryName(DictCache.getById(flowModel.getCategory()).getDictValue());
		}
		if (Func.isNotEmpty(flowModel.getSubclass())) {
			flowModelVO.setSubclassName(DictCache.getById(flowModel.getSubclass()).getDictValue());
		}
		if (Func.isNotEmpty(flowModel.getLastUpdatedBy()) && !StringUtil.equals("-1",flowModel.getLastUpdatedBy())) {
			flowModelVO.setUpdateUserName(UserCache.getUser(Long.valueOf(flowModel.getLastUpdatedBy())).getName());
		}
		flowModelVO.setStatusName(flowModel.getStatus() == 0 ? "启用" : "禁用");

		return flowModelVO;
	}

	@Override
	public IPage<FlowModelVO> pageVO(IPage<FlowModel> pages) {
		List<FlowModelVO> records = this.listVO(pages.getRecords());
		List<FlowModelVO> collect = records.stream().peek(record -> {
			if (Func.isNotEmpty(record.getCategory())) {
				record.setCategoryName(DictCache.getById(record.getCategory()).getDictValue());
			}
			if (Func.isNotEmpty(record.getSubclass())) {
				record.setSubclassName(DictCache.getById(record.getSubclass()).getDictValue());
			}
			record.setStatusName(record.getStatus() == 0 ? "启用" : "禁用");
		}).collect(Collectors.toList());
		IPage<FlowModelVO> pageVo = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
		pageVo.setRecords(collect);
		return pageVo;
	}

}
