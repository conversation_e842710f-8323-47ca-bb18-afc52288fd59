/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractExperiment;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractExperimentMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractExperimentService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractExperimentVO;
import org.springframework.stereotype.Service;

/**
 * 实验合同子表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ContractExperimentServiceImpl extends BaseServiceImpl<ContractExperimentMapper, ContractExperiment> implements IContractExperimentService {

    @Override
    public IPage<ContractExperimentVO> selectContractExperimentPage(IPage<ContractExperimentVO> page, ContractExperimentVO contractExperiment) {
        return page.setRecords(baseMapper.selectContractExperimentPage(page, contractExperiment));
    }

}
