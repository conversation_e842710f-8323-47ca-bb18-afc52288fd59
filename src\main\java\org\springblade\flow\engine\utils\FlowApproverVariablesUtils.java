package org.springblade.flow.engine.utils;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.RepositoryService;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.flow.engine.builder.ApproverTypeEnum;
import org.springblade.flow.engine.builder.entity.ApproverFlowNode;
import org.springblade.flow.engine.constant.FlowProcessConfigConstant;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.IUserService;

import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/19 11:05
 * @description: 流程审批人相关变量工具类
 */
public class FlowApproverVariablesUtils {

	private static final RepositoryService repositoryService;
	private static final UserMapper userMapper;
//	private static final ProcessEngineConfigurationImpl processEngineConfiguration;


	static {
		repositoryService = SpringUtil.getBean(RepositoryService.class);
		userMapper = SpringUtil.getBean(UserMapper.class);
//		processEngineConfiguration = SpringUtil.getBean(ProcessEngineConfigurationImpl.class);
	}


	/**
	 * 动态注入审批人相关流程变量
	 *
	 * @param processDefinitionId 流程定义Id
	 * @param variables           流程变量
	 */
	public static void initFlowApproverVariables(String processDefinitionId, Map<String, Object> variables) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		// 设置流程发起人Id到流程中
		Authentication.setAuthenticatedUserId(String.valueOf(user.getId()));
		// 注入发起人自己流程变量
		variables.put(PROC_INSTANCE_START_USER_NAME_VAR, String.valueOf(user.getId()));
		// 注入发起人主部门流程变量
		variables.put(PROC_INSTANCE_START_USER_DEPT_VAR, user.getDeptId());
		//注入直属主管和部门主管流程变量
		getProcessVariables(processDefinitionId, variables);

		// 直属主管变量
//		variables.put(USER_IMMEDIATE_SUPERVISOR, String.valueOf(user.getp()));
//		Set<Integer> leaderLevels = getLeaderLevels(processDefinitionId);
//		if (CollectionUtils.isNotEmpty(leaderLevels)) {
//			if (ObjectUtil.isNotNull(loginUser) && null != loginUser.getUser().getDept()) {
//				Long id = loginUser.getUser().getDept().getId();
//				leaderLevels.forEach(level -> {
//					String deptLevelLeader = flowIdentityExtUtils.getDeptLevelLeader(level, id);
//					// 不存在上级主管 默认发起人
//					if (StrUtil.isBlank(deptLevelLeader)) {
//						deptLevelLeader = userName;
//					}
//					//部门主管
//					variables.put(DEPT_LEVEL_LEADER + level, deptLevelLeader);
//				});
//			}
//		}
		// 动态注入表单字段  迭代器才能修改集合中的元素
		// 复制集合
//		List<String> varKeyList = new ArrayList<>(variables.keySet());
//		for (String key : varKeyList) {
//			if (StrUtil.startWith(key, FormItemTypeEnum.USER_SELECT.toString().toLowerCase())) {
//				JSONArray jsonArray = JSONUtil.parseArray(variables.get(key));
//				List<String> list = jsonArray.toList(JSONObject.class).stream().map(item -> item.getStr("name")).collect(Collectors.toList());
//				if (CollUtil.isNotEmpty(list)) {
//					variables.put(APPROVE_FORM_VAR + key, CollUtil.join(list, ","));
//				} else {
//					variables.put(APPROVE_FORM_VAR + key, userName);
//				}
//			}
//		}
	}

	private static void getProcessVariables(String processDefinitionId, Map<String, Object> variables) {
		List<Process> processes = repositoryService.getBpmnModel(processDefinitionId).getProcesses();
		for (Process process : processes) {
			Collection<FlowElement> flowElements = process.getFlowElements();
			if (CollectionUtils.isNotEmpty(flowElements)) {
				for (FlowElement flowElement : flowElements) {
					if (flowElement instanceof UserTask) {
						UserTask userTask = (UserTask) flowElement;
						String assignee = userTask.getAssignee();
						User user = UserCache.getUser(AuthUtil.getUserId());
						if (StrUtil.isNotBlank(assignee) && assignee.contains(DEPT_LEVEL_LEADER)) {
							Long deptLeader = Optional.ofNullable(userMapper.getLeaderUsersByDeptId(user.getDeptId()))
									.map(e -> CollectionUtils.isNotEmpty(e) ? e.get(0).getId() : null).orElse(null);
							if (deptLeader != null) {
								variables.put(DEPT_LEVEL_LEADER, deptLeader);
							} else {
								// 找不到审批人，给虚拟人员审批
								variables.put(DEPT_LEVEL_LEADER, FlowProcessConfigConstant.VIRTUAL_APPROVER);
								variables.put(ORIGINAL_APPROVER, "部门领导（为空）");
							}
						} else if (StrUtil.isNotBlank(assignee) && assignee.contains(USER_IMMEDIATE_SUPERVISOR)) {
							if (Func.isNotEmpty(user.getParentId())) {
								variables.put(USER_IMMEDIATE_SUPERVISOR, user.getParentId());
							} else {
								// 找不到审批人，给虚拟人员审批
								variables.put(USER_IMMEDIATE_SUPERVISOR, FlowProcessConfigConstant.VIRTUAL_APPROVER);
								variables.put(ORIGINAL_APPROVER, "直属领导（为空）");
							}
						}
					}
				}
			}
		}
	}

	/**
	 * 获取流程变量需要的部门领导审批的级别 查询相关的部门领导 注入到流程变量
	 *
	 * @param processDefinitionId 流程定义Id
	 * @return 部门领导级别
	 */
	private static Set<Integer> getLeaderLevels(String processDefinitionId) {
		Set<Integer> leaderLevels = new HashSet<>();
		List<Process> processes = repositoryService.getBpmnModel(processDefinitionId).getProcesses();
		for (Process process : processes) {
			Collection<FlowElement> flowElements = process.getFlowElements();
			if (CollectionUtils.isNotEmpty(flowElements)) {
				for (FlowElement flowElement : flowElements) {
					if (flowElement instanceof UserTask) {
						UserTask userTask = (UserTask) flowElement;
						String assignee = userTask.getAssignee();
						if (StrUtil.isNotBlank(assignee) && assignee.contains(DEPT_LEVEL_LEADER)) {
							// 数据为 ${deptLevelLeader1} 截取最后的数据 为选中的层级
							String level = assignee.substring(DEPT_LEVEL_LEADER.length() + 2, assignee.length() - 1);
							if (StrUtil.isNotEmpty(level)) {
								leaderLevels.add(Integer.valueOf(level));
							}
						}
					}
				}
			}
		}
		return leaderLevels;
	}

	public static List<WorkFlow> getUsersByUserIdList(List<String> userIdList, String taskName) {
		if (Func.isNotEmpty(userIdList)) {
			List<WorkFlow> workFlows = new ArrayList<>();
			userIdList.stream().forEach(userId -> {
				WorkFlow multFlow = new WorkFlow();
				User user = UserCache.getUser(Long.valueOf(userId));
				multFlow.setAssignee(user.getId().toString());
				multFlow.setAssigneeName(user.getName());
				multFlow.setAssigneeAvatar(user.getAvatar());
				multFlow.setTaskName(taskName);
				workFlows.add(multFlow);
			});
			return workFlows;
		}
		return null;
	}

	/**
	 * 获取候选组key对应
	 *
	 * @param groupKeys 候选组key
	 * @return
	 */
	public static List<WorkFlow> getUsersByGroupKeys(List<String> groupKeys, String taskName) {
		if (Func.isNotEmpty(groupKeys)) {
			List<User> users = new ArrayList<>();
			groupKeys.stream().forEach(groupKey -> {
				users.addAll(getUsersByGroupKey(groupKey));
			});

			List<WorkFlow> workFlows = new ArrayList<>();
			WorkFlow multFlow = new WorkFlow();
			if (users != null && users.size() > 0) {
				users.stream().forEach(user -> {
					multFlow.setAssignee(user.getId().toString());
					multFlow.setAssigneeName(user.getName());
					multFlow.setAssigneeAvatar(user.getAvatar());
					multFlow.setTaskName(taskName);
					workFlows.add(multFlow);
				});
			} else {
				multFlow.setRemark("未找到审批人");
				workFlows.add(multFlow);
			}

			return workFlows;
		}
		return null;
	}

	/**
	 * 根据候选组key获取用户
	 *
	 * @param groupKey
	 * @return
	 */
	private static List<User> getUsersByGroupKey(String groupKey) {
		if (StrUtil.isNotBlank(groupKey)) {
			if (StrUtil.startWith(groupKey, TASK_CANDIDATE_GROUP_ROLE)) {
				String roleIds = groupKey.replace(TASK_CANDIDATE_GROUP_ROLE, "");
				return SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleIds));
			}
		}
		return null;
	}

	/**
	 * 获取审批人
	 *
	 * @param approverFlowNode
	 * @return
	 */
	public static List<WorkFlow.Approver> getApproverList(ApproverFlowNode approverFlowNode, Map<String, Object> variables) {
		List<WorkFlow.Approver> assigneeList = new ArrayList<>();
		ApproverTypeEnum approverType = EnumUtil.getBy(ApproverTypeEnum.class, item -> Objects.equals(item.getValue(), approverFlowNode.getSettype()));
		switch (approverType) {
			case DESIGNATED://指定审批人
				List<ApproverFlowNode.NodeUser> nodeUserList = approverFlowNode.getNodeUserList();
				if (ObjectUtil.isNotEmpty(nodeUserList)) {
					assigneeList.addAll(
						nodeUserList.stream().map(nodeUser -> {
							User user = UserCache.getUser(Long.valueOf(nodeUser.getId()));
							return setApprover(user);
						}).collect(Collectors.toList()));
				}
				break;
			case ROLE:// 角色审批人
				List<ApproverFlowNode.NodeRole> nodeRoleList = approverFlowNode.getNodeRoleList();
				if (ObjectUtil.isNotEmpty(nodeRoleList)) {
					//获取角色下的人员
					String roleIds = nodeRoleList.stream().map(role -> String.valueOf(role.getId())).collect(Collectors.joining(StrUtil.COMMA));
					List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleIds));
					assigneeList.addAll(
						userList.stream().map(user -> {
							return setApprover(user);
						}).collect(Collectors.toList()));
				}
				break;
			case USER_IMMEDIATE_SUPERVISOR://直属主管
				User loginUser = UserCache.getUser(Long.valueOf(variables.get(PROC_INSTANCE_START_USER_NAME_VAR).toString()));
				assigneeList.add(setApprover(UserCache.getUser(loginUser.getParentId())));
				break;
			case DEPT_LEADER://部门主管
				Long deptLeader = Optional.ofNullable(userMapper.getLeaderUsersByDeptId(AuthUtil.getDeptId()))
						.map(e -> CollectionUtils.isNotEmpty(e) ? e.get(0).getId() : null).orElse(null);
				assigneeList.add(setApprover(UserCache.getUser(deptLeader)));
				break;
			default:
				break;
		}
		return assigneeList;
	}

	/**
	 * 获取用户可指定的审批人范围
	 *
	 * @param approverFlowNode
	 * @return
	 */
	public static List<WorkFlow.ApproverUser> getSponsorApproverList(ApproverFlowNode approverFlowNode) {
		List<WorkFlow.ApproverUser> assigneeList = new ArrayList<>();
		if (approverFlowNode.getSponsorType().endsWith("-1")) {
			return assigneeList;
		}

		if (approverFlowNode.getSponsorType().endsWith("-2")) {
			List<ApproverFlowNode.NodeUser> nodeUserList = approverFlowNode.getSponsorUserList();
			if (ObjectUtil.isNotEmpty(nodeUserList)) {
				assigneeList.addAll(
					nodeUserList.stream().map(nodeUser -> {
						User user = UserCache.getUser(Long.valueOf(nodeUser.getId()));
						return setSponsorApprover(user);
					}).collect(Collectors.toList()));
			}
			return assigneeList;
		} else if (approverFlowNode.getSponsorType().endsWith("-3")) {
			List<ApproverFlowNode.NodeRole> nodeRoleList = approverFlowNode.getSponsorRoleList();
			if (ObjectUtil.isNotEmpty(nodeRoleList)) {
				//获取角色下的人员
				String roleIds = nodeRoleList.stream().map(role -> String.valueOf(role.getId())).collect(Collectors.joining(StrUtil.COMMA));
				List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleIds));
				if (userList != null) {
					assigneeList.addAll(userList.stream().map(user -> setSponsorApprover(user)).collect(Collectors.toList()));
				}
			}
			return assigneeList;
		}
		return assigneeList;
	}

	public static WorkFlow.Approver setApprover(User user) {
		WorkFlow.Approver approver = new WorkFlow.Approver();
		approver.setAssignee(user.getId().toString());
		approver.setAssigneeName(user.getName());
		approver.setAssigneeAvatar(user.getAvatar());
		approver.setAssigneeIsDeleted(user.getIsDeleted());
		approver.setAssigneeStatus(user.getStatus());
		return approver;
	}

	public static WorkFlow.ApproverUser setSponsorApprover(User user) {
		WorkFlow.ApproverUser approver = new WorkFlow.ApproverUser();
		approver.setId(user.getId().toString());
		approver.setName(user.getName());
		approver.setAvatar(user.getAvatar());
		return approver;
	}

	public static List<WorkFlow.Approver> getApproverList(List<ApproverFlowNode.NodeUser> nodeUserList) {
		List<WorkFlow.Approver> assigneeList = new ArrayList<>();
		if (ObjectUtil.isNotEmpty(nodeUserList)) {
			assigneeList.addAll(nodeUserList.stream().map(nodeUser -> {
				WorkFlow.Approver approver = new WorkFlow.Approver();
				User user = UserCache.getUser(Long.valueOf(nodeUser.getId()));
				approver.setAssignee(user.getId().toString());
				approver.setAssigneeName(user.getName());
				approver.setAssigneeAvatar(user.getAvatar());
				return approver;
			}).collect(Collectors.toList()));
		}
		return assigneeList;
	}
}
