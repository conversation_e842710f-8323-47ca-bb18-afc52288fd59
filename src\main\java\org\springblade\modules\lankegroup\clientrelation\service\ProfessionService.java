package org.springblade.modules.lankegroup.clientrelation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.ProfessionDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Profession;
import org.springblade.modules.lankegroup.clientrelation.vo.ProfessionVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

public interface ProfessionService extends BaseService<Profession> {

    /**
     * 新增市场
     * @param professionDTO
     * @return
     */
    R saveProfession(ProfessionDTO professionDTO);

    /**
     * 区域市场列表
     * @param professionDTO
     * @param page
     * @return
     */
    R getProfessionList(ProfessionDTO professionDTO, IPage<ProfessionVO> page);

    /**
     * 查询区域市场及行业是否能修改
     * @param professionDTO
     * @return
     */
    R checkMarketAndArea(ProfessionDTO professionDTO);

    /**
     * 编辑市场
     * @param professionDTO
     * @return
     */
    R updateProfession(ProfessionDTO professionDTO);

    /**
     * 删除市场
     * @param id
     * @return
     */
    R deleteProfession(String id);

    /**
     * 根据部门查询区域市场列表
     * @param deptId
     * @return
     */
    R getProfessionListByDept(String deptId);
    /**
     * 客户管理使用
     * 客户详情，根据客户填写的客户所属行业及客户所属区域，匹配客户的所属市场区域
     * @return
     */
    List<Profession> selectByAreAndMarketForProfession(Map map);
    /**
     * 区域市场详情
     * @param id
     * @return
     */
    R detailProfession(Long id);

    /**
     * 行业信息
     * @param map
     * @return
     */
    R industryInformation(Map map);

    /**
     * 区域详情
     * @param map
     * @return
     */
    R getAreaList(Map map);

    /**
     * 获取某区域市场下区划列表
     * @param map
     * @return
     */
    R getRegionalizationList(Map map);

    /**
     * 获取某区域市场下行业列表
     * @param map
     * @return
     */
    R getProfessionList(Map map);

    /**
     * 查询部门是否创建区域市场
     * @param deptId
     * @return
     */
    R getProfessionData(String deptId);

    /**
     * 根据区域市场ID查询行业列表
     * @param professionId
     * @return
     */
    R getProfessionByMarketList(String professionId,String joinId);

}
