/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.common.constant.FlowFormConstant;
import org.springblade.common.constant.FormNameConstant;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.business.service.impl.FlowBusinessServiceImpl;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.controller.ContractUseChapterType;
import org.springblade.modules.lankegroup.contractManagement.entity.Approval;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractChangeMapper;
import org.springblade.modules.lankegroup.contractManagement.service.*;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import org.springblade.modules.lankegroup.contractManagement.wrapper.ContractChangeWrapper;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.enums.BusinessTypeEnum;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IApproverService;
import org.springblade.modules.lankegroup.pro_management.service.IProjectGroupService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同表变更记录 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@Service
public class ContractChangeServiceImpl extends BaseServiceImpl<ContractChangeMapper, ContractChange> implements IContractChangeService {
    @Autowired
    private final IUserService userService;
    @Autowired
    @Lazy
    private final IContractService contractService;
    @Autowired
    @Lazy
    private final IChannelContractService channelContractService;
    @Autowired
    private final IDeptService deptService;
    @Autowired
    private final IApproverService approverService;
    @Autowired
    private final FlowEngineService flowEngineService;
    @Autowired
    private final IFlowService flowService;
    @Autowired
    private final IContractChangeLogService contractChangeLogService;
    @Autowired
    private final FlowBusinessServiceImpl flowBusinessService;
    @Autowired
    @Lazy
    private final IArchiveDetailsService archiveDetailsService;
    @Autowired
    private final ICustomerConnectLogService customerConnectLogService;
    @Autowired
    private final IProjectGroupService projectGroupService;
    @Autowired
    private final SendBillMsgUtil sendBillMsgUtil;
    @Autowired
    private SystemMessageService systemMessageService;
    @Autowired
    private ProjectBasicMapper projectBasicMapper;
    @Autowired
    private IHistoricProcessService historicProcessService;

    public ContractChangeServiceImpl(IUserService userService, @Lazy IContractService contractService, IChannelContractService channelContractService, IDeptService deptService, IApproverService approverService, FlowEngineService flowEngineService, IFlowService flowService, IContractChangeLogService contractChangeLogService, FlowBusinessServiceImpl flowBusinessService, @Lazy IArchiveDetailsService archiveDetailsService, ICustomerConnectLogService customerConnectLogService, IProjectGroupService projectGroupService, SendBillMsgUtil sendBillMsgUtil) {
        this.userService = userService;
        this.contractService = contractService;
        this.channelContractService = channelContractService;
        this.deptService = deptService;
        this.approverService = approverService;
        this.flowEngineService = flowEngineService;
        this.flowService = flowService;
        this.contractChangeLogService = contractChangeLogService;
        this.flowBusinessService = flowBusinessService;
        this.archiveDetailsService = archiveDetailsService;
        this.customerConnectLogService = customerConnectLogService;
        this.projectGroupService = projectGroupService;
        this.sendBillMsgUtil = sendBillMsgUtil;
    }

    @Override
    public IPage<ContractChangeVO> selectContractChangePage(IPage<ContractChangeVO> page, ContractChangeVO contractChange) {
        return page.setRecords(baseMapper.selectContractChangePage(page, contractChange));
    }

    @Override
    public Map initContract() {
        Map result = new HashMap();
        Map map = new HashMap();


        map.put("userId", AuthUtil.getUserId());
        ContractChangeVO change = selectDraft(map);
        if (change != null) {
            //		返回处理后的草稿
            result.put("contractChange", tidyUpContractChange(change));
        } else {
            result.put("contractChange", change);
        }
        return result;
    }

    @Override
    public ContractChangeVO selectDraft(Map map) {
        return baseMapper.selectDraft(map);
    }

    /**
     * 详情接口参数整理
     *
     * @param change 合同变更
     * @return
     */
    @Override
    public ContractChangeVO tidyUpContractChange(ContractChange change) {
        ContractChangeVO vo = ContractChangeWrapper.build().entityVO(change);
        if (vo.getUseChapterTypeIds() != null && (!vo.getUseChapterTypeIds().equals(""))) {
            // 用章类型转为数组类型
            List ids = Arrays.stream(vo.getUseChapterTypeIds().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List vals = Arrays.stream(vo.getUseChapterTypeVals().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List<ContractUseChapterType> contractUseChapterTypes = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                ContractUseChapterType c = new ContractUseChapterType();
                c.setId(ids.get(i).toString());
                c.setName(vals.get(i).toString());
                contractUseChapterTypes.add(c);
            }
            vo.setUseChapterTypeList(contractUseChapterTypes);
        }
        // 发起人姓名
        User createUser = userService.getUserByIdNotDelete(vo.getCreateUser());
        List deptIds = Arrays.asList(createUser.getDeptId().split(","));
        vo.setCreateUserName(createUser.getName());
        // 流程[有流程实例id的时候返回审批流程，没有的为草稿，不返回流程]
//        List<Approval> approvals = new ArrayList<>();
        if (Func.isNotBlank(vo.getProcessInstanceId())) {
            // TODO 刘源兴工作流
//            List<BladeFlow> flowList = flowEngineService.getALLFlowNode(change.getProcessInstanceId());
            List<BladeFlow> flowList = null;
//            approvals.addAll(flowChartDeduction(vo.getProcessInstanceId()));
            // vo.getApprovalList().addAll(flowChartDeduction(vo.getProcessInstanceId()));
            // 给前端返回审批按钮状态位（是否可撤回，是否可审批）
            // 单据审批中，且当前登陆人为发起人，流程第二个人正在审批中时可撤回
//            if (vo.getStatus() == 0 && vo.getCreateUser().longValue() == AuthUtil.getUserId().longValue() && vo.getTaskUser().equals(AuthUtil.getUserId().toString())) {
//                vo.setWithdraw(true);
//            }
//            // 单据为审批中，当前登录人为当前流程审批人，状态为审批中时，可审批
//            if (vo.getStatus() == 0 && (vo.getTaskUser().contains(AuthUtil.getUserId().toString()) || vo.getTaskUser().equals(AuthUtil.getUserId().toString()))) {
//                if (vo.getStatus() == 0 && vo.getTaskUser().contains(AuthUtil.getUserId().toString())) {
//                    for (BladeFlow a : flowList) {
//                        if ("0".equals(a.getStatus()) && a.getAssignee().equals(AuthUtil.getUserId().toString())) {
//                            vo.setApproval(true);
//                        }
//                    }
//                }
//
//            }
            if (vo.getStatus() == ApprovedEnum.UNAPRROVED.getIndex()) {
                if (AuthUtil.getUserId().longValue() == vo.getCreateUser().longValue()) {
                    vo.setWithdraw(true);
                } else {
                    HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(vo.getId());
                    if (historicProcess != null) {
                        if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                            vo.setApproval(true);
                        }
                    }
                }
            }
            //审批中
            if (vo.getStatus() == ApprovedEnum.APPROVING.getIndex()) {
                HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(vo.getId());
                if (historicProcess != null) {
                    if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                        vo.setApproval(true);
                    }
                }
            }
            // 流程人存入
            vo.getApprovalList().addAll(flowList);
        }


        if (vo.getStatus() == -1) {
            vo.setContractStatus(0);
        }

        return vo;
    }

    @Override
    public ContractChangeVO selectDetail(Map map) {
        return baseMapper.selectDetail(map);
    }

    @Override
    public Map saveDraft(ContractChangeVO changeVO) {
        Map result = new HashMap();
        if (changeVO.getUseChapterTypeList() != null && changeVO.getUseChapterTypeList().size() > 0) {
            //        用章类型修改类型
            StringBuffer ids = new StringBuffer();
            StringBuffer vals = new StringBuffer();
            if (changeVO.getUseChapterTypeList() != null && changeVO.getUseChapterTypeList().size() > 0) {
                for (ContractUseChapterType c : changeVO.getUseChapterTypeList()) {
                    ids.append(c.getId());
                    ids.append(",");
                    vals.append(c.getName());
                    vals.append(",");
                }
            }
            ids.deleteCharAt(ids.length() - 1);
            vals.deleteCharAt(vals.length() - 1);
            changeVO.setUseChapterTypeIds(ids.toString());
            changeVO.setUseChapterTypeVals(vals.toString());
        }
        Boolean status = false;
        if (changeVO.getId() != null && StringUtil.isNotBlank(changeVO.getId().toString())) {
            status = updateById(changeVO);
        } else {
            status = save(changeVO);
        }
        result.put("status", status);
        result.put("contractChangeId", changeVO.getId());
        return result;
    }

    @Override
    public Map startProcess(ContractChangeVO changeVO) {
        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.CONTRACT_KEY);
        BladeFlow flow = null;
//        返回值
        Map result = new HashMap();
//            单据状态为审批中
        changeVO.setStatus(0);
//		当前审批人默认自己
        Long userId = AuthUtil.getUserId();
        changeVO.setTaskUser(String.valueOf(userId));
//        用章类型修改类型
        StringBuffer ids = new StringBuffer();
        StringBuffer vals = new StringBuffer();
        if (changeVO.getUseChapterTypeList() != null && changeVO.getUseChapterTypeList().size() > 0) {
            for (ContractUseChapterType c : changeVO.getUseChapterTypeList()) {
                ids.append(c.getId());
                ids.append(",");
                vals.append(c.getName());
                vals.append(",");
            }
            ids.deleteCharAt(ids.length() - 1);
            vals.deleteCharAt(vals.length() - 1);
            changeVO.setUseChapterTypeIds(ids.toString());
            changeVO.setUseChapterTypeVals(vals.toString());
        }

        //        有id且状态位置不为草稿则更新、无id保存,
        if (Func.isEmpty(changeVO.getId())) {
            if (save(changeVO)) {
                initCompleteTask(changeVO);
            }
        } else {
            //不是草稿
            if ("0".equals(changeVO.getIsUseChapter())) {//是否用章（1是/0否）
                changeVO.setUseChapterTypeIds("");
                changeVO.setUseChapterTypeVals("");
                changeVO.setNumOfCopies(-1);
            }
            if (updateById(changeVO)) {
                initCompleteTask(changeVO);
            }
        }
        result.put("name", changeVO.getName());
        if (flow != null) {
            result.put("processInstanceId", flow.getProcessInstanceId());
        } else {
            result.put("processInstanceId", changeVO.getProcessInstanceId());
        }
        result.put("contractChangeId", changeVO.getId());

        return result;
    }


    @Override
    public List<Map> changeList(Long contractId) {
        return baseMapper.changeList(contractId);
    }

    @Override
    public Integer countChange(Map map) {
        return baseMapper.countChange(map);
    }

    @Override
    public R initChangeProcess(Long contractId) {
        // 合同变更合同详情
        Contract contract = contractService.getById(contractId);

        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId());
        Map<String, Object> variable = variables;
        String processId = "";
        // TODO 刘源兴工作流
        // 根据合同类型判断使用的流程
//        if (contract.getContractType().equals("0")) {
//            processId = flowEngineService.getProcessId("销售合同", "salesContract");
//        } else if (contract.getContractType().equals("4")) {
//            processId = flowEngineService.getProcessId("渠道合同", "channelContract");
//        } else {
//            processId = flowEngineService.getProcessId("合同管理", "contract");
//        }
        if ("".equals(processId)) {
            return R.fail(500, "流程不存在");
        }
        List<Long> assigneeList = userService.getALlParentByUserId(AuthUtil.getUserId());
        if (assigneeList != null && assigneeList.size() > 0) {
            variables.put("assigneeList", assigneeList);
        }
        // TODO 刘源兴工作流
//        List<BladeFlow> bladeFlowList = flowEngineService.getFLowPrediction(processId, variable);
//
//        return R.data(bladeFlowList);
        return R.data(null);
    }


    /**
     * 发起合同开启审批流程
     *
     * @param contract
     * @return
     */
    private boolean initCompleteTask(ContractChangeVO contract) {
        // 启动流程
        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId())
                .set(FlowFormConstant.PROC_INSTANCE_FORM_NAME, FormNameConstant.CONTRACTCHANGE)
                .set(FlowFormConstant.PROC_INSTANCE_FORM_DATA_ID, contract.getId());
        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.CONTRACT_KEY);
        List<Long> assigneeList = userService.getALlParentByUserId(AuthUtil.getUserId());
        if (assigneeList != null && assigneeList.size() > 0) {
            variables.put("assigneeList", assigneeList);
        }
        // TODO 刘源兴工作流
//        BladeFlow flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
        BladeFlow flow = null;
        if (Func.isNotEmpty(flow)) {
            contract.setProcessInstanceId(flow.getProcessInstanceId());
            //反写流程id
            update(Wrappers.<ContractChange>update().lambda()
                    .set(ContractChange::getProcessInstanceId, flow.getProcessInstanceId())
                    .set(ContractChange::getStatus, ApprovedEnum.UNAPRROVED.getIndex())
                    .eq(ContractChange::getId, contract.getId()));

            HistoricProcessEntity historicProcessEntity = new HistoricProcessEntity();
            historicProcessEntity.setPid(contract.getId());
            historicProcessEntity.setBillName(contract.getName());
            historicProcessEntity.setMoney(contract.getAdjustedAmount());
            historicProcessEntity.setType(FormNameConstant.CONTRACTCHANGE);
            historicProcessEntity.setTypeName("合同变更");
            historicProcessEntity.setBusinessType(BusinessTypeEnum.合同.getCode());
            historicProcessEntity.setBillsType(contractService.getById(contract.getContractId()).getContractType());

            historicProcessService.saveHitoricProcessStart(historicProcessEntity, flow.getProcessInstanceId());
            return true;
        }
        return false;
    }

}
