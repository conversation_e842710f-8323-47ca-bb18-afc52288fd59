package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 行业
 */
@Data
@TableName("blade_marketplace")
public class Marketplace extends BaseEntity {

    private Long id;

    /**
     * 行业名称
     */
    private String name;

    /**
     * 客户数量
     */
    private Integer num;

    /**
     * 行业类型：1企业 2政府
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createName;

}
