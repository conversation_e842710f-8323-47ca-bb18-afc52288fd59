<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.flow.business.mapper.ApproverMapper">
    <select id="selectByApplyId" resultType="java.lang.String">
        SELECT parent_id FROM `blade_user`
        <if test="id!=null and id!=''">
            where id=#{id}
        </if>
    </select>
    <select id="selectUserByMap" parameterType="java.util.Map" resultType="java.lang.String">
        select id from blade_user
        where
            1=1
        <if test="postId!=null and postId!=''">
           and   post_id=#{postId}
        </if>
        <if test="account!=null and account!=''">
            and account=#{account}
        </if>
    </select>
    <select id="selectByProcessInstanceId" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        p.task_user
        from
        blade_project_basic as p
        left join ACT_RU_TASK as a on p.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>

    <select id="selectByProcessInstanceIdArchive" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        d.task_user
        from
        blade_archive_details as d
        left join ACT_RU_TASK as a on d.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>
    <select id="selectByProcessInstanceIdReim" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        r.task_user
        from
        blade_reim as r
        left join ACT_RU_TASK as a on r.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>
    <select id="headOfDepartment" parameterType="java.util.Map" resultType="org.springblade.modules.system.entity.User">
        SELECT DISTINCT
            u.*
        FROM
            blade_user AS u
                left JOIN blade_dept AS d ON u.dept_id like concat('%',d.id,'%')
        WHERE
            d.parent_id=1
          and u.leader=1
          and d.id IN (
            WITH recursive temp AS (
                SELECT
                    t.*
                FROM
                    blade_dept t
                WHERE
                    id in
        <foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        UNION
                SELECT
                    t.*
                FROM
                    blade_dept t
                        INNER JOIN temp ON t.id = temp.parent_id
            ) SELECT
                id
            FROM
                temp
        )
          and u.is_deleted=0
    </select>
    <select id="updateComment" parameterType="java.lang.String" resultType="java.lang.Integer">
        update ACT_HI_COMMENT  set USER_ID_=#{userId}   where TASK_ID_=#{taskId}
    </select>
    <!--<select id="queryHeader" resultType="org.springblade.modules.system.entity.User">
        select  DISTINCT
            u.*
        from
            blade_user_kingdee_header as h
                left join blade_user as u on u.id=h.header_id
        where
            h.user_id=#{userId}
    </select>-->
    <select id="queryKingdeeHeader" resultType="java.lang.String">
        SELECT DISTINCT
        e1.FDDID AS approvedDingId
        FROM
            T_ORG_POST AS p
                LEFT JOIN T_ORG_POSTREPORTLINE AS pp ON p.FPOSTID= pp.FPOSTID
                LEFT JOIN T_BD_STAFFTEMP AS s ON p.FPOSTID= S.FPOSTID
                LEFT JOIN T_ORG_POST_l AS pl ON pl.FPOSTID= S.FPOSTID
                LEFT JOIN T_HR_EMPINFO AS e ON e.fid= s.fid
                LEFT JOIN T_SEC_user AS u ON e.FPERSONID= u.FLINKOBJECT
                LEFT JOIN T_BD_DEPARTMENT_L AS dl ON s.FDEPTID= dl.FDEPTID
                LEFT JOIN T_BD_DEPARTMENT AS d ON dl.fdeptid= d.fdeptid
                LEFT JOIN T_BD_STAFF AS ST ON ST.FNUMBER= E.FNUMBER
                LEFT JOIN T_ORG_POST AS p1 ON pp.FSUPERIORPOST= p1.FPOSTID
                LEFT JOIN T_ORG_POST_l AS pl1 ON pl1.FPOSTID= p1.FPOSTID
                LEFT JOIN T_BD_STAFFTEMP AS s1 ON p1.FPOSTID= S1.FPOSTID
                LEFT JOIN T_HR_EMPINFO AS e1 ON e1.fid= s1.fid
                LEFT JOIN T_SEC_user AS u1 ON e1.FPERSONID= u1.FLINKOBJECT
                LEFT JOIN T_BD_DEPARTMENT_L AS dl1 ON s1.FDEPTID= dl1.FDEPTID
                LEFT JOIN T_BD_DEPARTMENT AS d1 ON dl1.fdeptid= d1.fdeptid
                LEFT JOIN T_BD_STAFF AS ST1 ON ST1.FNUMBER= E1.FNUMBER
        WHERE
            pp.FISVALID= 1
          AND e.FDDID= #{account}
          AND st.FSTAFFID= s.FSTAFFID
          and e.FDocumentStatus='C'
          and e.FForbidStatus='A'
          and e1.FDocumentStatus='C'
          and e1.FForbidStatus='A'
    </select>

    <select id="getMultipleEntityVariables" resultType="java.lang.Integer">
        select LONG_ from ACT_RU_VARIABLE
        where PROC_INST_ID_ = #{processInstanceId}
        and NAME_ = #{variablesName}
    </select>
    <select id="selectTaskId" resultType="java.lang.String">
        select
        ID_
        from
        ACT_RU_TASK
        where
        <if test="processInstanceId!=null and processInstanceId!=''">
            PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>
</mapper>
