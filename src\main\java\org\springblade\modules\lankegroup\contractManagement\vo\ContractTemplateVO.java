/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模板视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("合同模板视图实体类")
public class ContractTemplateVO extends ContractTemplate {
	private static final long serialVersionUID = 1L;

	/**
	 * 草稿数量
	 */
	@ApiParam("草稿数量")
	private Integer draftsCount = 0;
	/**
	 * 草稿数量
	 */
	@ApiParam("是否显示草稿数量 true-展示 false-不展示")
	public boolean isShowDraft() {
		return draftsCount != null && draftsCount > 0;
	}



}
