package org.springblade.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.utils.Func;

/**
 * 单据流程审批状态
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/17 14:20
 */
@Getter
@AllArgsConstructor
public enum ProcessStatusEnum {
	待审批("待审批", "待审批", 0),
	审批中("审批中", "审批中", 1),
	已驳回("已驳回", "已驳回", 2),
	已撤回("已撤回", "已撤回", 3),
	已完成("已完成", "已通过", 4),
	;

	/**
	 * nameType=1
	 */
	final String name;
	/**
	 * nameType=2
	 */
	final String nameTwo;
	final Integer code;


	public static String getProcessStatusEnumName(Integer code) {
		return getProcessStatusEnumNameV2(code, 1);
	}

	public static String getProcessStatusEnumNameV2(Integer code, Integer nameType) {
		if (Func.isNotEmpty(code)) {
			ProcessStatusEnum[] values = ProcessStatusEnum.values();
			for (ProcessStatusEnum value : values) {
				if (value.getCode().equals(code)) {
					return nameType == null || nameType == 1 ? value.getName() : value.getNameTwo();
				}
			}
		}
		return null;
	}


}
