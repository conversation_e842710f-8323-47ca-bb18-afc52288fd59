package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

/**
 * 租户数据源诊断工具类
 * 用于排查租户数据源相关问题
 * 
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@Slf4j
public class TenantDiagnosticUtil {

    /**
     * 诊断当前租户数据源状态
     * 
     * @param methodName 调用的方法名
     * @param businessId 业务ID
     * @return 诊断信息
     */
    public static String diagnoseTenantDataSource(String methodName, Object businessId) {
        StringBuilder diagnostic = new StringBuilder();
        
        try {
            // 获取当前用户信息
            BladeUser currentUser = AuthUtil.getUser();
            if (currentUser != null) {
                diagnostic.append(String.format("当前用户ID: %s, ", currentUser.getUserId()));
                diagnostic.append(String.format("租户ID: %s, ", currentUser.getTenantId()));
                diagnostic.append(String.format("用户名: %s, ", currentUser.getUserName()));
            } else {
                diagnostic.append("当前用户信息: 未登录或无效, ");
            }
            
            diagnostic.append(String.format("调用方法: %s, ", methodName));
            diagnostic.append(String.format("业务ID: %s", businessId));
            
        } catch (Exception e) {
            diagnostic.append(String.format("诊断过程异常: %s", e.getMessage()));
        }
        
        return diagnostic.toString();
    }
    
    /**
     * 记录租户数据源异常的详细信息
     * 
     * @param exception 异常对象
     * @param methodName 方法名
     * @param businessId 业务ID
     */
    public static void logTenantDataSourceException(Exception exception, String methodName, Object businessId) {
        String diagnostic = diagnoseTenantDataSource(methodName, businessId);
        
        // 合并为一行简洁的日志
        log.error("租户数据源异常 - {}, 异常类型: {}, 异常消息: {}", 
            diagnostic, exception.getClass().getSimpleName(), exception.getMessage());
    }
    
    /**
     * 获取异常的根本原因
     */
    private static Throwable getRootCause(Throwable throwable) {
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }
    
    /**
     * 检查租户ID是否有效
     */
    public static boolean isValidTenantId(String tenantId) {
        return Func.isNotBlank(tenantId) && !"000000".equals(tenantId);
    }
    
    /**
     * 生成租户数据源异常的建议修复方案
     */
    public static String generateFixSuggestion(Exception exception) {
        StringBuilder suggestion = new StringBuilder();
        suggestion.append("建议修复方案：\n");
        
        String message = exception.getMessage();
        if (message == null) {
            suggestion.append("1. 检查租户数据源配置是否正确\n");
            suggestion.append("2. 确认当前租户ID是否存在且有效\n");
            suggestion.append("3. 检查数据库连接是否正常\n");
            suggestion.append("4. 验证租户数据源映射配置\n");
        } else if (message.contains("Connection")) {
            suggestion.append("1. 检查数据库连接配置\n");
            suggestion.append("2. 确认数据库服务是否正常运行\n");
            suggestion.append("3. 检查网络连接是否正常\n");
        } else if (message.contains("null")) {
            suggestion.append("1. 检查租户数据源是否已正确配置\n");
            suggestion.append("2. 确认租户信息是否完整\n");
        } else {
            suggestion.append("1. 查看详细错误日志分析具体原因\n");
            suggestion.append("2. 检查租户配置和数据源映射\n");
        }
        
        return suggestion.toString();
    }
} 