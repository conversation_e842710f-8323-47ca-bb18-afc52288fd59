package org.springblade.common.annotation.log;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ControlsLog {

    /**
     * pid描述
     *
     * @return
     */
    String pidDesc() default "";

    /**
     * 操作人描述
     *
     * @return
     */
    String userIdDesc() default "";

    /**
     * 操作类型
     *
     * @return
     */
    int type();

    /**
     * 操作对象描述
     *
     * @return
     */
    String byUserIdDesc() default "";

    /**
     * 提示语
     *
     * @return
     */
    String msg() default "";

    /**
     * 保留扩展字符
     *
     * @return
     */
    String extStr() default "";

}