/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import org.springblade.modules.lankegroup.contractManagement.entity.ContractReagentQuotationMaterial;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractReagentQuotationMaterialVO;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractReagentQuotationMaterialMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractReagentQuotationMaterialService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 试剂合同子表-实验材料 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Service
public class ContractReagentQuotationMaterialServiceImpl extends BaseServiceImpl<ContractReagentQuotationMaterialMapper, ContractReagentQuotationMaterial> implements IContractReagentQuotationMaterialService {

	@Override
	public IPage<ContractReagentQuotationMaterialVO> selectContractReagentQuotationMaterialPage(IPage<ContractReagentQuotationMaterialVO> page, ContractReagentQuotationMaterialVO contractReagentQuotationMaterial) {
		return page.setRecords(baseMapper.selectContractReagentQuotationMaterialPage(page, contractReagentQuotationMaterial));
	}

}
