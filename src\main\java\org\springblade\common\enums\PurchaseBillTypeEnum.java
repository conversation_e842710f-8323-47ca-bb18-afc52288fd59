package org.springblade.common.enums;

public enum PurchaseBillTypeEnum {
    标准采购申请("标准采购申请","CGSQD01_SYS",1904152382906123751l),
    资产采购申请单("固定资产采购","CGSQD03_SYS",1904152382906123752l);
    private String name;
    private String number;
    private Long id;

    PurchaseBillTypeEnum(String name, String number,Long id) {
        this.name = name;
        this.number = number;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public String getNumber() {
        return number;
    }
    public Long getId() {
        return id;
    }
}
