/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.dto;

import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同表变更记录数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractChangeDTO extends ContractChange {
	private static final long serialVersionUID = 1L;

}
