/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import org.springblade.core.tool.api.R;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;

import java.util.List;
import java.util.Map;

/**
 * 合同表变更记录 服务类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
public interface IContractChangeService extends BaseService<ContractChange> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractChange
	 * @return
	 */
	IPage<ContractChangeVO> selectContractChangePage(IPage<ContractChangeVO> page, ContractChangeVO contractChange);
	/**
	 * 初始化草稿
	 * @param
	 * @return
	 */
	Map initContract();
	/**
	 * 查询当前登录人的草稿
	 * @return
	 */
	ContractChange selectDraft(Map map);
	/**
	 * 详情接口参数整理
	 * @param
	 * @return
	 */
	ContractChangeVO tidyUpContractChange(ContractChange change);
	/**
	 * 根据id查询合同详细的基础信息
	 * @param map
	 * @return
	 */
	ContractChangeVO selectDetail(Map map);
	/**
	 * 合同变更草稿存储
	 * @param
	 * @return
	 */
	Map saveDraft(ContractChangeVO changeVO);
	/**
	 * 合同开启流程
	 * @param
	 * @return
	 */
	Map startProcess(ContractChangeVO changeVO);
	/**
	 * 根据合同id返回已审批的变更列表
	 * @param contractId  合同id
	 * @return
	 */

	List<Map> changeList(Long contractId);
	Integer countChange(Map map);

	/**
	 * 根据合同id，返回变更的审批流
	 * 变更审批流与合同流程相同
	 * 采购合同：发起人--》部门主管+2个抄送人
	 * 其他合同：发起人--》直接上级+3个抄送人
	 * 销售合同/渠道合同：直接上级--》安服项目：网盾技术部负责人；司法鉴定项目：司法鉴定部负责人；其它项目：研发部负责人和交付部负责人
	 * --》财务部负责人--》抄送人
	 * @param contractId
	 * @return
	 */
	R initChangeProcess(Long contractId);
}
