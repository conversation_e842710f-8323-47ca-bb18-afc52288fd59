package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionArea;
import org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionAreaMapper;
import org.springblade.modules.lankegroup.clientrelation.service.ProfessionAreaService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@AllArgsConstructor
public class ProfessionAreaServiceImpl extends BaseServiceImpl<ProfessionAreaMapper, ProfessionArea> implements ProfessionAreaService {

    @Override
    public void saveProfessionArea(List<ProfessionArea> profession) {
        saveBatch(profession);
    }

    @Override
    public void deleteProfessionArea(String joinId) {
        QueryWrapper<ProfessionArea> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("join_id",joinId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void deleteProfessionAreas(List<Long> ids) {
        baseMapper.deleteProfessionAreas(ids);
    }
}
