package org.springblade.modules.lankegroup.clientrelation.controller;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.lankegroup.clientrelation.dto.MarketplaceDTO;
import org.springblade.modules.lankegroup.clientrelation.service.MarketplaceService;
import org.springframework.web.bind.annotation.*;
import org.springblade.core.tool.api.R;

/**
 * 行业
 */
@AllArgsConstructor
@RestController
@RequestMapping("marketplace")
public class MarketplaceController {

    private final MarketplaceService marketplaceService;

    /**
     * 添加行业
     *
     * @return
     */
    @PostMapping
    public R saveMarketplace(@RequestBody MarketplaceDTO marketplaceDTO) {
        try {
            return marketplaceService.saveMarketplace(marketplaceDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail("添加行业失败");
    }

    /**
     * 行业列表
     *
     * @return
     */
    @GetMapping
    public R getMarketplaceList(MarketplaceDTO marketplaceDTO, Query query,String status) {
        return marketplaceService.getMarketplaceList(marketplaceDTO, Condition.getPage(query),status);
    }

    /**
     * 行业详情
     * @param id
     * @return
     */
    @GetMapping("detail")
    public R detail(String id) {
        return marketplaceService.detail(id);
    }

    /**
     * 编辑
     * @param marketplaceDTO
     * @return
     */
    @PutMapping
    public R updateMarketplace(@RequestBody MarketplaceDTO marketplaceDTO) {
        try {
            return marketplaceService.updateMarketplace(marketplaceDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail("编辑行业失败");
    }

    /**
     * 删除
     * @param marketplaceDTO
     * @return
     */
    @DeleteMapping
    public R deleteMarketplace(@RequestBody MarketplaceDTO marketplaceDTO) {
        try {
            return marketplaceService.deleteMarketplace(marketplaceDTO.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail("删除行业失败");
    }

    /**
     * 启用禁用
     * @param marketplaceDTO
     * @return
     */
    @PutMapping("status")
    public R updateMarketplaceStatus(@RequestBody MarketplaceDTO marketplaceDTO) {
        try {
            return marketplaceService.updateMarketplaceStatus(marketplaceDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail("启用/禁用行业失败");
    }

    /**
     * 客户所属行业时展示
     * @param marketplaceDTO  内部name为行业名称模糊匹配
     * @return  返回行业列表状态正常的行业
     */
    @GetMapping("/industryList")
    public  R industryList(MarketplaceDTO marketplaceDTO){
        return R.data(marketplaceService.industryList(marketplaceDTO));
    }

    /**
     * 获取市场下行业列表
     * @param marketplaceDTO
     * @return
     */
    @GetMapping("personnelMarketList")
    public  R personnelMarketList(MarketplaceDTO marketplaceDTO){
        return R.data(marketplaceService.personnelMarketListByGroup(marketplaceDTO));
    }

    /**
     * 获取全部区划行业
     * @param marketplaceDTO
     * @return
     */
    @GetMapping("personnelMarketListByAll")
    public  R personnelMarketListByAll(MarketplaceDTO marketplaceDTO){
        return R.data(marketplaceService.personnelMarketListByAll(marketplaceDTO));
    }

    /**
     * 区域市场/人员管理详情
     * @return
     */
    @PostMapping("marketDetailList")
    public R marketDetailList(@RequestBody MarketplaceDTO marketplaceDTO){
        return marketplaceService.marketDetailList(marketplaceDTO);
    }

    @PostMapping("marketDetail")
    public R marketDetail(@RequestBody MarketplaceDTO marketplaceDTO){
        return marketplaceService.marketDetail(marketplaceDTO);
    }

    /**
     * 获取当前ID序号
      * @param id
     * @return
     */
    @GetMapping("getSort")
    public R getSort(String id,String name,Integer type,Integer status){
        return marketplaceService.getSort(id,name,type,status);
    }
}
