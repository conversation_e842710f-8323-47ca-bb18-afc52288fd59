//package org.springblade.modules.lankegroup.contractManagement.listener;
//
//import org.flowable.engine.delegate.DelegateExecution;
//import org.springframework.stereotype.Component;
//
//import java.io.Serializable;
//
////@Component("multiInstance")
//public class MultiInstanceCompleteTask implements Serializable {
//    /**
//     * 评估结果判定条件
//     * @param execution 分配执行实例
//     */
//    public boolean accessCondition(DelegateExecution execution){
//        //已完成的实例数
//        int completedInstance = (int)execution.getVariable("nrOfCompletedInstances");
//        //否决判断，一票否决
//        boolean pass = (Boolean) execution.getVariable("pass");
//        if(pass == false){
//                //一票否决其他实例没必要做，结束
//                return true;
//            //}
//        }
//        //所有实例任务未全部做完则继续其他实例任务
//        if(completedInstance != 2){
//            return false;
//        }else{
//            //所有都做完了没被否决，结束
//            return true;
//        }
//    }
//}
