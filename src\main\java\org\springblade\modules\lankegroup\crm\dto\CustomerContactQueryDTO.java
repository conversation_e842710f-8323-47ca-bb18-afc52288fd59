/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户联系人查询参数对象
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@ApiModel(value = "CustomerContactQueryDTO对象", description = "客户联系人查询参数对象")
public class CustomerContactQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String seek;

    @ApiModelProperty(value = "是否查询跟进的客户")
    private Boolean isVisit;
}
