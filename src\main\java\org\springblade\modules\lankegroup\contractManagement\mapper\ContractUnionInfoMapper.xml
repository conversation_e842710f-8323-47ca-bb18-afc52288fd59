<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractUnionInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractUnionInfoResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="kd_project_fid" property="kdProjectFid"/>
        <result column="contract_id" property="contractId"/>
        <result column="kd_contract_fid" property="kdContractFid"/>
        <result column="contract_amount" property="contractAmount"/>
        <result column="kd_contract_amount" property="kdContractAmount"/>
        <result column="kd_date" property="kdDate"/>
        <result column="approve_date" property="approveDate"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="inyear" property="inyear"/>
    </resultMap>
    <select id="getContractInfo" resultType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfoResult">
        select project.id as projectId,
            project.kd_project_fid as kdProjectFid,
            con.id as contractId,
            con.kd_fid as kdContractFid,
            con.contract_amount as contractAmount,
            detail.update_time as approveDate,
            m.rn as contractNumber
        from blade_project_basic project
            left join blade_contract con on con.project_id = project.id
            left join blade_archive_details detail on con.id = detail.contract_id
            LEFT JOIN (
            SELECT
            id,
            project_id,
            rn
        FROM
            ( SELECT con.id,con.project_id, row_number ( ) OVER ( partition BY con.project_id ORDER BY detail.update_time asc ) AS rn FROM blade_contract con
            left join blade_archive_details detail on con.id = detail.contract_id
            where con.status = 5
            and con.is_deleted = 0
            and (con.contract_type = 0 or con.contract_type = 4)
            and detail.status = 1
            and detail.is_deleted = 0
            ) tn
            ) m ON m.id = con.id
        where  project.push_down=1
            and project.status=1
            and project.is_Deleted=0
            and project.forbid_status='A'
            and project.project_closed in (0,1)
            and con.status = 5
            and con.is_deleted = 0
            and (con.contract_type = 0 or con.contract_type = 4)
            and detail.status = 1
            and detail.is_deleted = 0
    </select>
    <select id="getKdContractInfo" resultType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfoResult">
        SELECT
            c.fid as kdContractFid,
            c.F_XMDA as kdProjectFid,
            f.FCONTRACTAMOUNT as kdContractAmount,
            c.FDATE as kdDate,
            c.FAPPROVEDATE as approveDate,
            m.rn as contractNumber,
            (select count(1) from T_CRM_CONTRACTENTRY as mx where c.FID=mx.FID) as mxs
        FROM
            T_CRM_CONTRACT AS c -- 合同表
                LEFT JOIN T_CRM_CONTRACTFIN AS f ON f.FID= c.FID --子单据头_财务信息
                left join T_BAS_PREBDONE as x ON x.FID= c.F_XMDA
                LEFT JOIN (
                SELECT
                    fid,
                    F_XMDA,
                    rn
                FROM
                    ( SELECT *, row_number ( ) OVER ( partition BY F_XMDA ORDER BY FDATE asc ) AS rn FROM T_CRM_CONTRACT
                      WHERE
                          FDocumentStatus = 'C'
                        AND FCANCELSTATUS = 'A'
                        AND FSALEORGID != 1) tn

            ) m ON m.fid = c.fid
        WHERE
            c.FDOCUMENTSTATUS = 'C' -- 单据状态（Z暂存,A创建,B审核中,C已审核,D重新审核）
          AND c.FCANCELSTATUS = 'A'
          and c.FSALEORGID != 1
        order by c.F_XMDA,m.rn
    </select>
    <delete id="deleteData">
        TRUNCATE TABLE blade_contract_union_info
    </delete>
    <insert id="batchInsertContractInfo" parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        insert INTO
        blade_contract_union_info
        (project_id,
         kd_project_fid,
         contract_id,
         kd_contract_fid,
         contract_amount,
         kd_date,
         approve_date,
         contract_number,inyear)
        VALUES
        <foreach collection="contractUnionInfos" item="item" separator="," index="index">
            (#{item.projectId},
             #{item.kdProjectFid},
             #{item.contractId},
             #{item.kdContractFid},
             #{item.contractAmount},
             #{item.kdDate},
             #{item.approveDate},
             #{item.contractNumber},#{item.inyear})
        </foreach>
    </insert>
    <insert id="batchInsertKdContractInfo" parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        insert INTO
        blade_contract_union_info
        (project_id,
        kd_project_fid,
        kd_contract_fid,
        kd_contract_amount,
        kd_date,
        approve_date,
        contract_number,inyear)
        VALUES
        <foreach collection="contractUnionInfos" item="item" separator="," index="index">
            (
            #{item.projectId},
            #{item.kdProjectFid},
            #{item.kdContractFid},
            #{item.kdContractAmount},
            #{item.kdDate},
            #{item.approveDate},
            #{item.contractNumber},#{item.inyear})
        </foreach>
    </insert>
    <select id="getLatestContract" resultType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        select * from blade_contract_union_info
        where project_id = #{projectId}
        order by contract_number desc limit 1
    </select>
    <insert id="saveContractUnionInfo" parameterType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        insert INTO
            blade_contract_union_info
        (project_id,
         kd_project_fid,
         contract_id,
         kd_contract_fid,
         contract_amount,
         kd_contract_amount,
         kd_date,
         approve_date,
         contract_number,inyear)
            VALUE
            (#{projectId},
            #{kdProjectFid},
            #{contractId},
            #{kdContractFid},
            #{contractAmount},
            #{kdContractAmount},
            #{kdDate},
            #{approveDate},
            #{contractNumber},#{inyear})
    </insert>

    <select id="getContractAmountByProjectId" resultType="org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo">
        SELECT
            max( kd_date ) kd_date,
            sum( kd_contract_amount ) kd_contract_amount
        FROM
            blade_contract_union_info contract
        WHERE
            kd_contract_amount IS NOT NULL
            and project_id = #{projectId}
    </select>
</mapper>
