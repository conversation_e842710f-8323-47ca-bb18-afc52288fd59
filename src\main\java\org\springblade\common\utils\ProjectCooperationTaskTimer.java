package org.springblade.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.pro_management.entity.BiddingCooperation;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.enums.BiddingCooperationTaskStatusEnum;
import org.springblade.modules.lankegroup.pro_management.enums.ProjectCollaborationTaskStatusEnum;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IBiddingCooperationService;
import org.springblade.modules.lankegroup.pro_management.vo.BiddingCooperationVO;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 项目的招标协作 任务状态（0未开始/1进行中/2已超期/3已完成/4已终止）定时
 * -每天凌晨2:10运行
 *
 * <AUTHOR>
 * @since 2024年5月13日 10:52
 **/
@Slf4j
@Component
@AllArgsConstructor
public class ProjectCooperationTaskTimer {

    private final IBiddingCooperationService iBiddingCooperationService;
    private final ProjectBasicMapper projectBasicMapper;


    /**
     * 定时变更招标协作任务状态
     */
//    @Scheduled(cron = "0 10 2 * * ?")
    public void changeProjectCollaborationTaskStatus() {
        // 获取所有的协作任务+状态为（未开始、进行中）+未删除的数据
        R<List<BiddingCooperation>> dataListR = getNeedChangeStatusDataList();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (dataListR.isSuccess()) {
            for (BiddingCooperation data : dataListR.getData()) {
                // 判断状态是否需要变更
                Date startDate = data.getCreateTime();
                Date endDate;
                try {
                    if (data.getTaskType() == 1) {
                        endDate = simpleDateFormat.parse(data.getLatestTime());
                    } else {
                        endDate = simpleDateFormat.parse(data.getPublishTime());
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                BiddingCooperationTaskStatusEnum taskStatusEnum = iBiddingCooperationService.getProjectCollaborationTaskStatus(startDate, endDate);
                if (taskStatusEnum != null && !(taskStatusEnum.getCode().equals(data.getStatus()))) {
                    try {
                        R updateDataR = updateData(data.getId(), taskStatusEnum);
                        if (updateDataR.isSuccess() && taskStatusEnum.getCode().equals(ProjectCollaborationTaskStatusEnum.已超期.getCode())) {
                            // 发送消息
                            ProjectBasic projectBasic = projectBasicMapper.selectById(data.getProjectId());
                            if (null == projectBasic) {
                                log.info("【招标协作任务定时】发送消息-获取项目信息失败，跳过本次发送。招标协作任务ID={},项目ID={}", data.getId(), data.getProjectId());
                                continue;
                            }
                            iBiddingCooperationService.sendMsgCoreBefore(3, data, projectBasic.getProjectName(), null);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取待变更状态数据
     */
    private R<List<BiddingCooperation>> getNeedChangeStatusDataList() {
        LambdaQueryWrapper<BiddingCooperation> queryWrapper = new QueryWrapper<BiddingCooperation>().lambda();
        queryWrapper.and(wrapper -> wrapper.eq(BiddingCooperation::getStatus, 1));
        List<BiddingCooperation> list = null;
        try {
            list = iBiddingCooperationService.list(queryWrapper);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (list == null || list.size() == 0) {
            return R.fail("未获取到数据");
        }
        return R.data(list);
    }

    /**
     * 修改对应数据
     *
     * @param id 协作任务ID
     * @return
     */
    private R updateData(Long id, BiddingCooperationTaskStatusEnum taskStatusEnum) {
        LambdaUpdateWrapper<BiddingCooperation> updateWrapper = new UpdateWrapper<BiddingCooperation>().lambda();
        updateWrapper.eq(BiddingCooperation::getId, id);
        updateWrapper.set(BiddingCooperation::getStatus, taskStatusEnum.getCode());
        return R.status(iBiddingCooperationService.update(updateWrapper));
    }

}
