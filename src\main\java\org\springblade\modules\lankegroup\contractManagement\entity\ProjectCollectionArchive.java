/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 归档信息--回款计划--临时表实体类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@Data
@TableName("blade_project_collection_archive")
@EqualsAndHashCode(callSuper = true)
public class ProjectCollectionArchive extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 负责人名称
	*/
		private String name;
	/**
	* 负责人id
	*/
		private String userId;
	/**
	* 客户id
	*/
		private Long customerId;
	/**
	* 项目id
	*/
		private String projectId;
	/**
	* 预计回款金额
	*/
		private Double estimatedCollectionAmount;
	/**
	* 预计回款日期
	*/
		private String estimatedCollectionDate;
	/**
	* 回款状态（字典表）
	*/
		private String collectionStatus;
	/**
	* 计划完成状态（1已完成，2已超期,  3未完成 4 已确认）
	*/
		private Integer planStatus;
	/**
	* 实际回款金额
	*/
		private Double amountCashed;
	/**
	* 备注
	*/
		private String remark;
	/**
	 * 预计回款百分比
	 */
	private String expectedPaymentPercentage;
	/**
	 * 付款条件
	 */
	private String paymentCondition;

}
