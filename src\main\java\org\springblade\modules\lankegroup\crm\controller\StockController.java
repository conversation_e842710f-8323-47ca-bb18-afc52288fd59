/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.Stock;
import org.springblade.modules.lankegroup.crm.vo.StockVO;
import org.springblade.modules.lankegroup.crm.service.IStockService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 仓库表 控制器
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/stock")
@Api(value = "仓库表", tags = "仓库表接口")
public class StockController extends BladeController {

	private final IStockService stockService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入stock")
	public R<Stock> detail(Stock stock) {
		Stock detail = stockService.getOne(Condition.getQueryWrapper(stock));
		return R.data(detail);
	}

	/**
	 * 分页 仓库表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入stock")
	public R<IPage<Stock>> list(Stock stock, Query query) {
		IPage<Stock> pages = stockService.page(Condition.getPage(query), Condition.getQueryWrapper(stock));
		return R.data(pages);
	}

	/**
	 * 自定义分页 仓库表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入stock")
	public R<IPage<StockVO>> page(StockVO stock, Query query) {
		IPage<StockVO> pages = stockService.selectStockPage(Condition.getPage(query), stock);
		return R.data(pages);
	}

	/**
	 * 新增 仓库表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入stock")
	public R save(@Valid @RequestBody Stock stock) {
		return R.status(stockService.save(stock));
	}

	/**
	 * 修改 仓库表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入stock")
	public R update(@Valid @RequestBody Stock stock) {
		return R.status(stockService.updateById(stock));
	}

	/**
	 * 新增或修改 仓库表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入stock")
	public R submit(@Valid @RequestBody Stock stock) {
		return R.status(stockService.saveOrUpdate(stock));
	}

	
	/**
	 * 删除 仓库表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(stockService.deleteLogic(Func.toLongList(ids)));
	}
	/**
	 * 自定义分页 仓库表
	 */
	@GetMapping("/stockList")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "仓库列表", notes = "无参")
	public R stockList(){
		return R.data(stockService.stockList());
	}
}
