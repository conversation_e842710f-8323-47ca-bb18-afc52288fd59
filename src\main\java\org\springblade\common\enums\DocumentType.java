package org.springblade.common.enums;

public enum DocumentType {
    //system系统公告 collect收藏联系人 error违规 person联系人管理
    // invoice开票 contract合同 project项目
    // call拜访 log日志 done金蝶待办 sum统一消息提醒
    报销单("报销单","/bxd/expenseAccountDetails/","done"),
    采购订单("采购订单","/cg/PurorderDetails/","done"),
    采购申请单("采购申请单","/cg/purchaseDetails/-_-&","done"),
    出库单("出库单","/ck/stockoutDetails/-_-&","done"),
    付款单("付款单","/fkd/paymentDetails/","done"),
    回款单("回款单","/refundReceipt/refundReceiptDetail?type=approval&id=","done"),
    开票单("开票单","/kp/InvoiceDetail?type=approval&id=","done"),
    油卡("油卡","/ykcz/oilCardDetails/","done"),
    借款单("借款单","/jkd/loanDetails/","done"),
    应收单("应收单","/ysd/accountsReceivableDetsilA/我发起的&","done"),
    开票作废("开票作废","/kq/invoiceDetails/","done"),
    立项("立项","/xmlx/ProjectInitiation","project"),
    收藏联系人("收藏客户","","collect"),
    联系人管理("客户管理","","person"),
    客户管理("客户管理","/lxr/InstitutionApproval","customer"),
    开票统计("开票统计","/kq/invoiceStatistics/0","invoice"),
    合同("合同","/htgl/contractDetails","contract"),
    归档("归档","/htgl/contractFiling","contract"),
    变更("变更","/htgl/contractChangeDetails","contract"),
    拜访看板("拜访看板","/khbf/customerVisitDetails/","call"),
    日志管理("日志管理","/khbf/customerLogDetails/","log"),
    项目机会("项目机会","","opportunity"),
    项目协作任务("项目协作任务","/projectboard/taskdetail?id=","pct"),
    招标协作任务("招标协作任务","/projectboard/tenderCooperationDetail?id=","biddingcooperation"),
    项目进度阶段负责人("进度更新", "/projectboard/projectdetail", "ppta"),
    项目进度阶段参与人("进度更新", "/projectboard/projectdetail", "pptna"),
    项目成功关闭("成功关闭", "/projectboard/projectdetail", "psc"),
    项目失败关闭("失败关闭", "/projectboard/projectdetail", "pfc"),
    项目添加业务负责人("添加项目成员", "/projectboard/projectdetail", "pip"),
    项目添加阶段负责人("添加项目成员", "/projectboard/projectdetail", "pita"),
    项目添加阶段参与人("添加项目成员", "/projectboard/projectdetail", "pitnt"),
    项目质保流程("项目质保流程", "/kq/badDebtsProcessDetails", "done"),
    项目申请法务流程("项目申请法务流程", "/kq/badDebtsProcessDetails", "done"),
    项目坏账流程("项目坏账流程", "/kq/badDebtsProcessDetails", "done"),
    渠道商("渠道商","/channel/channelSupplierView/","channel"),
    渠道合同("渠道合同","/htgl/contractDetails","contract"),
    渠道合同归档("渠道合同归档","/htgl/contractFiling","contract"),
    渠道合同变更("渠道合同变更","/htgl/contractChangeDetails","contract"),
    挂账应付单("挂账应付单","/OtherPayable/otherPayableDetails","done"),
    备案登记项目业务负责人("备案提醒", "", "badjxmywfzr"),
    备案登记单位信息创建人("备案提醒", "", "badjdwxxcjr"),
    版本管理("兰科数字化版本更新","/versionManage/versionList","bbgl"),
    共享联系人申请("共享客户申请","/lxr/ContactsDetails/lxrDetails","gxlxrsq"),
    印章申请("印章申请","/signetApply/signetApplyDetail?id=","yzsq"),
    任务交付("任务交付","/taskDelivery/taskDeliveryDetail?id=","rwjf"),
    // 测试业务 ，暂时用客户（晶莱机构）的消息type
    测试业务("测试业务审批提醒","/KingdeeTodo","customer"),
    商机管理("商机管理","/projectOpportunities/ProjectOpportunitiesDetails","opportunity"),
    报价审批("报价审批","/lxr/QuotationApproval","customer"),
    销售合同("销售合同","/KingdeeTodo","contract"),
    ;

    DocumentType(String name, String url, String type) {
        this.name = name;
        this.url = url;
        this.type = type;
    }

    DocumentType() {
    }

    // 成员变量
    private String name;
    private String url;
    private String type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
