package org.springblade.modules.lankegroup.clientrelation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 人员
 */
@TableName("blade_personnel_market")
@Data
public class PersonnelMarket extends BaseEntity {

    private Long id;

    /**
     * 人员ID
     */
    private String personnelId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 行业ID
     */
    private String marketId;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 客户数量
     */
    private Integer num;

}
