//package org.springblade.common.utils;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springblade.common.config.AppConfig;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.LxDingMessageSender;
//import org.springblade.modules.lankegroup.kingdeeproject.dingmsg.LxMsgQueue;
//import org.springblade.modules.lankegroup.kingdeeproject.service.TokenService;
//import org.springblade.modules.lankegroup.projectKanBan.dingmsg.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//@Component
//public class MsgSenderSchedulerCard {
//
//    private final static Logger log = LoggerFactory.getLogger(MsgSenderSchedulerCard.class);
//
//    @Autowired
//    private TokenService tokenService;
//
//    @Autowired
//    private AppConfig appConfig;
//
////    @Scheduled(fixedDelay = 5000, initialDelay = 3000)
//    public void BillDingMessageCard() {
//        try {
//            BillDingMessageCard sender = SimpleMsgQueueCard.instance().poll();
//            BillMsgBoss boss= SimpleMsgBossQueue.instance().poll();
//            LxDingMessageSender lx= LxMsgQueue.instance().poll();
//            if (sender != null) {
//                log.info("获取到钉钉消息队列中的消息: " + sender);
//                sender.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
//                log.info("billDingMessageSender.sendMsg");
//            }
//            if(boss!=null){
//                log.info("获取到钉钉消息队列中的消息: " + boss);
//                boss.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
//                log.info("billDingMessageSender.sendMsg");
//            }
//            if (lx != null) {
//                log.info("获取到钉钉消息队列中的消息: " + sender);
//                lx.sendMsg(Long.parseLong(appConfig.getAgentId()), tokenService.getAccessToken().getResult());
//                log.info("billDingMessageSender.sendMsg");
//            }
//        } catch (Exception e) {
//            log.error("发送钉钉通知失败!");
//            e.printStackTrace();
//        }
//    }
//
//}
