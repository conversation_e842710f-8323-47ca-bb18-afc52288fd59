package org.springblade.modules.lankegroup.crm.entity.kingdee;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class KDSupplier {
    //实体主键
    public String FSupplierId;
    //创建组织
    public Map FCreateOrgId = new HashMap();
    //使用组织
    public  Map FUseOrgId = new HashMap();

    //供应商编码
    public String FNumber;
    //供应商名称
    public String FName;

    //供应商开户行
    public String F_KHH;
    //供应商银行账号
    public String F_YHZH;

//    供应商分组编码 名称
    public Map FGroup=new HashMap();
/**
 * 基本信息表
 * 基本信息表包含供应商的【通讯地址FAddress】【供应类别FSupplyClassify】等字段字段
 */
    public Map FBaseInfo=new HashMap();


}
