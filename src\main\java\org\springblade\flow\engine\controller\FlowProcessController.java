/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.engine.controller;

import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.flow.engine.vo.WorkFlowVO;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 流程通用控制器
 *
 * <AUTHOR>
 */
@NonDS
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("process")
@Api(value = "流程通用控制器", tags = "流程通用控制器")
public class FlowProcessController {

	private static final String IMAGE_NAME = "image";
	private final FlowEngineService flowEngineService;

	/**
	 * 获取流转历史列表
	 *
	 * @param processInstanceId 流程实例id
	 * @param startActivityId   开始节点id
	 * @param endActivityId     结束节点id
	 */
	@GetMapping(value = "history-flow-list")
	public R<List<BladeFlow>> historyFlowList(@RequestParam String processInstanceId, String startActivityId, String endActivityId) {
		return R.data(flowEngineService.historyFlowList(processInstanceId, startActivityId, endActivityId));
	}

	/**
	 * 流程节点进程图
	 *
	 * @param processDefinitionId 流程id
	 * @param processInstanceId   流程实例id
	 */
	@GetMapping(value = "model-view")
	public R modelView(String processDefinitionId, String processInstanceId) {
		return R.data(flowEngineService.modelView(processDefinitionId, processInstanceId));
	}

	/**
	 * 流程节点进程图
	 *
	 * @param processInstanceId   流程实例id
	 * @param httpServletResponse http响应
	 */
	@GetMapping(value = "diagram-view")
	public void diagramView(String processInstanceId, HttpServletResponse httpServletResponse) {
		flowEngineService.diagramView(processInstanceId, httpServletResponse);
	}

	/**
	 * 流程图展示
	 *
	 * @param processDefinitionId 流程id
	 * @param processInstanceId   实例id
	 * @param resourceType        资源类型
	 * @param response            响应
	 */
	@GetMapping("resource-view")
	public void resourceView(@RequestParam String processDefinitionId, String processInstanceId, @RequestParam(defaultValue = IMAGE_NAME) String resourceType, HttpServletResponse response) {
		flowEngineService.resourceView(processDefinitionId, processInstanceId, resourceType, response);
	}

	/**
	 * 流程预测
	 *
	 * @param
	 * @return
	 */
	@PostMapping("getFLowPrediction")
	@ApiOperation(value = "流程预测", notes = "传入formKey")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "formKey", value = "流程dictKey(来源于系统字典flow下字典项：\r\n" +
					"测试业务----flow_test_business\r\n" +
					"机构管理----blade_customer\r\n" +
					")", dataType = "string", required = true),

			@ApiImplicitParam(name = "processDefinitionId", value = "部署Id,可为空", dataType = "string", required = true),
	})
	public R<WorkFlowVO> getFLowPrediction(@ApiParam(value = "表单key") @RequestBody FlowModel flowModel) {
		return flowEngineService.getFLowPrediction(flowModel.getFormKey(), flowModel.getProcessDefinitionId());
	}

	/**
	 * 获取所有的用户流程节点
	 *
	 * @return
	 */
	@PostMapping("getALLFlowNode")
	@ApiOperation(value = "获取所有的用户流程节点", notes = "传入流程实例processInstanceId")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "processInstanceId", value = "流程实例ID", dataType = "string", required = true),
	})
	public R<WorkFlowVO> getALLFlowNode(@RequestBody WorkFlowDTO workFlow) {
		return R.data(flowEngineService.getALLFlowNode(workFlow));
	}

	/**
	 * 获取历史流程节点
	 *
	 * @return
	 */
	@PostMapping("getHistoryFlowNode")
//	@ApiOperation(value = "获取历史流程节点", notes = "传入业务表单主键")
	public R<WorkFlowVO> getHistoryFlowNode(@RequestParam String formKey, @RequestParam Long formDataId) {
		return R.data(flowEngineService.getHistoryFlowNode(formKey, formDataId));
	}

	/**
	 * 获取流程实例id
	 * @param formKey
	 * @param formDataId
	 * @return
	 */
	@GetMapping("getProcessInstanceId")
//	@ApiOperation(value = "", notes = "传入业务表单主键")
	public R getProcessInstanceId(@RequestParam String formKey, @RequestParam Long formDataId) {
		return R.data(flowEngineService.getProcessInstanceId(formKey, formDataId));
	}
}
