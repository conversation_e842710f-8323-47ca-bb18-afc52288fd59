package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

import java.util.List;

@Data
public class CustomerKanBanResult {

    /**
     * 行业编码
     */
    private String marketCode;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 应有客户
     */
    private Integer yCustomerCount;

    /**
     * 实有客户
     */
    private Integer sCustomerCount;

    /**
     * 客户覆盖度
     */
    private float customerCoverage;

    /**
     * 区域市场ID
     */
    private String professionId;

    /**
     * 用户组员ID
     */
    private List<String> userId;
}
