package org.springblade.modules.lankegroup.contractManagement.entity.kingdee;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ContractEntry {
    //结算组织
    @JSONField(name = "FSettleOrgId",ordinal = 1)
    private Map FSettleOrgId = new LinkedHashMap();

    //物料编码
    @JSONField(name = "FMaterialId",ordinal = 2)
    private Map FMaterialId = new LinkedHashMap();

    //销售数量
    @JSONField(name = "FQty",ordinal = 3)
    private Integer FQty;

    //含税单价
    @JSONField(name = "FTaxPrice",ordinal = 4)
    private Double FTaxPrice;

    //税率%
    @JSONField(name = "FEntryTaxRate",ordinal = 5)
    private Integer FEntryTaxRate;
//    单价（采购合同）
    @JSONField(name = "FPrice",ordinal = 2)
    private Double FPrice;

}
