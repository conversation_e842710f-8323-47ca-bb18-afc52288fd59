package org.springblade.modules.lankegroup.crm.dto;

import org.springblade.modules.lankegroup.crm.entity.ChannelSupplierContact;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道商专职联系人表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelSupplierContactDTO extends ChannelSupplierContact {
	private static final long serialVersionUID = 1L;

}
