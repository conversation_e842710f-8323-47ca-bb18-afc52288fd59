package org.springblade.modules.lankegroup.clientrelation.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 行业聚合
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfessionPolymerizationNew {

    /**
     * 行业ID
     */
    private String id;

    /**
     * 行业名称
     */
    private String name;

    /**
     * 应有客户
     */
    private Integer oughtCount;

    /**
     * 实有客户
     */
    private Integer actualCount;

    /**
     * 区域列表
     */
    private List<String> areaList = new ArrayList<>();

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 区域市场组员ID
     */
    private List<String> userList = new ArrayList<>();

}
