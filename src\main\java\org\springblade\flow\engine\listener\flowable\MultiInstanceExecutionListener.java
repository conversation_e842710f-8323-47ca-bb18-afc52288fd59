package org.springblade.flow.engine.listener.flowable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.utils.SpringContextUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.constant.WorkFlowConstants;
import org.springblade.flow.engine.builder.ApproverTypeEnum;
import org.springblade.flow.engine.builder.entity.ApproverFlowNode;
import org.springblade.flow.engine.constant.FlowProcessConfigConstant;
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.WorkFlowConstants.MULTI_INSTANCE_ASSIGNEE_LIST_VAR;
import static org.springblade.flow.core.constant.WorkFlowConstants.PROC_INSTANCE_START_USER_NAME_VAR;


/**
 * <AUTHOR> mtf
 * 会签执行监听器
 * 在会签的节点进入之前 动态根据选择的类型 查询到具体的人员 并且设置到流程变量中
 */
@Data
@Component
public class MultiInstanceExecutionListener implements ExecutionListener {

//	@Autowired
//    private FlowIdentityExtUtils flowIdentityExtUtils;
	@Autowired
    private UserMapper userMapper;


	/**
	 * 会签操作对象 bpmnbuild时传入
	 */
	private Expression assignee;

	@Override
	public void notify(DelegateExecution delegateTask) {
		Map<String, Object> variables = delegateTask.getVariables();
//		variables.put("assignee", variables.get("assignee"));
		//获取监听的对象
		FlowElement currentFlowElement = delegateTask.getCurrentFlowElement();
		if (currentFlowElement instanceof UserTask) {
			//用户任务
//			UserTask userTask = (UserTask) currentFlowElement;
			//获取任务的候选办理人
			// 取出相关数据 获取办理人
			Object value = assignee.getValue(delegateTask);
			List<String> assigneeList = CollUtil.newArrayList();
			if (ObjectUtil.isNotNull(value)) {
				ApproverFlowNode approverFlowNode = JSONUtil.toBean(value.toString(), ApproverFlowNode.class);
				ApproverTypeEnum approverType = EnumUtil.getBy(ApproverTypeEnum.class, item -> Objects.equals(item.getValue(), approverFlowNode.getSettype()));
				switch (approverType) {
					case DESIGNATED://指定审批人
						//设置为用户
						List<ApproverFlowNode.NodeUser> nodeUserList = approverFlowNode.getNodeUserList();
						if (ObjectUtil.isNotEmpty(nodeUserList)) {
							List<String> userList = nodeUserList.stream().map(ApproverFlowNode.NodeUser::getId).collect(Collectors.toList());
							StringBuilder userIdBuilder = new StringBuilder();
							userList.forEach(userId -> {
								User user = UserCache.getUser(Long.valueOf(userId));
								if (user != null && user.getIsDeleted() == 0 && user.getStatus() == 1) {
									assigneeList.add(userId);
								}
							});
							if (assigneeList.size() == 0) {
								delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, nodeUserList.stream().map(role -> String.valueOf(role.getName())).collect(Collectors.joining(StrUtil.COMMA)) + "（已删除或禁用）");
							}
//							assigneeList.addAll(nodeUserList.stream().map(ApproverFlowNode.NodeUser::getId).collect(Collectors.toList()));
						}
						break;
					case ROLE:// 角色审批人
						// 查询角色用户列表
						List<ApproverFlowNode.NodeRole> nodeRoleList = approverFlowNode.getNodeRoleList();
						if (ObjectUtil.isNotEmpty(nodeRoleList)) {
							//获取角色下的人员
							String roleIds = nodeRoleList.stream().map(role -> String.valueOf(role.getId())).collect(Collectors.joining(StrUtil.COMMA));
							List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleIds));
							if (userList != null && userList.size() > 0) {
								List userIds = userList.stream().filter(user -> user.getIsDeleted() == 0 && user.getStatus() == 1).map(User::getId).collect(Collectors.toList());
								if (userIds != null && userIds.size() > 0) {
									assigneeList.addAll(userIds);
								} else {
									delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, nodeRoleList.stream().map(role -> String.valueOf(role.getName())).collect(Collectors.joining(StrUtil.COMMA)) + "（无用户）");
								}
							} else {
								delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, nodeRoleList.stream().map(role -> String.valueOf(role.getName())).collect(Collectors.joining(StrUtil.COMMA)) + "（为空）");
							}
						}
						break;
					case USER_IMMEDIATE_SUPERVISOR://直属主管
						User loginUser = UserCache.getUser(AuthUtil.getUserId());
						if (Func.isEmpty(loginUser.getParentId())) {
							delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, "直属主管（已删除或禁用）");
						} else {
							assigneeList.add(String.valueOf(loginUser.getParentId()));
						}
						break;
					case DEPT_LEADER://部门主管
						User deptLeader = Optional.ofNullable(userMapper.getLeaderUsersByDeptId(AuthUtil.getDeptId()))
								.map(e -> CollectionUtil.isNotEmpty(e) ? e.get(0) : null).orElse(null);
						if (Func.isEmpty(deptLeader) || Func.isEmpty(deptLeader.getId())) {
							delegateTask.setVariable(WorkFlowConstants.ORIGINAL_APPROVER, StrUtil.format("{}（已删除或禁用）", Func.isNotEmpty(deptLeader) && Func.isNotBlank(deptLeader.getRealName()) ? deptLeader.getRealName() : "部门主管"));
						} else {
							assigneeList.add(String.valueOf(deptLeader.getId()));
						}
						break;
					case INITIATOR_OPTIONAL:
//						List<String> sponsorUserList = setInitiatorOptional(approverFlowNode);
//						if (ObjectUtil.isNotEmpty(sponsorUserList)) {
//							assigneeList.addAll(sponsorUserList);
//						}
						assigneeList.addAll(StrUtil.split(variables.get(currentFlowElement.getId()).toString(), StrUtil.COMMA));
						break;
				}
//				// 指定审批
//				if (Objects.equals(approverFlowNode.getSettype(), ApproverTypeEnum.DESIGNATED.getValue())) {
//
//					// 表单字段获取 表单字段
//				}
//				else if (Objects.equals(approverFlowNode.getSettype(), ApproverTypeEnum.FORM_USER_FIELD.getValue())) {
				// 表单字段获取 表单字段
//                    Object variable = delegateTask.getVariable(approverFlowNode.getFormUserFields());
//                    if (ObjectUtil.isNotNull(variable)) {
//                        List<String> list = JSONUtil.parseArray(variable.toString()).toList(JSONObject.class).stream().map(o -> o.getStr("name")).collect(Collectors.toList());
//                        assigneeList.addAll(list);
//                    }
//                }
				// 如果没有任何代办人 审批人为自己
//                if (ObjectUtil.isEmpty(assigneeList)) {
//                    assigneeList.add(delegateTask.getVariable(PROC_INSTANCE_START_USER_NAME_VAR).toString());
//                }
				if (assigneeList == null || assigneeList.size() == 0) {
					assigneeList.add(FlowProcessConfigConstant.VIRTUAL_APPROVER);
				}
				delegateTask.setVariable(MULTI_INSTANCE_ASSIGNEE_LIST_VAR, assigneeList);
			}
		}
	}

	/**
	 * 自选人数-自选范围：
	 * 1-1（自选一个人-全公司）；1-2（自选一个人-指定成员）；1-3（自选一个人-角色）；2-1（自选多人-全公司）；2-2（自选多人-指定成员）；2-3（自选多人-角色）
	 */
	private List<String> setInitiatorOptional(ApproverFlowNode approverFlowNode) {
		List<String> assigneeList = CollUtil.newArrayList();
		switch (approverFlowNode.getSponsorType()) {
			case "1-1":
			case "2-1":
				break;
			case "1-2":
			case "2-2":
				List<ApproverFlowNode.NodeUser> sponsorUserList = approverFlowNode.getSponsorUserList();
				if (ObjectUtil.isNotEmpty(sponsorUserList)) {
					assigneeList.addAll(sponsorUserList.stream().map(ApproverFlowNode.NodeUser::getId).collect(Collectors.toList()));
				}
				break;
			case "1-3":
			case "2-3":
				// 查询角色用户列表
				List<ApproverFlowNode.NodeRole> nodeRoleList = approverFlowNode.getSponsorRoleList();
				if (ObjectUtil.isNotEmpty(nodeRoleList)) {
					//获取角色下的人员
					String roleIds = nodeRoleList.stream().map(role -> String.valueOf(role.getId())).collect(Collectors.joining(StrUtil.COMMA));
					List<User> userList = SpringContextUtils.getBean(IUserService.class).listByRole(Func.toLongList(roleIds));

					assigneeList.addAll(userList.stream().map(user -> String.valueOf(user.getId())).collect(Collectors.toList()));
				}
				break;
		}
		return assigneeList;
	}
}
