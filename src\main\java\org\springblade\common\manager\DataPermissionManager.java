package org.springblade.common.manager;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.config.DataPermissionFieldConfig;
import org.springblade.common.enums.UserLevelEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.mapper.UserMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用数据权限管理器（改进版）
 * 支持默认权限配置和业务类型配置
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class DataPermissionManager {

    private final UserLevelManager userLevelManager;
    private final IUserService userService;
    private final DataPermissionFieldConfig dataPermissionFieldConfig;
    private final UserMapper userMapper;

    /**
     * 为VO对象设置数据权限（原版本兼容方法）
     *
     * @param vo 查询VO对象
     * @param dataAttribute 数据属性（如 "sub" 表示查看下级数据）
     */
    public void applyDataPermission(Object vo, String dataAttribute) {
        applyDataPermissionWithBusinessType(vo, dataAttribute, null);
    }

    /**
     * 为VO对象设置数据权限（支持业务类型配置）
     *
     * @param vo 查询VO对象
     * @param dataAttribute 数据属性（如 "sub" 表示查看下级数据）
     * @param businessType 业务类型（如 "quotation"，会自动使用配置的字段）
     */
    public void applyDataPermissionWithBusinessType(Object vo, String dataAttribute, String businessType) {
        if (vo == null) {
            return;
        }

        Long currentUserId = AuthUtil.getUserId();
        UserLevelEnum userLevel = userLevelManager.getUserLevel(currentUserId);

        // 设置用户级别
        setField(vo, "targetUserLevel", userLevel.getCode());

        // 根据用户级别设置允许查看的用户ID列表
        List<Long> allowedUserIds = calculateAllowedUserIds(currentUserId, userLevel, dataAttribute);
        setField(vo, "allowedUserIds", allowedUserIds);

        // 如果指定了业务类型，设置对应的字段列表
        if (Func.isNotBlank(businessType)) {
            List<String> ownerFields = dataPermissionFieldConfig.getOwnerFields(businessType);
            System.out.println("ow----"+ownerFields);
            setField(vo, "dataPermissionFields", ownerFields);

            // 设置关联表权限配置
            List<DataPermissionFieldConfig.RelatedTablePermission> relatedTablePermissions =
                dataPermissionFieldConfig.getRelatedTablePermissions(businessType);
            System.out.println("relatedTablePermissions1----"+relatedTablePermissions.size());
            setField(vo, "relatedTablePermissions", relatedTablePermissions);

            log.debug("业务类型 {} 使用字段: {}, 关联表权限: {}", businessType, ownerFields, relatedTablePermissions.size());
        }
    }

    /**
     * 计算允许查看的用户ID列表
     */
    private List<Long> calculateAllowedUserIds(Long currentUserId, UserLevelEnum userLevel, String dataAttribute) {
        switch (userLevel) {
            case 高层:
            case 财务:
                // 高层和财务可以看所有数据，返回null表示无限制
                return null;

            case 部门领导:
                // 部门领导可以看整个部门的数据
                List<Long> deptUsers = userService.getDeptUserIdsByLeader(currentUserId);
                return deptUsers;

            case 普通员工:
            default:
                // 普通员工只能看自己的数据
                List<Long> ownData = new ArrayList<>();
                ownData.add(currentUserId);

                // 如果是查看下级数据
                if ("sub".equals(dataAttribute)) {
                    List<Long> lowerUsers = userService.getLowerExcludeSelf(currentUserId);
                    ownData.addAll(lowerUsers);
                }

                return ownData;
        }
    }

    /**
     * 通过反射设置字段值
     * 支持在继承层次结构中查找字段
     */
    private void setField(Object obj, String fieldName, Object value) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = null;

            // 在继承层次结构中查找字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
            }
        } catch (Exception e) {
            log.debug("设置字段 {} 失败：{}", fieldName, e.getMessage());
        }
    }

    /**
     * 判断当前登录人是否是指定用户的上级或领导
     *
     * @param targetUserIds 目标用户ID列表，只要满足其中一个则返回true
     * @return 是否上级
     */
    public boolean isSuperiorOf(List<Long> targetUserIds) {
        Long currentUserId = AuthUtil.getUserId();

        if (Func.isEmpty(currentUserId) || Func.isEmpty(targetUserIds)) {
            log.debug("上级权限检查失败：参数为空，currentUserId={}, targetUserIds={}", currentUserId, targetUserIds);
            return false;
        }

        // 过滤掉空值
        List<Long> validTargetUserIds = targetUserIds.stream()
                .filter(id -> id != null && id > 0)
                .collect(java.util.stream.Collectors.toList());

        if (validTargetUserIds.isEmpty()) {
            log.debug("上级权限检查失败：有效的目标用户ID列表为空");
            return false;
        }

        try {
            // 获取目标用户的所有上级（包括目标用户本身）
            List<Long> allParentUserIds = userMapper.getALlParentByUserIdList(validTargetUserIds);

            // 判断当前用户是否在上级列表中
            boolean isSuperior = allParentUserIds != null && allParentUserIds.contains(currentUserId);

            log.debug("上级权限检查：当前用户={}, 目标用户={}, 上级列表={}, 结果={}",
                    currentUserId, validTargetUserIds, allParentUserIds, isSuperior);

            return isSuperior;
        } catch (Exception e) {
            log.error("上级权限检查异常：当前用户={}, 目标用户={}", currentUserId, validTargetUserIds, e);
            return false;
        }
    }



    /**
     * 为VO对象设置上级权限字段（仿照applyDataPermissionWithBusinessType的简洁模式）
     *
     * @param vo 查询VO对象
     * @param businessType 业务类型（如 "quotation"，会自动使用配置的字段判断上级关系）
     */
    public void applySuperiorPermission(Object vo, String businessType) {
        if (vo == null) {
            return;
        }

        try {
            // 获取业务类型对应的负责人字段（Java字段名）
            List<String> ownerJavaFields = dataPermissionFieldConfig.getOwnerJavaFields(businessType);
            List<Long> targetUserIds = new ArrayList<>();

            // 从VO对象中提取负责人ID
            for (String fieldName : ownerJavaFields) {
                Long userId = getFieldValue(vo, fieldName);
                if (userId != null && userId > 0) {
                    targetUserIds.add(userId);
                }
            }

            // 判断当前登录人是否是这些用户的上级
            boolean isSuperior = !targetUserIds.isEmpty() && isSuperiorOf(targetUserIds);

            // 设置isSuperior字段
            setField(vo, "isSuperior", isSuperior);

            log.debug("上级权限设置：业务类型={}, Java字段={}, 目标用户={}, 结果={}", businessType, ownerJavaFields, targetUserIds, isSuperior);
        } catch (Exception e) {
            // 异常情况下默认设置为false
            setField(vo, "isSuperior", false);
            log.debug("设置上级权限异常，默认设置为false：{}", e.getMessage());
        }
    }

    /**
     * 通过反射获取字段值
     * 支持在继承层次结构中查找字段
     */
    private Long getFieldValue(Object obj, String fieldName) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = null;

            // 在继承层次结构中查找字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value instanceof Long) {
                    return (Long) value;
                }
            }
        } catch (Exception e) {
            // 字段不存在或获取失败，忽略
        }
        return null;
    }
}
