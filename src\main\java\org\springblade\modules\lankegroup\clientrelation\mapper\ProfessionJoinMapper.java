package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionJoin;

import java.util.List;

public interface ProfessionJoinMapper extends BaseMapper<ProfessionJoin> {


    /**
     * 根据市场ID获取关联关系ID
     * @param id
     * @return
     */
    List<Long> getJoinList(Long id);

    List<AreaAndMarket> getJoinListByObject(Long id,List<Long> prohibitList);


    /**
     * 删除关联信息
     * @param id
     */
    void deleteProfessionJoin(@Param("id") Long id);
}
