package org.springblade.flow.engine.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.bpmn.model.ImplementationType;
import org.flowable.bpmn.model.ServiceTask;
import org.springblade.flow.engine.service.delegate.ProcessCcDelegate;

import java.util.List;
import java.util.stream.Collectors;

public class ServiceTaskBuilder {


    public static String id(String prefix) {
        return prefix + "_" + IdUtil.objectId();
    }

    /**
     * 创建服务任务 用来抄送
     *
     * @param flowNode 节点
     * @return 任务
     */
    public static ServiceTask createServiceTask(JSONObject flowNode) {
        ServiceTask serviceTask = new ServiceTask();
        // 获取抄送人
        if (!flowNode.isNull("nodeUserList")) {
			List<JSONObject> nodeUserList = flowNode.getJSONArray("nodeUserList").toList(JSONObject.class);
            List<String> listId = nodeUserList.stream().map(item -> item.get("id").toString()).collect(Collectors.toList());
//            List<String> listName = nodeUserList.stream().map(item -> item.get("name").toString()).collect(Collectors.toList());
            // 设置抄送人
            FieldExtension fieldExtension = new FieldExtension();
            fieldExtension.setFieldName(ProcessCcDelegate.Fields.copyToUserList);
            fieldExtension.setStringValue(CollUtil.join(listId, ","));
//            fieldExtension.setStringValue(CollUtil.join(listName, ","));
//			fieldExtension.setId(CollUtil.join(listId, ","));
            serviceTask.setFieldExtensions(CollUtil.newArrayList(fieldExtension));
        }
        serviceTask.setId(id("serviceTask"));
        serviceTask.setName(flowNode.getStr("nodeName"));
        //  指定抄送逻辑类
        serviceTask.setImplementationType(ImplementationType.IMPLEMENTATION_TYPE_CLASS);
        serviceTask.setImplementation(ProcessCcDelegate.class.getName());
        return serviceTask;
    }


}
