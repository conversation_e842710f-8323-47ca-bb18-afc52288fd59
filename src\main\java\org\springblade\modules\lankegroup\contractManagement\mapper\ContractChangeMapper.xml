<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractChangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractChangeResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractChange">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="operation_type_id" property="operationTypeId"/>
        <result column="operation_type_name" property="operationTypeName"/>
        <result column="contract_id" property="contractId"/>
        <result column="party_b_unit_id" property="partyBUnitId"/>
        <result column="party_b_unit_code" property="partyBUnitCode"/>
        <result column="party_b_unit_name" property="partyBUnitName"/>
        <result column="amount_adjustments" property="amountAdjustments"/>
        <result column="adjusted_amount" property="adjustedAmount"/>
        <result column="use_chapter_type_ids" property="useChapterTypeIds"/>
        <result column="use_chapter_type_vals" property="useChapterTypeVals"/>
        <result column="num_of_copies" property="numOfCopies"/>
        <result column="annex" property="annex"/>
        <result column="annex_name" property="annexName"/>
        <result column="process_definition_id" property="processDefinitionId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="task_user" property="taskUser"/>
        <result column="contract_name" property="contractName"/>
        <result column="is_use_chapter" property="isUseChapter"/>
    </resultMap>
    <resultMap id="contractChangeVOResultMap" type="org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="operation_type_id" property="operationTypeId"/>
        <result column="operation_type_name" property="operationTypeName"/>
        <result column="contract_id" property="contractId"/>
        <result column="party_b_unit_id" property="partyBUnitId"/>
        <result column="party_b_unit_code" property="partyBUnitCode"/>
        <result column="party_b_unit_name" property="partyBUnitName"/>
        <result column="amount_adjustments" property="amountAdjustments"/>
        <result column="adjusted_amount" property="adjustedAmount"/>
        <result column="use_chapter_type_ids" property="useChapterTypeIds"/>
        <result column="use_chapter_type_vals" property="useChapterTypeVals"/>
        <result column="num_of_copies" property="numOfCopies"/>
        <result column="annex" property="annex"/>
        <result column="annex_name" property="annexName"/>
        <result column="process_definition_id" property="processDefinitionId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="task_user" property="taskUser"/>
        <result column="createUserName" property="createUserName"/>
        <result column="contract_name" property="contractName"/>
        <result column="is_use_chapter" property="isUseChapter"/>
    </resultMap>
    <resultMap id="contractListResultMap" type="org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult">
        <result column="id" property="id"/>
        <result column="contractName" property="contractName"/>
        <result column="contractStatus" property="contractStatus"/>
        <result column="contractType" property="contractType"/>
        <result column="signedName" property="signedName"/>
        <result column="create_time" property="create_time"/>
        <result column="update_time" property="update_time"/>
        <result column="amount" property="amount"/>
        <result column="sponsor" property="sponsor"/>
        <result column="cid" property="cid"/>
    </resultMap>

    <select id="selectContractChangePage" resultMap="contractChangeResultMap">
        select * from blade_contract_change where is_deleted = 0
    </select>
    <select id="selectDraft" resultMap="contractChangeVOResultMap">
        select c.*, u.name as createUserName
        from
        blade_contract_change  c
        left join blade_user as u on u.id=c.create_user
    where
        c.status=6
      and c.is_deleted=0
      <if test="userId!=null and userId!=''">
          and c.create_user=#{userId}
      </if>
</select>
    <select id="selectDetail" parameterType="java.util.Map" resultMap="contractChangeVOResultMap">
        select c.*, u.name as createUserName
        from
        blade_contract_change  c
        left join blade_user as u on u.id=c.create_user

        where
        c.is_deleted = 0
        <if test="id!=null and id!=''">
            and c.id=#{id}
        </if>
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
    </select>
    <select id="selectTaskIdByBasic" resultType="java.lang.String">
        select
        a.ID_
        from
        blade_contract_change as c
        left join ACT_RU_TASK as a on a.PROC_INST_ID_=c.process_instance_id
        where
        1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
    </select>
    <select id="selectTaskIdByBasicAndUser" resultType="java.lang.String">
        select
        a.ID_
        from
        blade_contract_change as c
        left join ACT_RU_TASK as a on a.PROC_INST_ID_=c.process_instance_id
        where
        1=1
        <if test="processInstanceId!=null and processInstanceId!=''">
            and c.process_instance_id=#{processInstanceId}
        </if>
        <if test="assignee!=null and assignee!=''">
            and a.ASSIGNEE_ = #{assignee}
        </if>
    </select>
    <select id="selectByProcessInstanceId" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        c.task_user
        from
        blade_contract_change as c
        left join ACT_RU_TASK as a on c.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
    </select>
    <select id="selectByProcessInstanceIdString" parameterType="java.lang.String" resultType="java.lang.String">
        select
        c.task_user
        from
        blade_contract_change as c
        left join ACT_RU_TASK as a on c.process_instance_id=a.PROC_INST_ID_
        <if test="processInstanceId!=null and processInstanceId!=''">
            where a.PROC_INST_ID_=#{processInstanceId}
        </if>
        limit 1
    </select>
    <select id="changeList" resultMap="contractListResultMap">
        SELECT DISTINCT
        c.id AS cid,
        cc.id AS id,
        cc.NAME AS contractName,
        IF
        ( cc.STATUS = 1, 10, cc.STATUS ) AS contractStatus,
        '3' AS contractType,
        c.NAME AS signedName,
        DATE_FORMAT( cc.create_time, "%Y-%m-%d %H:%i:%s" ) AS create_time,
        DATE_FORMAT( cc.update_time, "%Y-%m-%d %H:%i:%s" ) AS update_time,
        IFNULL( cc.adjusted_amount, NULL ) AS amount,
        u.NAME AS sponsor
        FROM
        blade_contract_change cc
        LEFT JOIN blade_contract c ON c.id = cc.contract_id
        LEFT JOIN ACT_HI_TASKINST t ON t.proc_inst_id_ = cc.process_instance_id
        LEFT JOIN blade_user u ON u.id = cc.create_user
        WHERE
        cc.is_deleted = 0
        AND c.is_deleted = 0
        AND cc.STATUS &lt;&gt; 6
        AND cc.contract_id = #{contractId}
        order by create_time desc
       <!-- SELECT
            id,
            NAME,
            status as status,
            create_time as createTime,
            operation_type_name as operationTypeName
        FROM
            `blade_contract_change`
        WHERE
            1=1
          AND contract_id = #{contractId}
          order by create_time desc-->
    </select>
    <select id="countChange" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            blade_contract_change as cc
            left join blade_contract as c on c.id=cc.contract_id
        where
            1=1
            and cc.is_deleted=0
            <if test="status!=null">
                and  cc.status=#{status}
            </if>
            <if test="contractId!=null and contract_id!=''">
                and cc.contract_id=#{contractId}
            </if>
            <if test="isFlag!=null and isFlag!=''">
                and cc.operation_type_id=#{isFlag}
            </if>
            <if test="contractType!=null and contractType!=''">
                and c.contract_type=#{contractType}
            </if>
            <if test="allowArchivingCondition!=null and allowArchivingCondition=='true'.toString()">
                and (cc.status=0 or (cc.operation_type_id='void' and cc.status=1))
            </if>
    </select>
</mapper>
