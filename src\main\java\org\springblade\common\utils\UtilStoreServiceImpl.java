package org.springblade.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.controller.vo.UtilStoreGetReqVO;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.Duration;
import java.util.UUID;

/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service("utilServiceImpl")
@Slf4j
public class UtilStoreServiceImpl implements UtilStoreService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public JSONObject getStore(@Valid UtilStoreGetReqVO reqVO) {
        String redisKey  = "utilStore:" + reqVO.getId() + ":" + reqVO.getPwd();
        ValueOperations<String, String> valueOps = stringRedisTemplate.opsForValue();
        String string = valueOps.get(redisKey);
        return Func.isNotBlank(string) ? JSONUtil.parseObj(string) : null;
    }

    @Override
    public Boolean validStoreWithException(@Valid UtilStoreGetReqVO reqVO) {
        System.out.println(getStore(reqVO)+reqVO.getId() + ":" + reqVO.getPwd() );
        if(Func.isEmpty(getStore(reqVO))){
            throw new RuntimeException("无效的链接或口令");
        }
        return true;
    }

    @Override
    public String generateIdAndSet(String jsonStr, String pwd, int expirationTime) {
        Duration duration = Duration.ofDays(expirationTime);
        String id = generateUniqueId();

        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
        jsonObject.set("token", TokenUtil.createAuthInfoWithTemp(duration.toSeconds()));
        jsonStr = jsonObject.toString();

        String redisKey  = "utilStore:" + id + ":" + pwd;
        // 将信息存储到Redis中
        ValueOperations<String, String> valueOps = stringRedisTemplate.opsForValue();
        valueOps.set(redisKey, jsonStr);
        // 设置过期时间
        stringRedisTemplate.expire(redisKey, duration);
        return id;
    }

    private String generateUniqueId() {
        // 使用UUID生成唯一标识符
        String string = UUID.randomUUID().toString();
        // 去掉横线-
        return string.replaceAll("-", "");
    }


}
