<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionMapper">
    <update id="updateByMarketplaceData">
        update blade_profession_market set market_name = #{param.name} where market_code = #{param.id}
    </update>
    <update id="updatePersonnelCustomerNum">
        update blade_personnel_market set num = #{num} where personnel_id in (
            <foreach collection="personnelId" separator="," item="item" index="index">
                #{item}
            </foreach>
            ) and market_id = #{marketCode} and area_id = #{areaId}
    </update>
    <update id="updateProfessionData">
        update blade_profession set actual_client = #{param.actualClient},ought_client = #{param.oughtClient} where id = #{param.id}
    </update>
    <delete id="deleteByMarketplaceData">
        delete from blade_profession_market where market_code = #{param.id}
    </delete>

    <select id="getProfessionList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.ProfessionVO">
        select
        any_value(allcust.professionId)professionId,
        any_value(allcust.professionName)professionName,
        any_value(allcust.oughtClient)oughtClient,
        any_value(allcust.actualClient)actualClient,
        any_value(pro.charge_id) chargeId,
        any_value(pro.charge_name) chargeName,
        any_value(pro.charge_dept_id) chargeDeptId,
        any_value(pro.charge_dept_name) chargeDeptName,
        any_value(pro.id) id
        from blade_profession pro
        join profession_all_customer allcust
        on allcust.professionId = pro.id
        <where>
            pro.is_deleted = 0
            <if test="query.professionName != null and query.professionName != ''">
                and pro.profession_name like concat('%',#{query.professionName},'%')
            </if>
            <if test="query.userList != null or query.deptList != null">
                and (
                <if test="query.userList != null">
                    pro.create_user in (
                    <foreach collection="query.userList" separator="," item="item" index="index">
                        #{item}
                    </foreach>
                    ) or
                </if>
                <if test="query.deptList != null">
                    pro.charge_dept_id in (
                    <foreach collection="query.deptList" separator="," item="item" index="index">
                        #{item}
                    </foreach>
                    )
                </if>
                )
            </if>
            <if test="query.id != null and query.id != ''">
                and pro.id = #{query.id}
            </if>
        </where>
        group by pro.id
        order by pro.create_time desc
    </select>
    <select id="getProfessionListByDept"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.ProfessionVO">
        select profession.id,profession.profession_name professionName,profession.charge_dept_id
        chargeDeptId,profession.charge_dept_name chargeDeptName,
        profession.charge_id chargeId,profession.charge_name chargeName from blade_profession profession
        <where>
            profession.is_deleted = 0 and charge_dept_id = #{deptId}
        </where>
        order by profession.create_time desc limit 1
    </select>
    <select id="selectByAreAndMarketForProfession" parameterType="java.util.Map"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.Profession">
        SELECT
        p.profession_name, -- 市场名称
        p.id,
        p.charge_id,
        p.charge_name,
        p.charge_dept_id,
        p.charge_dept_name
        FROM
        blade_profession AS p -- 区域市场
        LEFT JOIN blade_join_profession AS j ON j.profession_id = p.id -- 中间表
        LEFT JOIN blade_profession_area AS a ON a.join_id = j.id -- 区域表
        LEFT JOIN blade_profession_market AS m ON m.join_id = j.id -- 行业表

        WHERE
        p.is_deleted = 0
        AND j.is_deleted = 0
        AND a.is_deleted = 0
        AND m.is_deleted = 0
        <!--          <if test="province!=null and province!=''">-->
        AND ( a.province_code = #{province} -- 省份
        AND a.city_code = #{urbanArea} -- 市
        AND a.counties_code = #{city} ) -- 县
        <!--          </if>-->
        <if test="industryInvolved!=null and industryInvolved!=''">
           and m.market_code in (
            <foreach collection="industryInvolved" separator="," item="item" index="index">
                #{item}
            </foreach>
            )-- 行业编码
        </if>
    </select>
    <select id="getAreaAndMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select DISTINCT area.counties_code areaCode,market.market_code marketCode
        from blade_profession pro
        join blade_join_profession jo on jo.profession_id = pro.id
        join blade_profession_area area on area.join_id = jo.id
        join blade_profession_market market on market.join_id = jo.id
        where pro.id = #{id}
        <if test="marketId != null">
            and market.market_code = #{marketId}
        </if>
        <if test="areaCode != null">
            and area.counties_code = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and area.counties_code is not null
    </select>

    <select id="getAreaAndMarketListMore"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select DISTINCT area.counties_code areaCode,market.market_code marketCode
        from blade_profession pro
        join blade_join_profession jo on jo.profession_id = pro.id
        join blade_profession_area area on area.join_id = jo.id
        join blade_profession_market market on market.join_id = jo.id
        where pro.id = #{id}
        <if test="marketId != null and marketId.size() > 0">
            and market.market_code in (
            <foreach collection="marketId" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaCode != null and areaCode.size() > 0">
            and area.counties_code in (
            <foreach collection="areaCode" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and area.counties_code is not null
    </select>
    <select id="getActualClientCount" resultType="java.lang.Integer">
        select count(DISTINCT customer.id) from blade_customer customer
        join blade_customer_industry_involved industry
        ON industry.customer_id = customer.id
        <if test="userId != null">
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        </if>
        where customer.is_deleted = 0 and customer.forbid_status = 'A'
        <if test="list != null">
            and (
            <foreach collection="list" separator="or" item="item" index="index">
                (customer.city = #{item.areaCode} and industry.industry_id = #{item.marketCode})
            </foreach>
            )
        </if>
        <if test="userId != null">
            and charge.user_id = #{userId}
        </if>
    </select>
    <select id="getActualClientList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.ActualResultVO">
        select DISTINCT actualCode,actualName,areaName,areaCode,marketCode,marketName,
        create_time from (
        select cust.id actualCode,cust.customer_name actualName,area.name areaName,area.code areaCode,
        market.id marketCode,market.name marketName,
        cust.create_time from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_marketplace market on market.id = industry.industry_id
        join blade_area area on cust.city = area.code
        <if test="userId != null">
        join blade_customer_charge_user charge
        on cust.id = charge.customer_id
        </if>
        where cust.is_deleted = 0 and cust.forbid_status = 'A'
        and cust.city = #{area.areaCode} and industry.industry_id = #{area.marketCode}
        <if test="userId != null">
            and charge.user_id = #{userId}
        </if>
        order by cust.create_time desc) t ORDER BY
        t.create_time DESC
    </select>
    <select id="getAreaOughtList" resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select
        <if test="query.type == 1">
            any_value(area.counties_code) areaCode,any_value(area.counties_name) areaName,
        </if>
        <if test="query.type == 2">
            any_value(area.city_code) areaCode,any_value(area.city_name) areaName,
        </if>
        sum(any_value(market.num)) num,group_concat(any_value(market.market_code)) marketStr
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        join blade_marketplace mp
        on mp.id = market.market_code
        where pro.is_deleted = 0
        <if test="query.code != null and query.code != ''">
            and area.city_code = #{query.code}
        </if>
        <if test="query.id != null and query.id != ''">
            and pro.id = #{query.id}
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and market.market_code not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.type == 1">
            group by area.counties_code
        </if>
        <if test="query.type == 2">
            group by area.city_code
        </if>
    </select>
    <select id="getAreaActualList"
            resultType="java.lang.Integer">
        select count(distinct cust.id)
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_area area on
        <if test="query.type == 1">
            area.code = cust.city
        </if>
        <if test="query.type == 2">
            area.code = cust.urban_area
        </if>
        <if test="query.userId != null or query.userList != null">
            join blade_customer_charge_user charge
            on charge.customer_id = cust.id and charge.is_deleted = 0
        </if>
        <if test="query.id != null">
            join blade_personnel per
            on per.user_id = charge.user_id and per.is_deleted = 0
            join blade_profession pro
            on pro.id = per.profession_id and pro.is_deleted = 0
        </if>
        where cust.is_deleted = 0 and cust.forbid_status='A'
        <if test="query.type == 1">
            and cust.city = #{query.code}
        </if>
        <if test="query.type == 2">
            and cust.urban_area = #{query.code}
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and industry.industry_id in (
        <foreach collection="query.list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.id != null">
            and pro.id = #{query.id}
        </if>
        <if test="query.userId != null">
            and charge.user_id = #{query.userId}
        </if>
        <if test="query.userList != null">
            and charge.user_id in (
                <foreach collection="query.userList" index="index" item="item" separator=",">
                    #{item}
                </foreach>
            )
        </if>
    </select>

    <select id="getProvince" resultType="java.util.Map">
        select distinct code, name
        from (
                 select province_code code, any_value(province_name) name
                 from blade_profession pro
                          join blade_join_profession jo
                               on jo.profession_id = pro.id
                          join blade_profession_area area
                               on area.join_id = jo.id
                 where pro.id = #{id}
                 group by province_code
                 union all
                 select area.code code, area.name name
                 from blade_personnel per
                          join blade_customer_charge_user charge
                               on charge.user_id = per.user_id and charge.is_deleted = 0
                          join blade_customer cust
                               on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                          join blade_area area
                               on area.code = cust.province
                 where per.profession_id = #{id}) t
    </select>
    <select id="getCity" resultType="java.util.Map">
        select distinct code, name
        from (
                 select city_code code, any_value(city_name) name
                 from blade_profession pro
                          join blade_join_profession jo
                               on jo.profession_id = pro.id
                          join blade_profession_area area
                               on area.join_id = jo.id
                 where pro.id = #{id}
                   and city_code like concat(#{code}, '%')
                 group by city_code
                 union all
                 select area.code code, area.name name
                 from blade_personnel per
                          join blade_customer_charge_user charge
                               on charge.user_id = per.user_id and charge.is_deleted = 0
                          join blade_customer cust
                               on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                          join blade_area area
                               on area.code = cust.urban_area
                 where per.profession_id = #{id}
                   and cust.province = #{code}) t
    </select>
    <select id="getCounties" resultType="java.util.Map">
        select distinct code, name
        from (
                 select counties_code code, any_value(counties_name) name
                 from blade_profession pro
                          join blade_join_profession jo
                               on jo.profession_id = pro.id
                          join blade_profession_area area
                               on area.join_id = jo.id
                 where pro.id = #{id}
                   and counties_code like concat(#{code}, '%')
                 group by counties_code
                 union all
                 select area.code code, area.name name
                 from blade_personnel per
                          join blade_customer_charge_user charge
                               on charge.user_id = per.user_id and charge.is_deleted = 0
                          join blade_customer cust
                               on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                          join blade_area area
                               on area.code = cust.city
                 where per.profession_id = #{id}
                   and cust.urban_area = #{code}) t
    </select>
    <select id="getProfessionListByArea" resultType="java.util.Map">
        select distinct marketCode, marketName
        from (
                 select market_code marketCode, any_value(market_name) marketName
                 from blade_profession pro
                          join blade_join_profession jo
                               on jo.profession_id = pro.id
                          join blade_profession_market market
                               on market.join_id = jo.id
                 where pro.id = #{id}
                 group by market_code
                 union all
                 select market.id marketCode, market.name marketName
                 from blade_personnel per
                          join blade_customer_charge_user charge
                               on per.user_id = charge.user_id and charge.is_deleted = 0
                          join blade_customer cust
                               on charge.customer_id = cust.id and
                                  cust.is_deleted = 0 and cust.forbid_status = 'A'
                          join blade_marketplace market
                               on market.id = cust.industry_involved and
                                  market.status = 0 and market.is_deleted = 0
                 where per.profession_id = #{id}
             ) t
    </select>
    <select id="getProfessionPersonnelList" resultType="java.util.Map">
        select distinct market.id marketCode, market.name marketName
        from blade_personnel per
                 join blade_customer_charge_user charge
                      on per.user_id = charge.user_id and charge.is_deleted = 0
                 join blade_customer cust
                      on charge.customer_id = cust.id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                 join blade_marketplace market
                      on market.id = cust.industry_involved and market.status = 0 and market.is_deleted = 0
        where per.profession_id = #{id}
    </select>
    <select id="getDeptByUserId" resultType="java.lang.Long">
        select id
        from blade_user
        where FIND_IN_SET(#{deptId}, dept_id) > 0
          and is_deleted = 0
    </select>
    <select id="getAreaAndMarketListStr"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select DISTINCT areaCode,marketCode from (
        select area.counties_code areaCode,market.market_code marketCode
        from blade_profession pro
        join blade_join_profession jo on jo.profession_id = pro.id
        join blade_profession_area area on area.join_id = jo.id
        join blade_profession_market market on market.join_id = jo.id
        where pro.id = #{id}
        <if test="marketId != null">
            and market.market_code = #{marketId}
        </if>
        <if test="areaCode != null">
            and area.counties_code = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and area.counties_code is not null
        union all
        select cust.city areaCode,cust.industry_involved marketCode from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_personnel per on per.user_id = charge.user_id and per.is_deleted = 0
        where per.profession_id = #{id}
        <if test="marketId != null">
            and cust.industry_involved = #{marketId}
        </if>
        <if test="areaCode != null">
            and cust.city = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and cust.city not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ) t
    </select>
    <select id="getActualClientListByUserList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.ActualResultVO">
        select DISTINCT actualCode,actualName,areaName,areaCode,marketCode,marketName,
        create_time from (
        select cust.id actualCode,cust.customer_name actualName,area.name areaName,area.code areaCode,
        market.id marketCode,market.name marketName,
        cust.create_time from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_marketplace market on market.id = industry.industry_id
        join blade_area area on cust.city = area.code
        join blade_customer_charge_user charge
        on cust.id = charge.customer_id
        and cust.forbid_status = 'A'
        where cust.is_deleted = 0
        and cust.city = #{area.areaCode} and industry.industry_id = #{area.marketCode}
        <if test="userList != null and userList.size() > 0">
            and charge.user_id in (
            <foreach collection="userList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        order by cust.create_time desc) t ORDER BY
        t.create_time DESC
    </select>
    <select id="getProfessionUserIdList" resultType="java.lang.String">
        select DISTINCT per.user_id
        from blade_profession pro
                 join blade_personnel per on per.profession_id = pro.id and per.is_deleted = 0
        where pro.id = #{id}
    </select>
    <select id="getAreaActualListAndUserCustomer" resultType="java.lang.Integer">
        SELECT
        count(distinct cust.id)
        FROM
        blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        JOIN blade_area area ON area.CODE = cust.city
        left join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        WHERE
        cust.is_deleted = 0
        AND cust.forbid_status = 'A'
        <if test="query.type == 1">
            and cust.city = #{query.code}
        </if>
        <if test="query.type == 2">
            and cust.urban_area = #{query.code}
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            AND industry.industry_id NOT IN (
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        AND (industry.industry_id IN (
        <foreach collection="query.list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="query.userList != null and query.userList.size() > 0">
            or charge.user_id in (
            <foreach collection="query.userList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        )
    </select>
    <select id="getIndustryInformationAreaAndMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select market.market_code marketCode,GROUP_CONCAT(area.counties_code) areaStr,count(area.counties_code) count
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.is_deleted = 0 and pro.id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market.market_code not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and area.counties_code is not null
        group by market.market_code
    </select>
    <select id="getIndustryInformationAreaAndMarketListByUser"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select distinct cust.city areaCode, industry.industry_id marketCode,
        per.user_id userId
        from blade_personnel per
        join blade_customer_charge_user charge
        on per.user_id = charge.user_id and charge.is_deleted = 0
        join blade_customer cust
        on cust.id = charge.customer_id and cust.is_deleted = 0
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        where per.is_deleted = 0
        and per.profession_id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.customer_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaList != null and areaList.size() > 0">
            AND NOT EXISTS (
            SELECT 1 FROM blade_customer sub_cust
            WHERE sub_cust.id = cust.id
            and (
            <foreach collection="areaList" index="index" item="item" separator="or">
                (industry.industry_id = #{item.marketCode} and cust.city in (
                <foreach collection="item.areaList" separator="," item="it" index="in">
                    #{it}
                </foreach>
                ) )
            </foreach>
            )
            )
        </if>
    </select>
    <select id="getIndustryInformationMarketListY"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerizationNew">
        select any_value(market.market_code) id,any_value(market.market_name) name, sum(market.num) oughtCount
        from blade_profession_market market
        join blade_join_profession jo
             on jo.id = market.join_id and jo.is_deleted = 0
        join blade_profession pro
             on pro.id = jo.profession_id and pro.is_deleted = 0
        join blade_profession_area area
             on area.join_id = jo.id and area.is_deleted = 0
        where pro.id = #{professionId} and market.market_code = #{query.marketCode}
    </select>
    <select id="getIndustryInformationMarketListS"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerization">
        select count(cust.id) actualCount
        from blade_customer cust
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        where cust.is_deleted = 0
        and cust.forbid_status = 'A'
        and industry.industry_id = #{marketCode}
        and cust.city in (
        <foreach collection="areaList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="getIndustryInformationPersonnelListS"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerizationNew">
        -- select count( cust.id ) AS actualCount,0 AS oughtCount,any_value(cust.id) AS id,any_value(market.name) AS name,GROUP_CONCAT(cust.id) customerId
        select count( DISTINCT cust.id ) AS actualCount,0 AS oughtCount,any_value(market.id) AS id,any_value(market.name) AS name,GROUP_CONCAT(cust.id) customerId
        from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_marketplace market
        on market.id = industry.industry_id
        where cust.is_deleted = 0
          and cust.forbid_status = 'A'
          and charge.is_deleted = 0
          and industry.industry_id = #{marketCode}
          and cust.city = #{areaCode}
          and charge.user_id = #{userId}
        group by industry.industry_id
    </select>
    <select id="getActualClientCountByUserGroup" resultType="java.lang.Integer">
        select count(DISTINCT customer.id) from blade_customer customer
        join blade_customer_industry_involved industry
        on industry.customer_id = customer.id and industry.is_deleted = 0
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        and customer.forbid_status = 'A'
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        where customer.is_deleted = 0
        <if test="list != null">
            and (
            <foreach collection="list" separator="or" item="item" index="index">
                (customer.city = #{item.areaCode} and industry.industry_id = #{item.marketCode})
            </foreach>
            )
        </if>
        <if test="professionId != null">
            and per.profession_id = #{professionId}
        </if>
    </select>
    <select id="getMarketIdByProfessionId" resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceVO">
        select mp.id id , mp.name name,market.num num,mp.status,mp.sort,mp.create_name createName,1 checked from blade_profession_market market
        join blade_join_profession jo
             on jo.id = market.join_id and jo.is_deleted = 0
        join blade_profession pro
             on pro.id = jo.profession_id and pro.is_deleted = 0 and pro.status = 0
        join blade_marketplace mp
             on mp.id = market.market_code and mp.status = 0 and mp.is_deleted = 0
        where market.is_deleted = 0 and pro.id = #{professionId} and jo.id = #{joinId}
    </select>
    <select id="getProfessionIdByPersonnelList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.Personnel">
        select * from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        where per.profession_id = #{professionId} and market_id = #{marketCode} and per.is_deleted = 0
    </select>
    <select id="getMarketByPersonnelCount" resultType="java.lang.String">
        select DISTINCT per.user_name from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        where per.is_deleted = 0 and (
            <foreach collection="marketId" index="index" item="item" separator="or">
               (market.market_id = #{item.marketCode} and market.area_id = #{item.areaCode})
            </foreach>
            )
        and per.profession_id = #{professionId}
    </select>
    <select id="getActualClientCountList"
            resultType="java.lang.String">
        select DISTINCT customer.id from blade_customer customer
        join blade_customer_industry_involved industry
        ON industry.customer_id = customer.id
        <if test="userId != null">
            join blade_customer_charge_user charge
            on customer.id = charge.customer_id
        </if>
        where customer.is_deleted = 0 and customer.forbid_status = 'A'
        <if test="list != null">
            and (
            <foreach collection="list" separator="or" item="item" index="index">
                (customer.city = #{item.areaCode} and industry.industry_id = #{item.marketCode})
            </foreach>
            )
        </if>
        <if test="userId != null">
            and charge.user_id = #{userId}
        </if>
    </select>
    <select id="getActualClientCountByUserGroupList" resultType="java.lang.String">
        select DISTINCT customer.id from blade_customer customer
        join blade_customer_industry_involved industry
        on industry.customer_id = customer.id and industry.is_deleted = 0
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        and customer.forbid_status = 'A'
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        where customer.is_deleted = 0
        <if test="list != null">
            and (
            <foreach collection="list" separator="or" item="item" index="index">
                (customer.city = #{item.areaCode} and industry.industry_id = #{item.marketCode})
            </foreach>
            )
        </if>
        <if test="professionId != null">
            and per.profession_id = #{professionId}
        </if>
    </select>
    <select id="checkMarketAndArea" resultType="java.lang.String">
        select per.user_name
        from blade_personnel per
                 join blade_personnel_market market
                      on market.personnel_id = per.id and market.is_deleted = 0
        where per.profession_id = #{professionId}
        <if test="null != list and list.size() > 0">
          and (
            <foreach collection="list" index="index" item="item" separator="or">
                (market.area_id = #{item.areaCode} and market.market_id = #{item.marketCode})
            </foreach>
            )
        </if>
          and per.is_Deleted = 0
    </select>
    <select id="getProfessionListAll"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.ProfessionVO">
        select profession.id,profession.profession_name professionName,profession.charge_dept_id
        chargeDeptId,profession.charge_dept_name chargeDeptName,
        profession.charge_id chargeId,profession.charge_name chargeName
        from blade_profession profession
        <where>
            profession.is_deleted = 0
        </where>
    </select>
    <select id="getAreaOughtListByMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select
        area.counties_code areaCode,
        market.market_code marketStr
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        join blade_marketplace mp
        on mp.id = market.market_code
        where pro.is_deleted = 0
        <if test="query.code != null and query.code != ''">
            and area.city_code = #{query.code}
        </if>
        <if test="query.id != null and query.id != ''">
            and pro.id = #{query.id}
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and market.market_code not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getProfessionAreaList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaResultListVO">
        select professionId,areaCode,areaName,oughtClient oughtCount,actualClient actualCount from customer_all_profession_area_group where professionId = #{professionId}
    </select>
    <select id="getProfessionAreaCityList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaResultListVO">
        select professionId,areaCode,areaName,oughtClient oughtCount,actualClient actualCount from customer_all_profession_area_city_group where professionId = #{professionId} and cityCode = #{cityCode}
    </select>

    <select id="getProfessionMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerizationNew">
        select any_value(marketCode) id, any_value(marketName) name, any_value(oughtClient) oughtCount, count(allpro.customerId) actualCount
        from (
        select any_value(marketCode)marketCode, any_value(marketName)marketName, any_value(oughtClient)oughtClient, max(type)
        from (
        select any_value(market.market_code) marketCode,
        any_value(market.market_name) marketName,
        sum(market.num) oughtClient,
        1 type
        from blade_profession pro
        join blade_join_profession jo
        on jo.profession_id = pro.id and jo.is_deleted = 0
        join blade_profession_area area
        on area.join_id = jo.id and area.is_deleted = 0
        join blade_profession_market market
        on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}
        group by market.market_code
        <if test="userList != null and userList.size() > 0">
            union all
            select any_value(market.id) marketCode, any_value(market.name) marketName, 0 oughtClient, 0 type
            from customer_all_data alldata
            join blade_marketplace market
            on market.id = alldata.marketId and market.is_deleted = 0 and market.status = 0
            where userId in (
            <foreach collection="userList" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
            group by market.id
        </if>
        ) t
        group by marketCode) t1
        left join customer_all_profession allpro
        on allpro.marketId = t1.marketCode and allpro.professionId = #{professionId}
        group by t1.marketCode
        order by marketCode
    </select>
    <select id="getAProfessionlCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceResultVO">
        select cust.id clientCode,cust.customer_name clientName,cust.city areaCode,cust.city_name
                       areaName,market.id marketCode,market.name marketName,concat(cust.city,market.id) areaAddMarket,'true' ywData
        from customer_all_profession allpro
                 join blade_customer cust
                      on cust.id = allpro.customerId and cust.forbid_status = 'A' and cust.is_deleted = 0
                 join blade_marketplace market
                      on market.id = allpro.marketId
        where allpro.professionId = #{professionId}
        <if test="areaCode != null and areaCode.size() > 0">
            and allpro.city in (
            <foreach collection="areaCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="marketCode != null and marketCode.size() > 0">
            and market.id in(
            <foreach collection="marketCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getOProfessionlCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceResultVO">
        select null clientCode,null clientName,area.counties_code areaCode,area.counties_name areaName,market.market_code
                    marketCode,market.market_name marketName,concat(area.counties_code,market.market_code) areaAddMarket,'false' ywData
        from blade_profession pro
                 join blade_join_profession jo
                      on jo.profession_id = pro.id and jo.is_deleted = 0
                 join blade_profession_area area
                      on area.join_id = jo.id and area.is_deleted = 0
                 join blade_profession_market market
                      on market.join_id = jo.id and market.is_deleted = 0
        where pro.id = #{professionId}
        <if test="areaCode != null and areaCode.size() > 0">
            and area.counties_code in (
            <foreach collection="areaCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="marketCode != null and marketCode.size() > 0">
            and market.market_code in(
            <foreach collection="marketCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
