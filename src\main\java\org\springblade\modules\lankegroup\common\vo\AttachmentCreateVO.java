/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * common_attachment表创建视图对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "AttachmentCreateVO对象", description = "common_attachment表创建视图对象")
@EqualsAndHashCode(callSuper = true)
public class AttachmentCreateVO extends AttachmentBaseVO {

	private static final long serialVersionUID = 1L;

	// 创建对象继承BaseVO的所有字段和校验规则
	// 可以在这里添加创建时特有的字段或重写某些字段的校验规则

} 