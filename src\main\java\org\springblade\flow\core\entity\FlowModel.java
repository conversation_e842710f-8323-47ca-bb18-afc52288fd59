/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程模型
 *
 * <AUTHOR>
 */
@Data
@TableName("ACT_DE_MODEL")
public class FlowModel implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final int MODEL_TYPE_BPMN = 0;
	public static final int MODEL_TYPE_FORM = 2;
	public static final int MODEL_TYPE_APP = 3;
	public static final int MODEL_TYPE_DECISION_TABLE = 4;
	public static final int MODEL_TYPE_CMMN = 5;

	private String id;
	private String name;
	private String modelKey;
	private String description;
	private Date created;
	private Date lastUpdated;
	private String createdBy;
	private String lastUpdatedBy;
	private Integer version;
	private String modelEditorJson;
	private String modelComment;
	private Integer modelType;
	private String tenantId;
	private byte[] thumbnail;
	private String modelEditorXml;

	/**
	 * 表单key
	 */
	private String formKey;

	/**
	 * 部署Id
	 */
	private String processDefinitionId;

	/**
	 * 流程设计内容
	 */
	@TableField(typeHandler = JacksonTypeHandler.class,value = "design_scheme")
	private Object designScheme;

	/**
	 * 流程分类
	 */
	private Long category;
	/**
	 * 流程分类-子类
	 */
	private Long subclass;

	/**
	 * 状态(0启用；1禁用)
	 */
	private Integer status;
	/**
	 * 发布状态(0未发布；1已发布)
	 */
	private Integer publishStatus;
	/**
	 * 数据来源(0.平台创建；1.数字化创建)
	 */
	private Integer dataSource;
}
