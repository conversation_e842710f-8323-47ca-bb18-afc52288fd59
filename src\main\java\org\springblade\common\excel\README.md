# 通用Excel导入功能说明

## 概述

本模块提供了一套通用的Excel导入解决方案，旨在减少重复代码，提高代码的可维护性和复用性。通过使用泛型和BeanUtil.copyProperties，可以轻松处理不同类型的Excel导入需求。

## 核心组件

### 1. GenericExcelImportListener

通用Excel导入监听器，使用泛型支持不同的Excel模型和实体类型。

**特性：**
- 使用BeanUtil.copyProperties进行实体转换
- 支持自定义校验逻辑
- 支持自定义转换逻辑
- 支持自定义保存逻辑
- 自动处理异常和错误信息

### 2. ExcelImportUtil（通用导入方法）

通用Excel导入工具类，封装了导入的核心逻辑。

**特性：**
- 提供多种重载方法适应不同需求
- 支持自定义校验、转换、保存函数
- 统一的错误处理机制

### 3. ExcelImportHelper

提供构建器模式的API，让Controller中的导入代码更加简洁易读。

**特性：**
- 链式调用API
- 可选的自定义配置
- 简化Controller代码

## 使用示例

### 基础用法（推荐）

```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
        .errorFileName("导入错误数据")
        .importExcel(file);
}
```

### 带自定义校验

```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
        .errorFileName("导入错误数据")
        .validator(data -> {
            List<String> errors = new ArrayList<>();
            // 自定义校验逻辑
            if (StringUtils.isBlank(data.getName())) {
                errors.add("名称不能为空");
            }
            return errors;
        })
        .importExcel(file);
}
```

### 带自定义转换

```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
        .errorFileName("导入错误数据")
        .converter(data -> {
            Entity entity = new Entity();
            BeanUtil.copyProperties(data, entity);
            // 自定义转换逻辑
            entity.setCreateTime(new Date());
            entity.setCreateUser(AuthUtil.getUserId());
            return entity;
        })
        .importExcel(file);
}
```

### 带自定义保存

```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
        .errorFileName("导入错误数据")
        .saver(entity -> {
            // 自定义保存逻辑
            return customService.saveWithValidation(entity);
        })
        .importExcel(file);
}
```

## API层次结构

### 1. ExcelImportHelper（最推荐）
构建器模式API，代码最简洁：
```java
ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
    .errorFileName("导入错误数据")
    .validator(customValidator)  // 可选
    .converter(customConverter)  // 可选
    .saver(customSaver)         // 可选
    .importExcel(file);
```

### 2. ExcelImportUtil.importExcelWithService（通用方法）
直接调用通用导入方法：
```java
ExcelImportUtil.importExcelWithService(
    file, ExcelModel.class, Entity.class, service, "导入错误数据");
```

### 3. ExcelImportUtil.importExcelWithGeneric（推荐底层方法）
使用通用监听器：
```java
ExcelImportUtil.importExcelWithGeneric(file, ExcelModel.class, 
    () -> new GenericExcelImportListener<>(service, entityClass, ...));
```

### 4. ExcelImportUtil.importExcel（兼容旧版本）
使用自定义监听器：
```java
ExcelImportUtil.importExcel(file, ExcelModel.class, 
    () -> new CustomImportListener(service, null));
```

## 迁移指南

### 从旧的ImportListener迁移

**旧代码：**
```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportUtil.importExcel(file, ExcelModel.class, 
        () -> new CustomImportListener(service, null));
}
```

**新代码（推荐）：**
```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportHelper.builder(ExcelModel.class, Entity.class, service)
        .errorFileName("导入错误数据")
        .importExcel(file);
}
```

**新代码（直接调用）：**
```java
@PostMapping("/importExcel")
public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file) {
    return ExcelImportUtil.importExcelWithService(
        file, ExcelModel.class, Entity.class, service, "导入错误数据");
}
```

## 优势

1. **减少重复代码**：不再需要为每个实体创建单独的ImportListener
2. **提高可维护性**：统一的导入逻辑，便于维护和升级
3. **增强灵活性**：支持自定义校验、转换、保存逻辑
4. **简化开发**：构建器模式API，代码更简洁易读
5. **统一错误处理**：标准化的异常处理和错误信息格式
6. **分层设计**：提供多层次API，满足不同复杂度的需求
7. **向后兼容**：保留旧版本API，平滑迁移

## 架构总结

经过重构，Excel导入功能现在具有清晰的分层架构：

- **ExcelImportHelper**：最高层API，提供构建器模式，推荐日常使用
- **ExcelImportUtil.importExcelWithService**：中间层API，直接调用通用方法
- **ExcelImportUtil.importExcelWithGeneric**：底层API，使用通用监听器
- **ExcelImportUtil.importExcel**：兼容层API，支持自定义监听器

所有方法最终都使用统一的GenericExcelImportListener来处理导入逻辑，确保了代码的一致性和可维护性。

## 注意事项

1. Excel模型类需要使用@ExcelProperty注解标注字段
2. 实体类需要继承BaseEntity
3. Service需要实现BaseService接口
4. 自定义校验函数返回的错误列表会自动添加到导入结果中
5. 如果不提供自定义转换函数，会使用BeanUtil.copyProperties进行默认转换
6. 推荐使用ExcelImportHelper.builder()来创建导入任务，API最简洁
7. 对于复杂场景，可以直接使用ExcelImportUtil的相应方法