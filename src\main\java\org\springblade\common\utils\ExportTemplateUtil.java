package org.springblade.common.utils;

import lombok.SneakyThrows;
import org.apache.commons.codec.Charsets;
import org.springblade.core.log.exception.ServiceException;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

public class ExportTemplateUtil {

    @SneakyThrows
    public static void export(HttpServletResponse response,String file,String fileName) {
        ClassPathResource classPathResource = new ClassPathResource("exceltemplate\\"+file);
        String outFileName = fileName;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(Charsets.UTF_8.name());
        outFileName = URLEncoder.encode(outFileName, Charsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=" + outFileName + ".xlsx");
        try (InputStream is = classPathResource.getInputStream(); OutputStream os = response.getOutputStream()) {
            byte[] b = new byte[1024];
            int length;
            while ((length = is.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("模板下载失败！");
        }
    }

}
