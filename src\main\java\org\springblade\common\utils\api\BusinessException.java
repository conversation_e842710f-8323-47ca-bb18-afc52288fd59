package org.springblade.common.utils.api;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 14:47
 * 业务异常
 */
public class BusinessException extends BaseException {

    public BusinessException(BaseException exception) {
        this(exception.getCode(), exception.getMessage());
    }

    public BusinessException(int code, String message) {
        super(code, message);
    }

    /**
     * 实例化异常
     * @param ex
     * @return
     */
    public static BusinessException fail(ErrorCode ex) {
        return new BusinessException(ex.getCode(), ex.getMessage());
    }

    @Override
    public String toString() {
        return "AException [message=" + message + ", code=" + code + "]";
    }
}
