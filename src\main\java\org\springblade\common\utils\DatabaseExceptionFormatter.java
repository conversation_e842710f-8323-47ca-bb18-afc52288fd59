package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据库异常格式化工具类
 * 将数据库底层异常转换为用户友好的中文提示信息
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Component
public class DatabaseExceptionFormatter {
    
    @Autowired
    private DatabaseMetadataUtil metadataUtil;
    
    // 唯一索引冲突正则表达式
    private static final Pattern DUPLICATE_ENTRY_PATTERN = Pattern.compile(
        "Duplicate entry '(.+?)' for key '(.+?)\\.(.+?)'", Pattern.CASE_INSENSITIVE);
    
    // 外键约束违反正则表达式
    private static final Pattern FOREIGN_KEY_PATTERN = Pattern.compile(
        "Cannot delete or update a parent row: a foreign key constraint fails.*?REFERENCES `(.+?)`", Pattern.CASE_INSENSITIVE);
    
    // 非空约束违反正则表达式
    private static final Pattern NOT_NULL_PATTERN = Pattern.compile(
        "Column '(.+?)' cannot be null", Pattern.CASE_INSENSITIVE);
    
    // 数据长度超限正则表达式
    private static final Pattern DATA_TOO_LONG_PATTERN = Pattern.compile(
        "Data too long for column '(.+?)'", Pattern.CASE_INSENSITIVE);
    
    // 字段没有默认值正则表达式
    private static final Pattern NO_DEFAULT_VALUE_PATTERN = Pattern.compile(
        "Field '(.+?)' doesn't have a default value", Pattern.CASE_INSENSITIVE);
    
    // 数据类型不匹配正则表达式
    private static final Pattern DATA_TYPE_MISMATCH_PATTERN = Pattern.compile(
        "Incorrect column specifier for column '(.+?)'", Pattern.CASE_INSENSITIVE);
    
    // 外键约束失败正则表达式（更通用的版本）
    private static final Pattern FOREIGN_KEY_GENERAL_PATTERN = Pattern.compile(
        "Cannot add or update a child row: a foreign key constraint fails.*?REFERENCES `(.+?)`", Pattern.CASE_INSENSITIVE);
    
    /**
     * 格式化数据库异常为用户友好的消息
     * 
     * @param exception 数据库异常
     * @return 格式化后的错误消息，包含中文字段描述和具体值
     */
    public String formatDatabaseException(Exception exception) {
        try {
            // 获取异常的根本原因
            Throwable rootCause = ExceptionUtil.getRootCause(exception);
            String errorMessage = rootCause.getMessage();
            
            if (errorMessage == null) {
                return "数据库操作失败，请稍后重试";
            }
            
            log.debug("开始格式化数据库异常: {}", errorMessage);
            
            // 处理唯一索引冲突
            if (rootCause instanceof DuplicateKeyException || 
                rootCause instanceof SQLIntegrityConstraintViolationException ||
                errorMessage.contains("Duplicate entry")) {
                return formatDuplicateKeyException(errorMessage);
            }
            
            // 处理外键约束违反
            if (errorMessage.contains("foreign key constraint fails")) {
                return formatForeignKeyException(errorMessage);
            }
            
            // 处理非空约束违反
            if (errorMessage.contains("cannot be null")) {
                return formatNotNullException(errorMessage, exception.getMessage());
            }
            
            // 处理数据长度超限
            if (errorMessage.contains("Data too long")) {
                return formatDataTooLongException(errorMessage, exception.getMessage());
            }
            
            // 处理字段没有默认值
            if (errorMessage.contains("doesn't have a default value")) {
                return formatNoDefaultValueException(errorMessage, exception.getMessage());
            }
            
            // 处理数据类型不匹配
            if (errorMessage.contains("Incorrect column specifier")) {
                return formatDataTypeMismatchException(errorMessage, exception.getMessage());
            }
            
            // 处理外键约束失败（更通用的版本）
            if (errorMessage.contains("Cannot add or update a child row") && errorMessage.contains("foreign key constraint fails")) {
                return formatForeignKeyGeneralException(errorMessage);
            }
            
            // 其他SQL异常的通用处理
            if (exception instanceof SQLException || exception instanceof DataAccessException) {
                return formatGenericSQLException(errorMessage);
            }
            
            return "数据库操作失败：" + errorMessage;
            
        } catch (Exception e) {
            log.warn("格式化数据库异常时发生错误", e);
            return "数据库操作失败，请稍后重试";
        }
    }
    
    /**
     * 格式化唯一索引冲突异常
     */
    private String formatDuplicateKeyException(String errorMessage) {
        Matcher matcher = DUPLICATE_ENTRY_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String duplicateValue = matcher.group(1);
            String tableName = matcher.group(2);
            String indexName = matcher.group(3);
            
            log.debug("解析唯一索引冲突: 表={}, 索引={}, 值={}", tableName, indexName, duplicateValue);
            
            // 获取表的中文名称
            String tableDisplayName = getTableDisplayName(tableName);
            
            // 获取索引涉及字段的中文描述
            String fieldDescription = getIndexFieldDescription(tableName, indexName);
            
            // 构建友好的错误消息
            String message = String.format("数据保存失败：该%s【%s】已存在，请勿重复创建",
                fieldDescription, duplicateValue);
                
            log.debug("唯一索引冲突格式化完成: {} -> {}", errorMessage.substring(0, Math.min(100, errorMessage.length())), message);
            return message;
        }
        
        // 如果正则匹配失败，返回通用消息
        return "数据保存失败：该记录已存在，请检查唯一字段的值";
    }
    
    /**
     * 格式化外键约束违反异常
     */
    private String formatForeignKeyException(String errorMessage) {
        Matcher matcher = FOREIGN_KEY_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String referencedTable = matcher.group(1);
            String referencedTableName = getTableDisplayName(referencedTable);
            return String.format("删除失败：存在关联的%s数据，请先删除相关数据", referencedTableName);
        }
        
        return "删除失败：存在关联数据，请先删除相关数据";
    }
    
    /**
     * 格式化非空约束违反异常
     */
    private String formatNotNullException(String errorMessage, String fullErrorMessage) {
        Matcher matcher = NOT_NULL_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String fieldName = matcher.group(1);
            
            // 从完整错误消息中提取表名
            String tableName = extractTableNameFromErrorMessage(fullErrorMessage);
            String fieldDescription = getFieldDisplayName(tableName, fieldName);
            
            return String.format("数据保存失败：【%s】字段不能为空", fieldDescription);
        }
        
        return "数据保存失败：必填字段不能为空";
    }
    
    /**
     * 格式化数据长度超限异常
     */
    private String formatDataTooLongException(String errorMessage, String fullErrorMessage) {
        Matcher matcher = DATA_TOO_LONG_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String fieldName = matcher.group(1);
            
            // 从完整错误消息中提取表名
            String tableName = extractTableNameFromErrorMessage(fullErrorMessage);
            String fieldDescription = getFieldDisplayName(tableName, fieldName);
            
            return String.format("数据保存失败：【%s】字段数据长度超出限制", fieldDescription);
        }
        
        return "数据保存失败：字段数据长度超出限制";
    }
    
    /**
     * 格式化字段没有默认值异常
     */
    private String formatNoDefaultValueException(String errorMessage, String fullErrorMessage) {
        Matcher matcher = NO_DEFAULT_VALUE_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String fieldName = matcher.group(1);
            
            // 从完整错误消息中提取表名（包含SQL语句信息）
            String tableName = extractTableNameFromErrorMessage(fullErrorMessage);
            String fieldDescription = getFieldDisplayName(tableName, fieldName);
            
            return String.format("数据保存失败：【%s】字段不能为空，请填写该字段的值", fieldDescription);
        }
        
        return "数据保存失败：必填字段不能为空，请填写该字段的值";
    }
    
    /**
     * 格式化数据类型不匹配异常
     */
    private String formatDataTypeMismatchException(String errorMessage, String fullErrorMessage) {
        Matcher matcher = DATA_TYPE_MISMATCH_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String fieldName = matcher.group(1);
            
            // 从完整错误消息中提取表名
            String tableName = extractTableNameFromErrorMessage(fullErrorMessage);
            String fieldDescription = getFieldDisplayName(tableName, fieldName);
            
            return String.format("数据保存失败：【%s】字段数据类型不正确", fieldDescription);
        }
        
        return "数据保存失败：字段数据类型不正确";
    }
    
    /**
     * 格式化外键约束失败异常（通用版本）
     */
    private String formatForeignKeyGeneralException(String errorMessage) {
        Matcher matcher = FOREIGN_KEY_GENERAL_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String referencedTable = matcher.group(1);
            String referencedTableName = getTableDisplayName(referencedTable);
            return String.format("数据保存失败：引用的%s数据不存在，请检查关联数据", referencedTableName);
        }
        
        return "数据保存失败：引用的关联数据不存在，请检查关联数据";
    }
    
    /**
     * 格式化通用SQL异常
     */
    private String formatGenericSQLException(String errorMessage) {
        if (errorMessage.contains("Connection")) {
            return "数据库连接异常，请稍后重试";
        } else if (errorMessage.contains("timeout")) {
            return "数据库操作超时，请稍后重试";
        } else if (errorMessage.contains("Table") && errorMessage.contains("doesn't exist")) {
            return "数据表不存在，请联系管理员";
        } else if (errorMessage.contains("Column") && errorMessage.contains("doesn't exist")) {
            return "数据字段不存在，请联系管理员";
        } else if (errorMessage.contains("Access denied")) {
            return "数据库访问权限不足，请联系管理员";
        } else if (errorMessage.contains("Duplicate entry")) {
            return "数据重复，请检查唯一字段的值";
        } else if (errorMessage.contains("cannot be null")) {
            return "数据保存失败：必填字段不能为空";
        } else if (errorMessage.contains("Data truncated")) {
            return "数据保存失败：数据被截断，请检查字段长度限制";
        } else if (errorMessage.contains("Out of range")) {
            return "数据保存失败：数据超出范围，请检查字段值";
        } else if (errorMessage.contains("Invalid default value")) {
            return "数据保存失败：默认值无效，请检查字段配置";
        }
        
        // 如果都不匹配，返回原始错误信息而不是通用提示
        return "数据库操作失败：" + errorMessage;
    }
    
    /**
     * 获取表的显示名称
     */
    private String getTableDisplayName(String tableName) {
        if (tableName == null) {
            return "数据表";
        }
        
        // 优先使用数据库注释
        try {
            String comment = metadataUtil.getTableComment(tableName);
            if (comment != null && !comment.trim().isEmpty()) {
                log.debug("获取到表注释: {}={}", tableName, comment);
                return comment;
            }
            log.debug("未获取到表注释: {}", tableName);
        } catch (Exception e) {
            log.warn("获取表注释失败: {}, error: {}", tableName, e.getMessage());
        }
        
        // 获取不到注释时，直接返回原表名
        return tableName;
    }
    
    /**
     * 获取索引字段的显示名称
     */
    private String getIndexFieldDescription(String tableName, String indexName) {
        if (tableName == null || indexName == null) {
            return "相关字段";
        }
        
        // 使用元数据工具获取字段描述
        try {
            String fieldsDescription = metadataUtil.getIndexFieldsDescription(tableName, indexName);
            if (fieldsDescription != null && !fieldsDescription.trim().isEmpty()) {
                log.debug("获取到索引字段描述: {}.{}={}", tableName, indexName, fieldsDescription);
                return fieldsDescription;
            }
            log.debug("未获取到索引字段描述: {}.{}", tableName, indexName);
        } catch (Exception e) {
            log.warn("获取索引字段描述失败: {}.{}, error: {}", tableName, indexName, e.getMessage());
        }
        
        // 获取不到注释时，直接返回索引名
        return indexName;
    }
    
    /**
     * 获取字段的显示名称
     */
    private String getFieldDisplayName(String tableName, String fieldName) {
        if (fieldName == null) {
            return "字段";
        }
        
        // 优先使用数据库注释
        if (tableName != null) {
            try {
                String comment = metadataUtil.getFieldComment(tableName, fieldName);
                if (comment != null && !comment.trim().isEmpty()) {
                    log.debug("获取到字段注释: {}={}", fieldName, comment);
                    return comment;
                }
                log.debug("未获取到字段注释: {}.{}", tableName, fieldName);
            } catch (Exception e) {
                log.warn("获取字段注释失败: {}.{}, error: {}", tableName, fieldName, e.getMessage());
            }
        }
        
        // 获取不到注释时，直接返回原字段名
        return fieldName;
    }
    
    /**
     * 从错误消息中提取表名
     * 主要从MyBatis的SQL语句中解析表名
     */
    private String extractTableNameFromErrorMessage(String errorMessage) {
        // 尝试从INSERT语句中提取表名
        Pattern tablePattern = Pattern.compile("INSERT INTO (\\w+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = tablePattern.matcher(errorMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 尝试从UPDATE语句中提取表名
        tablePattern = Pattern.compile("UPDATE (\\w+)", Pattern.CASE_INSENSITIVE);
        matcher = tablePattern.matcher(errorMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * 为控制台日志格式化异常信息
     * 
     * @param exception 数据库异常
     * @return 格式化的控制台日志信息
     */
    public String formatForConsole(Exception exception) {
        String userFriendlyMessage = formatDatabaseException(exception);
        
        // 获取技术详情
        Throwable rootCause = ExceptionUtil.getRootCause(exception);
        String technicalDetails = rootCause.getMessage();
        
        return String.format("用户提示: %s | 技术详情: %s", 
            userFriendlyMessage, 
            technicalDetails != null ? technicalDetails.substring(0, Math.min(200, technicalDetails.length())) : "无详情");
    }
    
    /**
     * 获取异常的修复建议
     * 
     * @param exception 数据库异常
     * @return 修复建议
     */
    public String getFixSuggestion(Exception exception) {
        Throwable rootCause = ExceptionUtil.getRootCause(exception);
        String errorMessage = rootCause.getMessage();
        
        if (errorMessage == null) {
            return "建议联系技术支持人员进行排查";
        }
        
        StringBuilder suggestion = new StringBuilder("修复建议：\n");
        
        if (rootCause instanceof DuplicateKeyException || 
            errorMessage.contains("Duplicate entry")) {
            suggestion.append("1. 检查输入的数据是否与现有记录重复\n");
            suggestion.append("2. 确认唯一字段的值是否已被使用\n");
            suggestion.append("3. 可以尝试使用不同的值重新提交");
        } else if (errorMessage.contains("foreign key constraint")) {
            suggestion.append("1. 先删除或修改关联的子数据\n");
            suggestion.append("2. 确认数据之间的依赖关系\n");
            suggestion.append("3. 按正确的顺序进行数据操作");
        } else if (errorMessage.contains("cannot be null")) {
            suggestion.append("1. 确保所有必填字段都有值\n");
            suggestion.append("2. 检查表单验证逻辑\n");
            suggestion.append("3. 补充缺失的必要信息");
        } else if (errorMessage.contains("Data too long")) {
            suggestion.append("1. 减少输入内容的长度\n");
            suggestion.append("2. 检查字段长度限制\n");
            suggestion.append("3. 可能需要调整数据库字段长度");
        } else {
            suggestion.append("1. 检查数据格式是否正确\n");
            suggestion.append("2. 确认数据库连接是否正常\n");
            suggestion.append("3. 如问题持续，请联系技术支持");
        }
        
                 return suggestion.toString();
    }
    
}