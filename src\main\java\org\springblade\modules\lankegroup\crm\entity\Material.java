/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料表实体类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@TableName("blade_material")
@EqualsAndHashCode(callSuper = true)
public class Material extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 物料id
	*/
//		private Long id;
	/**
	* 物料编号
	*/
		private String materialCode;
	/**
	* 物料名
	*/
		private String materialName;
	/**
	* 型号/规格
	*/
		private String specificationModel;
	/**
	 * 单位表id
	 */
	private long unitId;
	/**
	* 单位编码
	*/
		private String unitCode;
	/**
	* 单位名称
	*/
		private String unitName;
	/**
	* 物料属性（10为资产物料，9普通物料）
	*/
		private String materialProperties="9";
	/**
	* 物料库存量
	*/
		private Double existingStock;
	/**
	* 是否删除
	*/
		private Integer isDelete;
	/**
	 *单价
	 */
	private String price;


}
