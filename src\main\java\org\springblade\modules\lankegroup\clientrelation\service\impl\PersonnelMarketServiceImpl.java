package org.springblade.modules.lankegroup.clientrelation.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.PersonnelMarket;
import org.springblade.modules.lankegroup.clientrelation.mapper.PersonnelMarketMapper;
import org.springblade.modules.lankegroup.clientrelation.service.PersonnelMarketService;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
@AllArgsConstructor
public class PersonnelMarketServiceImpl extends BaseServiceImpl<PersonnelMarketMapper, PersonnelMarket> implements PersonnelMarketService {

    @Override
    public void savePersonnelMarket(List<PersonnelMarket> list) {
        saveBatch(list);
    }

    @Override
    public List<AreaAndMarket> getPersonnelMarketIds(Long id,List<Long> prohibitList) {
        return baseMapper.getPersonnelMarketIds(id,prohibitList);
    }

    @Override
    public void deletePersonnelMarket(Long id) {
        baseMapper.deletePersonnelMarket(id);
    }

    @Override
    public List<PersonnelMarketVO> getPersonnelMarketList(Long id) {
        return baseMapper.getPersonnelMarketList(id);
    }

    @Override
    public List<AreaAndMarket> getPersonnelMarketByName(Long id,Long marketId,String areaCode,List<Long> prohibitList) {
        return baseMapper.getPersonnelMarketByNameStr(id,marketId,areaCode,prohibitList);
    }

    @Override
    public Integer getMarketNum(String professionId, String marketId,String areaId) {
        return baseMapper.getMarketNum(professionId,marketId,areaId);
    }
}
