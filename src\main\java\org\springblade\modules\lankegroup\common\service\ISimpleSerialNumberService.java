/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.lankegroup.common.entity.SimpleSerialNumber;

/**
 * 简单全局序列号表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ISimpleSerialNumberService extends IService<SimpleSerialNumber> {

    /**
     * 生成编号
     *
     * @param bizType 业务类型
     * @return
     */
    String generateBillNumber(String bizType);
    /**
     * 生成编号
     *
     * @param bizType 业务类型
     * @param numberOfDigits 位数
     * @return
     */
    String generateBillNumber(String bizType, int numberOfDigits);


}
