package org.springblade.modules.lankegroup.clientrelation.vo;

import com.sun.source.doctree.IndexTree;
import lombok.Data;

@Data
public class MarketplaceVO {

    private String id;

    /**
     * 行业名称
     */
    private String name;

    /**
     * 客户数量
     */
    private Integer num;

    /**
     * 行业类型（1企业 2政府）
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createName;

    private Integer status;

    /**
     * 是否选中
     */
    private Integer checked;

}
