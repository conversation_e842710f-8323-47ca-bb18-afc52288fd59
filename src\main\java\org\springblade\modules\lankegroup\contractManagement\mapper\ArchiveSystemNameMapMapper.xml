<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveSystemNameMapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="archiveSystemNameMapResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ArchiveSystemNameMap">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="project_id" property="projectId"/>
        <result column="details_id" property="detailsId"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="system_name" property="systemName"/>
    </resultMap>



    <select id="getSystemNameDataListByInventoryId" resultMap="archiveSystemNameMapResultMap">
        select * from blade_archive_system_name_map where is_deleted = 0 and inventory_id = #{inventoryId}
    </select>

    <delete id="deleteByDetailsId">
        delete from blade_archive_system_name_map where is_deleted = 0 and details_id = #{detailsId}
    </delete>

    <update id="updateSystemNameById">
        update blade_archive_system_name_map set system_name = #{systemName} where id = #{id}
    </update>

</mapper>
