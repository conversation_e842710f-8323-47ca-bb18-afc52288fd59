package org.springblade.modules.lankegroup.clientrelation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.CustomerKanBanDTO;
import org.springblade.modules.lankegroup.clientrelation.vo.CustomerImportanctVO;
import org.springblade.modules.lankegroup.crm.vo.CustomerVO;

import java.util.List;

public interface CustomerKanBanService {

    /**
     * 客户看板-客户数量统计
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerCount(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户行业分析
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerProfession(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户行业分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerProfessionPieChart(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户行业分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerProfessionListDetail(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerRegionPieChart(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerRegion(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-列表
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerRegionList(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerRegionListDetail(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-行业产值
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerProfessionValue(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-行业产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerProfessionValueGroup(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户分析-客户产值
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerValue(CustomerKanBanDTO customerKanBanDTO);

    R getCustomerValueGroup(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-重要程度分析
     *
     * @param customerKanBanDTO
     * @return
     */
    R getCustomerImportance(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-重要程度分析
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerImportanctVO> getCustomerImportancePage(CustomerKanBanDTO customerKanBanDTO);
}
