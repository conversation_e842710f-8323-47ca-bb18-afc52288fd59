/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.flow.business.dto.BusinessProcessDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.vo.BusinessProcessVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 自定义流程审批中的所有业务单据任务 服务类
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IBusinessProcessService extends BaseService<BusinessProcessEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param BusinessProcessTask
	 * @return
	 */
	IPage<BusinessProcessVO> selectBusinessProcessPage(IPage<BusinessProcessVO> page, BusinessProcessDTO BusinessProcessTask);
	IPage<BusinessProcessVO> selectBusinessProcessTransferPage(IPage<BusinessProcessVO> page, BusinessProcessDTO BusinessProcessTask);

	/**
	 * 用户任务审批完成更新历史审批人和单据审批状态
	 *
	 * @param processInstanceId
	 * @param assigneeId
	 * @return
	 */
	boolean updateBusinessProcessUserCompleteTask(@RequestParam("processInstanceId") String processInstanceId, @RequestParam("assigneeId") String assigneeId, @RequestParam("isPass") boolean isPass);

	/**
	 * 驳回
	 * @param tableName
	 * @param formDataId
	 * @return
	 */
	boolean updateBusinessProcessRejectTask(String tableName, String formDataId, String processInstanceId);

	/**
	 * 撤销
	 * @param tableName
	 * @param formDataId
	 * @return
	 */
	boolean updateBusinessProcessRevokeTask(String tableName, String formDataId, String processInstanceId);
	boolean updateBusinessProcessDeleteFlow(String tableName, String formDataId, String processInstanceId);
	void businessProcessFinshed(String tableName, String formDataId, String processInstanceId);
	void businessProcessCcDelegate(String tableName, String formDataId,String ccUserIds, String processInstanceId);
}
