<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.common.mapper.SimpleSerialNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="simpleSerialNumberResultMap" type="org.springblade.modules.lankegroup.common.entity.SimpleSerialNumber">
        <result column="id" property="id"/>
        <result column="biz_type" property="bizType"/>
        <result column="current_value" property="currentValue"/>
        <result column="version" property="version"/>
    </resultMap>


    <select id="selectByBizType" resultType="org.springblade.modules.lankegroup.common.entity.SimpleSerialNumber">
        SELECT * FROM sys_simple_serial_number WHERE biz_type = #{bizType}
    </select>

    <select id="incrementValue" resultType="java.lang.Integer">
        UPDATE sys_simple_serial_number SET
        current_value = #{newValue},
        version = version + 1
        WHERE id = #{id}
        AND current_value = #{oldValue}
        AND version = #{version}
    </select>


</mapper>
