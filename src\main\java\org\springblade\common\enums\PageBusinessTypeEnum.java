package org.springblade.common.enums;

/**
 * 页面业务类型枚举
 *
 * <AUTHOR> <PERSON>
 * @since 2025年07月01日 16:00
 **/
public enum PageBusinessTypeEnum {
    
    /**
     * 客户页面
     */
    CUSTOMER_PAGE("customer_page", "客户页面"),

    /*
    * 客户详情页
    * */
    CUSTOMER_DETAIL_PAGE("customer_detail_page", "客户详情页"),
    
    /**
     * 联系人页面
     */
    CONTACT_PAGE("contact_page", "联系人页面"),
    
    /**
     * 商机页面
     */
    OPPORTUNITY_PAGE("opportunity_page", "商机页面"),
    
    /**
     * 跟进记录页面
     */
    FOLLOW_PAGE("follow_page", "跟进记录页面"),
    
    /**
     * 合同页面
     */
    CONTRACT_PAGE("contract_page", "合同页面"),
    
    /**
     * 财务页面
     */
    FINANCE_PAGE("finance_page", "财务页面");
    
    private final String code;
    private final String name;
    
    PageBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public static PageBusinessTypeEnum getByCode(String code) {
        for (PageBusinessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 