package org.springblade.common.utils;


import cn.hutool.json.JSONObject;
import org.springblade.common.controller.vo.UtilStoreGetReqVO;

import javax.validation.Valid;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface UtilStoreService {

    /**
     * 创建用户
     *
     * @param reqVO 用户信息
     * @return 用户编号
     */
    JSONObject getStore(@Valid UtilStoreGetReqVO reqVO);

    Boolean validStoreWithException(@Valid UtilStoreGetReqVO reqVO);

    String generateIdAndSet(String jsonStr, String pwd, int expirationTime);
}
