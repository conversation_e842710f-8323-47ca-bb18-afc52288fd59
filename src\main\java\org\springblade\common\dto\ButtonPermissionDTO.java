package org.springblade.common.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 按钮权限DTO
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 16:00
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonPermissionDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 页面业务标识
     */
    private String pageBusinessCode;
    
    /**
     * 场景标识列表（可选，如果不传则返回该页面所有场景的权限）
     */
    private List<String> sceneCodes;
    
    /**
     * 按钮类型列表（可选，如果不传则返回所有按钮的权限）
     */
    private List<String> buttonCodes;
    
    /**
     * 业务数据ID（用于权限判断的业务数据，如客户ID、联系人ID等）
     */
    private Long businessDataId;
    
    /**
     * 业务数据ID列表（用于批量权限判断）
     */
    private List<Long> businessDataIds;
    
    /**
     * 额外参数（用于复杂的权限判断场景）
     */
    private Object extraParams;
} 