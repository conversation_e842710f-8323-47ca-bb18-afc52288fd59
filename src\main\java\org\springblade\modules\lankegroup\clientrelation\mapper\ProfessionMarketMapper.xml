<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionMarketMapper">
    <delete id="deleteProfessionMarkets">
        delete from blade_profession_market where join_id in (
        <foreach collection="ids" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </delete>

    <select id="getProfessionMarketList" resultType="java.lang.Long">
        select market_code from blade_profession_market where join_id in (
            <foreach collection="ids" separator="," item="item" index="index">
                #{item}
            </foreach>
            ) and is_deleted = 0
    </select>
</mapper>
