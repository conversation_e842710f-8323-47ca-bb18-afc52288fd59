package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.ChannelSupplierContact;
import org.springblade.modules.lankegroup.crm.vo.ChannelSupplierContactVO;
import org.springblade.modules.lankegroup.crm.service.IChannelSupplierContactService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 渠道商专职联系人表 控制器
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/channel/suppliercontact")
@Api(value = "渠道商专职联系人表", tags = "渠道商专职联系人表接口")
public class ChannelSupplierContactController extends BladeController {

	private final IChannelSupplierContactService channelSupplierContactService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入channelSupplierContact")
	public R<ChannelSupplierContact> detail(ChannelSupplierContact channelSupplierContact) {
		ChannelSupplierContact detail = channelSupplierContactService.getOne(Condition.getQueryWrapper(channelSupplierContact));
		return R.data(detail);
	}

	/**
	 * 分页 渠道商专职联系人表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入channelSupplierContact")
	public R<IPage<ChannelSupplierContact>> list(ChannelSupplierContact channelSupplierContact, Query query) {
		IPage<ChannelSupplierContact> pages = channelSupplierContactService.page(Condition.getPage(query), Condition.getQueryWrapper(channelSupplierContact));
		return R.data(pages);
	}

	/**
	 * 自定义分页 渠道商专职联系人表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入channelSupplierContact")
	public R<IPage<ChannelSupplierContactVO>> page(ChannelSupplierContactVO channelSupplierContact, Query query) {
		IPage<ChannelSupplierContactVO> pages = channelSupplierContactService.selectChannelSupplierContactPage(Condition.getPage(query), channelSupplierContact);
		return R.data(pages);
	}

	/**
	 * 新增 渠道商专职联系人表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入channelSupplierContact")
	public R save(@Valid @RequestBody ChannelSupplierContact channelSupplierContact) {
		return R.status(channelSupplierContactService.save(channelSupplierContact));
	}

	/**
	 * 修改 渠道商专职联系人表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入channelSupplierContact")
	public R update(@Valid @RequestBody ChannelSupplierContact channelSupplierContact) {
		return R.status(channelSupplierContactService.updateById(channelSupplierContact));
	}

	/**
	 * 新增或修改 渠道商专职联系人表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入channelSupplierContact")
	public R submit(@Valid @RequestBody ChannelSupplierContact channelSupplierContact) {
		return R.status(channelSupplierContactService.saveOrUpdate(channelSupplierContact));
	}

	
	/**
	 * 删除 渠道商专职联系人表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(channelSupplierContactService.deleteLogic(Func.toLongList(ids)));
	}

	
}
