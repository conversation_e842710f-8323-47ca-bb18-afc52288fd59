package org.springblade.common.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 按钮权限VO
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 16:00
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonPermissionVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 按钮权限结果
     * 格式：Map<场景标识, Map<按钮标识, 权限属性>>
     * 例如：{
     *   "follow_add": {
     *     "addBtn": {"visible": true, "enabled": true, "tooltip": ""},
     *     "editBtn": {"visible": false, "enabled": false, "tooltip": "无权限"}
     *   },
     *   "opportunity_add": {
     *     "addBtn": {"visible": true, "enabled": true, "tooltip": ""}
     *   }
     * }
     */
    private Map<String, Map<String, ButtonPermissionDetailVO>> permissions;
} 