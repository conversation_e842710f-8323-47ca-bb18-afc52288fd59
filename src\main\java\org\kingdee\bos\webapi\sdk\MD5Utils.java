//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.security.MessageDigest;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class MD5Utils {
    public MD5Utils() {
    }

    public static String encrypt(String dataStr) {
        try {
            MessageDigest m = MessageDigest.getInstance("MD5");
            m.update(dataStr.getBytes("UTF8"));
            byte[] s = m.digest();
            String result = "";

            for(int i = 0; i < s.length; ++i) {
                result = result + Integer.toHexString(255 & s[i] | -256).substring(6);
            }

            return result;
        } catch (Exception var5) {
            var5.printStackTrace();
            return "";
        }
    }

    public static String hashMAC(String data, String secret) {
        try {
            Mac kdmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            kdmac.init(secret_key);
            byte[] rawHmac = kdmac.doFinal(data.getBytes());
            return Base64Utils.encodingToBase64(bytesToHex(rawHmac).getBytes());
        } catch (Exception var5) {
            var5.printStackTrace();
            return null;
        }
    }

    private static String bytesToHex(byte[] hashInBytes) {
        StringBuilder sb = new StringBuilder();

        for(int i = 0; i < hashInBytes.length; ++i) {
            String hex = Integer.toHexString(hashInBytes[i] & 255);
            if (hex.length() < 2) {
                hex = "0" + hex;
            }

            sb.append(hex);
        }

        return sb.toString();
    }
}
