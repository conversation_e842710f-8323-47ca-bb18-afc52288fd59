package org.springblade.flow.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.feign.WorkFlowClient;
import org.springblade.flow.business.mapper.BusinessProcessMapper;
import org.springblade.flow.core.constant.FlowFormConstant;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.demo.dto.FlowTestBusinessDTO;
import org.springblade.flow.demo.entity.FlowTestBusinessEntity;
import org.springblade.flow.demo.mapper.FlowTestBusinessMapper;
import org.springblade.flow.demo.service.FlowTestBusinessService;
import org.springblade.flow.demo.vo.FlowTestBusinessVO;
import org.springblade.workflow.service.WorkFlowService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Liu
 * @since 2025年06月16日 15:49
 **/

@Service
@AllArgsConstructor
public class FlowTestBusinessServiceImpl extends ServiceImpl<FlowTestBusinessMapper, FlowTestBusinessEntity> implements FlowTestBusinessService {

    private final WorkFlowService workFlowService;
    private final BusinessProcessMapper businessProcessMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveModle(FlowTestBusinessDTO flowTestBusinessEntity) {
        flowTestBusinessEntity.setCode(FlowFormConstant.FLOW_TEST_BUSINESS_KEY + Func.random(6));
        save(flowTestBusinessEntity);
        List<Kv> businessFieldList = new ArrayList<>();
        businessFieldList.add(Kv.create().set("key", "单据编号").set("value", flowTestBusinessEntity.getCode()));
        businessFieldList.add(Kv.create().set("key", "单据时间").set("value", cn.hutool.core.date.DateUtil.now()));
        String businessField = Func.toJson(businessFieldList);
        R startProcessR = workFlowService.startProcess(
                FlowFormConstant.FLOW_TEST_BUSINESS_KEY,
                flowTestBusinessEntity.getId(),
                flowTestBusinessEntity.getCode(),
                flowTestBusinessEntity.getSelectSponsorList(),
                businessField,
                null
        );
        return R.status(startProcessR.isSuccess());
    }

    @Override
    public R updateModle(FlowTestBusinessDTO flowTestBusinessEntity) {
        updateById(flowTestBusinessEntity);
        R startProcessR = workFlowService.updateProcess(
                FlowFormConstant.FLOW_TEST_BUSINESS_KEY,
                flowTestBusinessEntity.getId(),
                flowTestBusinessEntity.getCode(),
                new ArrayList<>(),
                null,
                null
        );
        return R.status(startProcessR.isSuccess());
    }

    @Override
    public R listModel(IPage<FlowTestBusinessEntity> page, FlowTestBusinessEntity flowTestBusinessEntity) {
        return R.data(page(page, Condition.getQueryWrapper(flowTestBusinessEntity)));
    }

    @Override
    public R detailModel(FlowTestBusinessEntity flowTestBusinessEntity) {
        FlowTestBusinessEntity byId = getById(flowTestBusinessEntity.getId());
        if (byId == null) {
            return R.fail("未查询到数据");
        }
        FlowTestBusinessVO flowTestBusinessVO = BeanUtil.copyProperties(byId, FlowTestBusinessVO.class);
        BusinessProcessEntity oneByFormKeyAndFormDataId = businessProcessMapper.getOneByFormKeyAndFormDataId(FlowFormConstant.FLOW_TEST_BUSINESS_KEY, byId.getId());
        if (oneByFormKeyAndFormDataId != null) {
            flowTestBusinessVO.setProcessDefinitionId(oneByFormKeyAndFormDataId.getProcessDefinitionId());
            flowTestBusinessVO.setProcessInstanceId(oneByFormKeyAndFormDataId.getProcessInstanceId());
        }
        return R.data(flowTestBusinessVO);
    }
}
