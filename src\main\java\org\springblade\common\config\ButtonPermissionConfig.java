package org.springblade.common.config;

import org.springblade.common.enums.BusinessAuthTypeEnum;
import org.springblade.common.enums.ButtonPermissionTypeEnum;
import org.springblade.common.enums.PageBusinessTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 按钮权限配置
 *
 * <AUTHOR> Liu
 * @since 2025年07月01日 16:00
 **/
@Component
public class ButtonPermissionConfig {
    
    /**
     * 权限配置规则映射
     * 格式：Map<页面业务标识, Map<场景标识, Map<按钮标识, 权限规则列表>>>
     */
    private volatile Map<String, Map<String, Map<String, List<BusinessAuthTypeEnum>>>> permissionRulesMap;
    
    /**
     * 预定义的权限规则组合
     */
    private static final List<BusinessAuthTypeEnum> ADMIN_ROLES = Arrays.asList(
            BusinessAuthTypeEnum.财务, BusinessAuthTypeEnum.高层, BusinessAuthTypeEnum.部门领导);
    
    private static final List<BusinessAuthTypeEnum> OWNER_AND_ADMIN_ROLES = Arrays.asList(
            BusinessAuthTypeEnum.目标人及其所有上级, BusinessAuthTypeEnum.财务, BusinessAuthTypeEnum.高层);

    private static final List<BusinessAuthTypeEnum> OWNER_AND_FINANCE_ROLES = Arrays.asList(
            BusinessAuthTypeEnum.目标人及其所有上级, BusinessAuthTypeEnum.财务);
    
    private static final List<BusinessAuthTypeEnum> VIEWER_ROLES = Arrays.asList(
            BusinessAuthTypeEnum.目标人及其所有上级, BusinessAuthTypeEnum.财务, BusinessAuthTypeEnum.高层, BusinessAuthTypeEnum.部门领导);
    
    /**
     * 使用@PostConstruct确保每次bean重新创建时都会重新初始化
     */
    @PostConstruct
    public void initConfig() {
        System.out.println("ButtonPermissionConfig 正在重新初始化配置... 时间: " + new Date());
        this.permissionRulesMap = initPermissionRules();
        System.out.println("ButtonPermissionConfig 配置初始化完成！");
    }
    
    /**
     * 手动刷新配置（提供给开发环境使用）
     */
    public void refreshConfig() {
        System.out.println("手动刷新 ButtonPermissionConfig 配置...");
        this.permissionRulesMap = initPermissionRules();
        System.out.println("ButtonPermissionConfig 手动刷新完成！");
    }
    
    /**
     * 初始化权限规则配置
     */
    private Map<String, Map<String, Map<String, List<BusinessAuthTypeEnum>>>> initPermissionRules() {
        Map<String, Map<String, Map<String, List<BusinessAuthTypeEnum>>>> rulesMap = new HashMap<>();
        
        // 客户页面权限配置
        rulesMap.put(PageBusinessTypeEnum.CUSTOMER_PAGE.getCode(), createCustomerPageRules());
        
        // 客户详情页权限配置
        rulesMap.put(PageBusinessTypeEnum.CUSTOMER_DETAIL_PAGE.getCode(), createCustomerDetailPageRules());
        
        // 联系人页面权限配置
        rulesMap.put(PageBusinessTypeEnum.CONTACT_PAGE.getCode(), createContactPageRules());
        
        // 商机页面权限配置
        rulesMap.put(PageBusinessTypeEnum.OPPORTUNITY_PAGE.getCode(), createOpportunityPageRules());
        
        // 跟进记录页面权限配置
        rulesMap.put(PageBusinessTypeEnum.FOLLOW_PAGE.getCode(), createFollowPageRules());
        
        return rulesMap;
    }
    
    /**
     * 创建客户页面权限配置
     */
    private Map<String, Map<String, List<BusinessAuthTypeEnum>>> createCustomerPageRules() {
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules = new HashMap<>();
        
        // 客户基础操作场景
        pageRules.put("customer_basic", createBasicSceneRules());
        
        // 跟进场景 - 使用基础操作权限
        pageRules.put("follow", createBasicSceneRules());
        
        // 商机场景 - 使用基础操作权限
        pageRules.put("opportunity", createBasicSceneRules());
        
        return pageRules;
    }
    
    /**
     * 创建客户详情页权限配置
     */
    private Map<String, Map<String, List<BusinessAuthTypeEnum>>> createCustomerDetailPageRules() {
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules = new HashMap<>();
        pageRules.put("basic", createBasicSceneRules());
        Map<String, List<BusinessAuthTypeEnum>> followSceneRules = new HashMap<>();
        followSceneRules.put(ButtonPermissionTypeEnum.ADD_BUTTON.getCode(), OWNER_AND_FINANCE_ROLES);
        pageRules.put("follow", followSceneRules);

        return pageRules;
    }
    
    /**
     * 创建联系人页面权限配置
     */
    private Map<String, Map<String, List<BusinessAuthTypeEnum>>> createContactPageRules() {
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules = new HashMap<>();
        pageRules.put("contact_basic", createBasicSceneRules());
        return pageRules;
    }
    
    /**
     * 创建商机页面权限配置
     */
    private Map<String, Map<String, List<BusinessAuthTypeEnum>>> createOpportunityPageRules() {
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules = new HashMap<>();
        
        Map<String, List<BusinessAuthTypeEnum>> basicScene = createBasicSceneRules();
        // 商机页面添加审核按钮
        basicScene.put(ButtonPermissionTypeEnum.AUDIT_BUTTON.getCode(), ADMIN_ROLES);
        
        pageRules.put("opportunity_basic", basicScene);
        return pageRules;
    }
    
    /**
     * 创建跟进记录页面权限配置
     */
    private Map<String, Map<String, List<BusinessAuthTypeEnum>>> createFollowPageRules() {
        Map<String, Map<String, List<BusinessAuthTypeEnum>>> pageRules = new HashMap<>();
        pageRules.put("follow_basic", createBasicSceneRules());
        return pageRules;
    }
    
    /**
     * 创建基础场景权限规则
     */
    private Map<String, List<BusinessAuthTypeEnum>> createBasicSceneRules() {
        Map<String, List<BusinessAuthTypeEnum>> sceneRules = new HashMap<>();
        sceneRules.put(ButtonPermissionTypeEnum.EDIT_BUTTON.getCode(), OWNER_AND_FINANCE_ROLES);
        sceneRules.put(ButtonPermissionTypeEnum.MORE_BUTTON.getCode(), OWNER_AND_FINANCE_ROLES);
        sceneRules.put(ButtonPermissionTypeEnum.MORE_BUTTON.getCode(), OWNER_AND_FINANCE_ROLES);
        return sceneRules;
    }
    
    /**
     * 获取权限规则
     */
    public List<BusinessAuthTypeEnum> getPermissionRules(String pageBusinessCode, String sceneCode, String buttonCode) {
        return Optional.ofNullable(permissionRulesMap.get(pageBusinessCode))
                .map(pageRules -> pageRules.get(sceneCode))
                .map(sceneRules -> sceneRules.get(buttonCode))
                .orElse(Collections.emptyList());
    }
    
    /**
     * 获取页面所有场景的权限规则
     */
    public Map<String, Map<String, List<BusinessAuthTypeEnum>>> getPagePermissionRules(String pageBusinessCode) {
        return Optional.ofNullable(permissionRulesMap.get(pageBusinessCode))
                .orElse(Collections.emptyMap());
    }
    
    /**
     * 获取场景的所有按钮权限规则
     */
    public Map<String, List<BusinessAuthTypeEnum>> getScenePermissionRules(String pageBusinessCode, String sceneCode) {
        return Optional.ofNullable(permissionRulesMap.get(pageBusinessCode))
                .map(pageRules -> pageRules.get(sceneCode))
                .orElse(Collections.emptyMap());
    }
} 