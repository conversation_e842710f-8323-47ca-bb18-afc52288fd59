/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;

import java.util.List;
import java.util.Map;

/**
 * 归档详情 服务类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface IChannelArchiveDetailsService extends BaseService<ArchiveDetails> {

	/**
	 * 归档详情 详情查询
	 *
	 * @param
	 * @return
	 */
	ArchiveDetailsVO selectArchiveDetails(Map map);

	Map saveArchive(ArchiveDetailsVO archiveDetailsVO);

	List initContractArchiveDetails();

	List approvalProcess(String processInstanceId);

	Boolean updateArchive(ArchiveDetailsVO archiveDetailsVO);

	//去合同变更表中查询 合同信息
	List<ContractChange> contractChange(Long contractId);

}
