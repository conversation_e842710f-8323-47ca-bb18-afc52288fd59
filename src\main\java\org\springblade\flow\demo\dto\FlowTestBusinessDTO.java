package org.springblade.flow.demo.dto;

import lombok.Data;
import org.springblade.flow.demo.entity.FlowTestBusinessEntity;
import org.springblade.workflow.dto.FlowVariableDTO;

import java.util.List;
import java.util.Map;

/**
 * flow_test_business
 *
 * <AUTHOR>
@Data
public class FlowTestBusinessDTO extends FlowTestBusinessEntity {

    private Map<String, Object> variables;
    private List<FlowVariableDTO> selectSponsorList;

}
