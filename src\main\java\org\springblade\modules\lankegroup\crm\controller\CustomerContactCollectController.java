/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.ChangedUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.crm.entity.CustomerContactCollect;
import org.springblade.modules.lankegroup.crm.vo.CustomerContactCollectVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerContactCollectService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.Map;

/**
 * 客户联系人收藏表 控制器
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/collect/customercontactcollect")
@Api(value = "客户联系人收藏表", tags = "客户联系人收藏表接口")
public class CustomerContactCollectController extends BladeController {

	private final ICustomerContactCollectService customerContactCollectService;

	private final ICustomerConnectLogService customerConnectLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerContactCollect")
	public R<CustomerContactCollect> detail(CustomerContactCollect customerContactCollect) {
		try {
			CustomerContactCollect detail = customerContactCollectService.getOne(Condition.getQueryWrapper(customerContactCollect));
			return R.data(detail);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 分页 客户联系人收藏表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerContactCollect")
	public R<IPage<CustomerContactCollect>> list(CustomerContactCollect customerContactCollect, Query query) {
		try {
			IPage<CustomerContactCollect> pages = customerContactCollectService.page(Condition.getPage(query), Condition.getQueryWrapper(customerContactCollect));
			return R.data(pages);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 自定义分页 客户联系人收藏表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerContactCollect")
	public R<IPage<CustomerContactCollectVO>> page(CustomerContactCollectVO customerContactCollect, Query query) {
		try {
			IPage<CustomerContactCollectVO> pages = customerContactCollectService.selectCustomerContactCollectPage(Condition.getPage(query), customerContactCollect);
			return R.data(pages);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 新增 客户联系人收藏表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerContactCollect")
	public R save(@Valid @RequestBody CustomerContactCollect customerContactCollect) {
		try {

			boolean save = customerContactCollectService.save(customerContactCollect);
			if (save) {
				//                                    写入记录日志表
				CustomerConnectLog contractChangeLog = new CustomerConnectLog();
				contractChangeLog.setCustomerId(customerContactCollect.getCustomerId());
				contractChangeLog.setChangeTable(TableIdEnum.客户联系人收藏.getTableId());
				contractChangeLog.setContactId(customerContactCollect.getId());
				contractChangeLog.setCustomerContactId(customerContactCollect.getContactsId());
				contractChangeLog.setAboutDescription("收藏了客户");
				contractChangeLog.setDescription("收藏了客户");
				contractChangeLog.setChargeUser(AuthUtil.getUserId());
				customerConnectLogService.save(contractChangeLog);
			}
			return R.status(save);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 修改 客户联系人收藏表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerContactCollect")
	public R update(@Valid @RequestBody CustomerContactCollect customerContactCollect) {
		try {
			CustomerContactCollect old = customerContactCollectService.selectId(customerContactCollect);
			//                                    写入记录日志表
			String changedFields = ChangedUtil.getUpdateFields(customerContactCollect, old);
			if (changedFields != null && (!changedFields.equals(""))) {
				CustomerConnectLog contractChangeLog = new CustomerConnectLog();
				contractChangeLog.setDescription(changedFields);
				contractChangeLog.setCustomerId(customerContactCollect.getCustomerId());
				contractChangeLog.setChangeTable(TableIdEnum.客户联系人收藏.getTableId());
				contractChangeLog.setContactId(customerContactCollect.getId());
				contractChangeLog.setAboutDescription("修改了客户");
				contractChangeLog.setChargeUser(AuthUtil.getUserId());
				customerConnectLogService.save(contractChangeLog);
			}
			return R.status(customerContactCollectService.updateBy(customerContactCollect));
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 新增或修改 客户联系人收藏表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerContactCollect")
	public R submit(@Valid @RequestBody CustomerContactCollect customerContactCollect) {
		try {
			return R.status(customerContactCollectService.saveOrUpdate(customerContactCollect));
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}


	/**
	 * 删除 客户联系人收藏表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam Long contactsId, @RequestParam Long customerId) {
		try {
			boolean delete = customerContactCollectService.delete(contactsId);
			if (delete){
				//                                    写入记录日志表
				CustomerConnectLog contractChangeLog=new CustomerConnectLog();
				contractChangeLog.setCustomerId(customerId);
				contractChangeLog.setChangeTable(TableIdEnum.客户联系人收藏.getTableId());
				contractChangeLog.setCustomerContactId(contactsId);
				contractChangeLog.setAboutDescription("取消收藏了客户");
				contractChangeLog.setDescription("取消收藏了客户");
				contractChangeLog.setChargeUser(AuthUtil.getUserId());
				customerConnectLogService.save(contractChangeLog);
			}
			return R.status(delete);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}
	/**
	 * 统计 客户联系人收藏统计
	 */
	@PostMapping("/statistics")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "统计", notes = "传入deptId,userId,sortFlag(desc或asc)")
	public R statistics(@Valid @RequestBody Map paramMap){
		try {
			return customerContactCollectService.statistics(paramMap);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}
	/**
	 * 自定义分页 客户联系人收藏表
	 */
	@GetMapping("/collectPage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerContactCollect")
	public R<IPage<Map>> collectPage(CustomerContactCollectVO customerContactCollect, Query query) {
		try {
			IPage<Map> pages = customerContactCollectService.selectCollectPage(Condition.getPage(query), customerContactCollect);
			return R.data(pages);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 首页重要的人
	 * @param customerContactCollect
	 * @return
	 */
	@GetMapping("/collectList")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "列表", notes = "传入customerContactCollect")
	public R colectList(CustomerContactCollectVO customerContactCollect){
		try {
			return customerContactCollectService.selectCollectList(customerContactCollect);
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}

	/**
	 * 自定义分页 客户联系人收藏表
	 */
	@GetMapping("/collectPageDetails")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerContactCollect")
	public R<Map> collectPageDetails(String contactsId) {
		try {
			return R.data(customerContactCollectService.collectPageDetails(contactsId));
		}catch (Exception e){
			return R.fail("系统繁忙，请稍后重试");
		}
	}
}
