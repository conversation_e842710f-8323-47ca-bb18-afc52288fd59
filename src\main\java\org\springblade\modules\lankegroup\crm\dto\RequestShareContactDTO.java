/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.dto;

import org.springblade.modules.lankegroup.crm.entity.RequestShareContact;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户联系人申请共享联系人表	点击客户联系人的申请共享，联系人的负责人可以选择是否同意共享	根据负责人是否回应控制当前登陆人是否可以继续点击申请共享/脱敏数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RequestShareContactDTO extends RequestShareContact {
	private static final long serialVersionUID = 1L;

}
