//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

public class CfgUtil {
    static String cfgFilePath = null;
    static AppCfg instance;

    public CfgUtil() {
    }

    static void setCfgFilePath(String cfgPath) {
        cfgFilePath = cfgPath;
    }

    public static AppCfg getAppDefaultCfg() {
        if (instance != null) {
            return instance;
        } else {
            AppCfg cfg = null;
            Properties properties = new Properties();
            File file = null;
            boolean isLoad = false;

            try {
                if (cfgFilePath != null) {
                    file = new File(cfgFilePath);
                } else {
                    file = new File((new File(".")).getCanonicalPath() + "/kdwebapi.properties");
                }

                if (!file.exists()) {
                    Thread.currentThread().getContextClassLoader();
                    InputStream inputStream = ClassLoader.getSystemResourceAsStream("kdwebapi.properties");
                    if (inputStream != null) {
                        properties.load(new InputStreamReader(inputStream, "utf-8"));
                        inputStream.close();
                        isLoad = true;
                    }
                } else {
                    InputStream inputStream = new FileInputStream(file.getPath());
                    properties.load(new InputStreamReader(inputStream, "utf-8"));
                    inputStream.close();
                    isLoad = true;
                }
            } catch (IOException var9) {
                var9.printStackTrace();
            }

            if (isLoad) {
                AppCfg scfg = new AppCfg();
                String svr = properties.getProperty("X-KDApi-ServerUrl");
                if (svr != null) {
                    scfg.setServerUrl(svr);
                }

                scfg.setdCID(properties.getProperty("X-KDApi-AcctID"));
                scfg.setUserName(properties.getProperty("X-KDApi-UserName"));
                scfg.setAppId(properties.getProperty("X-KDApi-AppID"));
                scfg.setAppSecret(properties.getProperty("X-KDApi-AppSec"));
                String lcid = properties.getProperty("X-KDApi-LCID");
                if (lcid != null) {
                    scfg.setlCID(new Integer(lcid));
                }

                scfg.setOrgNum(properties.getProperty("X-KDApi-OrgNum"));
                String timeout = properties.getProperty("X-KDApi-ConnectTimeout");
                if (timeout != null) {
                    scfg.setConnectTimeout(new Integer(timeout));
                }

                timeout = properties.getProperty("X-KDApi-RequestTimeout");
                if (timeout != null) {
                    scfg.setRequestTimeout(new Integer(timeout));
                }

                timeout = properties.getProperty("X-KDApi-StockTimeout");
                if (timeout != null) {
                    scfg.setStockTimeout(new Integer(timeout));
                }

                scfg.setProxy(properties.getProperty("X-KDApi-Proxy"));
                cfg = scfg;
            }

            instance = cfg;
            return instance;
        }
    }
}

