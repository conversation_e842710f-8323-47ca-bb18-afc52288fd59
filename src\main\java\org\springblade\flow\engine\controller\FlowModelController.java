/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.engine.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.flow.engine.vo.FlowModelDeployVO;
import org.springblade.flow.engine.vo.FlowModelVO;
import org.springblade.flow.engine.wrapper.FlowModelWrapper;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 流程模型控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@RequestMapping("model")
@AllArgsConstructor
@Api(value = "流程模型控制器", tags = "流程模型控制器")
//@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
public class FlowModelController {

	private final FlowEngineService flowEngineService;

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "删除", notes = "传入主键集合")
	public R remove(@ApiParam(value = "主键集合") @RequestParam String ids) {
		boolean temp = flowEngineService.removeByIds(Func.toStrList(ids));
		return R.status(temp);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "modelKey", value = "模型标识", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "模型名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<IPage<FlowModel>> list(@ApiIgnore @RequestParam Map<String, Object> flow, Query query) {
		IPage<FlowModel> pages = flowEngineService.page(Condition.getPage(query), Condition.getQueryWrapper(flow, FlowModel.class)
			.select("id,model_key modelKey,name,description,version,created,last_updated lastUpdated")
				.eq("data_source", 0)
			.orderByDesc("last_updated"));
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "modelKey", value = "模型标识", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "模型名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<IPage<FlowModelVO>> page(@ApiIgnore @RequestParam Map<String, Object> flow, Query query) {
		IPage<FlowModel> pages = flowEngineService.page(Condition.getPage(query), Condition.getQueryWrapper(flow, FlowModel.class)
			.select("id,category,subclass,name,status,publish_status,created,last_updated lastUpdated,last_updated_by")
//				.eq("tenant_id", AuthUtil.getTenantId())
				.eq("data_source", 1)
			.orderByDesc("last_updated"));
		return R.data(FlowModelWrapper.build().pageVO(pages));
	}

	/**
	 * 部署
	 */
	@PostMapping("/deploy")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "部署", notes = "传入模型id和分类")
	public R deploy(@ApiParam(value = "模型id") @RequestParam String modelId,
					@ApiParam(value = "工作流分类") @RequestParam String category,
					@ApiParam(value = "租户ID") @RequestParam(required = false, defaultValue = "") String tenantIds) {
		boolean temp = flowEngineService.deployModel(modelId, category, Func.toStrList(tenantIds));
		return R.status(temp);
	}

	/**
	 * 部署
	 */
	@PostMapping("/deployById")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "部署-钉钉流程的发布按钮", notes = "传入模型id和分类")
	public R deploy(@ApiParam(value = "模型id") @RequestParam String modelId, @RequestParam Boolean automaticApproval, @RequestParam String handlerType) {
		boolean temp = flowEngineService.deployModel(modelId);
		return R.status(temp);
	}

	@PostMapping("submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "保存/编辑")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "模型id"),
		@ApiImplicitParam(name = "name", value = "模型名称", required = true),
		@ApiImplicitParam(name = "modelKey", value = "模型key", required = true),
		@ApiImplicitParam(name = "description", value = "模型描述"),
		@ApiImplicitParam(name = "xml", value = "模型xml", required = true),
	})
	public R submit(@RequestBody @ApiIgnore FlowModel model) {
		if (Func.isEmpty(model.getDesignScheme())) {
			return R.fail("请先设计流程图");
		}
		if (Func.isEmpty(model.getName())) {
			return R.fail("请填写审批流名称");
		}
		if (model.getId() == null) {
			Long count = flowEngineService.count(Wrappers.lambdaQuery(FlowModel.class)
				.eq(FlowModel::getCategory, model.getCategory())
				.eq(FlowModel::getSubclass, model.getSubclass()));
			if (count > 0) {
				return R.fail("此审批流已存在，请不要重复添加");
			}
		}
		return flowEngineService.submitModel(model);
	}

	/**
	 * 自定义保存并且部署流程（钉钉流程的发布按钮）
	 *
	 * @param model
	 * @return
	 */
	@PostMapping("saveAndDeploy")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "自定义保存并且部署流程")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "模型id"),
		@ApiImplicitParam(name = "name", value = "模型名称", required = true),
		@ApiImplicitParam(name = "modelKey", value = "模型key", required = true),
		@ApiImplicitParam(name = "description", value = "模型描述"),
		@ApiImplicitParam(name = "xml", value = "模型xml", required = true),
		@ApiImplicitParam(name = "noApprover", value = "无审批人（离职/找不到人）对应系统字典表（1转交管理员；2自动通过；3自动拒绝）", required = true),
		@ApiImplicitParam(name = "deduplication", value = "是否自动去重(1去重，0不去重)", required = true),
	})
	public R saveAndDeploy(@RequestBody @ApiIgnore FlowModelDeployVO model) {
		if (Func.isEmpty(model.getDesignScheme())) {
			return R.fail("请先设计流程图");
		}
		if (Func.isEmpty(model.getName())) {
			return R.fail("请填写审批流名称");
		}

		if (model.getId() == null) {
			Long count = flowEngineService.count(Wrappers.lambdaQuery(FlowModel.class)
				.eq(FlowModel::getCategory, model.getCategory())
				.eq(FlowModel::getSubclass, model.getSubclass()));
			if (count > 0) {
				return R.fail("此审批流已存在，请不要重复添加");
			}
		}

		return flowEngineService.saveAndDeploy(model);
	}

	@GetMapping("detail")
	@ApiOperation(value = "详情")
	@ApiOperationSupport(order = 5)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "模型id", required = true),
	})
	public R<FlowModelDeployVO> detail(String id) {
		return flowEngineService.detail(id);
	}

	/**
	 * 变更流程状态（启用/禁用）
	 *
	 * @param state     状态（0启用；1禁用）
	 * @param processId 流程id
	 */
	@PostMapping("changeState")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "变更流程状态（启用/禁用）", notes = "传入state,processId")
	public R changeState(@RequestParam int state, @RequestParam String processId) {
		boolean temp = flowEngineService.update(Wrappers.lambdaUpdate(FlowModel.class)
			.eq(FlowModel::getId, processId)
			.set(FlowModel::getStatus, state));
		return R.status(temp);
	}
}
