package org.springblade.common.annotation.check.util;

import org.hibernate.validator.HibernateValidator;
import org.springframework.security.authentication.BadCredentialsException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

public class CheckParamUtil {

    public static final Validator VALIDATOR = Validation.byProvider(HibernateValidator.class).configure().failFast(false).buildValidatorFactory().getValidator();

    public static <T> void validate(T data, Class<?>... groups) {
        Set<ConstraintViolation<T>> constraintViolations;
        if (null == groups) {
            constraintViolations = VALIDATOR.validate(data);
        } else {
            constraintViolations =  VALIDATOR.validate(data, groups);
        }
        if (constraintViolations.size() > 0) {
            Map<String, Object> map = new HashMap<>();
            Iterator<ConstraintViolation<T>> iterator = constraintViolations.iterator();
            while (iterator.hasNext()) {
                ConstraintViolation<T> constraintViolation =  iterator.next();
                map.put(constraintViolation.getPropertyPath().toString(),constraintViolation.getMessage());
            }
            throw new BadCredentialsException(("参数" + map).replaceAll("=","->"));
        }
    }
}
