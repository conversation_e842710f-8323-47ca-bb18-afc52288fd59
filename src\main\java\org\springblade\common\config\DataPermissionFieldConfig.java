package org.springblade.common.config;

import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据权限字段配置
 * 用于配置不同业务表的负责人字段，支持多个负责人字段的权限控制
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Component
public class DataPermissionFieldConfig {

    /**
     * 业务表的负责人字段映射（数据库字段名格式：下划线）
     * key: 业务标识
     * value: 负责人字段列表（支持多个负责人字段）
     */
    private static final Map<String, List<String>> BUSINESS_OWNER_FIELDS = new HashMap<>();
    
    /**
     * 关联表权限配置（支持基于关联表的权限控制）
     * key: 主表业务标识
     * value: 关联表权限配置列表
     */
    private static final Map<String, List<RelatedTablePermission>> RELATED_TABLE_PERMISSIONS = new HashMap<>();

    static {
        // 报价单模块
        BUSINESS_OWNER_FIELDS.put("quotation", Arrays.asList("quotation_user_id", "create_user"));
        
        // 商机模块
        BUSINESS_OWNER_FIELDS.put("business_opportunity", Arrays.asList("sales_owner_id", "pre_sales_id", "create_user"));
        
        // 客户模块
        BUSINESS_OWNER_FIELDS.put("customer", Arrays.asList("responsible_user_id", "create_user"));
        
        // 合同模块
        BUSINESS_OWNER_FIELDS.put("contract", Arrays.asList("responsible_user_id", "create_user"));
        
        // 项目模块
        BUSINESS_OWNER_FIELDS.put("project", Arrays.asList("project_manager_id", "create_user"));
        
        // 可以继续添加其他业务模块...
    }

    /**
     * 获取业务表的负责人字段列表（数据库字段名，用于SQL查询）
     * 
     * @param businessType 业务类型
     * @return 负责人字段列表
     */
    public List<String> getOwnerFields(String businessType) {
        return getOwnerDbFields(businessType);
    }

    /**
     * 获取业务表的负责人字段列表（数据库字段名，用于SQL查询）
     * 
     * @param businessType 业务类型
     * @return 负责人字段列表
     */
    public List<String> getOwnerDbFields(String businessType) {
        return BUSINESS_OWNER_FIELDS.getOrDefault(businessType, Arrays.asList("create_user"));
    }

    /**
     * 获取业务表的负责人字段列表（Java字段名，用于反射获取值）
     * 
     * @param businessType 业务类型
     * @return 负责人字段列表（驼峰格式）
     */
    public List<String> getOwnerJavaFields(String businessType) {
        List<String> dbFields = getOwnerDbFields(businessType);
        return dbFields.stream()
                .map(this::convertToCamelCase)
                .collect(Collectors.toList());
    }

    /**
     * 将下划线格式的字段名转换为驼峰格式
     * 
     * @param underscoreName 下划线格式的字段名
     * @return 驼峰格式的字段名
     */
    private String convertToCamelCase(String underscoreName) {
        if (underscoreName == null || underscoreName.isEmpty()) {
            return underscoreName;
        }
        
        String[] parts = underscoreName.split("_");
        StringBuilder camelCase = new StringBuilder(parts[0]);
        
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i];
            if (!part.isEmpty()) {
                camelCase.append(Character.toUpperCase(part.charAt(0)));
                if (part.length() > 1) {
                    camelCase.append(part.substring(1));
                }
            }
        }
        
        return camelCase.toString();
    }


    
    /**
     * 获取关联表权限配置
     * 
     * @param businessType 业务类型
     * @return 关联表权限配置列表
     */
    public List<RelatedTablePermission> getRelatedTablePermissions(String businessType) {
        return RELATED_TABLE_PERMISSIONS.getOrDefault(businessType, new ArrayList<>());
    }
    
    /**
     * 关联表权限配置类
     */
    public static class RelatedTablePermission {
        private String joinTable;           // 关联表名
        private String joinCondition;       // 关联条件
        private List<String> ownerFields;   // 关联表中的负责人字段
        
        public RelatedTablePermission(String joinTable, String joinCondition, List<String> ownerFields) {
            this.joinTable = joinTable;
            this.joinCondition = joinCondition;
            this.ownerFields = ownerFields;
        }
        
        // getters
        public String getJoinTable() { return joinTable; }
        public String getJoinCondition() { return joinCondition; }
        public List<String> getOwnerFields() { return ownerFields; }
    }
    
    static {
        // 初始化关联表权限配置
        // 报价单关联商机权限：如果商机是我的，那么报价单也应该可以看到
        List<RelatedTablePermission> quotationRelated = new ArrayList<>();
        quotationRelated.add(new RelatedTablePermission(
            "pm_business_opportunity bo",
            "bo.id = jl_quotation.bz_op_id",
            Arrays.asList("bo.sales_owner_id", "bo.pre_sales_id", "bo.create_user")
        ));
        RELATED_TABLE_PERMISSIONS.put("quotation", quotationRelated);
    }
}