# 数据库异常处理器使用说明

## 功能概述

本项目已集成全局数据库异常处理器，能够自动将数据库约束异常转换为用户友好的中文提示信息。当发生数据库唯一索引冲突、外键约束违反、非空约束等异常时，系统会自动解析错误信息并返回清晰的中文提示。

## 支持的异常类型

### 1. 唯一索引冲突异常
**原始错误：** `Duplicate entry '111' for key 'crm_expense_report_skill.uniq'`

**转换后：** `数据保存失败：报账技巧中的"唯一标识"值【111】已存在，请使用不同的值`

### 2. 外键约束异常
**原始错误：** `Cannot delete or update a parent row: a foreign key constraint fails`

**转换后：** `删除失败：客户信息中存在关联的联系人数据，请先删除关联数据`

### 3. 非空约束异常
**原始错误：** `Column 'customer_name' cannot be null`

**转换后：** `数据保存失败："客户名称"字段不能为空`

### 4. 数据长度超限异常
**原始错误：** `Data too long for column 'content'`

**转换后：** `数据保存失败："技巧内容"字段数据长度超出限制`

## 技术实现

### 1. 核心组件

- **`GlobalExceptionHandler`**: 全局异常处理器，捕获和处理数据库异常
- **`DatabaseFieldMappingConfig`**: 数据库字段映射配置类，管理表名和字段的中文映射
- **`DatabaseMetadataUtil`**: 数据库元数据工具类，动态查询表结构和字段注释

### 2. 错误信息优先级

1. **数据库注释** - 优先使用数据库表和字段的 COMMENT 注释
2. **配置映射** - 使用预配置的中文映射关系
3. **智能推测** - 根据表名前缀和字段名称智能推测

## 配置说明

### 1. 添加表名映射

```java
@Autowired
private DatabaseFieldMappingConfig mappingConfig;

// 添加表名映射
mappingConfig.addTableMapping("your_table_name", "您的表中文名");
```

### 2. 添加索引映射

```java
// 添加索引映射
mappingConfig.addIndexMapping("your_table_name", "your_index_name", "索引中文描述");
```

### 3. 添加字段映射

```java
// 添加字段映射
mappingConfig.addFieldMapping("your_table_name", "your_field_name", "字段中文描述");
```

### 4. 数据库注释配置

建议在数据库表和字段创建时添加中文注释：

```sql
CREATE TABLE `crm_expense_report_skill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID', 
  `contact_id` bigint DEFAULT NULL COMMENT '联系人ID',
  `content` text COMMENT '技巧内容',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq` (`customer_id`,`contact_id`) COMMENT '客户联系人唯一约束'
) ENGINE=InnoDB COMMENT='报账技巧表';
```

## 已配置的映射关系

### CRM 模块
- `crm_expense_report_skill` → 报账技巧
- `crm_customer` → 客户信息
- `crm_customer_contact` → 客户联系人
- `crm_customer_care` → 客户关怀
- `crm_customer_visit` → 客户拜访

### 系统模块
- `sys_user` → 用户
- `sys_role` → 角色
- `sys_dept` → 部门
- `sys_menu` → 菜单
- `blade_dict` → 字典
- `blade_dict_biz` → 业务字典

### 项目模块
- `project_basic` → 项目基本信息
- `project_team` → 项目团队
- `project_progress` → 项目进度

## 使用示例

### 在你的错误日志中，如果看到：

```
### Error updating database.  
Cause: java.sql.SQLIntegrityConstraintViolationException: 
Duplicate entry '111' for key 'crm_expense_report_skill.uniq'
```

### 用户将收到的友好提示：

```json
{
  "code": 400,
  "success": false,
  "data": null,
  "msg": "数据保存失败：报账技巧中的"唯一标识"值【111】已存在，请使用不同的值"
}
```

## 扩展自定义映射

### 1. 在 `DatabaseFieldMappingConfig` 中添加新的映射关系

```java
private static void initTableNameMap() {
    // 添加你的表映射
    TABLE_NAME_MAP.put("your_new_table", "你的新表");
}

private static void initIndexNameMap() {
    // 添加你的索引映射
    INDEX_NAME_MAP.put("your_new_table.your_index", "你的索引描述");
}
```

### 2. 动态添加映射（适用于插件化场景）

```java
@Service
public class YourService {
    
    @Autowired
    private DatabaseFieldMappingConfig mappingConfig;
    
    @PostConstruct
    public void initCustomMappings() {
        // 动态添加映射关系
        mappingConfig.addTableMapping("plugin_table", "插件表");
        mappingConfig.addIndexMapping("plugin_table", "idx_unique", "唯一索引");
        mappingConfig.addFieldMapping("plugin_table", "plugin_name", "插件名称");
    }
}
```

## 性能优化

### 1. 元数据缓存

系统会自动缓存数据库元数据查询结果，避免重复查询影响性能。

### 2. 预加载

可以在系统启动时预加载重要表的元数据：

```java
@PostConstruct
public void preloadMetadata() {
    metadataUtil.preloadTableMetadata("crm_expense_report_skill");
    metadataUtil.preloadTableMetadata("crm_customer");
    // ... 其他重要表
}
```

### 3. 清理缓存

如果数据库结构发生变化，可以清理缓存：

```java
metadataUtil.clearCache();
```

## 注意事项

1. **数据库权限**: 确保应用有权限访问 `information_schema` 数据库
2. **数据库类型**: 当前主要支持 MySQL，其他数据库可能需要调整 SQL 语句
3. **性能考虑**: 元数据查询会有一定性能开销，建议合理使用缓存
4. **异常处理**: 如果元数据查询失败，系统会降级使用配置映射或智能推测

## 测试验证

可以通过以下方式测试异常处理器是否正常工作：

1. 尝试插入重复的唯一索引数据
2. 尝试删除有外键约束的父记录
3. 尝试插入空值到非空字段
4. 检查返回的错误信息是否为中文描述

通过这个全局异常处理器，用户将获得更友好的错误提示，有助于快速定位和解决数据问题。 