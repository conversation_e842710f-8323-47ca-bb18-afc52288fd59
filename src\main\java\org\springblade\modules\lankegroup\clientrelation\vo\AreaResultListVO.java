package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;

import java.util.List;

@Data
public class AreaResultListVO {

    /**
     * 区划编码
     */
    private String areaCode;

    /**
     * 区划名称
     */
    private String areaName;

    /**
     * 实有
     */
    private Integer actualCount;

    /**
     * 应有
     */
    private Integer oughtCount;

    /**
     * 是否地级市
     */
    private boolean parent;

    private String professionId;

    /**
     * 子类区县
     */
    private List<AreaResultListVO> childAreaList;

    public void updateActualCount() {
        if (childAreaList != null && !childAreaList.isEmpty()) {
            actualCount = childAreaList.stream()
                    .mapToInt(AreaResultListVO::getActualCount)
                    .sum();
        }
    }

    public static List<AreaResultListVO> updateActualCountForList(List<AreaResultListVO> areaResultList) {
        if (areaResultList != null && !areaResultList.isEmpty()) {
            for (AreaResultListVO areaResult : areaResultList) {
                areaResult.updateActualCount();
            }
        }
        return areaResultList;
    }
}
