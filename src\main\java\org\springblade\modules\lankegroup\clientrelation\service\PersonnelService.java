package org.springblade.modules.lankegroup.clientrelation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.PersonnelDTO;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelListVO;

import java.util.List;
import java.util.Map;

public interface PersonnelService {

    /**
     * 新增人员
     * @param personnel
     * @return
     */
    R savePersonnel(PersonnelDTO personnel);

    /**
     * 人员管理列表
     * @param page
     * @param personnelDTO
     * @return
     */
    R personnelList(IPage<PersonnelListVO> page, PersonnelDTO personnelDTO);

    R updatePersonnel(PersonnelDTO personnelDTO);

    R deletePersonnel(Long id);

    /**
     * 查询当前人员部门信息
     * @param userId
     * @return
     */
    R getUserByDept(String userId);

    /**
     * 详情
     * @param id
     * @return
     */
    R detailPersonnel(Long id);

    /**
     * 行业信息
     * @param map
     * @return
     */
    R industryInformation(Map map);

    /**
     * 区域详情
     * @param map
     * @return
     */
    R getAreaList(Map map);

    /**
     * 获取某区域市场下区划列表
     * @param map
     * @return
     */
    R getRegionalizationList(Map map);

    /**
     * 获取某区域市场下行业列表
     * @param map
     * @return
     */
    R getProfessionList(Map map);

    /**
     * 客户管理
     * 根据人员id获取 客户关系人员管理表的id
     * @param userId
     * @return
     */
    Long getIdByUserId(Long userId);

    /**
     * 查询是否创建过人员
     * @param id
     * @return
     */
    R getUserData(String id);

    /**
     *根据行业id、区域id匹配区域市场下的所有人
     * @return
     */
    List<String> getUserIdByMarketAndDeptAndProList(String marketId, String cityId);

    /**
     * 根据人员id获取市场id
     * @param userId
     * @return
     */
    String getProfessIdByUserId (Long userId);
}
