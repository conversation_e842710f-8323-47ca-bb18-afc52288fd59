package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.kingdee.bos.webapi.sdk.OperatorResult;
import org.kingdee.bos.webapi.sdk.SaveParam;
import org.kingdee.bos.webapi.sdk.SaveResult;
import org.kingdee.bos.webapi.sdk.SuccessEntity;
import org.springblade.common.constant.FlowFormConstant;
import org.springblade.common.constant.FormNameConstant;
import org.springblade.common.constant.UserIdConstant;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.enums.DocumentType;
//import org.springblade.common.enums.FormIdEnum;
import org.springblade.common.enums.OrgEnum;
import org.springblade.common.utils.AesUtil;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.business.service.impl.FlowBusinessServiceImpl;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.BladeFlowResult;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.entity.kingdee.ContractEntry;
import org.springblade.modules.lankegroup.contractManagement.entity.kingdee.KDArchive;
import org.springblade.modules.lankegroup.contractManagement.enums.ArchiveInventoryInformationGradeEnum;
import org.springblade.modules.lankegroup.contractManagement.mapper.ApproveMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveDetailsMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveSystemNameMapMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractUnionInfoMapper;
import org.springblade.modules.lankegroup.contractManagement.service.*;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveInventoryInformationVO;
import org.springblade.modules.lankegroup.disposition.entity.BusinessGroupDisposition;
import org.springblade.modules.lankegroup.disposition.mapper.BusinessGroupDispositionMapper;
import org.springblade.modules.lankegroup.equalProtectionKanBan.entity.EqualProtectionProjectTask;
import org.springblade.modules.lankegroup.equalProtectionKanBan.enums.EqualProtectionProjectTaskAllocationStatusEnum;
import org.springblade.modules.lankegroup.equalProtectionKanBan.service.IEqualProtectionProjectTaskService;
import org.springblade.modules.lankegroup.file.entity.FileModel;
import org.springblade.modules.lankegroup.file.service.ProjectFileService;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springblade.modules.lankegroup.kingdeeApi.KingDeeAPI;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IApproverService;
import org.springblade.modules.lankegroup.project.entity.ProjectProgress;
import org.springblade.modules.lankegroup.project.entity.ProjectTeam;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectTeamMapper;
import org.springblade.modules.lankegroup.project.mapper.UserProjectNodeMapper;
import org.springblade.modules.lankegroup.project.service.impl.ProjectTeamServiceImpl;
import org.springblade.modules.lankegroup.project.utils.ProjectXMJD;
import org.springblade.modules.lankegroup.project.vo.ProgressEnum;
import org.springblade.modules.lankegroup.project.vo.UserProjectNodeVO;
import org.springblade.modules.lankegroup.projectKanBan.constant.ProjectTypeV1Mapping;
import org.springblade.modules.lankegroup.projectKanBan.entity.ProjectCollection;
import org.springblade.modules.lankegroup.projectKanBan.enums.ProjectTypeV1Enum;
import org.springblade.modules.lankegroup.projectKanBan.service.IProjectCollectionService;
import org.springblade.modules.lankegroup.projectKanBan.service.IProjectKanBanMsgService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.springblade.common.constant.UserIdConstant.BUSINESS_GROUP_SFJD;

/**
 * 归档详情 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service
@AllArgsConstructor
public class ChannelArchiveDetailsServiceImpl extends BaseServiceImpl<ArchiveDetailsMapper, ArchiveDetails> implements IChannelArchiveDetailsService {
	private final IFlowService flowService;
	private final FlowBusinessServiceImpl flowBusinessService;
	private final IUserService userService;

	private final IApproverService approverService;
	private final IProjectKanBanMsgService projectKanBanMsgService;
	private final ContractServiceImpl contractService;
	private final IChannelContractService channelContractService;
	private  final KingDeeAPI kingDeeAPI;
	private final ApproveServiceImpl approveService;

	private final IEqualProtectionProjectTaskService equalProtectionProjectTaskService;
	private final ArchiveSystemNameMapMapper archiveSystemNameMapMapper;
	private final ApproveMapper approveMapper;
	private final IProjectCollectionArchiveService iProjectCollectionArchiveService;
	private final IProjectCollectionService iProjectCollectionService;
	private final IArchiveInventoryInformationService archiveInventoryInformationService;
	private final ProjectProgressMapper projectProgressMapper;
	private final UserProjectNodeMapper userProjectNodeMapper;
	private final ProjectBasicMapper projectBasicMapper;
	//项目团队的mapper
	private final ProjectTeamMapper projectTeamMapper;
	private final FlowEngineService flowEngineService;

	//项目团队的server
	private final ProjectTeamServiceImpl projectTeamService;

	private final ControlsLogService controlsLogService;
	private final ProjectFileService projectFileService;
	private final SystemMessageService systemMessageService;
	private final SendBillMsgUtil sendBillMsgUtil;
	private final ContractUnionInfoMapper contractUnionInfoMapper;
	final DecimalFormat sf = new DecimalFormat("#.00");


	private final BusinessGroupDispositionMapper businessGroupDispositionMapper;
	private final UserMapper userMapper;

	private final ArchiveDetailsServiceImpl archiveDetailsService;
	private final IHistoricProcessService historicProcessService;

	@Override
	public ArchiveDetailsVO selectArchiveDetails(Map map) {
		ArchiveDetailsVO archiveDetailsVO = baseMapper.selectArchive(map);
		if (null != archiveDetailsVO && Func.isNotBlank(archiveDetailsVO.getArchiveAnnex())) {
			try {
				Base64Util.decode(archiveDetailsVO.getArchiveAnnex());
			} catch (Exception e) {
				archiveDetailsVO.setArchiveAnnex(Base64Util.encode(AesUtil.encryptBase64(archiveDetailsVO.getArchiveAnnex())));
			}
		}
		// 查询出来这个归档下的 全部的合同清单
		List<ArchiveInventoryInformation> information = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetailsVO.getId());
		List<ArchiveInventoryInformationVO> informationVOList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(information)) {
			for (ArchiveInventoryInformation archiveInventoryInformation : information) {
				ArchiveInventoryInformationVO archiveInventoryInformationVO = BeanUtil.copy(archiveInventoryInformation, ArchiveInventoryInformationVO.class);
				// 处理归档下的 系统名称
				archiveInventoryInformationVO.setSystemNameList(archiveSystemNameMapMapper.getSystemNameDataListByInventoryId(archiveInventoryInformationVO.getId()));
				informationVOList.add(archiveInventoryInformationVO);
			}
		}
		archiveDetailsVO.setArchiveInventoryInformation(informationVOList);
		if (archiveDetailsVO.getPaymentMethod()!=null){
			if (archiveDetailsVO.getPaymentMethod().equals("1")){
				archiveDetailsVO.setPaymentMethodValue("一次性付款");
			}
			if (archiveDetailsVO.getPaymentMethod().equals("2")){
				archiveDetailsVO.setPaymentMethodValue("分期付款");
			}
		}
		if (archiveDetailsVO.getContractTypeMethod()!= null){
			if (archiveDetailsVO.getContractTypeMethod().toString().equals("1") || archiveDetailsVO.getContractTypeMethod().toString().equals("4")){
				archiveDetailsVO.setContractTypeMethodValue("信息化集成");
			}
			if (archiveDetailsVO.getContractTypeMethod().toString().equals("2")){
				archiveDetailsVO.setContractTypeMethodValue("网盾");
			}
			if (archiveDetailsVO.getContractTypeMethod().toString().equals("3")){
				archiveDetailsVO.setContractTypeMethodValue("其他");
			}
		}
		// 存放list中
		if (archiveDetailsVO.getOneCollectionPlanId() != null && archiveDetailsVO.getOneCollectionPlanId() != -1){
			ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getOneCollectionPlanId());
			extracted(archiveDetailsVO, archive);
		}
		if (archiveDetailsVO.getTwoCollectionPlanId() != null && archiveDetailsVO.getTwoCollectionPlanId() != -1){
			ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getTwoCollectionPlanId());
			extracted(archiveDetailsVO, archive);
		}
		if (archiveDetailsVO.getThreeCollectionPlanId() != null && archiveDetailsVO.getThreeCollectionPlanId() != -1){
			ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getThreeCollectionPlanId());
			extracted(archiveDetailsVO, archive);
		}
		if (archiveDetailsVO.getFourCollectionPlanId() != null && archiveDetailsVO.getFourCollectionPlanId() != -1){
			ProjectCollectionArchive archive = iProjectCollectionArchiveService.getById(archiveDetailsVO.getFourCollectionPlanId());
			extracted(archiveDetailsVO, archive);
		}


//		if (Func.isNotBlank(archiveDetailsVO.getProcessInstanceId())) {
//			List<BladeFlow> flowList = flowEngineService.getALLFlowNode(archiveDetailsVO.getProcessInstanceId());
//			// 单据为审批中，当前登录人为当前流程审批人，状态为审批中时，可审批
//			if (CollectionUtil.isNotEmpty(flowList)) {
//				BladeFlow bladeFlow = flowList.get(flowList.size() - 1);
//				if (archiveDetailsVO.getStatus() == 0 && bladeFlow.getAssignee().contains(AuthUtil.getUserId().toString())) {
//					archiveDetailsVO.setApproval(true);
//				}
//			}
//		}
		if (archiveDetailsVO.getProcessDefinitionId() != null && (!archiveDetailsVO.getProcessDefinitionId().isEmpty())) {
			//未审批
			if (archiveDetailsVO.getStatus() == ApprovedEnum.UNAPRROVED.getIndex()) {
				if (AuthUtil.getUserId().longValue() == archiveDetailsVO.getCreateUser().longValue()) {
					archiveDetailsVO.setWithdraw(true);
				} else {
					HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(archiveDetailsVO.getId());
					if (historicProcess != null) {
						if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
							archiveDetailsVO.setApproval(true);
						}
					}
				}
			}
			//审批中
			if (archiveDetailsVO.getStatus() == ApprovedEnum.APPROVING.getIndex()) {
				HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(archiveDetailsVO.getId());
				if (historicProcess != null) {
					if (historicProcess.getAssigneeId().contains(AuthUtil.getUserId().toString())) {
						archiveDetailsVO.setApproval(true);
					}
					if (AuthUtil.getUserId().longValue() == archiveDetailsVO.getCreateUser().longValue() && Func.isEmpty(historicProcess.getHistoryAssignee())){
						archiveDetailsVO.setWithdraw(true);
					}
				}
			}

			Contract contract = contractService.getById(archiveDetailsVO.getContractId());
			if (contract.getStatus() == 1 && contract.getCreateUser().equals(AuthUtil.getUserId())) {
				archiveDetailsVO.setArchive(true);
			}
		}
		archiveDetailsVO.setArchiveDetailsFiles(baseMapper.countArchiveDetailsFiles(archiveDetailsVO.getId()));
		return archiveDetailsVO;
	}

	private void extracted(ArchiveDetailsVO archiveDetailsVO, ProjectCollectionArchive archive) {
		EstimatedCollection estimatedCollection = new EstimatedCollection();
		estimatedCollection.setEstimatedCollectionAmount(archive.getEstimatedCollectionAmount());
		estimatedCollection.setEstimatedCollectionDate(archive.getEstimatedCollectionDate());
		estimatedCollection.setExpectedPaymentPercentage(archive.getExpectedPaymentPercentage());
		estimatedCollection.setPaymentCondition(archive.getPaymentCondition());
		archiveDetailsVO.getEstimatedCollectionList().add(estimatedCollection);
	}

	@Override
	public Map saveArchive(ArchiveDetailsVO archiveDetailsVO) {
		// 不质保，没有值保期
		if(archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0)) {
			archiveDetailsVO.setWarrantyPeriod(null);
		}

		ArchiveDetails archiveDetails = new ArchiveDetails();
		//转化 归档详情实体类
		ConvertUtils.register(new DateConverter(null), Date.class);
		BeanUtils.copyProperties(archiveDetailsVO,archiveDetails);

		ProjectCollectionArchive projectCollectionArchive = new ProjectCollectionArchive();
		// 去set赋值
		extractedArchive(archiveDetailsVO, projectCollectionArchive);

		List<Long> list = new ArrayList<>();
		Map<String, String> result = new HashMap<>();
		String businessTable = FlowUtil.getBusinessTable(ProcessConstant.ARCHIVE_KEY);
		BladeFlow flow = null;

		// 状态为审批中
		archiveDetails.setStatus(0);
		// 有id更新、无id保存
		if (Func.isEmpty(archiveDetails.getId())) {
			// 去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
			extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
			//当前审批人 是 当前登录人
			Long userId = AuthUtil.getUserId();
			archiveDetails.setTaskUser(userId.toString());

			save(archiveDetails);
			// 把归档的合同清单 添加到 合同清单的小表中
			List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
			for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
				archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
				ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
				archiveInventoryInformationService.save(archiveInventoryInformation);
				// 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
				saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
			}

			// 启动流程
			archiveDetailsService.initCompleteTask(archiveDetails);

			//update 2025-01-14 hz  调整文件上传
			saveArchiveFiles(archiveDetailsVO.getArchiveDetailsFiles(),archiveDetails.getId());
//			// 启动流程
//			Kv variables = Kv.create()
//					.set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserName())
//					.set("applyUser", TaskUtil.getTaskUser())
//					.set("archiveId", archiveDetails.getId());
//			flow = flowService.startProcessInstanceById(archiveDetails.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(archiveDetails.getId())), variables);
//			if (Func.isNotEmpty(flow)) {
//				log.debug("流程已启动,流程ID:" + flow.getProcessInstanceId());
//				// 返回流程id写入blade_archive_details 表中
//				archiveDetails.setProcessInstanceId(flow.getProcessInstanceId());
//				updateById(archiveDetails);
//			} else {
//				throw new ServiceException("开启流程失败");
//			}
		}else {
//			//如果是发起人重新提交则逻辑删除流程中之前的审批记录
//			if (archiveDetails.getCreateUser().equals(AuthUtil.getUserId())){
//				approveService.update(Wrappers.<Approve>update().lambda().set(Approve::getIsDeleted,1).eq(Approve::getProcessInstanceId,archiveDetails.getProcessInstanceId()));
//			}
			// 更新的话 就先去进行完成置空  添加  在进行替换
			extractedDelect(archiveDetails);
			// 去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
			extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
			if (archiveDetails.getPaymentMethod().equals("2")){
				baseMapper.update(
						archiveDetails,
						Wrappers.<ArchiveDetails>lambdaUpdate()
								.set(ArchiveDetails::getOnceCollectionPlanId, null)
								.set(ArchiveDetails::getOnceCollectionDate, null)
								.eq(ArchiveDetails::getId, archiveDetails.getId())
				);
			}
			if (archiveDetails.getPaymentMethod().equals("1")){
				baseMapper.update(
						archiveDetails,
						Wrappers.<ArchiveDetails>lambdaUpdate()
								.set(ArchiveDetails::getOneCollectionPlanId, null)
								.set(ArchiveDetails::getTwoCollectionPlanId, null)
								.set(ArchiveDetails::getThreeCollectionPlanId, null)
								.set(ArchiveDetails::getFourCollectionPlanId, null)
								.set(ArchiveDetails::getPeriodsNumber, null)
								.eq(ArchiveDetails::getId, archiveDetails.getId())
				);
			}
			if (archiveDetails.getPaymentMethod() == null || archiveDetailsVO.getPaymentMethod().isEmpty()){
				updateById(archiveDetails);
			}
			// 查询出来这个归档下的 全部的合同清单 然后删除了 从新添加
			List<ArchiveInventoryInformation> information = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetails.getId());
			archiveInventoryInformationService.removeBatchByIds(information);
			// 把归档的合同清单 添加到 合同清单的小表中
			List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
			for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
				archiveInventoryInformationVO.setId(null);
				archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
				ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
				archiveInventoryInformationService.save(archiveInventoryInformation);
				// 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
				saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
			}

			// 启动流程
			archiveDetailsService.initCompleteTask(archiveDetails);
			//update 2025-01-14 hz  删除关联附件的表数据 然后 重新添加
			baseMapper.deleteArchiveFilesByArchiveId(archiveDetails.getId());
			saveArchiveFiles(archiveDetailsVO.getArchiveDetailsFiles(),archiveDetails.getId());
		}
		result.put("contractName", archiveDetails.getName());
		if (flow != null) {
			result.put("processInstanceId", flow.getProcessInstanceId());
		} else {
			result.put("processInstanceId", archiveDetails.getProcessInstanceId());
		}
//		result.put(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserName());
//		String substring = TaskUtil.getTaskUser().substring(TaskUtil.getTaskUser().lastIndexOf("_") + 1);
//		result.put("applicantUserId", substring);
//		result.put("applicantUser", userService.getById(substring).getName());
//		result.put("applyUser", TaskUtil.getTaskUser());
		result.put("archiveId", archiveDetails.getId().toString());

		return result;
	}
	private int saveArchiveFiles(ArchiveDetailsFiles files, Long archiveDetailsId) {
		files.setArchiveId(archiveDetailsId);
		files.setId(IdWorker.getId());
		files.setCreateTime(LocalDateTime.now());
		files.setCreateUser(AuthUtil.getUserId());
		return baseMapper.saveArchiveFiles(files);
	}
	/**
	 * @param projectId      项目ID
	 * @param detailsId      归合同档ID
	 * @param inventoryId    合同清单信息ID
	 * @param systemNameList 系统名称列表
	 */
	private void saveArchiveSystemNameMap(String projectId, Long detailsId, Long inventoryId, List<ArchiveSystemNameMap> systemNameList) {
		if (CollectionUtil.isEmpty(systemNameList)) {
			return;
		}
		Long aLong = Func.isNotBlank(projectId) ? Long.valueOf(projectId) : null;;
		for (ArchiveSystemNameMap model : systemNameList) {
			ArchiveSystemNameMap archiveSystemNameMap = new ArchiveSystemNameMap();
			archiveSystemNameMap.setProjectId(aLong);
			archiveSystemNameMap.setDetailsId(detailsId);
			archiveSystemNameMap.setInventoryId(inventoryId);
			archiveSystemNameMap.setSystemName(model.getSystemName());
			archiveSystemNameMap.setIsDeleted(0);
			try {
				archiveSystemNameMapMapper.insert(archiveSystemNameMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private void deleteArchiveSystemNameMap(Long detailsId) {
		this.archiveSystemNameMapMapper.deleteByDetailsId(detailsId);
	}

	private void extracted(ArchiveDetailsVO archiveDetailsVO, ArchiveDetails archiveDetails, ProjectCollectionArchive projectCollectionArchive, List<Long> list) {
		// 付款方式（1一次性付款/2分期付款）
		if (archiveDetails.getPaymentMethod().equals("1")){
			projectCollectionArchive.setEstimatedCollectionAmount(archiveDetailsVO.getContractAmount());
			projectCollectionArchive.setEstimatedCollectionDate(archiveDetails.getOnceCollectionDate());
			// 去添加回款计划
			iProjectCollectionArchiveService.save(projectCollectionArchive);
			archiveDetails.setOnceCollectionPlanId(projectCollectionArchive.getId());
		}
		if (archiveDetails.getPaymentMethod().equals("2")){
			for (EstimatedCollection estimatedCollection : archiveDetailsVO.getEstimatedCollectionList()) {
				projectCollectionArchive.setEstimatedCollectionAmount(estimatedCollection.getEstimatedCollectionAmount());
				projectCollectionArchive.setEstimatedCollectionDate(estimatedCollection.getEstimatedCollectionDate());
				projectCollectionArchive.setExpectedPaymentPercentage(estimatedCollection.getExpectedPaymentPercentage());
				projectCollectionArchive.setPaymentCondition(estimatedCollection.getPaymentCondition());
				// 去添加回款计划
				iProjectCollectionArchiveService.save(projectCollectionArchive);
				list.add(projectCollectionArchive.getId());
				projectCollectionArchive.setId(null);
			}
			if (list.size() == 1){
				archiveDetails.setOneCollectionPlanId(list.get(0));
			}
			if (list.size() == 2){
				archiveDetails.setOneCollectionPlanId(list.get(0));
				archiveDetails.setTwoCollectionPlanId(list.get(1));
			}
			if (list.size() == 3){
				archiveDetails.setOneCollectionPlanId(list.get(0));
				archiveDetails.setTwoCollectionPlanId(list.get(1));
				archiveDetails.setThreeCollectionPlanId(list.get(2));
			}
			if (list.size() == 4){
				archiveDetails.setOneCollectionPlanId(list.get(0));
				archiveDetails.setTwoCollectionPlanId(list.get(1));
				archiveDetails.setThreeCollectionPlanId(list.get(2));
				archiveDetails.setFourCollectionPlanId(list.get(3));
			}
			archiveDetails.setPeriodsNumber(archiveDetailsVO.getPeriodsNumber());
		}
	}

	private void extracted(ArchiveDetailsVO archiveDetailsVO) {
		String url = "contract/"+archiveDetailsVO.getArchiveAnnex();
		FileModel fileModel = new FileModel(Long.valueOf(archiveDetailsVO.getProjectId()),Long.valueOf(ProgressEnum.合同签订.getCode()),
				"contract",null,archiveDetailsVO.getArchiveAnnexName(),url,"合同扫描件");
		projectFileService.saveFile(fileModel);
	}

	private ProjectProgress saveProjectData(ProjectBasic projectBasic, Integer code,UserProjectNodeVO userProjectNodeVO,ArchiveDetailsVO archiveDetailsVO){
		ProjectProgress projectProgress = new ProjectProgress();
		projectProgress.setProjectId(Long.valueOf(archiveDetailsVO.getProjectId()));
		projectProgress.setCurrentProgressKey(code);
		Long userId = userProjectNodeVO.getUserId();
		String name = userProjectNodeVO.getName();
		if (code.toString().equals(ProgressEnum.合同签订.getCode().toString())) {
			userId = projectBasic.getPrincipalId();
			name = projectProgressMapper.userName(projectBasic.getPrincipalId());
		}
		projectProgress.setStageLeaderId(userId);
		projectProgress.setStageLeaderName(name);
		Long currentProgressId =code.toString().equals(ProgressEnum.合同签订.getCode().toString()) ? archiveDetailsVO.getId() : null;
		projectProgress.setCurrentProgressId(currentProgressId);
		projectProgress.setStatus(2);
		// 前人栽树，后人乘凉
		projectProgress.setCreateUser(AuthUtil.getUserId());
		projectProgress.setCreateTime(new Date());
		projectProgressMapper.insert(projectProgress);

		// 添加到团队表中
		ProjectTeam projectTeam = new ProjectTeam();
		projectTeam.setProjectId(Long.valueOf(archiveDetailsVO.getProjectId()));
		projectTeam.setUserId(projectProgress.getStageLeaderId());
		projectTeam.setAdmin(1);
		projectTeam.setNodeId(Long.valueOf(projectProgress.getCurrentProgressKey()));
		projectTeam.setCreatePost(projectTeamMapper.UserPostId(projectProgress.getStageLeaderId()));
		projectTeamService.save(projectTeam);
		return projectProgress;
	}
	private void saveLog(Long pid, String msg, Long userId, Integer type) {
		LogModel log = new LogModel();
		log.setUserId(userId);
		log.setMsg(msg);
		log.setPid(pid);
		log.setType(type);
		controlsLogService.saveLog(log);
	}
	private void extracted(Long CollectionPlanId) {
		if (null == CollectionPlanId || "-1".equals(CollectionPlanId.toString())){
			return;
		}
		ProjectCollectionArchive byId = iProjectCollectionArchiveService.getById(CollectionPlanId);
		ProjectCollection projectCollection = new ProjectCollection();
		// 转化 归档详情实体类
		ConvertUtils.register(new DateConverter(null), Date.class);
		BeanUtils.copyProperties(byId,projectCollection);
		// 添加进 回款计划表中
		iProjectCollectionService.save(projectCollection);
	}

	@Override
	public List initContractArchiveDetails() {
		// 审批流列表
		List<Approval> approvalList = new ArrayList<>();

		//先去找  115431515452113081L  冯美娜   ,  109460333062441860L 张紫姗
		User apply1 = userService.getById(115431515452113081L);
		User apply2 = userService.getById(109460333062441860L);

//		User apply1 = userService.getById(141636619442932268L);
//		User apply2 = userService.getById(123691865482343234L);

		approvalList.add(contractService.userToApproval(apply1));
		approvalList.add(contractService.userToApproval(apply2));
		return approvalList;
	}

	@Override
	public List approvalProcess(String processInstanceId) {
		//		审批流列表
		List<Approval> approvalList = new ArrayList<>();
//        根据流程图实例id获取立项详情
		Map map = new HashMap();

		map.put("processInstanceId", processInstanceId);
//		详情
		ArchiveDetailsVO archiveDetails =  baseMapper.selectArchive(map);
//		发起人
		User apply = userService.getById(archiveDetails.getCreateUser());
//先去找  115431515452113081L  冯美娜
		User apply1 = userService.getById(115431515452113081L);
//		User apply1 = userService.getById(141636619442932268L);
//		109460333062441860L 张紫姗
		User apply2 = userService.getById(109460333062441860L);
//		User apply2 = userService.getById(123691865482343234L);

		approvalList.add(contractService.userToApproval(apply));
		approvalList.add(contractService.userToApproval(apply1));
		approvalList.add(contractService.userToApproval(apply2));
		List<Approve> selectApprove = approveMapper.selectApprove(processInstanceId);
		for (Approve approve : selectApprove) {
//			发起人
//			if (!approve.getCreateUser().toString().equals("115431515452113081") && !approve.getCreateUser().toString().equals("109460333062441860") ){
//			if (!approve.getCreateUser().toString().equals("101091622034438219") && !approve.getCreateUser().toString().equals("123691865482343234") ){
			if (approve.getRemarks().toString().equals("1") || approve.getStatus().toString().equals("3") ){
				approvalList.get(0).setApprovedStatus(approve.getStatus());
				//approvalList.get(0).setReason(approve.getComment());
				approvalList.get(0).setApproveDateStr(approve.getCreateTime());
				if (approve.getStatus().toString().equals("1")){
					approvalList.get(1).setApprovedStatus(0);
					approvalList.get(1).setReason(null);
					//approvalList.get(1).setApproveDateStr(approve.getCreateTime());
				}
			}
//			115431515452113081 冯美娜     101091622034438219  韩旭阳
//			if (approve.getCreateUser().toString().equals("141636619442932268") && approve.getRemarks().toString().equals("2")){
			if (approve.getCreateUser().toString().equals("115431515452113081") && approve.getRemarks().toString().equals("2")){
				approvalList.get(1).setApprovedStatus(approve.getStatus());
				if (approve.getStatus().toString().equals("2")) {
					approvalList.get(1).setReason(approve.getComment());
				}
				approvalList.get(1).setApproveDateStr(approve.getCreateTime());
				if (approve.getStatus().toString().equals("1")){
					approvalList.get(2).setApprovedStatus(0);
					//approvalList.get(2).setApproveDateStr(approve.getCreateTime());
				}

			}
//			109460333062441860  张紫姗  123691865482343234  孙甜甜
//			if (approve.getCreateUser().toString().equals("123691865482343234") && approve.getRemarks().toString().equals("2") && !approve.getStatus().toString().equals("3")){
			if (approve.getCreateUser().toString().equals("109460333062441860") && approve.getRemarks().toString().equals("2") && !approve.getStatus().toString().equals("3")){
				approvalList.get(2).setApprovedStatus(approve.getStatus());
				if (approve.getStatus().toString().equals("2")) {
					approvalList.get(2).setReason(approve.getComment());
				}
				approvalList.get(2).setApproveDateStr(approve.getCreateTime());
			}
		}

		if (approvalList.get(0).getApprovedStatus().toString().equals("3")){
			approvalList.get(0).setApprovedStatus(3);
			approvalList.get(0).setReason(null);
			approvalList.remove(1);
			approvalList.remove(1);
		}
		return approvalList;
	}

	/**
	 * 原流程不变，现增加一个流程：已归档后能修改系统名称（已归档后和等保看板有联系，所以不能采用先清除后创建的策略！！！）
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateArchive(ArchiveDetailsVO archiveDetailsVO) {
		// 状态（0审批中/1已审批/2已驳回/3撤回）
		if (1 != archiveDetailsVO.getStatus()) {
			// 原来的正常流程
			return updateArchiveByOriginalProcess(archiveDetailsVO);
		} else {
			// 已审批只能修改系统名称
			return updateArchiveInventory(archiveDetailsVO).isSuccess();
		}
	}


	/**
	 * 原修改流程
	 */
	public Boolean updateArchiveByOriginalProcess(ArchiveDetailsVO archiveDetailsVO) {
		ArchiveDetails archiveDetails = new ArchiveDetails();
		List<Long> list = new ArrayList<>();
		//转化 归档详情实体类
		ConvertUtils.register(new DateConverter(null), Date.class);
		BeanUtils.copyProperties(archiveDetailsVO,archiveDetails);

		ProjectCollectionArchive projectCollectionArchive = new ProjectCollectionArchive();
		//	去set赋值
		extractedArchive(archiveDetailsVO, projectCollectionArchive);

		//更新的话 就先去进行完成置空   添加  在进行替换
		extractedDelect(archiveDetails);
		//去添加回款计划到 临时表 中 然后把归档详情表中的 回款计划id进行替换
		extracted(archiveDetailsVO, archiveDetails, projectCollectionArchive, list);
		// 查询出来这个归档下的 全部的合同清单 然后删除了 从新添加
		List<ArchiveInventoryInformation> information =
				archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetails.getId());
		archiveInventoryInformationService.removeBatchByIds(information);
		// 查询出来这个归档下的保测评总系统个数 对应的系统名称  保存到映射表，然后删除，重新添加
		deleteArchiveSystemNameMap(archiveDetails.getId());
		// 把归档的合同清单 添加到 合同清单的小表中
		List<ArchiveInventoryInformationVO> informationVOList = archiveDetailsVO.getArchiveInventoryInformation();
		for (ArchiveInventoryInformationVO archiveInventoryInformationVO : informationVOList) {
			archiveInventoryInformationVO.setId(null);
			archiveInventoryInformationVO.setDetailsId(archiveDetails.getId());
			ArchiveInventoryInformation archiveInventoryInformation = BeanUtil.copy(archiveInventoryInformationVO, ArchiveInventoryInformation.class);
			archiveInventoryInformationService.save(archiveInventoryInformation);
			// 把 合同清单信息 保测评总系统个数 对应的系统名称  保存到映射表
			saveArchiveSystemNameMap(archiveDetailsVO.getProjectId(), archiveDetails.getId(), archiveInventoryInformation.getId(), archiveInventoryInformationVO.getSystemNameList());
		}
		int update = 0;
		if (archiveDetails.getPaymentMethod().equals("2")){
			 update = baseMapper.update(
					archiveDetails,
					Wrappers.<ArchiveDetails>lambdaUpdate()
							.set(ArchiveDetails::getOnceCollectionPlanId, null)
							.set(ArchiveDetails::getOnceCollectionDate, null)
							.set(archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0), ArchiveDetails::getWarrantyPeriod, null)
							.eq(ArchiveDetails::getId, archiveDetails.getId())
			);
		}
		if (archiveDetails.getPaymentMethod().equals("1")){
			 update = baseMapper.update(
					archiveDetails,
					Wrappers.<ArchiveDetails>lambdaUpdate()
							.set(ArchiveDetails::getOneCollectionPlanId, null)
							.set(ArchiveDetails::getTwoCollectionPlanId, null)
							.set(ArchiveDetails::getThreeCollectionPlanId, null)
							.set(ArchiveDetails::getFourCollectionPlanId, null)
							.set(ArchiveDetails::getPeriodsNumber, null)
							.set(archiveDetailsVO.getIsWarranty() == null || archiveDetailsVO.getIsWarranty().equals(0), ArchiveDetails::getWarrantyPeriod, null)
							.eq(ArchiveDetails::getId, archiveDetails.getId())
			);
		}
		if (archiveDetails.getPaymentMethod() == null || archiveDetailsVO.getPaymentMethod().isEmpty()){
			return updateById(archiveDetails);
		}
		return (update == 1);
	}

	/**
	 * 已归档情况下，只修改系统名称
	 */
	private R updateArchiveInventory(ArchiveDetailsVO archiveDetailsVO) {
		// 只有归档创建人有权限
		BladeUser user = AuthUtil.getUser();
		if (Func.hasEmpty(user, user.getUserId(), archiveDetailsVO.getCreateUser()) || !archiveDetailsVO.getCreateUser().equals(user.getUserId())) {
			return R.fail("暂无权限");
		}

		List<ArchiveInventoryInformationVO> newInformationList = archiveDetailsVO.getArchiveInventoryInformation();
		if (CollectionUtil.isEmpty(newInformationList)) {
			return R.fail("参数中未发现合同清单");
		}
		for (ArchiveInventoryInformationVO informationVO : newInformationList) {
			List<ArchiveSystemNameMap> systemNameList = informationVO.getSystemNameList();
			if (CollectionUtil.isEmpty(systemNameList)) {
				continue;
			}
			for (ArchiveSystemNameMap systemNameMap : systemNameList) {
				if (Func.isNotBlank(systemNameMap.getSystemName())) {
					archiveSystemNameMapMapper.updateSystemNameById(systemNameMap.getId(), systemNameMap.getSystemName());
				}
			}
		}
		return R.success("操作成功");
	}

	private void extractedArchive(ArchiveDetailsVO archiveDetailsVO, ProjectCollectionArchive projectCollectionArchive) {
		// 负责人名称
		projectCollectionArchive.setName(archiveDetailsVO.getUserName());
		// 负责人id
		projectCollectionArchive.setUserId(archiveDetailsVO.getUserId());
		// 项目id
		projectCollectionArchive.setProjectId(archiveDetailsVO.getProjectId());
		// 客户id
		projectCollectionArchive.setCustomerId(archiveDetailsVO.getCustomerId());
	}

	private void extractedDelect(ArchiveDetails archiveDetails) {
		if (archiveDetails.getOnceCollectionPlanId() != null && archiveDetails.getOnceCollectionPlanId() != -1){
			archiveDetails.setOnceCollectionPlanId(null);
//			archiveDetails.setOnceCollectionDate(null);
		}
		if (archiveDetails.getOneCollectionPlanId() != null && archiveDetails.getOneCollectionPlanId() != -1){
			archiveDetails.setOneCollectionPlanId(null);
			archiveDetails.setPeriodsNumber(null);
		}
		if (archiveDetails.getTwoCollectionPlanId() != null && archiveDetails.getTwoCollectionPlanId() != -1){
			archiveDetails.setTwoCollectionPlanId(null);
			archiveDetails.setPeriodsNumber(null);
		}
		if (archiveDetails.getThreeCollectionPlanId() != null && archiveDetails.getThreeCollectionPlanId() != -1){
			archiveDetails.setThreeCollectionPlanId(null);
			archiveDetails.setPeriodsNumber(null);
		}
		if (archiveDetails.getFourCollectionPlanId() != null && archiveDetails.getFourCollectionPlanId() != -1){
			archiveDetails.setFourCollectionPlanId(null);
			archiveDetails.setPeriodsNumber(null);
		}
	}
	@Override
	public List<ContractChange> contractChange(Long contractId) {
		return baseMapper.contractChange(contractId);
	}

	/**
	 * 将接口返回参数对接到金蝶接口参数上
	 *
	 * @param archiveDetailsVO 接口实体类
	 * @return 金蝶接口参数
	 */
	private KDArchive convert(ArchiveDetailsVO archiveDetailsVO) {
		//立项流程单据创建时间
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		String day = df.format((new Date()));
		KDArchive bill = new KDArchive();

		bill.setFName(archiveDetailsVO.getName());
		bill.setFValiStartDate(archiveDetailsVO.getServiceStartDate());
		bill.setFValiEndDate(archiveDetailsVO.getServiceEndDate());
		bill.setFDate(day);
		// 实际业务组织==签订公司
		Map map1=new HashMap();
		map1.put("id",archiveDetailsVO.getContractId());
		Contract contract = channelContractService.selectDetail(map1);
		if(contract!=null){
			bill.getF_SJYWZZ().put("FNumber", contract.getPartyBUnitCode());
		}
		// 备注==归档时填的备注
		bill.setF_RWSQ_Remarks(archiveDetailsVO.getRemarks());
		// 销售组织
		bill.getFSALEORGID().put("FNumber", OrgEnum.LK.getCode());
		// 客户
		Map companyCode = baseMapper.selectSignCompanyCode(archiveDetailsVO.getContractId());
		bill.getFBDCUSTID().put("FNumber", companyCode.get("signCompanyCode"));
		// 项目
		bill.getF_XMDA().put("FNumber", companyCode.get("code"));
		// 合同明细
		// 项目进度收入
		if (archiveDetailsVO.getProjectProgressIncome() !=null && !archiveDetailsVO.getProjectProgressIncome().isEmpty() ){
			ContractEntry contractEntry1 = new ContractEntry();
			contractEntry1.getFSettleOrgId().put("FNumber",OrgEnum.LK.getCode());
			contractEntry1.getFMaterialId().put("FNumber","29006172");
			contractEntry1.setFQty(1);
			contractEntry1.setFTaxPrice(Double.parseDouble(archiveDetailsVO.getProjectProgressIncome()));
			contractEntry1.setFEntryTaxRate(0);
			bill.getFCRMContractEntry().add(contractEntry1);
		}

		// 技术服务费
		if (archiveDetailsVO.getTechnicalServiceFee() !=null && !archiveDetailsVO.getTechnicalServiceFee().isEmpty() ){
			ContractEntry contractEntry2 = new ContractEntry();
			contractEntry2.getFSettleOrgId().put("FNumber",OrgEnum.LK.getCode());
			contractEntry2.getFMaterialId().put("FNumber","8804005973");
			contractEntry2.setFQty(1);
			contractEntry2.setFTaxPrice(Double.parseDouble(archiveDetailsVO.getTechnicalServiceFee()));
			contractEntry2.setFEntryTaxRate(0);
			bill.getFCRMContractEntry().add(contractEntry2);
		}
		for (ArchiveInventoryInformation inventoryInformation : archiveDetailsVO.getArchiveInventoryInformation()) {
			// 等保测评二级服务
			if (inventoryInformation.getSecondaryServiceNumber() != null && !inventoryInformation.getSecondaryServiceNumber().isEmpty()
					&& inventoryInformation.getSecondaryServicePrice() !=null && !inventoryInformation.getSecondaryServicePrice().isEmpty()
					&& inventoryInformation.getSecondaryServiceOrder() !=null && !inventoryInformation.getSecondaryServiceOrder().isEmpty()){
				ContractEntry contractEntry3 = new ContractEntry();
				contractEntry3.getFSettleOrgId().put("FNumber",OrgEnum.LK.getCode());
				contractEntry3.getFMaterialId().put("FNumber","2900001");
				contractEntry3.setFQty(Integer.valueOf(inventoryInformation.getSecondaryServiceNumber()) * Integer.valueOf(inventoryInformation.getSecondaryServiceOrder()) );
				contractEntry3.setFTaxPrice(Double.parseDouble(inventoryInformation.getSecondaryServicePrice()));
				contractEntry3.setFEntryTaxRate(0);
				bill.getFCRMContractEntry().add(contractEntry3);
			}
			// 等保测评三级服务
			if (inventoryInformation.getTertiaryServiceNumber() != null && !inventoryInformation.getTertiaryServiceNumber().isEmpty()
					&& inventoryInformation.getTertiaryServicePrice() !=null && !inventoryInformation.getTertiaryServicePrice().isEmpty()
					&& inventoryInformation.getTertiaryServiceOrder() !=null && !inventoryInformation.getTertiaryServiceOrder().isEmpty()
			){
				ContractEntry contractEntry4 = new ContractEntry();
				contractEntry4.getFSettleOrgId().put("FNumber",OrgEnum.LK.getCode());
				contractEntry4.getFMaterialId().put("FNumber","2900002");
				contractEntry4.setFQty(Integer.valueOf(inventoryInformation.getTertiaryServiceNumber()) * Integer.valueOf(inventoryInformation.getTertiaryServiceOrder()));
				contractEntry4.setFTaxPrice(Double.parseDouble(inventoryInformation.getTertiaryServicePrice()));
				contractEntry4.setFEntryTaxRate(0);
				bill.getFCRMContractEntry().add(contractEntry4);
			}
		}
		return bill;
	}

	private List<Integer> getTargetArray(List<Integer> orderList, String orderNumber, Integer size, String serviceNumber, String price, List<String> expectMoneyList) {
		Integer orderNumberTemp = Func.isNotBlank(orderNumber) ? Integer.valueOf(orderNumber) : 0;
		Integer serviceNumberTemp = Func.isNotBlank(serviceNumber) ? Integer.valueOf(serviceNumber) : 0;
		for (int i = 0; i < size; i++) {
			if (orderNumberTemp > i) {
				orderList.set(i, orderList.get(i) + serviceNumberTemp);
				expectMoneyList.set(i, String.valueOf(Integer.parseInt(expectMoneyList.get(i)) + (Integer.parseInt(price) * serviceNumberTemp)));
			}
		}
		return orderList;
	}

	private BigDecimal calculateTargetValue(String price, String number) {
		BigDecimal priceBd = new BigDecimal(price);
		BigDecimal numberBd = new BigDecimal(number);
		BigDecimal multiply = priceBd.multiply(numberBd);
		return multiply;
	}


	/**
	 *
	 */
	private void createProjectTaskData(ArchiveDetailsVO archiveDetailsVO, ProjectBasic projectBasic) {
		// 需要生成几个项目任务
		List<ArchiveInventoryInformation> list = null;
		try {
			list = archiveInventoryInformationService.selectArchiveInventoryInformation(archiveDetailsVO.getId());
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 获取二级或者三级等保测评的最大次数
		long secondaryServiceMaxNumber = 0L;
		long tertiaryServiceMaxNumber = 0L;
		BigDecimal secondaryServicePrice = BigDecimal.ZERO;
		BigDecimal tertiaryServicePrice = BigDecimal.ZERO;
		if (CollectionUtil.isNotEmpty(list)) {
			secondaryServiceMaxNumber = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServiceNumber()) ? e.getSecondaryServiceNumber() : StringPool.ZERO).map(Integer::valueOf).reduce(Integer::sum).orElse(0);
			tertiaryServiceMaxNumber = list.stream().map(e -> Func.isNotBlank(e.getTertiaryServiceNumber()) ? e.getTertiaryServiceNumber() : StringPool.ZERO).map(Integer::valueOf).reduce(Integer::sum).orElse(0);
			secondaryServicePrice = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServicePrice()) ? calculateTargetValue(e.getSecondaryServicePrice(), e.getSecondaryServiceNumber()) : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
			tertiaryServicePrice = list.stream().map(e -> Func.isNotBlank(e.getTertiaryServicePrice()) ? calculateTargetValue(e.getTertiaryServicePrice(), e.getTertiaryServiceNumber()) : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add);
			if (secondaryServiceMaxNumber < 0L) {
				secondaryServiceMaxNumber = 0L;
			}
			if (tertiaryServiceMaxNumber < 0L) {
				tertiaryServiceMaxNumber = 0L;
			}
			if (secondaryServicePrice == null) {
				secondaryServicePrice = BigDecimal.ZERO;
			}
			if (tertiaryServicePrice == null) {
				tertiaryServicePrice = BigDecimal.ZERO;
			}
		}
		BigDecimal secondaryAndTertiaryServicePrice = secondaryServicePrice.add(tertiaryServicePrice);


		Integer mAxOrder = 0;
		if (CollectionUtil.isNotEmpty(list)) {
			mAxOrder = list.stream().map(e -> Func.isNotBlank(e.getSecondaryServiceOrder()) ? e.getSecondaryServiceOrder() : e.getTertiaryServiceOrder()).map(Integer::valueOf).max(Integer::compareTo).orElse(0);
			if (mAxOrder == null || mAxOrder < 0) {
				mAxOrder = 0;
			}
		}
//		boolean isNoHasMaxOrder = (mAxOrder == 0);
//
//
//		List<Integer> secondaryServiceList = null;
//		List<Integer> tertiaryServiceList = null;
//		List<String> expectMoneyList = null;
//		if (!isNoHasMaxOrder) {
//			secondaryServiceList = new ArrayList(Collections.nCopies(mAxOrder, 0));
//			tertiaryServiceList = new ArrayList(Collections.nCopies(mAxOrder, 0));
//			expectMoneyList = new ArrayList(Collections.nCopies(mAxOrder, StringPool.ZERO));
//			for (ArchiveInventoryInformation information : list) {
//				if (ArchiveInventoryInformationGradeEnum.等保二级.getCode().equals(information.getGrade())) {
//					getTargetArray(secondaryServiceList, information.getSecondaryServiceOrder(), mAxOrder, information.getSecondaryServiceNumber(),
//							information.getSecondaryServicePrice(), expectMoneyList);
//				}
//				if (ArchiveInventoryInformationGradeEnum.等保三级.getCode().equals(information.getGrade())) {
//					getTargetArray(tertiaryServiceList, information.getTertiaryServiceOrder(), mAxOrder, information.getTertiaryServiceNumber(),
//							information.getTertiaryServicePrice(), expectMoneyList);
//				}
//			}
//		}

		for (int i = 0; i < mAxOrder; i++) {
			EqualProtectionProjectTask model = new EqualProtectionProjectTask();
			model.setProjectId(projectBasic.getId());
			model.setProjectName(projectBasic.getProjectName());
			model.setKdProjectFid(projectBasic.getKdProjectFid());
			model.setArchiveDetailsId(archiveDetailsVO.getId());
			int iTemp = i;
			// model.setSecondaryServiceNumber(Optional.ofNullable(secondaryServiceList).map(e -> e.get(iTemp)).orElse(0).toString());
			model.setSecondaryServiceNumber(String.valueOf(secondaryServiceMaxNumber));
			model.setAllocatedSecondaryServiceNumber(StringPool.ZERO);
			// model.setTertiaryServiceNumber(Optional.ofNullable(tertiaryServiceList).map(e -> e.get(iTemp)).orElse(0).toString());
			model.setTertiaryServiceNumber(String.valueOf(tertiaryServiceMaxNumber));
			model.setAllocatedTertiaryServiceNumber(StringPool.ZERO);
			model.setProjectTaskOrder(iTemp + 1);
			model.setAllocationStatus(EqualProtectionProjectTaskAllocationStatusEnum.未分配.getCode());
			model.setAllocationTime(null);
			// model.setExpectMoney(Optional.ofNullable(expectMoneyList).map(e -> e.get(iTemp)).orElse(StringPool.ZERO));
			model.setExpectMoney(secondaryAndTertiaryServicePrice.setScale(2, RoundingMode.HALF_UP).toPlainString());
			model.setAllocatedMoney(StringPool.ZERO);
			model.setExpectAllocationTime(i == 0 ? DateUtil.now() : DateUtil.plusYears(DateUtil.parse(DateUtil.format(DateUtil.now(), "yyyy-01-01"), DateUtil.DATE_FORMAT), i));
			try {
				equalProtectionProjectTaskService.save(model);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return;
	}
	public void saveContractUnionInfo(Long contractId, String billId){
		Contract contract = contractService.getById(contractId);
		ProjectBasic basic = projectBasicMapper.selectById(contract.getProjectId());
		ContractUnionInfo info = contractUnionInfoMapper.getLatestContract(basic.getId());
		ContractUnionInfo unionInfo = new ContractUnionInfo();
		unionInfo.setProjectId(basic.getId());
		unionInfo.setKdProjectFid(basic.getKdProjectFid());
		unionInfo.setContractId(contract.getId());
//		unionInfo.setKdContractFid(Long.valueOf(billId));
		unionInfo.setContractAmount(new BigDecimal(Double.valueOf(contract.getContractAmount())));
		unionInfo.setKdContractAmount(new BigDecimal(Double.valueOf(contract.getContractAmount())));
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		String day = df.format((new Date()));
		try {
			unionInfo.setKdDate(DateUtils.parseDate(day, DateUtils.DATE_FORMAT_10));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		unionInfo.setApproveDate(new Date());
		if(info != null){
			unionInfo.setContractNumber(info.getContractNumber() + 1);
		}else{
			unionInfo.setContractNumber(1);
		}
		contractUnionInfoMapper.saveContractUnionInfo(unionInfo);
	}
}
