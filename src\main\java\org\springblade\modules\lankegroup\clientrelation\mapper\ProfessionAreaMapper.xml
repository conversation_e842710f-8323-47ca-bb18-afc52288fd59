<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionAreaMapper">

    <delete id="deleteProfessionAreas">
        delete from blade_profession_area where join_id in (
        <foreach collection="ids" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </delete>

    <select id="geAreaListByDeptId" resultType="java.lang.Long">
        SELECT area.counties_code
        FROM blade_profession AS pro -- 区域市场
                 LEFT JOIN blade_join_profession AS j ON j.profession_id = pro.id -- 中间表
                 LEFT JOIN blade_profession_area AS area ON area.join_id = j.id -- 区域表
        WHERE pro.charge_dept_id = #{deptId}
    </select>
</mapper>
