package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.ProfessionDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.*;
import org.springblade.modules.lankegroup.clientrelation.mapper.CustomerTaskMapper;
import org.springblade.modules.lankegroup.clientrelation.mapper.PersonnelMapper;
import org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionMapper;
import org.springblade.modules.lankegroup.clientrelation.service.*;
import org.springblade.modules.lankegroup.clientrelation.vo.*;
import org.springblade.modules.lankegroup.crm.service.ICustomerChargeUserService;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
public class ProfessionServiceImpl extends BaseServiceImpl<ProfessionMapper, Profession> implements ProfessionService {

    private final ProfessionMapper professionMapper;

    private final ProfessionJoinService professionJoinService;

    private final ProfessionAreaService professionAreaService;

    private final ProfessionMarketService professionMarketService;

    private final MarketplaceService marketplaceService;

    private final AreaService areaService;

    private final DeptByUserService deptByUserService;

    private final IUserService userService;

    private final PersonnelMapper personnelMapper;

    private final CustomerTaskMapper customerTaskMapper;

    /**
     * 新增区域市场
     *
     * @param professionDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveProfession(ProfessionDTO professionDTO) {
        if (null == professionDTO.getJoinList() || professionDTO.getJoinList().size() < 1) {
            return R.fail("缺少参数");
        }
        // 查询部门是否创建区域市场
        QueryWrapper<Profession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("charge_dept_id", professionDTO.getChargeDeptId());
        queryWrapper.eq("is_deleted", 0);
        List<Profession> professions = professionMapper.selectList(queryWrapper);
        if (null != professions && professions.size() > 0) {
            return R.fail("本部门已创建区域市场");
        }
        // 查询部门是否创建区域市场
        QueryWrapper<Profession> queryWrapperName = new QueryWrapper<>();
        queryWrapperName.eq("profession_name", professionDTO.getProfessionName());
        queryWrapperName.eq("is_deleted", 0);
        List<Profession> professionsName = professionMapper.selectList(queryWrapperName);
        if (null != professionsName && professionsName.size() > 0) {
            return R.fail("区域市场名称不能重复");
        }
        professionDTO.setCreateUser(AuthUtil.getUserId());
        professionDTO.setCreateTime(new Date());
        professionDTO.setUpdateTime(new Date());
        professionDTO.setUpdateUser(AuthUtil.getUserId());
        professionDTO.setCreateDept(Long.valueOf(AuthUtil.getDeptId().split(",")[0]));
        professionDTO.setStatus(0);
        // 新增市场
        if (professionMapper.insert(professionDTO) > 0) {
            saveProfessionExtend(professionDTO);
        }
        Controls controls = new Controls();
        controls.setContent("创建了区域市场");
        controls.setTargetId(String.valueOf(professionDTO.getId()));
        controls.setUserId(String.valueOf(AuthUtil.getUserId()));
        controls.setUserName(AuthUtil.getNickName());
        controls.setCreateTime(new Date());
        areaService.saveControlsLog(controls);
        return R.success("新增市场成功");
    }

    /**
     * 区域市场列表
     *
     * @param professionDTO
     * @param page
     * @return
     */
    @Override
    public R getProfessionList(ProfessionDTO professionDTO, IPage<ProfessionVO> page) {
        List<String> specialList = deptByUserService.getSpecialList();
        if (!specialList.contains(AuthUtil.getUserId().toString())) {
            List<Long> userIdByLevel = deptByUserService.getUserIdByLevel();
            userIdByLevel.add(AuthUtil.getUserId());
            professionDTO.setUserList(userIdByLevel);
            professionDTO.setDeptList(Arrays.asList(AuthUtil.getDeptId().split(",")));
        }
        IPage<ProfessionVO> professionList = professionMapper.getProfessionList(professionDTO, page);
        return R.data(professionList);
    }

    @Override
    public R checkMarketAndArea(ProfessionDTO professionDTO) {
        List<String> strings = baseMapper.checkMarketAndArea(professionDTO.getProfessionId(), professionDTO.getList());
        return R.data(strings);
    }

    /**
     * 编辑区域市场
     *
     * @param professionDTO
     * @return
     */
    @Override
    public R updateProfession(ProfessionDTO professionDTO) {
        if (null == professionDTO.getJoinList() || professionDTO.getJoinList().size() < 1) {
            return R.fail("缺少参数");
        }
        // 查询部门是否创建区域市场
        QueryWrapper<Profession> queryWrapperName = new QueryWrapper<>();
        queryWrapperName.eq("profession_name", professionDTO.getProfessionName());
        queryWrapperName.eq("is_deleted", 0);
        List<Profession> professionsName = professionMapper.selectList(queryWrapperName);
        if (null != professionsName && professionsName.size() > 0 && !professionsName.get(0).getId().equals(professionDTO.getId())) {
            return R.fail("区域市场名称不能重复");
        }
        // 判断删除的行业ID人员是否绑定
        if (null != professionDTO.getNotMarketList() && professionDTO.getNotMarketList().size() > 0) {
            List<String> marketByPersonnelCount = professionMapper.getMarketByPersonnelCount(professionDTO.getNotMarketList(), professionDTO.getId());
            if(null != marketByPersonnelCount && marketByPersonnelCount.size() > 0){
                return R.fail("本区域市场的区域行业已分配至" + Joiner.on(",").join(marketByPersonnelCount) + "，请将人员取消分配后再更新区域市场");
            }
        }
        // 删除区域市场行业、关联、区县数据
        deleteProfessionExtend(professionDTO.getId());
        if (professionMapper.updateById(professionDTO) > 0) {
            // 新增区域市场行业、关联、区县数据
            saveProfessionExtend(professionDTO);
            // 编辑人员表区划市场
            personnelMapper.updatePersonnelByProfession(professionDTO.getId().toString(), professionDTO.getProfessionName());
        }
        Controls controls = new Controls();
        controls.setContent("编辑了区域市场");
        controls.setTargetId(String.valueOf(professionDTO.getId()));
        controls.setUserId(String.valueOf(AuthUtil.getUserId()));
        controls.setUserName(AuthUtil.getNickName());
        controls.setCreateTime(new Date());
        areaService.saveControlsLog(controls);
        return R.success("编辑区域市场成功");
    }

    @Override
    public R deleteProfession(String id) {
        Profession profession = professionMapper.selectById(id);
        if (null == profession) {
            return R.fail("暂无数据信息");
        }
        QueryWrapper<Personnel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profession_id", id);
        queryWrapper.eq("is_deleted", 0);
        List<Personnel> personnels = personnelMapper.selectList(queryWrapper);
        if (null != personnels && personnels.size() > 0) {
            return R.fail("该区域市场下已添加人员，暂时无法删除");
        }
//        // 清空市场下实有客户负责人
//        customerChargeUserService.deletedProfessionToChargeUser(profession.getId(), Long.valueOf(profession.getChargeDeptId()));
        // 删除区域市场数据
        if (professionMapper.deleteById(id) > 0) {
            // 删除区域市场行业、关联、区县数据
            deleteProfessionExtend(Long.valueOf(id));
            return R.success("删除区域市场成功");
        }
        return R.fail("删除区域市场失败");
    }

    @Override
    public R getProfessionListByDept(String deptId) {
        return R.data(professionMapper.getProfessionListByDept(deptId));
    }

    /**
     * 新增区域市场行业和区域市场区县
     *
     * @param professionDTO
     */
    private void saveProfessionExtend(ProfessionDTO professionDTO) {
        professionDTO.getJoinList().stream().forEach(join -> {
            // 新增市场中间关系
            ProfessionJoin professionJoin = new ProfessionJoin();
            professionJoin.setProfessionId(String.valueOf(professionDTO.getId()));
            ProfessionJoin professionJoinResult = professionJoinService.saveProfession(professionJoin);
            if (null != professionJoinResult && null != professionJoinResult.getId()) {
                // 新增区域
                join.getAreaList().stream().forEach(area -> {
                    if (null == area.getCountiesCode()) {
                        area.setCountiesCode(area.getCityCode() + "00");
                        area.setCountiesName(area.getCityName());
                    }
                    area.setJoinId(String.valueOf(professionJoin.getId()));
                });
                professionAreaService.saveProfessionArea(join.getAreaList());
                // 新增行业
                join.getMarketList().stream().forEach(area -> {
                    area.setJoinId(String.valueOf(professionJoin.getId()));
                });
                professionMarketService.saveProfessionMarket(join.getMarketList());
                // 根据区域市场ID和行业ID查询对应人员信息
                join.getAreaList().stream().forEach(area -> {
                    join.getMarketList().stream().forEach(market -> {
                        List<Personnel> professionIdByPersonnelList = professionMapper.getProfessionIdByPersonnelList(professionDTO.getId(), market.getMarketCode());
                        // 编辑人员行业对应应有客户数量
                        if (null != professionIdByPersonnelList && professionIdByPersonnelList.size() > 0) {
                            professionMapper.updatePersonnelCustomerNum(professionIdByPersonnelList.stream().map(Personnel::getId).collect(Collectors.toList()), market.getMarketCode(),area.getCountiesCode(), market.getNum());
                        }
                    });
                });

            }
        });
    }

    /**
     * 删除区域市场行业和区县
     *
     * @param id
     */
    private void deleteProfessionExtend(Long id) {
        // 根据行业市场ID查询关联信息
        List<Long> joinList = professionJoinService.getJoinList(id);
        if (null != joinList && joinList.size() > 0) {
            // 删除行业信息
            professionMarketService.deleteProfessionMarkets(joinList);
            // 删除区县信息
            professionAreaService.deleteProfessionAreas(joinList);
            // 删除关联信息
            professionJoinService.deleteProfessionJoin(id);
        }
    }

    @Override
    public List<Profession> selectByAreAndMarketForProfession(Map map) {
        return baseMapper.selectByAreAndMarketForProfession(map);
    }

    @Override
    public R detailProfession(Long id) {
        Profession profession = baseMapper.selectById(id);
        ProfessionListVO professionListVO = new ProfessionListVO();
        professionListVO.setParent(false);
        List<String> specialList = deptByUserService.getSpecialList();
        if (specialList.contains(AuthUtil.getUserId().toString())) {
            professionListVO.setParent(true);
        } else {
            // 判断当前登录人是否是创建人的上级
            List<Long> userIdByLevel = deptByUserService.getUserIdByLevel();
            if (userIdByLevel.contains(profession.getCreateUser())) {
                professionListVO.setParent(true);
            }
        }

        if (null != profession) {
            BeanUtils.copyProperties(profession, professionListVO);
            professionListVO.setCreateId(profession.getCreateUser().toString());
            User byId = userService.getById(AuthUtil.getUserId());
            professionListVO.setCreateName(byId.getName());
            // 获取市场行业关联表
            List<Long> joinList = professionJoinService.getJoinList(profession.getId());
            if (null != joinList && joinList.size() > 0) {
                List<ProfessionJoinVO> joinVOList = new ArrayList<>();
                joinList.stream().forEach(join -> {
                    ProfessionJoinVO professionJoinVO = new ProfessionJoinVO();
                    professionJoinVO.setJoinId(join.toString());
                    // 查询行业列表
                    QueryWrapper<ProfessionMarket> marketQueryWrapper = new QueryWrapper<>();
                    marketQueryWrapper.eq("is_deleted", 0);
                    marketQueryWrapper.eq("join_id", join);
                    List<ProfessionMarket> marketList = professionMarketService.list(marketQueryWrapper);
                    professionJoinVO.setMarketList(marketList);
                    // 查询区域列表
                    QueryWrapper<ProfessionArea> areaQueryWrapper = new QueryWrapper<>();
                    areaQueryWrapper.eq("is_deleted", 0);
                    areaQueryWrapper.eq("join_id", join);
                    List<ProfessionArea> areaList = professionAreaService.list(areaQueryWrapper);
                    professionJoinVO.setAreaList(areaList);
                    joinVOList.add(professionJoinVO);
                });
                professionListVO.setJoinVOList(joinVOList);
            }
        }

        return R.data(professionListVO);
    }

    /**
     * 行业信息
     *
     * @param map
     * @return
     */
    @Override
    public R industryInformation(Map map) {
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        List<String> professionByUserId = customerTaskMapper.getProfessionByUserId(map.get("id").toString());
        List<ProfessionPolymerizationNew> id = professionMapper.getProfessionMarketList(map.get("id").toString(), professionByUserId);
        return R.data(id);
    }

    @Override
    public R getAreaList(Map map) {
        // 查询地级市应有客户
        List<AreaResultListVO> updatedAreaResultList = professionMapper.getProfessionAreaList(map.get("id").toString());
        if(null != updatedAreaResultList && updatedAreaResultList.size() > 0){
            updatedAreaResultList.stream().forEach(obj ->{
                List<AreaResultListVO> professionAreaCityList = professionMapper.getProfessionAreaCityList(obj.getProfessionId(), obj.getAreaCode());
                obj.setChildAreaList(professionAreaCityList);
                obj.setParent(true);
            });
        }
        return R.data(updatedAreaResultList);
    }

    @Override
    public R getRegionalizationList(Map map) {
        List<Map> list = new ArrayList<>();
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        // 查省
        if (null == map.get("parentCode")) {
            list = professionMapper.getProvince(map.get("id").toString());
        }
        // 查地级市
        if (null != map.get("parentCode") && 2 == map.get("parentCode").toString().length()) {
            list = professionMapper.getCity(map.get("id").toString(), map.get("parentCode").toString());
        }
        // 查区县
        if (null != map.get("parentCode") && 4 == map.get("parentCode").toString().length()) {
            list = professionMapper.getCounties(map.get("id").toString(), map.get("parentCode").toString());
        }
        return R.data(list);
    }

    @Override
    public R getProfessionList(Map map) {
        if (null == map.get("id")) {
            return R.fail("缺少主键参数");
        }
        // 查询人员应有行业
        List<Map> professionList = professionMapper.getProfessionListByArea(map.get("id").toString());
        return R.data(professionList);
    }

    @Override
    public R getProfessionData(String deptId) {
        // 查询部门是否创建区域市场
        QueryWrapper<Profession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("charge_dept_id", deptId);
        queryWrapper.eq("is_deleted", 0);
        List<Profession> professions = professionMapper.selectList(queryWrapper);
        if (null != professions && professions.size() > 0) {
            return R.status(true);
        }
        return R.status(false);
    }

    /**
     * 根据区域市场ID查询行业列表
     *
     * @param professionId
     * @return
     */
    @Override
    public R getProfessionByMarketList(String professionId, String joinId) {
        List<MarketplaceVO> result = new ArrayList<>();
        // 查询符合区域市场的行业信息
        if (null != professionId && !"".equals(professionId)) {
            // 根据区域市场ID查询
            result.addAll(professionMapper.getMarketIdByProfessionId(professionId, joinId));
        }
        // 剔除符合区域市场的行业信息
        QueryWrapper<Marketplace> queryWrapper = new QueryWrapper<>();
        if (result.size() > 0) {
            queryWrapper.notIn("id", result.stream().map(MarketplaceVO::getId).collect(Collectors.toList()));
        }
        queryWrapper.eq("status", 0);
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByAsc("status");
        queryWrapper.orderByAsc("sort");
        List<Marketplace> list = marketplaceService.list(queryWrapper);
        if (null != list && list.size() > 0) {
            list.stream().forEach(obj -> {
                MarketplaceVO marketplaceVO = new MarketplaceVO();
                marketplaceVO.setId(obj.getId().toString());
                marketplaceVO.setChecked(0);
                BeanUtils.copyProperties(obj, marketplaceVO);
                result.add(marketplaceVO);
            });
        }
        return R.data(result);
    }
}
