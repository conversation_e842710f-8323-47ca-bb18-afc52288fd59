//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.net.URL;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;

public class HttpRequester {
    int connectTimeout = 120;
    RequestBodyObject reqJson;
    int requestTimeout = 120;
    String url;
    Map<String, String> header;
    int statusCode;
    Set<Cookie> repoCookies;

    public HttpRequester(String url, Map<String, String> header, RequestBodyObject reqJson) {
        this.url = url;
        this.header = header;
        this.reqJson = reqJson;
    }

    public HttpRequester(String url, Map<String, String> header, RequestBodyObject reqJson, int connectTimeout, int requestTimeout) {
        this.url = url;
        this.header = header;
        this.reqJson = reqJson;
        this.connectTimeout = connectTimeout;
        this.requestTimeout = requestTimeout;
    }

    public String post() throws Exception {
        PrintWriter out = null;
        String result = "";
        URL realUrl = new URL(this.url);
        HttpURLConnection conn = null;
        Proxy proxy = HttpUtils.getProxy();
        if (proxy != null) {
            conn = (HttpURLConnection)realUrl.openConnection(proxy);
        } else {
            conn = (HttpURLConnection)realUrl.openConnection();
        }

        conn.setConnectTimeout(this.getConnectTimeout() * 1000);
        conn.setReadTimeout(this.getRequestTimeout() * 1000);
        if (this.header != null) {
            Iterator var7 = this.header.entrySet().iterator();

            while(var7.hasNext()) {
                Entry<String, String> entry = (Entry)var7.next();
                conn.setRequestProperty((String)entry.getKey(), (String)entry.getValue());
            }
        }

        conn.setRequestProperty("Content-type", "applicatin/json");
        conn.setRequestProperty("User-Agent", "Kingdee/Java WebApi SDK 7.3 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        conn.setDoOutput(true);
        conn.setDoInput(true);

        try {
            if (this.reqJson != null) {
                out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), "utf8"));
                out.print(this.reqJson.toJson());
                out.flush();
            }

            int statusCode = conn.getResponseCode();
            if (statusCode != 200 && statusCode != 206) {
                result = this.readRepoBody(conn);
                throw new Exception(String.format("StatusCode:%s,\tDesc:%s", statusCode, result));
            } else {
                Iterator var8 = conn.getHeaderFields().entrySet().iterator();

                while(true) {
                    Entry cookie;
                    do {
                        do {
                            if (!var8.hasNext()) {
                                result = this.readRepoBody(conn);
                                return result;
                            }

                            cookie = (Entry)var8.next();
                        } while(cookie.getKey() == null);
                    } while(!((String)cookie.getKey()).equals("Set-Cookie"));

                    this.repoCookies = new HashSet();
                    Iterator var10 = ((List)cookie.getValue()).iterator();

                    while(var10.hasNext()) {
                        String c = (String)var10.next();
                        Cookie ck = Cookie.parse(c);
                        if (ck != null) {
                            this.repoCookies.add(new Cookie(c));
                        }
                    }
                }
            }
        } catch (Exception var15) {
            throw var15;
        } finally {
            if (out != null) {
                out.close();
            }

        }
    }

    String readRepoBody(HttpURLConnection conn) throws UnsupportedEncodingException, IOException {
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf8"));
        StringBuilder sb = new StringBuilder();

        String line;
        while((line = in.readLine()) != null) {
            sb.append(line);
        }

        in.close();
        return sb.toString();
    }

    public int getConnectTimeout() {
        return this.connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getRequestTimeout() {
        return this.requestTimeout;
    }

    public void setRequestTimeout(int requestTimeout) {
        this.requestTimeout = requestTimeout;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getHeader() {
        return this.header;
    }

    public void setHeader(Map<String, String> header) {
        this.header = header;
    }

    public RequestBodyObject getReqJson() {
        return this.reqJson;
    }

    public void setReqJson(RequestBodyObject reqJson) {
        this.reqJson = reqJson;
    }

    public int getStatusCode() {
        return this.statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public Set<Cookie> getRepoCookies() {
        return this.repoCookies;
    }

    public void setRepoCookies(Set<Cookie> repoCookies) {
        this.repoCookies = repoCookies;
    }
}
