<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.SignatoryCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="signatoryCompanyResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.SignatoryCompany">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="tax_number" property="taxNumber"/>
        <result column="address" property="address"/>
        <result column="phone" property="phone"/>
        <result column="account_name" property="accountName"/>
        <result column="bank_code" property="bankCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectSignatoryCompanyPage" resultMap="signatoryCompanyResultMap">
        select
        *
        from cm_signatory_company
        where
        is_deleted = 0
        <if test="param2.name != null and param2.name != ''">
            and name like concat('%', #{param2.name}, '%')
        </if>
        <if test="param2.status != null">
            and status = #{param2.status}
        </if>
        order by sort_order, id
    </select>

    <select id="getCountByTaxNumber" resultType="java.lang.Integer">
        select
        count(id)
        from cm_signatory_company
        where
        is_deleted = 0
        and tax_number = #{taxNumber}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="getCountBySortOrder" resultType="java.lang.Integer">
        select
        count(id)
        from cm_signatory_company
        where
        is_deleted = 0
        and sort_order = #{sortOrder}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
