package org.springblade.common.enums;

public enum TableIdEnum {
    客户("customer"),
    客户联系人("concent_user"),
    供应商("supplier"),
    项目("project_basic"),
    合同("contract"),
    合同变更("contract_change"),
    拜访记录("customer_visit"),

    客户联系人收藏("blade_customer_contact_collect");
    private String tableId;

    TableIdEnum(String tableId) {
        this.tableId = tableId;
    }

    public String getTableId() {
        return tableId;
    }

}
