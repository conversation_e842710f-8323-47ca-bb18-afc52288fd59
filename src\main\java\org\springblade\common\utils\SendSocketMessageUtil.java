package org.springblade.common.utils;

import org.springframework.stereotype.Component;
import javax.websocket.OnClose;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 消息通知工具类
 */
@Component
@ServerEndpoint("/systemMessage/ws/{uuid}")
public class SendSocketMessageUtil {

    private static Map<String, Set<SendSocketMessageUtil>> sessionMap = new ConcurrentHashMap<>();

    private Session session;

    private String uuid;

    /**
     * 连接建立时触发
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("uuid") String uuid) {
        this.session = session;
        this.uuid = uuid;
        Set<SendSocketMessageUtil> sessionManager = sessionMap.get(uuid);
        if(sessionManager == null) {
            sessionManager = new CopyOnWriteArraySet<>();
        }
        sessionManager.add(this);
        sessionMap.put(uuid, sessionManager);
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 发送消息给指定的 UUID
     */
    public void sendMessage(String uuid, String message) {
        Set<SendSocketMessageUtil> wsSet = sessionMap.get(uuid);
        try {
            for (SendSocketMessageUtil item : wsSet) {
                if(!uuid.equals(item.uuid)){
                    continue;
                }
                try {
                    item.sendMessage(message);
                } catch (Exception e) {
                    continue;
                }
            }
        }catch (Exception e){
            new RuntimeException("无效通讯通道");
        }
    }

    /**
     * 关闭指定 UUID 的 WebSocket 连接并从sessionMap中移除对应关系
     */
    @OnClose
    public void onClose() {
        Set wsSet = sessionMap.get(this.uuid);
        wsSet.remove(this);
        if(wsSet.isEmpty()) {
            sessionMap.remove(this.uuid);
        }
    }

    public void sessionClose(String uuid) {
        Set wsSet = sessionMap.get(uuid);
        wsSet.remove(this);
        if(wsSet.isEmpty()) {
            sessionMap.remove(uuid);
        }
    }

}
