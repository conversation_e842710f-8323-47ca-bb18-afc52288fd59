package org.springblade.common.enums;

public enum PayBillContactTypeEnum {
    GYS("供应商","BD_Supplier"),
    ZG("职工","BD_Empinfo"),
    KH("客户","BD_Customer");

    // 成员变量
    private String name;
    private String index;

    PayBillContactTypeEnum(String name, String index) {
        this.name = name;
        this.index = index;
    }
    public static String getName(String index) {
        for (PayBillContactTypeEnum c : PayBillContactTypeEnum.values()) {
            if (index.equalsIgnoreCase(c.getIndex())) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public String getIndex() {
        return index;
    }
}
