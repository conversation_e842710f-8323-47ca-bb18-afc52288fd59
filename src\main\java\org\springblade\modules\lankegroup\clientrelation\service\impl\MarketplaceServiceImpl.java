package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.MarketplaceDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Area;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.Marketplace;
import org.springblade.modules.lankegroup.clientrelation.entity.Personnel;
import org.springblade.modules.lankegroup.clientrelation.mapper.*;
import org.springblade.modules.lankegroup.clientrelation.service.MarketplaceService;
import org.springblade.modules.lankegroup.clientrelation.service.PersonnelMarketService;
import org.springblade.modules.lankegroup.clientrelation.vo.*;
import org.springblade.modules.lankegroup.customerVisit.vo.CompletePlanLogGroupVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;


@Service
@AllArgsConstructor
public class MarketplaceServiceImpl extends BaseServiceImpl<MarketplaceMapper, Marketplace> implements MarketplaceService {

    private final MarketplaceMapper marketplaceMapper;

    private final PersonnelMarketMapper personnelMarketMapper;

    private final ProfessionMapper professionMapper;

    private final PersonnelMapper personnelMapper;

    private final AreaMapper areaMapper;


    @Override
    public R saveMarketplace(MarketplaceDTO marketplaceDTO) {
        // 校验名称是否重复
        QueryWrapper<Marketplace> queryWrapperName = new QueryWrapper<>();
        queryWrapperName.eq("name", marketplaceDTO.getName());
        queryWrapperName.eq("is_deleted", 0);
        Marketplace marketplaceName = baseMapper.selectOne(queryWrapperName);
        if (null != marketplaceName) {
            return R.fail("行业名称已存在");
        }
        // 校验排序是否重复
        QueryWrapper<Marketplace> queryWrapperSort = new QueryWrapper<>();
        queryWrapperSort.eq("sort", marketplaceDTO.getSort());
        queryWrapperSort.eq("is_deleted", 0);
        Marketplace marketplaceSort = baseMapper.selectOne(queryWrapperSort);
        if (null != marketplaceSort) {
            return R.fail("当前序号已存在，行业名称是：" + marketplaceSort.getName());
        }
        marketplaceDTO.setCreateDept(Long.valueOf(AuthUtil.getDeptId().split(",")[0]));
        marketplaceDTO.setCreateUser(AuthUtil.getUserId());
        marketplaceDTO.setUpdateUser(AuthUtil.getUserId());
        marketplaceDTO.setCreateName(AuthUtil.getNickName());
        marketplaceDTO.setStatus(0);
        if (baseMapper.insert(marketplaceDTO) < 1) {
            return R.fail("新增行业失败");
        }
        return R.success("新增行业成功");
    }

    @Override
    public R getMarketplaceList(MarketplaceDTO marketplaceDTO, IPage<MarketplaceVO> page, String status) {
        try {
            return R.data(marketplaceMapper.getMarketplaceList(marketplaceDTO, page, status));
        } catch (Exception e) {
            return R.data(page);
        }
    }

    @Override
    public R detail(String id) {
        return R.data(marketplaceMapper.selectById(id));
    }

    @Override
    public R deleteMarketplace(Long id) {
        // 查询此行业下客户数量
        Integer byCustomerList = personnelMapper.getByCustomerList(id);
        if (byCustomerList > 0) {
            return R.fail("该行业下存在客户信息，暂时无法删除");
        }
        if (baseMapper.deleteById(id) > 0) {
            Marketplace marketplace = new Marketplace();
            marketplace.setId(id);
            professionMapper.deleteByMarketplaceData(marketplace);
            personnelMapper.deleteByMarketplaceData(marketplace);
            return R.success("删除行业成功");
        }
        return R.fail("删除行业失败");
    }

    @Override
    public R updateMarketplace(MarketplaceDTO marketplaceDTO) {
        QueryWrapper<Marketplace> queryWrapperName = new QueryWrapper<>();
        queryWrapperName.eq("name", marketplaceDTO.getName());
        queryWrapperName.eq("is_deleted", 0);
        Marketplace marketplaceName = baseMapper.selectOne(queryWrapperName);
        if (null != marketplaceName && !marketplaceDTO.getId().equals(marketplaceName.getId())) {
            return R.fail("行业名称已存在");
        }
        // 校验排序是否重复
        QueryWrapper<Marketplace> queryWrapperSort = new QueryWrapper<>();
        queryWrapperSort.eq("sort", marketplaceDTO.getSort());
        queryWrapperSort.eq("is_deleted", 0);
        Marketplace marketplaceSort = baseMapper.selectOne(queryWrapperSort);
        if (null != marketplaceSort && !marketplaceDTO.getId().equals(marketplaceSort.getId())) {
            return R.fail("当前序号已存在，行业名称是：" + marketplaceSort.getName());
        }
        if (baseMapper.updateById(marketplaceDTO) > 0) {
            // 修改人员和区域市场对应的行业名称
            Marketplace marketplace = new Marketplace();
            marketplace.setId(marketplaceDTO.getId());
            marketplace.setName(marketplaceDTO.getName());
            professionMapper.updateByMarketplaceData(marketplace);
            personnelMapper.updateByMarketplaceData(marketplace);
            return R.success("修改成功");
        }
        return R.fail("修改失败");
    }

    @Override
    public R updateMarketplaceStatus(MarketplaceDTO marketplaceDTO) {
        // 确认行业
        Marketplace marketplace = baseMapper.selectById(marketplaceDTO.getId());
        if (null == marketplace) {
            return R.fail("暂无行业信息");
        }
        if (baseMapper.updateById(marketplaceDTO) > 0) {
            if (marketplaceDTO.getStatus() == 0) {
                return R.success("启用成功");
            }
            return R.success("禁用成功");
        }
        return R.fail("启用/禁用失败");
    }

    @Override
    public List<MarketplaceVO> industryList(MarketplaceDTO marketplaceDTO) {
        return baseMapper.industryList(marketplaceDTO);
    }

    @Override
    public Integer getMarketClient(List<Long> ids, Long professionId) {
        return baseMapper.getMarketClient(ids, professionId);
    }

    @Override
    public Integer getMarketClientByJoinId(List<Long> ids, Long professionId, String joinId) {
        return baseMapper.getMarketClientByJoinId(ids, professionId, joinId);
    }

    @Override
    public Integer getMarketClientByUserId(String marketId, String areaId, Long userId) {
        return baseMapper.getMarketClientByUserId(marketId, areaId, userId);
    }

    @Override
    public Integer getMarketClientByPersonnelId(List<Long> ids, Long personnelId, String joinId) {
        return baseMapper.getMarketClientByPersonnelId(ids, personnelId, joinId);
    }

    @Override
    public Map getMarketClientByName(Long id, String personnelId) {
        return baseMapper.getMarketClientByName(id, personnelId);
    }

    /**
     * 区域市场/人员管理详情
     *
     * @param marketplaceDTO
     * @return
     */
    @Override
    public R marketDetailList(MarketplaceDTO marketplaceDTO) {
        List<MarketplaceResultVO> result = new ArrayList<>();
        if (null == marketplaceDTO.getType() || null == marketplaceDTO.getTypeId()) {
            return R.fail("缺少类型信息");
        }
        // 查询个人
        if (1 == marketplaceDTO.getType()) {
            List<MarketplaceResultVO> aResult = personnelMapper.getAPersonerlCustomerList(marketplaceDTO.getTypeId(),
                    marketplaceDTO.getAreaCodeList(),marketplaceDTO.getMarketCodeList());
            List<MarketplaceResultVO> areaCode = personnelMapper.getOPersonerlCustomerList(marketplaceDTO.getTypeId(),
                    marketplaceDTO.getAreaCodeList(),marketplaceDTO.getMarketCodeList());
            if (null != aResult && aResult.size() > 0) {
                List<String> areaCodeAndMarket = aResult.stream().map(MarketplaceResultVO::getAreaAddMarket).collect(Collectors.toList());
                if (null != areaCode && areaCode.size() > 0) {
                    areaCode.stream().forEach(obj -> {
                        if (!areaCodeAndMarket.contains(obj.getAreaAddMarket())) {
                            aResult.add(obj);
                        }
                    });
                }
            }else{
                aResult.addAll(areaCode);
            }
            result.addAll(aResult);
        }
        // 查询区域市场
        if (2 == marketplaceDTO.getType()) {
            List<MarketplaceResultVO> aResult = professionMapper.getAProfessionlCustomerList(marketplaceDTO.getTypeId(),
                    marketplaceDTO.getAreaCodeList(),marketplaceDTO.getMarketCodeList());
            List<MarketplaceResultVO> areaCode = professionMapper.getOProfessionlCustomerList(marketplaceDTO.getTypeId(),
                    marketplaceDTO.getAreaCodeList(),marketplaceDTO.getMarketCodeList());
            if (null != aResult && aResult.size() > 0) {
                List<String> areaCodeAndMarket = aResult.stream().map(MarketplaceResultVO::getAreaAddMarket).collect(Collectors.toList());
                if (null != areaCode && areaCode.size() > 0) {
                    areaCode.stream().forEach(obj -> {
                        if (!areaCodeAndMarket.contains(obj.getAreaAddMarket())) {
                            aResult.add(obj);
                        }
                    });
                }
            }else{
                aResult.addAll(areaCode);
            }
            result.addAll(aResult);
        }
        if (null != marketplaceDTO.getHaveOrNot() && marketplaceDTO.getHaveOrNot() != 0) {
            // 有客户的
            if (1 == marketplaceDTO.getHaveOrNot()) {
                return R.data(result.stream().distinct().filter(res -> res.isYwData() && res.getAreaCode() != null && res.getMarketCode() != null)
                        .sorted(Comparator.comparing(MarketplaceResultVO::getAreaCode).thenComparing(MarketplaceResultVO::getMarketCode))
                        .collect(Collectors.toList()));
            }
            if (2 == marketplaceDTO.getHaveOrNot()) {
                return R.data(result.stream().distinct().filter(res -> !res.isYwData() && res.getAreaCode() != null && res.getMarketCode() != null)
                        .sorted(Comparator.comparing(MarketplaceResultVO::getAreaCode).thenComparing(MarketplaceResultVO::getMarketCode))
                        .collect(Collectors.toList()));
            }
        }
        return R.data(result.stream().distinct().filter(res -> res.getAreaCode() != null && res.getMarketCode() != null)
                .sorted(Comparator.comparing(MarketplaceResultVO::getAreaCode).thenComparing(MarketplaceResultVO::getMarketCode))
                .collect(Collectors.toList()));
    }

    @Override
    public R marketDetail(MarketplaceDTO marketplaceDTO) {
        List<MarketplaceResultVO> result = new ArrayList<>();
        if (null == marketplaceDTO.getType() || null == marketplaceDTO.getTypeId()) {
            return R.fail("缺少类型信息");
        }
        // 查询个人
        if (1 == marketplaceDTO.getType()) {
            // 查询当前人员档案对应用户ID
            Personnel personnel = personnelMapper.selectById(marketplaceDTO.getTypeId());
            List<AreaAndMarket> areaList = personnelMarketMapper.getPersonnelMarketByName(Long.valueOf(marketplaceDTO.getTypeId()), Long.valueOf(marketplaceDTO.getMarketCode()), marketplaceDTO.getAreaCode(), null);
            if (null != areaList && areaList.size() > 0) {
                areaList.stream().forEach(area -> {
                    List<ActualResultVO> actualClientList = professionMapper.getActualClientList(area, personnel.getUserId());
                    if (null != actualClientList && actualClientList.size() > 0) {
                        actualClientList.stream().forEach(client -> {
                            MarketplaceResultVO marketplaceResultVO = new MarketplaceResultVO();
                            BeanUtils.copyProperties(client, marketplaceResultVO);
                            marketplaceResultVO.setClientName(client.getActualName());
                            marketplaceResultVO.setClientCode(client.getActualCode());
                            result.add(marketplaceResultVO);
                        });
                    }
                });
            }
        }
        // 查询区域市场
        if (2 == marketplaceDTO.getType()) {
            List<AreaAndMarket> areaList = professionMapper.getAreaAndMarketListStr(Long.valueOf(marketplaceDTO.getTypeId()), Long.valueOf(marketplaceDTO.getMarketCode()), marketplaceDTO.getAreaCode(), null);
            if (null != areaList && areaList.size() > 0) {
                // 获取区域市场人员列表
//                List<String> userList = professionMapper.getProfessionUserIdList(marketplaceDTO.getTypeId());
                areaList.stream().forEach(area -> {
//                    List<ActualResultVO> actualClientList = professionMapper.getActualClientListByUserList(area, userList);
                    // 查询当前区域市场人员列表
//                    List<String> userList = professionMapper.getProfessionUserIdList(marketplaceDTO.getTypeId());
//                    List<ActualResultVO> actualClientList = professionMapper.getActualClientListByUserList(area, userList);
                    List<ActualResultVO> actualClientList = professionMapper.getActualClientListByUserList(area, null);
                    if (null != actualClientList && actualClientList.size() > 0) {
                        actualClientList.stream().forEach(client -> {
                            MarketplaceResultVO marketplaceResultVO = new MarketplaceResultVO();
                            BeanUtils.copyProperties(client, marketplaceResultVO);
                            marketplaceResultVO.setClientName(client.getActualName());
                            marketplaceResultVO.setClientCode(client.getActualCode());
                            result.add(marketplaceResultVO);
                        });
                    }
                });
            }
        }
        return R.data(result);
    }

    @Override
    public R getSort(String id, String name, Integer type, Integer status) {
        return R.data(baseMapper.getSort(id, name, type, status));
    }

    @Override
    public R personnelMarketList(MarketplaceDTO marketplaceDTO) {
        List<PersonnelMarketResultVO> result = new ArrayList<>();
        // 查询禁用行业ID列表
        QueryWrapper<Marketplace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("is_deleted", 0);
        List<Marketplace> list = marketplaceMapper.selectList(queryWrapper);
        List<Long> prohibitList = null;
        if (null != list && list.size() > 0) {
            prohibitList = list.stream().map(Marketplace::getId).collect(Collectors.toList());
        }
        List<PersonnelMarketVO> personnelMarketVOS = baseMapper.personnelMarketList(marketplaceDTO.getId(), prohibitList);
        if (null != personnelMarketVOS && personnelMarketVOS.size() > 0) {
            personnelMarketVOS.stream().filter(obj -> obj.getAreaName() != null).collect(groupingBy(PersonnelMarketVO::getAreaName)).entrySet().stream().forEach(obj -> {
                PersonnelMarketResultVO personnelMarketResultVO = new PersonnelMarketResultVO();
                personnelMarketResultVO.setAreaName(obj.getKey());
                personnelMarketResultVO.setMarketVOList(obj.getValue());
                result.add(personnelMarketResultVO);
            });
        }
        return R.data(result);
    }

    @Override
    public R personnelMarketListByAll(MarketplaceDTO marketplaceDTO) {
        // 查询禁用行业ID列表
        QueryWrapper<Marketplace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("is_deleted", 0);
        List<Marketplace> list = marketplaceMapper.selectList(queryWrapper);
        List<Long> prohibitList = null;
        if (null != list && list.size() > 0) {
            prohibitList = list.stream().map(Marketplace::getId).collect(Collectors.toList());
        }
        List<PersonnelMarketVO> personnelMarketVOS = baseMapper.personnelMarketList(marketplaceDTO.getId(), prohibitList);
        return R.data(personnelMarketVOS);
    }

    @Override
    public R personnelMarketListByGroup(MarketplaceDTO marketplaceDTO) {
        // 查询禁用行业ID列表
        QueryWrapper<Marketplace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("is_deleted", 0);
        List<Marketplace> list = marketplaceMapper.selectList(queryWrapper);
        List<Long> prohibitList = null;
        if (null != list && list.size() > 0) {
            prohibitList = list.stream().map(Marketplace::getId).collect(Collectors.toList());
        }
        String cityCode = null;

        String areaCode = null;

        if (null != marketplaceDTO.getCode() && marketplaceDTO.getCode().length() == 4) {
            cityCode = marketplaceDTO.getCode();
        }

        if (null != marketplaceDTO.getCode() && marketplaceDTO.getCode().length() == 6) {
            areaCode = marketplaceDTO.getCode();
        }
        List<PersonnelMarketResultByGroupVO> personnelMarketVOS = baseMapper.personnelMarketListByGroup(marketplaceDTO.getId(), prohibitList, cityCode, areaCode);
        return R.data(personnelMarketVOS);
    }
}
