//package org.springblade.modules.lankegroup.contractManagement.listener;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import org.flowable.engine.delegate.TaskListener;
//import org.flowable.task.service.delegate.DelegateTask;
//import org.springblade.flow.engine.utils.SpringContextUtils;
//import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
//import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
//
///**
// * 销售合同项目所属分组负责人审批，通过监听器设置审批人（暂时不用了）
// */
//public class SetFinancialApprovalListener implements TaskListener {
//    private IContractService contractService = SpringContextUtils.getBean(IContractService.class);
//
//    @Override
//    public void notify(DelegateTask delegateTask) {
//        Long contractId = Long.valueOf(delegateTask.getVariable("contractId").toString());
//        //项目分组负责人审批通过之后下级是财务审批
//        // TODO: 2024/7/11 更新合同表审批人,这里下一级审批人是王丛丛着急上线先写死了，后期有时间需要改成动态获取
//        contractService.update((Wrappers.<Contract>update().lambda().set(Contract::getTaskUser, "10626332229065259")).eq(Contract::getId, contractId));
//    }
//}
