##################################################### 1、数据库 #####################################################
###################  blade_knowledge_case 
ALTER TABLE `bladex_boot`.`blade_knowledge_case` 
ADD COLUMN `is_publish` int(0) NULL COMMENT '是否发布：0 未发布   1 已发布（发布的可以展示在工作台）' AFTER `case_logo`;
###################  blade_param 
INSERT INTO `bladex_boot`.`blade_param`(`id`, `param_name`, `param_key`, `param_value`, `remark`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (1771064833861112001, '工作台跳案例列表路径', 'workbenchSkip.case', '/knowledge/classicCase', NULL, 1073838033024456547, 1, '2024-04-02 14:42:16', 1073838033024456547, '2024-04-02 14:42:16', 1, 0);
INSERT INTO `bladex_boot`.`blade_param`(`id`, `param_name`, `param_key`, `param_value`, `remark`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (1771064833861112002, '工作台跳案例详情路径', 'workbenchSkip.case_id', '/knowledge/classicCaseView', NULL, 1073838033024456547, 1, '2024-04-02 14:42:16', 1073838033024456547, '2024-04-02 14:42:16', 1, 0);
INSERT INTO `bladex_boot`.`blade_param`(`id`, `param_name`, `param_key`, `param_value`, `remark`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (1771064833861112003, '工作台跳产品列表路径', 'workbenchSkip.product', '/knowledge/productList', NULL, 1073838033024456547, 1, '2024-04-02 14:42:16', 1073838033024456547, '2024-04-02 14:42:16', 1, 0);
INSERT INTO `bladex_boot`.`blade_param`(`id`, `param_name`, `param_key`, `param_value`, `remark`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (1771064833861112004, '工作台跳产品详情路径', 'workbenchSkip.product_id', '/knowledge/productListView', NULL, 1073838033024456547, 1, '2024-04-02 14:42:16', 1073838033024456547, '2024-04-02 14:42:16', 1, 0);

##################################################### 2、application.yml文件 #####################################################
secure:
  skip-url:
    - /lk-digitize-skip/**
##################################################### 3、application-prod.yml文件(根据环境调整) #####################################################
dingtalk:
  digitize_interface_secret: ep17SEeTYONZjf!

digitize-skip:
  oss-prefix: https://lkszh.shimingbao.cn:20443/oss/voucher/
  jump-prefix: http://************:1278/#/
  
##################################################### 4、配置数据源 #####################################################
开发者后台（钉钉）-》开放能力-》数据源管理，新增数据源
名称：知识库数据源
api key：qwertyQWERTY
url：http://*************:9900/lk-digitize-skip/knowledge/getPublishCase（根据测试、正式环境，修改）

##################################################### 5、部署项目 #####################################################
lanke-digitize
  


