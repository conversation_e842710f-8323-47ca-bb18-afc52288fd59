<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.contractManagement.mapper.ContractPrestoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractPrestoreResultMap"
               type="org.springblade.modules.lankegroup.contractManagement.entity.ContractPrestore">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="service_charge" property="serviceCharge"/>
    </resultMap>


    <select id="selectContractPrestorePage" resultMap="contractPrestoreResultMap">
        select *
        from cm_contract_prestore
        where is_deleted = 0
    </select>

</mapper>
