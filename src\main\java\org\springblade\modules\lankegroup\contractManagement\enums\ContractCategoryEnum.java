package org.springblade.modules.lankegroup.contractManagement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.utils.Func;

/**
 * 合同类别枚举
 * 1:标书,2:实验,3:实验框架,4:试剂,5:预存,6:动物饲养
 */
@Getter
@AllArgsConstructor
public enum ContractCategoryEnum {
    BID_DOCUMENT(1, "标书", "标书撰写合同模板"),
    EXPERIMENT(2, "实验", "实验服务合同模板"),
    EXPERIMENT_FRAMEWORK(3, "实验框架", "晶莱生物实验检测框架协议"),
    REAGENT(4, "试剂", "试剂合同模板"),
    PRESTORE(5, "预存", "预存合同模板"),
    ANIMAL_BREEDING(6, "动物饲养", "动物饲养框架合作协议");

    private final Integer code;
    private final String name;
    private final String templateName;

    public static String getTemplateName(String name) {
        if (Func.isBlank(name)) {
            return null;
        }
        ContractCategoryEnum[] values = ContractCategoryEnum.values();
        for (ContractCategoryEnum value : values) {
            if (value.getName().equals(name)) {
                return value.getTemplateName();
            }
        }
        return null;
    }

    public static ContractCategoryEnum getTargetEnum(String name) {
        if (Func.isBlank(name)) {
            return null;
        }
        ContractCategoryEnum[] values = ContractCategoryEnum.values();
        for (ContractCategoryEnum value : values) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
