package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.PersonnelDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.Marketplace;
import org.springblade.modules.lankegroup.clientrelation.entity.Personnel;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerization;
import org.springblade.modules.lankegroup.clientrelation.vo.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface PersonnelMapper extends BaseMapper<Personnel> {

    /**
     * 行业列表
     *
     * @param personnelDTO
     * @param page
     * @return
     */
    IPage<PersonnelListVO> getPersonnelList(@Param("query") PersonnelDTO personnelDTO, IPage<PersonnelListVO> page);

    /**
     * 行业列表
     *
     * @param page
     * @return
     */
    IPage<PersonnelListVO> getPersonnelListByAll(IPage<PersonnelListVO> page);

    /**
     * 根据用户ID查询实有客户数量
     *
     * @return
     */
    Integer getActualClientCount(Long userId, List<Long> prohibitList);

    /**
     * 按人员查询应有客户
     *
     * @param map
     * @return
     */
    List<AreaListVO> getAreaOughtList(@Param("query") Map map);

    /**
     * 按人员查询实有客户
     *
     * @param map
     * @return
     */
    Integer getAreaActualList(@Param("query") Map map);

    /**
     * 按人员模糊查询实有客户
     *
     * @param map
     * @return
     */
    Integer getAreaLikeActualList(@Param("query") Map map);

    /**
     * 获取省
     *
     * @param id
     * @return
     */
    List<Map> getProvince(String id);

    /**
     * 获取市
     *
     * @param id
     * @param code
     * @return
     */
    List<Map> getCity(String id, String code);

    /**
     * 获取区县
     *
     * @param id
     * @param code
     * @return
     */
    List<Map> getCounties(String id, String code);

    /**
     * 获取行业
     *
     * @return
     */
    List<Map> getProfessionList(String id);

    /**
     * 获取人员实有行业
     *
     * @param id
     * @return
     */
    List<Map> getProfessionPersonnelList(String id);

    List<AreaAndMarket> getCustomerList(String userId, List<Long> prohibitList);

    List<AreaAndMarket> getCustomerListByThree(String userId, List<String> marketId, List<String> areaCode);

    /**
     * 根据区域市场ID查询下属人员负责的区划信息
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getMarketByPersonnelList(String id);

    /**
     * 根据区域市场ID查询下属人员实有负责的行业信息
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getMarketByPersonnelListT(String id, List<String> marketId, List<String> areaId, List<Long> prohibitList);

    List<AreaAndMarket> getMarketByPersonnelListTByUserId(String id, List<String> marketId, List<String> areaId, List<Long> prohibitList);

    /**
     * 根据区域市场ID查询下属人员实有负责的区划信息
     *
     * @param id
     * @param marketId
     * @param areaId
     * @return
     */
    List<AreaListVO> getAreaByPersonnelListT(String id, List<String> marketId, List<String> areaId, Integer type, List<Long> prohibitList);

    /**
     * 查询区域市场下客户
     *
     * @param id
     * @param code
     * @param type
     * @return
     */
    List<AreaListVO> getAreaByCustomerList(String id, String code, Integer type, Integer groupType, List<Long> prohibitList);

    /**
     * 查询区域市场下客户
     *
     * @param id
     * @return
     */
    List<AreaListVO> getAreaByCustomerPersonnelList(String id, String code, Integer type, Integer groupType, List<Long> prohibitList);


    /**
     * 查询区域市场下客户
     *
     * @param id
     * @return
     */
    List<AreaListVO> getAreaByCustomerPersonnelListT(String id, List<Long> prohibitList);

    /**
     * 查询行业下是否有客户
     *
     * @param id
     * @return
     */
    Integer getByCustomerList(Long id);

    /**
     * 根据区域市场ID修改信息
     *
     * @param id
     */
    void updatePersonnelByProfession(String id, String name);

    /**
     * 客户管理
     * 根据人员id获取 客户关系人员管理表的id
     * @param userId
     * @return
     */
    Long getIdByUserId(Long userId);

    /**
     *根据行业id、区域id匹配区域市场下的所有人
     * @return
     */
    List<String> getUserIdByMarketAndDeptAndProList(String marketId,String cityId);

    /**
     * 根据人员id获取市场id
     * @param userId
     * @return
     */
    String getProfessIdByUserId (Long userId);

    /**
     * 编辑对应行业信息
     * @param marketplace
     */
    void updateByMarketplaceData(@Param("param") Marketplace marketplace);

    /**
     * 删除对应行业信息
     * @param marketplace
     */
    void deleteByMarketplaceData(@Param("param") Marketplace marketplace);

    /**
     * 查询某区域下行业数量
     * @param userId
     * @param areaName
     * @return
     */
    Integer getMarketCountByArea(Long userId,String areaName);

    void updatePersonnelData(@Param("param") PersonnelListVO personnelVO);

    /**
     * 查询人员行业列表
     * @param personnelId
     * @return
     */
    List<ProfessionPolymerization> getPersonnelMarketList(String personnelId);

    /**
     * 查询市区实有应有客户
     * @param personnelId
     * @return
     */
    List<AreaResultListVO> getPersonnelUrbanList(String personnelId);

    /**
     * 查询区县实有应有客户
     * @param personnelId
     * @param areaCode
     * @return
     */
    List<AreaResultListVO> getPersonnelCityList(String personnelId,String areaCode);

    /**
     * 查询应负责客户
     * @param personnelId
     * @param areaCode
     * @return
     */
    List<MarketplaceResultVO> getAPersonerlCustomerList(String personnelId,List<String> areaCode,List<String> marketCode);

    /**
     * 查询应负责区域行业
     * @param personnelId
     * @param areaCode
     * @return
     */
    List<MarketplaceResultVO> getOPersonerlCustomerList(String personnelId,List<String> areaCode,List<String> marketCode);
}
