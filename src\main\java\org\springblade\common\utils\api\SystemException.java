package org.springblade.common.utils.api;

/**
 * <AUTHOR>
 * @version 1.0
 * 系统异常
 * @date 2024/6/12 14:47
 */
public class SystemException extends BaseException {

    public SystemException(BaseException exception) {
        this(exception.getCode(), exception.getMessage());
    }

    public SystemException(int code, String message) {
        super(code, message);
    }

    /**
     * 实例化异常
     *
     * @param ex
     * @return
     */
    public static SystemException fail(ErrorCode ex) {
        return new SystemException(ex.getCode(), ex.getMessage());
    }

    @Override
    public String toString() {
        return "AException [message=" + message + ", code=" + code + "]";
    }
}