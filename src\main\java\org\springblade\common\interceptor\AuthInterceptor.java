package org.springblade.common.interceptor;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.config.AppConfig;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Base64;

@Component
@Slf4j
@AllArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private AppConfig appConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        return ddValid(request);
    }

    private boolean ddValid(HttpServletRequest req){
        //calcSign
        boolean result = calcSign(req);
        if(!result) return false;
        //valid corpId
        return validCorpId(req);
    }

    private boolean calcSign(HttpServletRequest req){
        //从配置文件读取
        String interfaceSecret = appConfig.getDigitizeInterfaceSecret();

        String ts = req.getHeader("x-ddpaas-signature-timestamp");
        String ddSign = req.getHeader("x-ddpaas-signature");
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec key = new SecretKeySpec(interfaceSecret.getBytes(), "HmacSHA256");
            mac.init(key);
            String sign =  Base64.getEncoder().encodeToString(mac.doFinal(ts.getBytes()));
            log.info("dd sign==========={}",ddSign);
            log.info("sign==========={}" ,sign);
            return sign.equals(ddSign) ? true : false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    private boolean validCorpId(HttpServletRequest req){
        //从配置文件读取
        String corpId = appConfig.getCorpId();
        String ddCorpId = req.getParameter("corpId");
        log.info("ddCorpId==========={}" ,ddCorpId);
        return corpId.equals(ddCorpId) ? true : false;
    }


}
