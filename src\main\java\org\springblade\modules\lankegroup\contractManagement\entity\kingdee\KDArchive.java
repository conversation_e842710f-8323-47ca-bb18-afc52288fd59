package org.springblade.modules.lankegroup.contractManagement.entity.kingdee;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.*;

@Data
public class KDArchive {
    //实体主键
    @JSONField(name = "FID",ordinal = 1)
    private String FID;
    //单据编号
    @JSONField(name = "FBillNo",ordinal = 2)
    private String FBillNo;
    //合同名称
    @JSONField(name = "FName",ordinal = 3)
    private String FName;
    //有效起始日期
    @JSONField(name = "FValiStartDate",ordinal = 4)
    private String FValiStartDate;
    //有效截止日期
    @JSONField(name = "FValiEndDate",ordinal = 5)
    private String FValiEndDate;
    //日期
    @JSONField(name = "FDate",ordinal = 6)
    private String FDate;
    //销售组织
    @JSONField(name = "FSALEORGID",ordinal = 7)
    private Map FSALEORGID = new LinkedHashMap();
    //单据类型
    @JSONField(name = "FBillTypeID",ordinal = 8)
    private Map FBillTypeID = new LinkedHashMap();
    //项目档案
    @JSONField(name = "F_XMDA",ordinal = 9)
    private Map F_XMDA = new LinkedHashMap();
    //客户
    @JSONField(name = "FBDCUSTID",ordinal = 10)
    private Map FBDCUSTID = new LinkedHashMap();
    //合同明细
    @JSONField(name = "FCRMContractEntry",ordinal = 11)
    private List FCRMContractEntry = new ArrayList();

    /**
     * 实际业务组织==签订公司
     */
    @JSONField(name = "F_SJYWZZ",ordinal = 12)
    private Map F_SJYWZZ=new LinkedHashMap();
    /**
     * 备注==归档时的备注
     */
    @JSONField(name = "F_RWSQ_Remarks",ordinal = 13)
    private String F_RWSQ_Remarks;
}
