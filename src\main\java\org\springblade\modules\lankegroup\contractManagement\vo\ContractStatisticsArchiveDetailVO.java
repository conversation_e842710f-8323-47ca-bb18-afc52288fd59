package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class ContractStatisticsArchiveDetailVO {

    private String conId;

    private String conCode;

    private String conName;

    private String deMethod;

    private String deTerm;

    private String deDate;

    private String deDuration;

    private String deStartDate;

    private String deEndDate;

    private String deWarranty;

    private String deFee;

    private String deSeNumber;

    private String deSePrice;

    private String deSeOrder;

    private String deTeNumber;

    private String deTePrice;

    private String deTeOrder;

    private String deNumber;

    private String deRemark;

    private String deIncome;

    private String deOnceDate;

}
