package org.springblade.modules.lankegroup.contractManagement.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractUnionInfoResult;

import java.util.List;
import java.util.Map;


/**
 * 项目合同信息钉钉表+金蝶表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
public interface ContractUnionInfoMapper extends BaseMapper<ContractUnionInfo> {

    // 查询钉钉表合同信息
    List<ContractUnionInfoResult> getContractInfo();

    // 查询金蝶表合同信息
    @DS("sqlserver")
    List<ContractUnionInfoResult> getKdContractInfo();

    void deleteData();

    void batchInsertContractInfo(List<ContractUnionInfo> contractUnionInfos);

    void batchInsertKdContractInfo(List<ContractUnionInfo> contractUnionInfos);
    /**
     * 根据项目id查询在合同定时表中最新一条合同信息
     */
    ContractUnionInfo getLatestContract(Long projectId);
    /**
     * 归档同步金蝶成功后保存合同信息到定时表
     */
    void saveContractUnionInfo(ContractUnionInfo contractUnionInfo);

    ContractUnionInfo getContractAmountByProjectId(Long projectId);
}
