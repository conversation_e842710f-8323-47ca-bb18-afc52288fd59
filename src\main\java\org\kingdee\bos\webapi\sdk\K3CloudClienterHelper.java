//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package org.kingdee.bos.webapi.sdk;

public class K3CloudClienterHelper {
    static K3CloudClienter clienter;
    static String serverUrl;
    static String dcID;
    static String userName;
    static String appId;
    static String appSecret;

    K3CloudClienterHelper() {
    }

    public static K3CloudClienter privateGetClienter() {
        if (clienter == null) {
            if (serverUrl == null) {
                AppCfg cfg = CfgUtil.getAppDefaultCfg();
                if (cfg != null) {
                    serverUrl = cfg.getServerUrl();
                    dcID = cfg.getdCID();
                    userName = cfg.getUserName();
                    appId = cfg.getAppId();
                    appSecret = cfg.getAppSecret();
                }
            }

            clienter = new K3CloudClienter(serverUrl);
            clienter.initIdentify(serverUrl, dcID, userName, appId, appSecret);
        }

        return clienter;
    }

    public static void initClienter(String serverUrl, String dcID, String userName, String appId, String appSecret) {
        K3CloudClienterHelper.serverUrl = serverUrl;
        K3CloudClienterHelper.dcID = dcID;
        K3CloudClienterHelper.userName = userName;
        K3CloudClienterHelper.appId = appId;
        K3CloudClienterHelper.appSecret = appSecret;
        clienter = null;
    }

    public static K3CloudClienter getClienter() {
        K3CloudClienter clienter = privateGetClienter();
        return clienter;
    }
}
