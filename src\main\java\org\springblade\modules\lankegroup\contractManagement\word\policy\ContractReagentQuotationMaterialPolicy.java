package org.springblade.modules.lankegroup.contractManagement.word.policy;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.TableRenderPolicy;
import com.deepoove.poi.util.TableTools;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractReagentQuotationMaterialVO;

import java.util.List;

/**
 * 试剂-实验材料
 */
public class ContractReagentQuotationMaterialPolicy extends DynamicTableRenderPolicy {

    // 实验材料填充数据所在行数
    int goodsStartRow = 1;

    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data) return;
        List<ContractReagentQuotationMaterialVO> detailData = (List) data;


//        // 货物
//        List<RowRenderData> goods = detailData.getGoods();
//        if (null != goods) {
//            table.removeRow(goodsStartRow);
//            for (int i = 0; i < goods.size(); i++) {
//                XWPFTableRow insertNewTableRow = table.insertNewTableRow(goodsStartRow);
//                for (int j = 0; j < 7; j++) insertNewTableRow.createCell();
//                TableRenderPolicy.Helper.renderRow(table.getRow(goodsStartRow), goods.get(i));
//            }
//        }
    }

}
