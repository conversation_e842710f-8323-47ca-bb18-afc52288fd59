package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.TaskService;
import org.springblade.common.constant.FlowFormConstant;
import org.springblade.common.constant.FormNameConstant;
import org.springblade.common.enums.ApprovedEnum;
import org.springblade.common.enums.DocumentType;
import org.springblade.common.enums.OrgEnum;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.SendBillMsgUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.business.service.impl.FlowBusinessServiceImpl;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.BladeFlowResult;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.controller.ContractUseChapterType;
import org.springblade.modules.lankegroup.contractManagement.entity.*;
import org.springblade.modules.lankegroup.contractManagement.mapper.ArchiveDetailsMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractChangeMapper;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IArchiveDetailsService;
import org.springblade.modules.lankegroup.contractManagement.service.IChannelContractService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.lankegroup.contractManagement.wrapper.ContractWrapper;
import org.springblade.modules.lankegroup.crm.entity.CustomerConnectLog;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.disposition.bo.OtherProjectGroupBO;
import org.springblade.modules.lankegroup.disposition.entity.ProjectGroupDisposition;
import org.springblade.modules.lankegroup.disposition.enums.SourceTypeEnum;
import org.springblade.modules.lankegroup.disposition.mapper.OtherProjectGroupMapper;
import org.springblade.modules.lankegroup.historicprocess.entity.HistoricProcessEntity;
import org.springblade.modules.lankegroup.historicprocess.enums.BusinessTypeEnum;
import org.springblade.modules.lankegroup.historicprocess.service.IHistoricProcessService;
import org.springblade.modules.lankegroup.log.entity.LogModel;
import org.springblade.modules.lankegroup.log.service.ControlsLogService;
import org.springblade.modules.lankegroup.message.entity.BillMsgParams;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IApproverService;
import org.springblade.modules.lankegroup.pro_management.service.IProjectGroupService;
import org.springblade.modules.lankegroup.project.entity.ProjectProgress;
import org.springblade.modules.lankegroup.project.entity.ProjectTeam;
import org.springblade.modules.lankegroup.project.mapper.ProjectProgressMapper;
import org.springblade.modules.lankegroup.project.mapper.ProjectTeamMapper;
import org.springblade.modules.lankegroup.project.mapper.UserProjectNodeMapper;
import org.springblade.modules.lankegroup.project.service.impl.ProjectTeamServiceImpl;
import org.springblade.modules.lankegroup.project.vo.ProgressEnum;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service
@AllArgsConstructor
public class ChannelContractServiceImpl extends BaseServiceImpl<ContractMapper, Contract> implements IChannelContractService {
    private final IFlowService flowService;
    private final IUserService userService;
    private final FlowBusinessServiceImpl flowBusinessService;
    private final IApproverService approverService;
    private final IProjectGroupService projectGroupService;
    private final FlowEngineService flowEngineService;
    private final TaskService taskService;
    private final IDeptService deptService;
    private final ContractChangeMapper contractChangeMapper;
    private final IArchiveDetailsService archiveDetailsService;
    private final ICustomerConnectLogService customerConnectLogService;
    private final ProjectBasicMapper projectBasicMapper;
    private final ProjectProgressMapper projectProgressMapper;
    private final UserProjectNodeMapper userProjectNodeMapper;
    private final IContractService contractService;
    //项目团队的mapper
    private final ProjectTeamMapper projectTeamMapper;

    //项目团队的server
    private final ProjectTeamServiceImpl projectTeamService;
    private final ControlsLogService controlsLogService;
    private final SendBillMsgUtil sendBillMsgUtil;
    private final SystemMessageService systemMessageService;
    private final IHistoricProcessService historicProcessService;

    private final OtherProjectGroupMapper otherProjectGroupMapper;
    private final ArchiveDetailsMapper archiveDetailsMapper;
    @Override
    public Map startProcess(ContractVO contract) {
//        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.CONTRACT_KEY);
        BladeFlow flow = null;
        // 返回值
        Map result = new HashMap();
        // 单据状态为审批中
        contract.setStatus(0);
        // 当前审批人默认自己
        Long userId = AuthUtil.getUserId();
        contract.setTaskUser(String.valueOf(userId));
        // 用章类型修改类型
        StringBuilder ids = new StringBuilder();
        StringBuilder vals = new StringBuilder();
        if (contract.getUseChapterTypeList() != null && !contract.getUseChapterTypeList().isEmpty()) {
            for (ContractUseChapterType c : contract.getUseChapterTypeList()) {
                ids.append(c.getId());
                ids.append(",");
                vals.append(c.getName());
                vals.append(",");
            }
        }
        ids.deleteCharAt(ids.length() - 1);
        vals.deleteCharAt(vals.length() - 1);
        contract.setUseChapterTypeIds(ids.toString());
        contract.setUseChapterTypeVals(vals.toString());

        // 有id且状态位置不为草稿则更新、无id保存,
        if (Func.isEmpty(contract.getId())) {
            if (save(contract)) {
                saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
                //启动审批流
                initCompleteTask(contract);
                saveLog(contract.getProjectId());
                extracted(contract);
            }

//            // 启动流程
//            Kv variables = Kv.create()
//                    .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserName())
//                    .set("applyUser", TaskUtil.getTaskUser())
//                    .set("contractId", contract.getId());
//
//            flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
//            if (Func.isNotEmpty(flow)) {
//                log.debug("流程已启动,流程ID:" + flow.getProcessInstanceId());
//                // 返回流程id写入合同
//                contract.setProcessInstanceId(flow.getProcessInstanceId());
//                updateById(contract);
//                extracted(contract);
//            } else {
//                throw new ServiceException("开启流程失败");
//            }
        } else {
            if (updateById(contract)) {
                saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
                //启动审批流
                initCompleteTask(contract);
                extracted(contract);
            }
            //if (contract.getProcessInstanceId() == null || contract.getProcessInstanceId().equals("")) {
            // 是草稿
            // 启动流程
//            Kv variables = Kv.create()
//                    .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserName())
//                    .set("applyUser", TaskUtil.getTaskUser())
//                    .set("contractId", contract.getId());
//            flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
//            if (Func.isNotEmpty(flow)) {
//                log.debug("流程已启动,流程ID:" + flow.getProcessInstanceId());
//                // 返回流程id写入合同
//                contract.setProcessInstanceId(flow.getProcessInstanceId());
//                updateById(contract);
//                extracted(contract);
//            } else {
//                throw new ServiceException("开启流程失败");
//            }
           /* } else {
                updateById(contract);
            }*/
        }
        // 写入记录日志表
        CustomerConnectLog contractChangeLog = new CustomerConnectLog();
        contractChangeLog.setCustomerId(Long.valueOf(contract.getSignCompanyId()));
        contractChangeLog.setAboutDescription("添加了渠道合同");
        contractChangeLog.setDescription("添加了渠道合同");
        contractChangeLog.setChangeTable(TableIdEnum.合同.getTableId());
        contractChangeLog.setContactId(contract.getId());
        contractChangeLog.setChargeUser(AuthUtil.getUserId());
        customerConnectLogService.save(contractChangeLog);
        result.put("name", contract.getName());
        if (flow != null) {
            result.put("processInstanceId", flow.getProcessInstanceId());
        } else {
            result.put("processInstanceId", contract.getProcessInstanceId());
        }
        result.put(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserName());
        String substring = TaskUtil.getTaskUser().substring(TaskUtil.getTaskUser().lastIndexOf("_") + 1);
        result.put("applicantUserId", substring);
        result.put("applicantUser", userService.getById(substring).getName());
        result.put("applyUser", TaskUtil.getTaskUser());
        result.put("contractId", contract.getId());
        return result;
    }

    private void extracted(ContractVO contract) {
        //                判断是否是这个项目第一次起合同，如果是 第一次发起合同 在去判断 阶段是这什么阶段，
//                如果是合同签订之前 直接跳到合同签订阶段，之后的话不管。
        List<String> strings = baseMapper.selectProjectName(contract.getProjectId());
        if (strings.size() == 1) {
            Integer integer = projectBasicMapper.selectScheduledId(contract.getProjectId());
            if (integer != null && integer < ProgressEnum.合同签订.getCode()) {
//                  跳到 合同签订 阶段  如果之前的阶段有空的话 就直接删除掉
                List<ProjectProgress> progressList = projectProgressMapper.selectProgress(contract.getProjectId());
                for (ProjectProgress projectProgress : progressList) {
                    if (projectProgress.getCurrentProgressKey() < 3 && projectProgress.getCurrentProgressId() == null) {
                        projectProgress.setCurrentProgressId(11111L);
                        projectProgressMapper.updateById(projectProgress);
                    }
                }
                // 更新上个进度的更新时间
                ProjectProgress lastProgress = this.projectProgressMapper.selectProgresByProjectId(contract.getProjectId());
                if (lastProgress != null) {
                    lastProgress.setUpdateTime(DateUtil.now());
                    projectProgressMapper.updateById(lastProgress);
                }

//                UserProjectNodeVO userProjectNodeVO = userProjectNodeMapper.selectAll(ProgressEnum.合同签订.getCode(), 0);
//                        合同签订的负责人 加入到小表中
                ProjectProgress projectProgress = new ProjectProgress();
                projectProgress.setProjectId(contract.getProjectId());
                projectProgress.setCurrentProgressKey(ProgressEnum.合同签订.getCode());
                Long principalId = Optional.ofNullable(projectBasicMapper.selectById(contract.getProjectId())).map(ProjectBasic::getPrincipalId).orElse(null);
                projectProgress.setStageLeaderId(principalId);
                projectProgress.setStageLeaderName(Func.isEmpty(principalId) ? null : projectProgressMapper.userName(principalId));
                projectProgress.setCurrentProgressId(null);
                projectProgress.setCreateTime(DateUtil.now());
                projectProgress.setStatus(2);
                projectProgressMapper.insert(projectProgress);

                //            添加到团队表中
                ProjectTeam projectTeam = new ProjectTeam();
                projectTeam.setProjectId(contract.getProjectId());
                projectTeam.setUserId(projectProgress.getStageLeaderId());
                projectTeam.setAdmin(1);
                projectTeam.setNodeId(Long.valueOf(projectProgress.getCurrentProgressKey()));
                projectTeam.setCreatePost(projectTeamMapper.UserPostId(projectProgress.getStageLeaderId()));
                projectTeamService.save(projectTeam);

                //先去把项目基本信息表的 这个项目 的 状态给更新了
                ProjectBasic basic = new ProjectBasic();
                basic.setId(contract.getProjectId());
                basic.setScheduleId(Long.valueOf(projectProgress.getCurrentProgressKey()));
                basic.setPushDown(1);
                //项目的 进度状态给更新了
                int updateById = projectBasicMapper.updateById(basic);

//                //金蝶中的 项目进度 对应的 进度id
//                String xmjd = ProjectXMJD.XMJD(basic.getScheduleId().toString());
//                //下面就是 到 金蝶 去更新
//                boolean b = projectProgressMapper.updateXmjd(basic.getKdProjectFid(), xmjd);

            }
        }
    }

    private void saveLog(Long pid) {
        Integer integer = projectBasicMapper.selectScheduledId(pid);
        if (integer != null && integer < ProgressEnum.合同签订.getCode()) {
            LogModel log = new LogModel();
            log.setUserId(AuthUtil.getUserId());
            log.setMsg("项目进度更新至合同签订");
            log.setPid(pid);
            log.setType(2);
            controlsLogService.saveLog(log);
        }
    }

    @Override
    public ContractVO selectDetail(Map map) {
        return baseMapper.selectDetail(map);
    }

    @Override
    public ContractVO selectDraft(Map map) {
        return baseMapper.selectDraft(map);
    }

    @Override
    public R initContract() {
        Map result = new HashMap();
        Kv variables = Kv.create()
                .set(ProcessConstant.TASK_VARIABLE_CREATE_USER, AuthUtil.getUserId());
        Map<String, Object> variable = variables;
        // TODO 刘源兴工作流
//        String processId = flowEngineService.getProcessId("渠道合同", "channelContract");
        String processId = null;
        if ("".equals(processId)) {
            return R.fail(500, "流程不存在");
        }
        List<Long> assigneeList = userService.getALlParentByUserId(AuthUtil.getUserId());
        if (assigneeList != null && assigneeList.size() > 0) {
            variables.put("assigneeList", assigneeList);
        }
        // TODO 刘源兴工作流
//        List<BladeFlow> bladeFlowList = flowEngineService.getFLowPrediction(processId, variable);
        List<BladeFlow> bladeFlowList = null;
        result.put("approvalList", bladeFlowList);

        Map map = new HashMap();
        map.put("type", 4);
        map.put("userId", AuthUtil.getUserId());
        ContractVO contractVO = selectDraft(map);
        if (contractVO != null) {
            if (contractVO.getUseChapterTypeIds() != null && (!contractVO.getUseChapterTypeIds().equals(""))) {
                //        用章类型转为数组类型
                List ids = Arrays.stream(contractVO.getUseChapterTypeIds().split(",")).map(s -> s.trim()).collect(Collectors.toList());
                List vals = Arrays.stream(contractVO.getUseChapterTypeVals().split(",")).map(s -> s.trim()).collect(Collectors.toList());
                List<ContractUseChapterType> contractUseChapterTypes = new ArrayList<>();
                for (int i = 0; i < ids.size(); i++) {
                    ContractUseChapterType c = new ContractUseChapterType();
                    c.setId(ids.get(i).toString());
                    c.setName(vals.get(i).toString());
                    contractUseChapterTypes.add(c);
                }
                contractVO.setUseChapterTypeList(contractUseChapterTypes);
            }
        }
        result.put("contract", contractVO);

        return R.data(result);
    }

    @Override
    public ContractVO tidyUpContract(Contract contract) {
        ContractVO contractVO = ContractWrapper.build().entityVO(contract);
        //     重新获取合同的业务类型（2024/07/31项目的分组和业务类型可以自己配置并修改，需要根据id进行联查）
//        if (contractVO.getProjectId() != null && contractVO.getProjectId() != -1l) {
////        根据项目id获取业务类型id和业务类型名称(业务类型id/业务类型名称之前存的是金蝶项目分组id和名称，为了影响小，现在放的也是项目的金蝶分组id和名称)
//            ProjectGroupDisposition group = projectBasicMapper.getGroupByProjectId(contractVO.getProjectId());
//            if (group != null) {
//                contractVO.setProjectTypeId(group.getKingdeeId());
//                contractVO.setProjectTypeName(group.getName());
//            }
//        }
        // 销售合同俺项目类型区分集成信息项目/安服项目
//        contractVO.setLargeType(contractVO.getProjectTypeId().intValue());
        // 判断是否安服项目
//        Integer integer = projectGroupService.selectLargeType(contractVO.getProjectTypeId());
//        if (integer > 0) {
//            contractVO.setLargeType(2); // 安服
//        } else {
//            ProjectGroup projectGroup = projectGroupService.getById(contractVO.getProjectTypeId());
//            if (projectGroup.getId().equals(147132l) || projectGroup.getPid().equals(147132l)) {
//                contractVO.setLargeType(4); // 司法鉴定类
//            } else {
//                contractVO.setLargeType(1); // 非安服
//            }
//        }
        if (Func.isNotEmpty(contract.getSigningMode())) {
            if (contract.getSigningMode().equals("direct")) {
                contractVO.setSigningModeName("直签签约");
            } else {
                contractVO.setSigningModeName("渠道签约");
            }
        }
        if (contractVO.getUseChapterTypeIds() != null && (!contractVO.getUseChapterTypeIds().equals(""))) {
            //        用章类型转为数组类型
            List ids = Arrays.stream(contractVO.getUseChapterTypeIds().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List vals = Arrays.stream(contractVO.getUseChapterTypeVals().split(",")).map(s -> s.trim()).collect(Collectors.toList());
            List<ContractUseChapterType> contractUseChapterTypes = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                ContractUseChapterType c = new ContractUseChapterType();
                c.setId(ids.get(i).toString());
                c.setName(vals.get(i).toString());
                contractUseChapterTypes.add(c);
            }
            contractVO.setUseChapterTypeList(contractUseChapterTypes);
        }
        // 发起人姓名
        User createUser = userService.getUserByIdNotDelete(contractVO.getCreateUser());
        // 判断当前合同是否有归档数据,有归档数则全员三页，无归档数据则仅合同发起人有归档页
        ArchiveDetails archiveDetails = archiveDetailsService.queryByContractId(contractVO.getId());
        if (archiveDetails != null) {
            contractVO.setIsOwner(true);
            ArchiveDetailsFiles fileList= archiveDetailsMapper.countArchiveDetailsFiles(archiveDetails.getId());
            if(fileList!=null){
                contractVO.setArchiveDetailsFiles(fileList);
            }
        } else {
            if (createUser.getId().longValue() == AuthUtil.getUserId().longValue()) {
                contractVO.setIsOwner(true);
            }
        }
//        List deptIds = Arrays.asList(createUser.getDeptId().split(","));
        contractVO.setCreateUserName(createUser.getName());
        //未审批
        if (contractVO.getStatus() == ApprovedEnum.UNAPRROVED.getIndex()) {
            if (AuthUtil.getUserId().longValue() == contractVO.getCreateUser().longValue()) {
                contractVO.setWithdraw(true);
            }
            HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(contractVO.getId());
            if (historicProcess != null) {
                if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                    contractVO.setApproval(true);
                }
            }

        }
        //审批中
        if (contractVO.getStatus() == ApprovedEnum.APPROVING.getIndex()) {
            HistoricProcessEntity historicProcess = historicProcessService.getDetailByPid(contractVO.getId());
            if (historicProcess != null) {
                if (Func.equals(historicProcess.getAssigneeId(), AuthUtil.getUserId().toString())) {
                    contractVO.setApproval(true);
                }
            }
        }

//        // 流程[有流程实例id的时候返回审批流程，没有的为草稿，不返回流程]
//        List<Approval> approvals;
//        if (contractVO.getProcessDefinitionId() != null && (!contractVO.getProcessDefinitionId().equals(""))) {
//            List<BladeFlow> flowList = flowEngineService.getALLFlowNode(contractVO.getProcessInstanceId());
//            // 给前端返回审批按钮状态位（是否可撤回，是否可审批）
//            // 单据审批中，且当前登陆人为发起人，流程第二个人正在审批中时可撤回
//            if (null != flowList && flowList.size()>1) {
//                if (contractVO.getStatus() == 0 && contractVO.getCreateUser().longValue() == AuthUtil.getUserId().longValue() && "0".equals(flowList.get(1).getStatus())) {
//                    contractVO.setWithdraw(true);
//                }
//            }else{
//                if (contractVO.getStatus() != 2) {
//                    contractVO.setWithdraw(true);
//                }
//            }
//            // 单据为审批中，当前登录人为当前流程审批人，状态为审批中时，可审批
//            if (contractVO.getStatus() == 0 && contractVO.getTaskUser().contains(AuthUtil.getUserId().toString())) {
//                for (BladeFlow a : flowList) {
//                    if ("0".equals(a.getStatus()) && a.getAssignee().equals(AuthUtil.getUserId().toString())) {
//                        contractVO.setApproval(true);
//                    }
//                }
//            }
//            contractVO.getApprovalList().addAll(flowList);
//
//        }
        Map map = new HashMap();
        map.put("allowArchivingCondition", "true");
        map.put("contractId", contractVO.getId());
        Integer allowArchivingCount = contractChangeMapper.countChange(map);
        if (allowArchivingCount > 0) {
            contractVO.setAllowArchiving(true);
        }
        List<OtherProjectGroupBO> bySourceId = otherProjectGroupMapper.getBySourceId(contract.getId());
        contractVO.setContractProjectGroupList(bySourceId);
        return contractVO;
    }

    @Override
    public Map saveDraft(ContractVO contract) {
        Map result = new HashMap();
        if (contract.getUseChapterTypeList() != null && !contract.getUseChapterTypeList().isEmpty()) {
            //        用章类型修改类型
            StringBuilder ids = new StringBuilder();
            StringBuilder vals = new StringBuilder();
            for (ContractUseChapterType c : contract.getUseChapterTypeList()) {
                ids.append(c.getId());
                ids.append(",");
                vals.append(c.getName());
                vals.append(",");
            }
            ids.deleteCharAt(ids.length() - 1);
            vals.deleteCharAt(vals.length() - 1);
            contract.setUseChapterTypeIds(ids.toString());
            contract.setUseChapterTypeVals(vals.toString());
        }
        boolean status = false;
        if (contract.getId() != null && StringUtil.isNotBlank(contract.getId().toString())) {
            status = updateById(contract);
        } else {
            status = save(contract);
        }
        if(status){

            saveContractProjectGroup(contract.getId(),contract.getContractProjectGroupList());
        }
        result.put("status", status);
        result.put("contractId", contract.getId());
        return result;
    }
    private void saveContractProjectGroup(Long contractId,List<OtherProjectGroupBO> groupBOList){
        otherProjectGroupMapper.removeBySourceId(contractId);
        for (OtherProjectGroupBO otherProjectGroupBO : groupBOList) {
            otherProjectGroupBO.setId(IdWorker.getId());
            otherProjectGroupBO.setOtherSourceId(contractId);
            otherProjectGroupBO.setOtherSourceTypeName(SourceTypeEnum.合同.getCode());
            otherProjectGroupBO.setCreateTime(LocalDateTime.now());
            otherProjectGroupBO.setCreateUser(AuthUtil.getUserId());
            otherProjectGroupMapper.insert(otherProjectGroupBO);
        }
    }
    /**
     * 发起合同开启审批流程
     *
     * @param contract
     * @return
     */
    private boolean initCompleteTask(ContractVO contract) {
        // 启动流程
        Kv variables = Kv.create()
                .set("superior", userService.getParentIdByUserId(AuthUtil.getUserId()))
                .set(FlowFormConstant.PROC_INSTANCE_FORM_NAME, FormNameConstant.CONTRACT)
                .set(FlowFormConstant.PROC_INSTANCE_FORM_DATA_ID, contract.getId());
        String businessTable = FlowUtil.getBusinessTable(ProcessConstant.CONTRACT_KEY);
// TODO 刘源兴工作流
//        BladeFlow flow = flowService.startProcessInstanceById(contract.getProcessDefinitionId(), FlowUtil.getBusinessKey(businessTable, String.valueOf(contract.getId())), variables);
        BladeFlow flow = null;
        if (Func.isNotEmpty(flow)) {
            contract.setProcessInstanceId(flow.getProcessInstanceId());
            //反写流程id
            update(Wrappers.<Contract>update().lambda()
                    .set(Contract::getProcessInstanceId, flow.getProcessInstanceId())
                    .set(Contract::getStatus, ApprovedEnum.UNAPRROVED.getIndex())
                    .eq(Contract::getId, contract.getId()));

            HistoricProcessEntity historicProcessEntity = new HistoricProcessEntity();
            historicProcessEntity.setPid(contract.getId());
            historicProcessEntity.setBillNo(contract.getCode());
            historicProcessEntity.setBillName(contract.getName());
            historicProcessEntity.setMoney(contract.getContractAmount());
            historicProcessEntity.setType(FormNameConstant.CONTRACT);
            //合同类型（0销售合同/1采购合同/2其他合同/4渠道合同）
            historicProcessEntity.setTypeName("渠道合同");
            historicProcessEntity.setBusinessType(BusinessTypeEnum.合同.getCode());
            historicProcessEntity.setBillsType(contract.getContractType());

            historicProcessEntity.setBillName(contract.getName());

            historicProcessService.saveHitoricProcessStart(historicProcessEntity, flow.getProcessInstanceId());
            return true;
        }
        return false;
    }
}
