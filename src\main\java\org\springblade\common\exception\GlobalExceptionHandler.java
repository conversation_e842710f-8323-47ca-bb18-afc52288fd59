package org.springblade.common.exception;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.ErrorCodeEnum;
import org.springblade.common.model.ErrorResponse;
import org.springblade.common.utils.DatabaseExceptionFormatter;
import org.springblade.common.utils.ExceptionLogFormatter;
import org.springblade.common.utils.ExceptionUtil;
import org.springblade.common.utils.api.BusinessException;
import org.springblade.common.utils.api.SystemException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestControllerAdvice
@Order(1000) // 设置较低优先级，确保其他特定异常处理器先执行
@Slf4j
public class GlobalExceptionHandler {

    @Value("${spring.profiles.active:prod}")
    private String activeProfile;

    @Autowired
    private DatabaseExceptionFormatter databaseExceptionFormatter;

    @Autowired
    private ExceptionLogFormatter exceptionLogFormatter;


    /**
     * 处理 ServiceException ,该异常正常转成R返回
     */
    @ExceptionHandler(ServiceException.class)
    public R handleServiceException(Exception e) {
        return R.fail(e.getMessage());
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public R<ErrorResponse> handleBusinessException(BusinessException e) {
        String userMessage = e.getMessage();
        String technicalMessage = "业务异常：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logBusinessException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.BUSINESS_ERROR, e.getMessage());
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.BUSINESS_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public R<ErrorResponse> handleSystemException(SystemException e) {
        String userMessage = "系统内部错误，请稍后重试";
        String technicalMessage = "系统异常：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logSystemException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.INTERNAL_SERVER_ERROR, e.getMessage());
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.INTERNAL_SERVER_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理租户异常
     */


    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<ErrorResponse> handleValidationException(MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        String userMessage = "参数校验失败：" + errorMessage;
        String technicalMessage = "MethodArgumentNotValidException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logValidationException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.VALIDATION_ERROR, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.VALIDATION_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public R<ErrorResponse> handleBindException(BindException e) {
        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        String userMessage = "参数绑定失败：" + errorMessage;
        String technicalMessage = "BindException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logValidationException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.VALIDATION_ERROR, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.VALIDATION_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<ErrorResponse> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        String userMessage = "约束违反：" + errorMessage;
        String technicalMessage = "ConstraintViolationException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logValidationException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.VALIDATION_ERROR, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.VALIDATION_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R<ErrorResponse> handleMissingParameterException(MissingServletRequestParameterException e) {
        String errorMessage = String.format("缺少必需的参数: %s", e.getParameterName());
        String userMessage = errorMessage;
        String technicalMessage = "MissingServletRequestParameterException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logRequestException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.BAD_REQUEST, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.BAD_REQUEST.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<ErrorResponse> handleTypeMismatchException(MethodArgumentTypeMismatchException e) {
        String errorMessage = String.format("参数 '%s' 类型不正确，期望类型: %s",
                e.getName(), e.getRequiredType().getSimpleName());
        String userMessage = errorMessage;
        String technicalMessage = "MethodArgumentTypeMismatchException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logRequestException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.BAD_REQUEST, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.BAD_REQUEST.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R<ErrorResponse> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        String userMessage = "请求体格式错误或不可读";
        String technicalMessage = "HttpMessageNotReadableException：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logRequestException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.BAD_REQUEST, "请求体格式错误或不可读");
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.BAD_REQUEST.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R<ErrorResponse> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        String errorMessage = String.format("不支持的请求方法: %s", e.getMethod());
        String userMessage = errorMessage;
        String technicalMessage = "HttpRequestMethodNotSupportedException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logRequestException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.BAD_REQUEST, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.BAD_REQUEST.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public R<ErrorResponse> handleNoHandlerFoundException(NoHandlerFoundException e) {
        String errorMessage = String.format("请求的资源不存在: %s %s", e.getHttpMethod(), e.getRequestURL());
        String userMessage = errorMessage;
        String technicalMessage = "NoHandlerFoundException：" + errorMessage;

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logRequestException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.NOT_FOUND, errorMessage);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.NOT_FOUND.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理数据库访问异常 - 统一入口
     * 确保所有数据库异常都能提供具体的信息
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class, DuplicateKeyException.class, SQLIntegrityConstraintViolationException.class})
    public R<ErrorResponse> handleDatabaseException(Exception e) {
        // 获取用户友好的错误消息 - 这里会调用DatabaseExceptionFormatter提供具体信息
        String userFriendlyMessage = databaseExceptionFormatter.formatDatabaseException(e);
        String fixSuggestion = databaseExceptionFormatter.getFixSuggestion(e);

        // 获取技术详情
        Throwable rootCause = ExceptionUtil.getRootCause(e);
        String technicalDetails = rootCause.getMessage();

        // 检查是否为已知的特定数据库异常
        if (rootCause instanceof DuplicateKeyException ||
            rootCause instanceof SQLIntegrityConstraintViolationException ||
            e instanceof DuplicateKeyException ||
            e instanceof SQLIntegrityConstraintViolationException ||
            (rootCause.getMessage() != null && rootCause.getMessage().contains("Duplicate entry"))) {

            // 使用统一的日志格式化工具输出单行ERROR日志
            exceptionLogFormatter.logUniqueConstraintException(e, userFriendlyMessage, technicalDetails);

            ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.DATABASE_ERROR);
            errorResponse.setMessage(userFriendlyMessage);
            errorResponse.setSuggestion(fixSuggestion);
            setCommonErrorInfo(errorResponse, e);

            R<ErrorResponse> result = R.fail(ErrorCodeEnum.DATABASE_ERROR.getCode(), userFriendlyMessage);
            result.setData(errorResponse);
            return result;
        }

        // 其他数据库异常的处理
        exceptionLogFormatter.logDatabaseException(e, userFriendlyMessage, technicalDetails);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.DATABASE_ERROR);
        errorResponse.setMessage(userFriendlyMessage);
        errorResponse.setSuggestion(fixSuggestion);
        setCommonErrorInfo(errorResponse, e);

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.DATABASE_ERROR.getCode(), userFriendlyMessage);
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理网络连接异常
     */
    @ExceptionHandler({ConnectException.class, SocketTimeoutException.class})
    public R<ErrorResponse> handleNetworkException(Exception e) {
        String userMessage = "网络连接异常，请检查网络连接";
        String technicalMessage = "NetworkException：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logNetworkException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.NETWORK_ERROR);
        errorResponse.setDetails("网络连接异常，请检查网络连接");
        setCommonErrorInfo(errorResponse, e);
        errorResponse.generateSuggestion();

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.NETWORK_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public R<ErrorResponse> handleNullPointerException(NullPointerException e) {
        String userMessage = "系统内部错误：空指针异常";
        String technicalMessage = "NullPointerException：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logNullPointerException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse = new ErrorResponse(ErrorCodeEnum.INTERNAL_SERVER_ERROR);
        errorResponse.setDetails("系统内部错误：空指针异常");
        setCommonErrorInfo(errorResponse, e);

        // 在开发环境显示详细堆栈信息
        if (isDevelopmentEnvironment()) {
            errorResponse.setSuggestion("空指针异常修复建议：\n1. 检查代码中的空值判断\n2. 确保对象在使用前已正确初始化\n3. 使用Optional或其他空安全方法");
        }

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.INTERNAL_SERVER_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }





    /**
     * 处理其他所有未被捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public R<ErrorResponse> handleGenericException(Exception e) {
        String userMessage = "系统内部错误，请稍后重试";
        String technicalMessage = "UnhandledException：" + e.getMessage();

        // 使用统一的日志格式化器打印堆栈信息
        exceptionLogFormatter.logUnknownException(e, userMessage, technicalMessage);

        ErrorResponse errorResponse;
        String exceptionName = e.getClass().getSimpleName();

        // 1. 检查是否为真正的租户业务异常（而非包装异常）
        if (exceptionName.toLowerCase().contains("tenant") &&
            !exceptionName.contains("TenantDataSource")) {

            log.info("检测到真正的租户业务异常: {}", exceptionName);

            // 根据异常类型选择合适的错误码
            ErrorCodeEnum errorCode = ErrorCodeEnum.TENANT_INVALID;
            String details = "租户相关业务处理失败";
            String suggestion = "检查租户配置和权限";

            // 分析具体的租户异常类型
            if (exceptionName.toLowerCase().contains("notfound") ||
                exceptionName.toLowerCase().contains("not_found")) {
                errorCode = ErrorCodeEnum.TENANT_NOT_FOUND;
                details = "租户不存在或已被删除";
                suggestion = "检查租户ID是否正确，确认租户状态";
            } else if (exceptionName.toLowerCase().contains("permission") ||
                       exceptionName.toLowerCase().contains("access")) {
                errorCode = ErrorCodeEnum.TENANT_INVALID;
                details = "租户权限不足或访问被拒绝";
                suggestion = "联系管理员分配相应的租户权限";
            } else if (exceptionName.toLowerCase().contains("expired") ||
                       exceptionName.toLowerCase().contains("expire")) {
                errorCode = ErrorCodeEnum.TENANT_EXPIRED;
                details = "租户使用期限已过期";
                suggestion = "联系管理员进行租户续费操作";
            }

            errorResponse = new ErrorResponse(errorCode);
            errorResponse.setDetails(String.format("%s：%s", details,
                StringUtil.isBlank(e.getMessage()) ? "无具体错误信息" : e.getMessage()));
            errorResponse.setExceptionType(exceptionName);

            setCommonErrorInfo(errorResponse, e);

            if (isDevelopmentEnvironment()) {
                errorResponse.setSuggestion(String.format(
                    "租户异常处理建议：\n" +
                    "1. %s\n" +
                    "2. 验证租户状态和有效期\n" +
                    "3. 确认租户数据源配置\n" +
                    "4. 异常类型：%s\n" +
                    "5. 如果是新的租户异常，可在GlobalExceptionHandler中添加专门处理",
                    suggestion, exceptionName));
            }

            R<ErrorResponse> result = R.fail(errorCode.getCode(), errorResponse.getMessage());
            result.setData(errorResponse);
            return result;
        }

        // 2. 深度分析异常，找出真正的根本原因
        Throwable rootCause = ExceptionUtil.getRootCause(e);
        Exception actualException = (Exception) rootCause;

        // 3. 特殊处理租户数据源异常包装：解析出真正的异常
        if (e.getClass().getSimpleName().contains("TenantDataSource")) {
            log.warn("检测到租户数据源异常包装，分析根本原因...");

            // 如果根本原因是空指针异常，按空指针异常处理
            if (rootCause instanceof NullPointerException) {
                log.warn("租户数据源异常的根本原因是空指针异常: {}", rootCause.getMessage());
                return handleNullPointerException((NullPointerException) rootCause);
            }

            // 记录详细的异常分析
            StringBuilder analysisLog = new StringBuilder();
            analysisLog.append("租户数据源异常分析：\n");
            analysisLog.append("- 包装异常类型: ").append(e.getClass().getSimpleName()).append("\n");
            analysisLog.append("- 包装异常消息: ").append(e.getMessage()).append("\n");
            analysisLog.append("- 根本异常类型: ").append(rootCause.getClass().getSimpleName()).append("\n");
            analysisLog.append("- 根本异常消息: ").append(rootCause.getMessage()).append("\n");
            analysisLog.append("- 异常链路深度: ");

            Throwable current = e;
            int depth = 0;
            while (current.getCause() != null && current.getCause() != current) {
                depth++;
                current = current.getCause();
                analysisLog.append(depth).append(":").append(current.getClass().getSimpleName()).append(" -> ");
            }

            log.warn(analysisLog.toString());

            // 根据根本原因创建响应
            errorResponse = ExceptionUtil.buildErrorResponse(actualException);
            errorResponse.setDetails(String.format("租户数据源处理时发生异常，根本原因：%s - %s",
                    rootCause.getClass().getSimpleName(),
                    StringUtil.isBlank(rootCause.getMessage()) ? "无具体错误信息" : rootCause.getMessage()));

            // 添加租户数据源相关的修复建议
            if (isDevelopmentEnvironment()) {
                String suggestion = "租户数据源异常修复建议：\n" +
                        "1. 检查租户配置和数据源映射\n" +
                        "2. 根本原因是 " + rootCause.getClass().getSimpleName() + "，请检查相关代码逻辑\n" +
                        "3. 确保数据对象在使用前已正确初始化\n" +
                        "4. 验证当前用户的租户权限和数据访问权限";

                if (rootCause instanceof NullPointerException) {
                    suggestion += "\n5. 特别注意：空指针异常通常是对象未初始化或查询结果为空导致";
                }

                errorResponse.setSuggestion(suggestion);
            }
        } else {
            errorResponse = ExceptionUtil.buildErrorResponse(e);
        }

        // 在生产环境隐藏敏感信息
        if (!isDevelopmentEnvironment()) {
            if (StringUtil.isBlank(errorResponse.getDetails())) {
                errorResponse.setDetails("系统内部错误，请联系管理员");
            }
        }

        R<ErrorResponse> result = R.fail(ErrorCodeEnum.INTERNAL_SERVER_ERROR.getCode(), errorResponse.getMessage());
        result.setData(errorResponse);
        return result;
    }

    /**
     * 设置通用错误信息
     */
    private void setCommonErrorInfo(ErrorResponse errorResponse, Exception e) {
        // 设置请求信息
        ExceptionUtil.buildErrorResponse(e);

        // 注意：不再在这里设置堆栈信息，因为 ErrorResponse 构造函数中已经设置了过滤后的堆栈信息
        // 避免覆盖已经过滤的业务代码堆栈信息
    }

    /**
     * 判断是否为开发环境
     */
    private boolean isDevelopmentEnvironment() {
        return "dev".equals(activeProfile) || "test".equals(activeProfile);
    }

    /**
     * 测试数据库异常格式化（用于调试）
     * 这个方法可以用来测试各种数据库异常的格式化效果
     */
    public String testDatabaseExceptionFormat(String errorMessage) {
        if (!isDevelopmentEnvironment()) {
            return "测试方法仅在开发环境可用";
        }

        try {
            // 创建一个模拟的数据库异常
            Exception testException = new SQLException(errorMessage);
            return databaseExceptionFormatter.formatDatabaseException(testException);
        } catch (Exception e) {
            return "测试失败：" + e.getMessage();
        }
    }

    /**
     * 获取Excel导入操作失败的错误信息（不记录日志）
     * 这个方法只返回格式化的错误信息，不记录日志，用于避免重复日志记录
     *
     * @param operation 操作描述（如"保存客户联系人"）
     * @param entity 操作的实体对象
     * @return 格式化后的错误信息
     */
    public String getExcelImportOperationFailedMessage(String operation, Object entity) {
        // 返回用户友好的错误信息，但不记录日志
        StringBuilder userMessage = new StringBuilder();
        userMessage.append(operation).append("失败：操作未能成功完成");

        // 根据实体类型提供具体的建议
        if (entity != null) {
            String entityType = entity.getClass().getSimpleName();
            if (entityType.contains("Customer")) {
                userMessage.append("，请检查客户信息是否完整且唯一");
            } else if (entityType.contains("Contact")) {
                userMessage.append("，请检查联系人信息是否完整且唯一");
            } else {
                userMessage.append("，请检查数据是否完整且符合业务规则");
            }
        }

        userMessage.append("。常见原因包括：数据重复、必填字段缺失、格式不正确或权限不足");

        return userMessage.toString();
    }

    /**
     * 处理Excel导入过程中操作返回false的情况
     * 这个方法用于分析可能的失败原因并提供详细的错误信息
     *
     * @param operation 操作描述（如"保存客户联系人"）
     * @param entity 操作的实体对象
     * @return 格式化后的错误信息
     */
    public String handleExcelImportOperationFailed(String operation, Object entity) {
        // 记录操作失败的详细日志
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[Excel导入] ").append(operation).append("操作返回false");

        if (entity != null) {
            logMessage.append(" | 实体类型: ").append(entity.getClass().getSimpleName());
            logMessage.append(" | 实体信息: ").append(entity.toString());
        }

        // 尝试获取用户信息
        try {
            var user = org.springblade.core.secure.utils.AuthUtil.getUser();
            if (user != null) {
                logMessage.append(" | 用户: ").append(user.getUserName()).append("(").append(user.getUserId()).append(")");
                if (StringUtil.isNotBlank(user.getTenantId())) {
                    logMessage.append(" | 租户: ").append(user.getTenantId());
                }
            }
        } catch (Exception ignored) {
            // 忽略获取用户信息异常
        }

        // 添加可能的失败原因分析
        logMessage.append(" | 可能原因: 数据验证失败、唯一约束冲突、业务规则限制或权限不足");

        // 使用统一的日志格式化器记录
        log.warn(logMessage.toString());

        // 返回用户友好的错误信息
        StringBuilder userMessage = new StringBuilder();
        userMessage.append(operation).append("失败：操作未能成功完成");

        // 根据实体类型提供具体的建议
        if (entity != null) {
            String entityType = entity.getClass().getSimpleName();
            if (entityType.contains("Customer")) {
                userMessage.append("，请检查客户信息是否完整且唯一");
            } else if (entityType.contains("Contact")) {
                userMessage.append("，请检查联系人信息是否完整且唯一");
            } else {
                userMessage.append("，请检查数据是否完整且符合业务规则");
            }
        }

        userMessage.append("。常见原因包括：数据重复、必填字段缺失、格式不正确或权限不足");

        return userMessage.toString();
    }

    /**
     * 处理Excel导入过程中的异常
     * 这个方法专门用于Excel导入场景，只返回格式化后的错误信息，不处理HTTP响应
     *
     * @param e 异常对象
     * @param operation 操作描述（如"保存客户联系人"）
     * @return 格式化后的错误信息
     */
    public String handleExcelImportException(Exception e, String operation) {
        // 使用相应的异常处理器进行处理，但只获取错误信息
        R<ErrorResponse> response = null;

        try {
            // 根据异常类型调用相应的处理方法
            if (e instanceof BusinessException) {
                response = handleBusinessException((BusinessException) e);
            } else if (e instanceof SystemException) {
                response = handleSystemException((SystemException) e);
            } else if (e instanceof java.sql.SQLException ||
                       e instanceof org.springframework.dao.DataAccessException ||
                       e instanceof org.springframework.dao.DuplicateKeyException ||
                       e instanceof java.sql.SQLIntegrityConstraintViolationException) {
                // 所有数据库异常统一处理
                response = handleDatabaseException(e);
            } else if (e instanceof NullPointerException) {
                response = handleNullPointerException((NullPointerException) e);
            } else if (e instanceof javax.validation.ConstraintViolationException) {
                response = handleConstraintViolationException((javax.validation.ConstraintViolationException) e);
            } else {
                // 其他异常统一处理
                response = handleGenericException(e);
            }

            // 从响应中提取错误信息
            if (response != null && response.getData() != null) {
                ErrorResponse errorResponse = response.getData();
                StringBuilder message = new StringBuilder();
                message.append(operation).append("失败：").append(errorResponse.getMessage());

                if (errorResponse.getDetails() != null && !errorResponse.getDetails().isEmpty()) {
                    message.append("，详情：").append(errorResponse.getDetails());
                }

                if (errorResponse.getSuggestion() != null && !errorResponse.getSuggestion().isEmpty()) {
                    message.append("，建议：").append(errorResponse.getSuggestion());
                }

                return message.toString();
            }

        } catch (Exception ex) {
            // 如果在处理过程中出现异常，记录日志并返回基础错误信息
            log.warn("Excel导入异常处理失败: {}", ex.getMessage());
        }

        // 如果上述处理都失败，返回基础错误信息
        return operation + "失败：" + e.getMessage();
    }
}
