package org.springblade.flow.engine.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.BpmnAutoLayout;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ValidationError;

import org.springblade.core.tool.utils.Func;
import org.springblade.flow.engine.listener.flowable.ProcessFinishedListener;
import org.springblade.flow.engine.utils.HtmlUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 〈功能简述〉<br>
 * 〈基于 json 格式自动生成 bpmn 文件〉
 *
 * <AUTHOR>
 * @date 2021-09-07
 * @since 1.0.0
 */
@Setter
public class BpmnBuilder {


	private BpmnModel model;
	private Process process;
	private List<SequenceFlow> sequenceFlows;

//	public static void main(String[] args) {
//        String json = "{\n" +
//                "    \"process\":{\n" +
//                "        \"processId\":\"work-flow-id\",\n" +
//                "        \"name\":\"自动生成\"\n" +
//                "    },\n" +
//                "    \"processNode\":{\n" +
//                "        \"nodeName\":\"审核人\",\n" +
//                "        \"id\":\"user-78a98767-e311-4757-91fb-ccf1676a3b2b\",\n" +
//                "        \"jobType\":\"user-78a98767-e311-4757-91fb-ccf1676a3b2b\",\n" +
//                "        \"type\":\"task\",\n" +
//                "        \"nodeType\":\"serviceTask\",\n" +
//                "        \"nextNode\":{\n" +
//                "            \"nodeName\":\"审核人\",\n" +
//                "            \"id\":\"user-8f75a8f7-2e0d-4b56-b0de-dd9848dfe9f0\",\n" +
//                "            \"jobType\":\"user-8f75a8f7-2e0d-4b56-b0de-dd9848dfe9f0\",\n" +
//                "            \"type\":\"task\",\n" +
//                "            \"nodeType\":\"serviceTask\",\n" +
//                "            \"nextNode\":null,\n" +
//                "            \"error\":true\n" +
//                "        }\n" +
//                "    }\n" +
//                "}";
//
//        String s = build(json);
//        System.out.println(s);
//		System.out.println(IdUtil.objectId());
//	}


	public static BpmnModel build(String json) {

		try {
			BpmnBuilder builder = new BpmnBuilder();
			BpmnModel model = new BpmnModel();
			builder.setModel(model);
			Process process = new Process();
			builder.setProcess(process);
			List<SequenceFlow> sequenceFlows = CollUtil.newArrayList();
			builder.setSequenceFlows(sequenceFlows);
			JSONObject jsonObject = JSONUtil.parseObj(json);
			JSONObject workflow = jsonObject.getJSONObject("process");

			model.addProcess(process);
			process.setName(HtmlUtils.cleanHtmlTag(workflow.getStr("name")));
			process.setId(workflow.getStr("processId"));
			//bpmn开始节点
			StartEvent startEvent = builder.createStartEvent();
			process.addFlowElement(startEvent);
			JSONObject flowNode = jsonObject.getJSONObject("processNode");
			String lastNode = builder.create(startEvent.getId(), flowNode);
			// bpmn结束节点
			EndEvent endEvent = builder.createEndEvent();
			process.addFlowElement(endEvent);
//			process.addFlowElement(builder.connect(lastNode, endEvent.getId()));
			List<String> lastList = Func.toStrList(lastNode);
			lastList.forEach(last -> process.addFlowElement(builder.connect(last, endEvent.getId())));
			// 添加流程全局监听器
			addProcessInstanceEndListener(model, ProcessFinishedListener.class);
			new BpmnAutoLayout(model).execute();
			return model;
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("创建失败: e=" + e.getMessage());
		}
	}


	public static String buildStr(String json) {
		final BpmnModel model = build(json);
		return new String(new BpmnXMLConverter().convertToXML(model));
	}


	public static void validateBpmnModel(BpmnModel bpmnModel) {
		ProcessValidatorFactory processValidatorFactory = new ProcessValidatorFactory();
		ProcessValidator processValidator = processValidatorFactory.createDefaultProcessValidator();
		List<ValidationError> validate = processValidator.validate(bpmnModel);
		if (validate.size() >= 1) {
			for (ValidationError validationError : validate) {
				System.out.println(validationError.getProblem());
				System.out.println(validationError.isWarning());
			}
		}
	}

	public String id(String prefix) {
		return prefix + "_" + IdUtil.objectId();
	}


	private ServiceTask serviceTask(String name) {
		ServiceTask serviceTask = new ServiceTask();
		serviceTask.setName(name);
		return serviceTask;
	}

	private SequenceFlow connect(String from, String to) {
		SequenceFlow flow = new SequenceFlow();
		flow.setId(id("sequenceFlow"));
		flow.setSourceRef(from);
		flow.setTargetRef(to);
		sequenceFlows.add(flow);
		return flow;
	}

	private StartEvent createStartEvent() {
		StartEvent startEvent = new StartEvent();
		startEvent.setId(id("start"));
		return startEvent;
	}

	private EndEvent createEndEvent() {
		EndEvent endEvent = new EndEvent();
		endEvent.setId(id("end"));
		return endEvent;
	}

	private static void addProcessInstanceEndListener(BpmnModel bpmnModel, Class<? extends ExecutionListener> listenerClazz) {
		Process process = bpmnModel.getMainProcess();
		FlowableListener flowableListener = new FlowableListener();
		flowableListener.setEvent("end");
		flowableListener.setImplementationType("class");
		flowableListener.setImplementation(listenerClazz.getName());
		process.getExecutionListeners().add(flowableListener);
	}


	private String create(String fromId, JSONObject flowNode) throws InvocationTargetException, IllegalAccessException {
		String nodeType = flowNode.getStr("nodeType");
		if (Type.PARALLEL.isEqual(nodeType)) {
			return createParallelGatewayBuilder(fromId, flowNode);
		} else if (Type.EXCLUSIVE.isEqual(nodeType)) {
			return createExclusiveGatewayBuilder(fromId, flowNode);
		} else if (Type.USER_TASK.isEqual(nodeType)) {
			flowNode.set("incoming", Collections.singletonList(fromId));
			String id = createUserTask(flowNode);
			// 如果当前任务还有后续任务，则遍历创建后续任务
			JSONObject nextNode = flowNode.getJSONObject("nextNode");
			if (ObjectUtil.isNotNull(nextNode)) {
				FlowElement flowElement = model.getFlowElement(id);
				return create(id, nextNode);
			} else {
				return id;
			}
		} else if (Type.SERVICE_TASK.isEqual(nodeType)) {
			flowNode.set("incoming", Collections.singletonList(fromId));
			String id = createServiceTask(flowNode);
			// 如果当前任务还有后续任务，则遍历创建后续任务
			JSONObject nextNode = flowNode.getJSONObject("nextNode");
			if (ObjectUtil.isNotNull(nextNode)) {
				FlowElement flowElement = model.getFlowElement(id);
				return create(id, nextNode);
			} else {
				return id;
			}
		} else {
			throw new RuntimeException("未知节点类型: nodeType=" + nodeType);
		}
	}

	/**
	 * 排他网关
	 *
	 * @param formId
	 * @param flowNode
	 * @return
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 */
	private String createExclusiveGatewayBuilder(String formId, JSONObject flowNode) throws InvocationTargetException, IllegalAccessException {
		String name = flowNode.getStr("nodeName");
		String exclusiveGatewayId = id("exclusiveGateway");
		ExclusiveGateway exclusiveGateway = new ExclusiveGateway();
		exclusiveGateway.setId(exclusiveGatewayId);
		exclusiveGateway.setName(name);
		process.addFlowElement(exclusiveGateway);
		process.addFlowElement(connect(formId, exclusiveGatewayId));
		/**
		 * 不存在分支条件和下一个节点
		 */
		if (flowNode.isNull("branchNodes") && flowNode.isNull("nextNode")) {
			return exclusiveGatewayId;
		}
		// 处理分支节点
		List<JSONObject> branchNodes = Optional.ofNullable(flowNode.getJSONArray("branchNodes")).map(e -> e.toList(JSONObject.class)).orElse(Collections.emptyList());
		List<String> incoming = CollUtil.newArrayList();
		// 遍历分支节点
		List<JSONObject> conditions = Lists.newCopyOnWriteArrayList();
		for (JSONObject element : branchNodes) {
			JSONObject nextNode = element.getJSONObject("nextNode");

			String nodeName = element.getStr("nodeName");
			String expression = ExpressionsBuilder.build(element.getStr("conditionList"));

			if (JSONUtil.isNull(nextNode)) {
				incoming.add(exclusiveGatewayId);
				JSONObject condition = new JSONObject();
				condition.set("nodeName", nodeName).set("expression", expression);
				conditions.add(condition);
				continue;
			}
			nextNode.set("incoming", Collections.singletonList(exclusiveGatewayId));
			String identifier = create(exclusiveGatewayId, nextNode);
			// 寻找连接线 设置条件
			List<SequenceFlow> flows = sequenceFlows.stream().filter(flow -> StringUtils.equals(exclusiveGatewayId, flow.getSourceRef())).collect(Collectors.toList());
			flows.stream().forEach(e -> {
				if (StringUtils.isBlank(e.getName()) && StringUtils.isNotBlank(nodeName)) {
					e.setName(nodeName);
				}
				// 设置条件表达式
				if (Objects.isNull(e.getConditionExpression()) && StringUtils.isNotBlank(expression)) {
					e.setConditionExpression(expression);
				}
			});
			if (Objects.nonNull(identifier)) {
				incoming.add(identifier);
			}
		}

		// 处理主线
		JSONObject childNode = flowNode.getJSONObject("nextNode");
		if (Objects.nonNull(childNode)) {
			if (incoming == null || incoming.isEmpty()) {
				return create(exclusiveGatewayId, childNode);
			} else {
				// 所有 user task 连接 end exclusive gateway
				childNode.set("incoming", incoming);
				FlowElement flowElement = model.getFlowElement(incoming.get(0));
				// 1.0 先进行边连接, 暂存 nextNode
				JSONObject nextNode = childNode.getJSONObject("nextNode");
				String identifier = createUserTask(childNode);
				for (int i = 1; i < incoming.size(); i++) {
					process.addFlowElement(connect(incoming.get(i), identifier));
				}

				//  针对 gateway 空任务分支 添加条件表达式
				if (!conditions.isEmpty()) {
					FlowElement flowElement1 = model.getFlowElement(identifier);
					// 获取从 gateway 到目标节点 未设置条件表达式的节点
					List<SequenceFlow> flows = sequenceFlows.stream().filter(flow -> StringUtils.equals(flowElement1.getId(), flow.getTargetRef())).filter(flow -> StringUtils.equals(flow.getSourceRef(), exclusiveGatewayId)).collect(Collectors.toList());
					flows.stream().forEach(sequenceFlow -> {
						if (!conditions.isEmpty()) {
							JSONObject condition = conditions.get(0);
							String nodeName = condition.getStr("nodeName");
							String expression = ExpressionsBuilder.build(condition.getStr("conditionList"));

							if (StringUtils.isBlank(sequenceFlow.getName()) && StringUtils.isNotBlank(nodeName)) {
								sequenceFlow.setName(nodeName);
							}
							// 设置条件表达式
							if (Objects.isNull(sequenceFlow.getConditionExpression()) && StringUtils.isNotBlank(expression)) {
								sequenceFlow.setConditionExpression(expression);
							}

							conditions.remove(0);
						}
					});

				}

				// 1.1 边连接完成后，在进行 nextNode 创建
				if (ObjectUtil.isNotNull(nextNode)) {
					return create(identifier, nextNode);
				} else {
					return identifier;
				}
			}
		}
//		return exclusiveGatewayId;
		return String.join(",", incoming);
	}


	@Deprecated
	private String createParallelGatewayBuilder(String formId, JSONObject flowNode) throws InvocationTargetException, IllegalAccessException {
		String name = flowNode.getStr("nodeName");
		ParallelGateway parallelGateway = new ParallelGateway();
		String parallelGatewayId = id("parallelGateway");
		parallelGateway.setId(parallelGatewayId);
		parallelGateway.setName(name);
		process.addFlowElement(parallelGateway);
		process.addFlowElement(connect(formId, parallelGatewayId));

		if (Objects.isNull(flowNode.getJSONArray("branchNodes")) && Objects.isNull(flowNode.getJSONObject("nextNode"))) {
			return parallelGatewayId;
		}

		List<JSONObject> flowNodes = Optional.ofNullable(flowNode.getJSONArray("branchNodes")).map(e -> e.toList(JSONObject.class)).orElse(Collections.emptyList());
		List<String> incoming = CollUtil.newArrayList();
		for (JSONObject element : flowNodes) {
			JSONObject childNode = element.getJSONObject("nextNode");
			if (Objects.isNull(childNode)) {
				incoming.add(parallelGatewayId);
				continue;
			}
			String identifier = create(parallelGatewayId, childNode);
			if (Objects.nonNull(identifier)) {
				incoming.add(identifier);
			}
		}

		JSONObject childNode = flowNode.getJSONObject("nextNode");
		if (Objects.nonNull(childNode)) {
			// 普通结束网关
			if (CollectionUtils.isEmpty(incoming)) {
				return create(parallelGatewayId, childNode);
			} else {
				// 所有 service task 连接 end parallel gateway
				childNode.put("incoming", incoming);
				FlowElement flowElement = model.getFlowElement(incoming.get(0));
				// 1.0 先进行边连接, 暂存 nextNode
				JSONObject nextNode = childNode.getJSONObject("nextNode");
				childNode.put("nextNode", null);
				String identifier = createUserTask(childNode);
				for (int i = 1; i < incoming.size(); i++) {
					FlowElement flowElement1 = model.getFlowElement(incoming.get(i));
					process.addFlowElement(connect(flowElement1.getId(), identifier));
				}
				// 1.1 边连接完成后，在进行 nextNode 创建
				if (Objects.nonNull(nextNode)) {
					return create(identifier, nextNode);
				} else {
					return identifier;
				}
			}
		}
		return parallelGatewayId;
	}


	private String createServiceTask(JSONObject flowNode) {
		List<String> incoming = flowNode.getJSONArray("incoming").toList(String.class);
		// 自动生成id
		if (incoming != null && !incoming.isEmpty()) {
			ServiceTask serviceTask = ServiceTaskBuilder.createServiceTask(flowNode);
			String id = serviceTask.getId();
			process.addFlowElement(connect(incoming.get(0), id));
			process.addFlowElement(serviceTask);
			return id;
		}
		return null;
	}

	/**
	 * 创建用户任务
	 *
	 * @param flowNode 节点
	 * @return 节点Id
	 */
	private String createUserTask(JSONObject flowNode) {
		List<String> incoming = flowNode.getJSONArray("incoming").toList(String.class);
		// 自动生成id
		if (incoming != null && !incoming.isEmpty()) {
			UserTask userTask = UserTaskBuilder.createUserTask(flowNode);
			String id = userTask.getId();
			process.addFlowElement(connect(incoming.get(0), id));
			process.addFlowElement(userTask);
			return id;
		}
		return null;
	}


	public enum Type {

		/**
		 * 并行事件
		 */
		PARALLEL("parallel", ParallelGateway.class),

		/**
		 * 排他网关
		 */
		EXCLUSIVE("exclusiveGateway", ExclusiveGateway.class),

		/**
		 * 任务
		 */
		USER_TASK("userTask", UserTask.class),
		/**
		 * 任务
		 */
		SERVICE_TASK("serviceTask", ServiceTask.class);

		public final static Map<String, Class<?>> TYPE_MAP = Maps.newHashMap();

		static {
			for (Type element : Type.values()) {
				TYPE_MAP.put(element.type, element.typeClass);
			}
		}

		private final String type;
		private final Class<?> typeClass;

		Type(String type, Class<?> typeClass) {
			this.type = type;
			this.typeClass = typeClass;
		}

		public boolean isEqual(String type) {
			return this.type.equals(type);
		}

	}
}
