package org.springblade.modules.lankegroup.contractManagement.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.utils.Func;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  是与否（适用于：有、无；已评审、未评审；是、否） 枚举类
 */
@Getter
@AllArgsConstructor
public enum YesOrNoEnum {


	YES(1, "有", "是", "已审批", "已评审"),
	NO(0, "无", "否", "未审批", "未评审");


	private Integer index;
	private String describe1;
	private String describe2;
	private String describe3;
	private String describe4;


//	public static String getDescribe(Integer index) {
//		return Optional.ofNullable(index).map(e -> {
//			for (XtxxYseOrNoEnum en : XtxxYseOrNoEnum.values()) {
//				if (index.equals(en.index)) {
//					return en.describe;
//				}
//			}
//			return null;
//		}).orElse(null);
//	}

//	public static Integer getIndex(String describe) {
//		if (Func.isNotBlank(describe)) {
//			for (XtxxYseOrNoEnum value : XtxxYseOrNoEnum.values()) {
//				if (value.describe1.equals(describe) || value.describe2.equals(describe) || value.describe3.equals(describe) || value.describe4.equals(describe)) {
//					return value.index;
//				}
//			}
//		}
//		return null;
//	}


	public static List<Integer> getEnumIndexList() {
		return Arrays.stream(values()).map(v -> v.index).collect(Collectors.toList());
	}

}
