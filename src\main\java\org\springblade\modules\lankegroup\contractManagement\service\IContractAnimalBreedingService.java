/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractAnimalBreeding;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractAnimalBreedingVO;

/**
 * 动物饲养合同子表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
public interface IContractAnimalBreedingService extends BaseService<ContractAnimalBreeding> {

    /**
     * 自定义分页
     *
     * @param page
     * @param contractAnimalBreeding
     * @return
     */
    IPage<ContractAnimalBreedingVO> selectContractAnimalBreedingPage(IPage<ContractAnimalBreedingVO> page, ContractAnimalBreedingVO contractAnimalBreeding);

}
