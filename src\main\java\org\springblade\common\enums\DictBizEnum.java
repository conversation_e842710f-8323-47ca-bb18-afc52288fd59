/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务字典枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DictBizEnum {


    /**
     * 项目机会状态类型
     */
    OPPORTUNITYSTATUS("opportunity_status"),
    /**
     * 项目机会机会来源
     */
    OPPORTUNITYSOURCE("opportunity_source"),
    /**
     * 项目机会类型
     */
    OPPORTUNITYTYPE("opportunity_type"),
    /**
     * 项目等级
     */
    PROJECTLEVELTYPE("project_level_type"),
    /**
     * 晶莱 客户 所属机构类型
     */
    CRM_KH_CUSTOMER_TYPE("customer_type"),
    /**
     * 晶莱 客户 来源
     */
    CRM_KH_SOURCE("kh_resource"),
    /**
     * 晶莱 客户 类型
     */
    CRM_KH_CATEGORY("customer_contact_type"),
    /**
     * 晶莱 客户 级别
     */
    CRM_KH_LEVEL("kh_level"),
    /**
     * 晶莱 客户  教职
     */
    CRM_KH_ACADEMIC_TITLE("kh_academic_title"),
    /**
     * 晶莱 客户  学历
     */
    CRM_KH_EDUCATION_LEVEL("kh_education_level"),

    /**
     * 测试
     */
    TEST("test"),
    ;

    final String name;

}
