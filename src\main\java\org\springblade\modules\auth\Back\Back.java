//package org.springblade.modules.auth.Back;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.dingtalk.api.DefaultDingTalkClient;
//import com.dingtalk.api.DingTalkClient;
//import com.dingtalk.api.request.OapiGettokenRequest;
//import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
//import com.dingtalk.api.request.OapiV2UserGetRequest;
//import com.dingtalk.api.response.OapiGettokenResponse;
//import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
//import com.dingtalk.api.response.OapiV2UserGetResponse;
//import com.taobao.api.ApiException;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
//import org.springblade.common.config.AppConfig;
//import org.springblade.common.config.UrlConstant;
//import org.springblade.core.cache.utils.CacheUtil;
//import org.springblade.core.log.annotation.ApiLog;
//import org.springblade.core.tenant.annotation.NonDS;
//import org.springblade.modules.auth.utils.DingCallbackCrypto;
//import org.springblade.modules.auth.utils.DingtalkEncryptException;
//import org.springblade.modules.lankegroup.crm.service.ICustomerChargeUserService;
//import org.springblade.modules.lankegroup.crm.service.ICustomerContactService;
//import org.springblade.modules.system.entity.Dept;
//import org.springblade.modules.system.entity.Post;
//import org.springblade.modules.system.entity.User;
//import org.springblade.modules.system.service.IDeptService;
//import org.springblade.modules.system.service.IUserDeptService;
//import org.springblade.modules.system.service.IUserService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//import springfox.documentation.annotations.ApiIgnore;
//
//import java.util.*;
//
//import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;
//
//@NonDS
//@Slf4j
//@ApiIgnore
//@AllArgsConstructor
//@Component
//@RestController
//public class Back {
//    @Autowired
//    private AppConfig appConfig;
//
//    @Autowired
//    private IUserService userService;
//    @Autowired
//    private IDeptService deptService;
//    @Autowired
//    private IUserDeptService userDeptService;
//    @Autowired
//    private ICustomerContactService customerContactService;
//    @Autowired
//    private ICustomerChargeUserService customerChargeUserService;
//
//    /**
//     * 钉钉回调函数
//     * 员工新增会在本地库引入数据，金蝶会创建员工，由于金蝶和钉钉组织架构不同，所以无法将金蝶数据补全，还需要人事去金蝶对员工数据进行补充
//     * 员工离职，将本地员工数据逻辑删除，员工负责的客户联系人全部转入该员工未离职的上级名下
//     * 员工修改，仅修改本地数据库数据和钉钉同步，金蝶需要人事去金蝶修改
//     *
//     * @param msg_signature
//     * @param timeStamp
//     * @param nonce
//     * @param json
//     * @return
//     * @throws DingtalkEncryptException
//     * @throws ApiException
//     */
//    @ApiLog("钉钉人员事件回调")
//    @PostMapping("/getCallBack")
//    public Map<String, String> callBack(
//            @RequestParam(value = "msg_signature", required = false) String msg_signature,
//            @RequestParam(value = "timestamp", required = false) String timeStamp,
//            @RequestParam(value = "nonce", required = false) String nonce,
//            @RequestBody(required = false) JSONObject json) throws Exception {
//        CacheUtil.clear(USER_CACHE);
//        log.info("钉钉人员部门回调开始");
//        // 1. 从http请求中获取加解密参数
//
//        // 2. 使用加解密类型
//        // Constant.OWNER_KEY 说明：
//        // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
//        // 2、调用订阅事件接口订阅的事件为企业级事件推送，
//        //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
////        DingCallbackCrypto callbackCrypto = new DingCallbackCrypto("oaZamak8F5jhjYzhtK", "q8rGhU1q6RSa9gFAhkOnF1SEiKHfKOI4hgVGJNQmiJy", appConfig.getAppKey());
////        正式消息通知
//        DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(appConfig.getSjhdAesToken(), appConfig.getSjhdAesKey(), appConfig.getAppKey());
//        String encryptMsg = json.getString("encrypt");
//        String decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);
//
//        // 3. 反序列化回调事件json数据
//        JSONObject eventJson = JSON.parseObject(decryptMsg);
//        String eventType = eventJson.getString("EventType");
//        log.info("钉钉推送事件："+eventType);
//
//        //获取access_token
////        获取token
//        String accessToken;
//        DefaultDingTalkClient cl = new DefaultDingTalkClient(UrlConstant.URL_GET_TOKEN);
//        OapiGettokenRequest request = new OapiGettokenRequest();
//        OapiGettokenResponse response;
//        request.setAppkey(appConfig.getAppKey());
//        request.setAppsecret(appConfig.getAppSecret());
//        request.setHttpMethod("GET");
//        response = cl.execute(request);
//        accessToken = response.getAccessToken();
//        //获取有变动的员工id
//        String ids = eventJson.getString("UserId");
////        获取有变动的部门id
//        String deptIds = eventJson.getString("DeptId");
////        对返回的员工id数据进行处理
//        List<String> userIds = new ArrayList<>();
//        if (ids != null) {
//            ids = StringUtils.strip(ids, "[]");
//            List<String> list = Arrays.asList(ids.split(","));
//            for (String s : list) {
//                s = s.replace("\"", "");
//                userIds.add(s);
//            }
//
//        }
////        对返回的部门id数据进行处理
//        List<Long> deptIdList = new ArrayList<>();
//        if (deptIds != null) {
//            deptIds = StringUtils.strip(deptIds, "[]");
//            List<String> list = Arrays.asList(deptIds.split(","));
//            for (String s : list) {
//                s = s.replace("\"", "");
//                deptIdList.add(Long.valueOf(s));
//            }
//
//        }
//        // 4. 根据EventType分类处理
//        if ("check_url".equals(eventType)) {
//            // 测试回调url的正确性
//            log.info("测试回调url的正确性");
//        } else if ("user_add_org".equals(eventType)) {
//            // 处理通讯录用户增加事件
////            用户列表有数据时查询用户信息
//            if (userIds.size() > 0) {
//                List<User> users = dingUser(userIds, accessToken);
//                if (users != null && users.size() > 0) {
//                    for (User ding : users) {
////                        将员工创建时间写入数据库
//                        ding.setCreateTime(new Date());
////                            新增用户信息插入数据库
//                        ding.setIsDeleted(0);
//                        Boolean s = userService.saveOrUpdate(ding);
//                        if (s) {
//                            userDeptService.insertDingDeptUser(ding);
//                            userService.sort();
//                            log.info("保存成功！");
//                        } else {
//                            log.info("保存失败！");
//                        }
//                    }
//                }
//                log.info("发生了：" + eventType + "事件");
//            }
//        } else if ("user_modify_org".equals(eventType)) {
//            // 处理通讯录用户修改或者离职操作
////            用户列表有数据时查询用户信息
//            if (userIds.size() > 0) {
//                List<User> users = dingUser(userIds, accessToken);
//                if (users != null && users.size() > 0) {
//                    for (User ding : users) {
////                            更新用户信息到数据库
//                        Integer s = userService.updateUserNoKd(ding);
//                        if (s > 0) {
//                            userDeptService.insertDingDeptUser(ding);
//                            userService.sort();
//                            log.info("更新成功！");
//                        } else {
//                            log.info("更新失败！");
//                        }
//                    }
//                }
//
//            }
//            log.info("发生了：" + eventType + "事件");
//        } else if ("user_leave_org".equals(eventType)) {
//            List<Long> Ids = new ArrayList<>();
//            if (userIds.size() > 0) {
//                for (String userId : userIds) {
//                    User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, userId);
//                    Long parentId = queryDimensionParent(user);
////                    员工离职该员工负责的所有联系人转至其未离职的上级名下
//                    Integer integer = customerContactService.updateDimensionAllContact(user.getId(), parentId);
//                    if (integer > 0) {
//                        log.info("客户联系人移交完成");
//                    }
//                    Ids.add(user.getId());
//                }
////                    员工离职，客户删除客户负责人是该员工的记录
//                boolean a = customerChargeUserService.delectByUserId(Ids);
//                if (a) {
//                    log.info("客户负责人删除成功");
//                }
//                boolean b = userService.deleteLogic(Ids);
//                if (b) {
//                    log.info("用户删除成功！");
//                } else {
//                    log.info("用户删除失败！");
//                }
//            }
//            log.info("发生了：" + eventType + "事件");
//        } else if ("org_dept_create".equals(eventType)) {
////            通讯录企业部门创建。
////            用户列表有数据时查询用户信息
//            if (deptIdList.size() > 0) {
//                List<Dept> depts = dingDept(deptIdList, accessToken);
////                               插入数据库
//                if (depts != null && depts.size() > 0) {
//
//                    for (Dept dept : depts) {
////                        部门创建时间
//                        dept.setCreateTime(new Date());
//                        boolean s = deptService.save(dept);
//                        if (s == true) {
//                            log.info("保存成功！");
//                        } else {
//                            log.info("保存失败！");
//                        }
//                    }
//
//
//                }
//                log.info("发生了：" + eventType + "事件");
//
//            }
//
//        } else if ("org_dept_modify".equals(eventType)) {
////            通讯录企业部门更新或者移除
////            用户列表有数据时查询用户信息
//            if (deptIdList.size() > 0) {
//                List<Dept> depts = dingDept(deptIdList, accessToken);
////                               插入数据库
//                if (depts != null && depts.size() > 0) {
//                    for (Dept dept : depts) {
////                        部门修改时间
//                        dept.setUpdateTime(new Date());
//                        boolean s = deptService.updateById(dept);
//
//                        if (s == true) {
//                            log.info("部门更新成功！");
//                        } else {
//                            log.info("部门更新失败！");
//                        }
//                    }
//                }
//                log.info("发生了：" + eventType + "事件");
//            }
//        } else if ("org_dept_remove".equals(eventType)) {
//            if (deptIdList.size() > 0) {
//                for (Long deptId : deptIdList) {
//                    boolean s = deptService.removeDept(deptId.toString());
//                    if (s == true) {
//                        log.info("部门删除成功！");
//                    } else {
//                        log.info("部门删除失败！");
//                    }
//
//                }
//
//            }
//            log.info("发生了：" + eventType + "事件");
//        }
//
//
//        // 5. 返回success的加密数据
//        Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
//        return successMap;
//
//
//    }
//
//    /**
//     * 钉钉部门详情信息调用
//     */
//    private List<Dept> dingDept(List<Long> deptIdList, String accessToken) throws ApiException {
//        //            通讯录企业部门创建。
//        List<Dept> deptlists = new ArrayList<>();
////            遍历部门列表
//        for (int i = 0; i < deptIdList.size(); i++) {
//            DingTalkClient client = new DefaultDingTalkClient(UrlConstant.URL_DEPTDETAIL);
//            OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
//            req.setDeptId(deptIdList.get(i));
//            req.setLanguage("zh_CN");
//            OapiV2DepartmentGetResponse rs = client.execute(req, accessToken);
//            log.info(rs.getBody());
//
//            if (rs.getMsg().equals("ok")) {
//                if (rs.getResult() != null) {
//                    OapiV2DepartmentGetResponse.DeptGetResponse rsResult = rs.getResult();
//                    Dept dept = new Dept();
//                    dept.setId(rsResult.getDeptId());
//                    dept.setParentId(rsResult.getParentId());
//                    dept.setDeptName(rsResult.getName());
//                    deptlists.add(dept);
////                    查询 部门主管是否有值，有值去人员表将列表中的人员leader置为1，该部门其他人员改为0
//                    if (rsResult.getDeptManagerUseridList() != null && rsResult.getDeptManagerUseridList().size() > 0) {
//                        List<String> deptLeaders = rsResult.getDeptManagerUseridList();
//                        Map map = new HashMap();
//                        map.put("leader", 1);
//                        map.put("userIds", deptLeaders);
//                        Integer integer = userService.updateLeader(map);
//                        if (integer != null && integer > 0) {
//                            map.put("leader", 0);
//                            map.put("deptId", deptIdList.get(i));
//                            userService.updateLeader(map);
//                        }
//                    }
//
//                }
//            }
//        }
//
//        return deptlists;
//    }
//
//    private List<User> dingUser(List<String> userIds, String accessToken) throws ApiException {
//        List<User> userList = new ArrayList<>();
////            遍历用户列表
//        for (int i = 0; i < userIds.size(); i++) {
//            DingTalkClient client = new DefaultDingTalkClient(UrlConstant.URL_USERDETAIL);
//            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
//            req.setUserid(userIds.get(i));
//            OapiV2UserGetResponse rs = client.execute(req, accessToken);
//
//            log.info(rs.getBody());
//
//            if (rs.getMsg().equals("ok")) {
//                if (rs.getResult() != null) {
//                    OapiV2UserGetResponse.UserGetResponse rsResult = rs.getResult();
//                    User ding = new User();
//                    if (rsResult.getBoss() == true) {
//                        ding.setBoss("1");
//                    } else {
//                        ding.setBoss("0");
//                    }
//                    if (rsResult.getLeaderInDept().get(0).getLeader() == true) {
//                        ding.setLeader(1);
//                    } else {
//                        ding.setLeader(0);
//                    }
//                    //处理人的id
//                    String newId = rsResult.getUserid().replace("-", "");
//                    newId = "1" + newId;
//                    if (newId.length() > 18) {
//                        newId = newId.substring(0, 18);
//                    }
//                    ding.setId(Long.valueOf(newId));
//                    ding.setName(rsResult.getName());
//                    ding.setAccount(rsResult.getUserid());
//                    ding.setJobNumber(rsResult.getJobNumber());
//                    ding.setPost(rsResult.getTitle());
//                    ding.setUnionid(rsResult.getUnionid());
//                    ding.setAvatar(rsResult.getAvatar());
//                    if(rsResult.getAvatar()==null||rsResult.getAvatar().equals("")){
////                   todo     默认头像 使用时需要在此项目的minio上先上传，改用自己项目的头像
//                        ding.setAvatar("https://dingding.lankecloud.com/oss/avatar/upload/********/82dabc7f765ad15b7cf937dbeea08095.png");
//                    }
//                    ding.setPhone(rsResult.getMobile());
//                    String deptlist = "";
//                    if (rsResult.getDeptIdList().size() > 0) {
//                        for (int j = 0; j < rsResult.getDeptIdList().size(); j++) {
//                            if (j == rsResult.getDeptIdList().size() - 1) {
//                                deptlist += rsResult.getDeptIdList().get(j);
//                            } else {
//                                deptlist += rsResult.getDeptIdList().get(j) + ",";
//                            }
//                        }
//                    }
//                    Map map = new HashMap();
//                    map.put("deptIds", rsResult.getDeptIdList());
////                    List<Long> longs = roleDeptService.roleSelectByDeptId(map);
////                    if(longs!=null&&longs.size()>0){
////                        ding.setRoleId( longs.stream()
////                                .map(Object::toString)
////                                .collect(Collectors.joining(", ")));
////                    }
//                    //		所有人员都要有普通员工的权限
//                    ding.setRoleId("1552895891959816194");
//                    ding.setDeptId(deptlist);
//                    ding.setRealName(rsResult.getName());
////                            获取上级id
//                    User user = userService.userByAccount(SzhConstant.SZH_DEFAULT_TENANT_ID, rsResult.getManagerUserid());
//                    if (user != null) {
//                        ding.setParentId(user.getId());
//                    }
//                    ding.setPost(rsResult.getTitle());
////                            获取岗位表，如果岗位表有则直接查询出后赋值，如果岗位表没有则岗位表新增记录
//                    List<Post> posts = userService.selectPostOrByName(rsResult.getTitle());
//                    if (posts != null && posts.size() > 0) {
//                        ding.setPostId(String.valueOf(posts.get(0).getId()));
//                    } else {
//                        userService.insertPost((int) ((Math.random() * 9 + 1) * 100000), rsResult.getTitle(), SzhConstant.SZH_DEFAULT_TENANT_ID, 1, 0);
//                        List<Post> post = userService.selectPostOrByName(rsResult.getTitle());
//                        ding.setPostId(String.valueOf(post.get(0).getId()));
//
//                    }
//                    userList.add(ding);
//                }
//            }
//        }
//        return userList;
//    }
//
//    /**
//     * 递归获取当前员工未禁用的上级（未离职）
//     *
//     * @param user
//     * @return
//     */
//    private Long queryDimensionParent(User user) {
////                    客户关联联系人，当员工离职，该员工负责的客户联系人修改为未离职的上级
////                    获取该员工负责的所有客户联系人
//        Long parentId = userService.queryDimensionParent(user.getId());
//        User parent = userService.getById(parentId);
//        if (parent.getIsDeleted() != 0) {
//            queryDimensionParent(parent);
//        }
//        return parentId;
//    }
//
// /*   @ApiLog("钉钉人员事件回调")
//    @PostMapping("/getBack")
//    public void getBack(String ids) throws ApiException {
//        //获取access_token
////        获取token
//        String accessToken;
//        DefaultDingTalkClient cl = new DefaultDingTalkClient(UrlConstant.URL_GET_TOKEN);
//        OapiGettokenRequest request = new OapiGettokenRequest();
//        OapiGettokenResponse response;
//        request.setAppkey(appConfig.getAppKey());
//        request.setAppsecret(appConfig.getAppSecret());
//        request.setHttpMethod("GET");
//        response = cl.execute(request);
//
//        accessToken = response.getAccessToken();
////        对返回的员工id数据进行处理
//*//*        List<String> userIds = new ArrayList<>();
//        if (ids != null) {
//            ids = StringUtils.strip(ids, "[]");
//            List<String> list = Arrays.asList(ids.split(","));
//            for (String s : list) {
//                s = s.replace("\"", "");
//                userIds.add(s);
//            }
//        }*//*
//     *//*        if (userIds.size() > 0) {
//            List<User> users = dingUser(userIds, accessToken);
//            if (users != null && users.size() > 0) {
//                for (User ding : users) {
////                            更新用户信息到数据库
//                    Integer s = userService.updateUserNoKd(ding);
//                    if (s > 0) {
//                        userDeptService.insertDingDeptUser(ding);
//                        userService.sort();
//                        log.debug("更新成功！");
//                    } else {
//                        log.debug("更新失败！");
//                    }
//                }
//            }
//
//        }*//*
//        //        对返回的部门id数据进行处理
//        List<Long> deptIdList = new ArrayList<>();
//        if (ids != null) {
//            ids = StringUtils.strip(ids, "[]");
//            List<String> list = Arrays.asList(ids.split(","));
//            for (String s : list) {
//                s = s.replace("\"", "");
//                deptIdList.add(Long.valueOf(s));
//            }
//
//        }
////            通讯录企业部门创建。
//        if (deptIdList.size() > 0) {
//            for (Long deptId : deptIdList) {
//                boolean s = deptService.removeDept(deptId.toString());
//                if (s == true) {
//                    log.debug("部门删除成功！");
//                } else {
//                    log.debug("部门删除失败！");
//                }
//
//            }
//
//        }
//
//    }*/
//}
