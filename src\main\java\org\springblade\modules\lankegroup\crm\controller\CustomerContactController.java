/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.TableIdEnum;
import org.springblade.common.utils.ChangedUtil;
import org.springblade.common.utils.ExcelImportUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.utils.*;
import cn.hutool.core.bean.BeanUtil;
import org.springblade.modules.lankegroup.crm.dto.CustomerContactDTO;
import org.springblade.modules.lankegroup.crm.dto.CustomerContactQueryDTO;
import org.springblade.modules.lankegroup.crm.entity.*;
import org.springblade.modules.lankegroup.crm.excel.CustomerContactExcel;
import org.springblade.common.excel.ExcelImportHelper;
import org.springblade.modules.lankegroup.crm.excel.ExcelImportResult;
import org.springblade.modules.lankegroup.crm.mapper.ConcentUserMapper;
import org.springblade.modules.lankegroup.crm.mapper.CustomerContactMapper;
import org.springblade.modules.lankegroup.crm.mapper.CustomerContactTagsMapper;
import org.springblade.modules.lankegroup.crm.service.IConcentUserService;
import org.springblade.modules.lankegroup.crm.service.ICustomerConnectLogService;
import org.springblade.modules.lankegroup.crm.service.ICustomerContactCollectService;
import org.springblade.modules.lankegroup.crm.service.ICustomerService;
import org.springblade.modules.lankegroup.crm.vo.*;
import org.springblade.modules.system.service.IUserService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springblade.modules.lankegroup.crm.entity.CustomerContact;
import org.springblade.modules.lankegroup.crm.vo.CustomerContactVO;
import org.springblade.modules.lankegroup.crm.service.ICustomerContactService;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户联系人表【晶莱客户】 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@NonDS
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/contact/customercontact")
@Api(value = "客户联系人表", tags = "客户联系人表接口")
public class CustomerContactController extends BladeController {

    private final ICustomerContactService customerContactService;
    private final ICustomerService customerService;
    private final IUserService userService;

    private final IConcentUserService concentUserService;
    private final ICustomerConnectLogService customerConnectLogService;
    private final ICustomerContactCollectService customerContactCollectService;
    private final CustomerContactTagsMapper customerContactTagsMapper;
    private final CustomerContactMapper customerContactMapper;
    private final ConcentUserMapper concentUserMapper;

    /**
     * 详情
     */
    @ApiLog("详情")
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入customerContact")
    public R<CustomerContactVO> detail(CustomerContactVO customerContact) {
        try {
//		CustomerContact detail = customerContactService.getOne(Condition.getQueryWrapper(customerContact));
            CustomerContactVO customerContactVO = customerContactService.selectAll(customerContact);
            if (Func.isEmpty(customerContactVO)) {
                return R.fail("未查询到数据");
            }
            //查询共享人
//        Long p = 0l;
//        customerContactVO.setConcentUserVO(concentUserService.selectAllByContactsId(customerContactVO.getId(), p));
            return R.data(concentUserService.selectSharMans(customerContactVO));
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
            return R.fail("系统繁忙，请稍后重试");
        }
    }

//    /**
//     * 分页 客户联系人表
//     */
//    @ApiLog("分页 客户联系人表")
//    @GetMapping("/list")
//    @ApiOperationSupport(order = 2)
//    @ApiOperation(value = "分页", notes = "传入customerContact")
//    public R<List<CustomerContact>> list(CustomerContact customerContact, Query query) {
//        customerContact.setContacts(null);
//        List<CustomerContact> pages = customerContactService.list( Condition.getQueryWrapper(customerContact));
//        return R.data(pages);
//    }


    /**
     * 自定义分页 客户联系人表
     * 刘亚君默认展示所有客户联系人
     * contacts:查询条件
     * importance：重要程度
     * sex：性别
     * principal：负责人
     * customerId：客户id
     * <p>
     * 返回值：
     * 2024/07/10
     * desensitization参数
     * 显示前三后四的前端样式改为【申请共享】按钮需要根据对方同意共享/不同意共享的结果区分该按钮是否可点
     * 0显示全部手机号
     * 1显示前三后四【申请共享】置灰（申请共享联系人了，但是未回复）
     * 2显示前三后四【申请共享】蓝色（未申请共享联系人/申请共享联系人被拒绝了）
     */
    @ApiLog("客户联系人列表")
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入customerContact")
    public R<IPage<CustomerContactVO>> page(CustomerContactVO customerContact, Query query) {
        try {
            IPage<CustomerContactVO> pages = customerContactService.selectCustomerContactCollectPage(Condition.getPage(query), customerContact);
            return R.data(CollectionUtil.isEmpty(pages.getRecords()) ? pages.setRecords(new ArrayList<>()) : pages);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 客户列表【适用于报价管理-列表搜索框下拉】
     * - 数据范围：除 公海客户 以外的全部
     * -排序：创建时间倒序
     */
    @GetMapping("/pageByQuote")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "客户列表【适用于报价管理-列表搜索框下拉】")
    public R pageByQuote() {
        return customerContactService.pageByQuote();
    }

    /**
     * 新增 客户联系人表
     */
    @ApiLog("新增 客户联系人表")
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入customerContact")
    @Transactional(rollbackFor = Exception.class)
    public R save(@Valid @RequestBody CustomerContactDTO customerContact) {
//        for (int i = 0; i < list.size(); i++) {
//            if (list.get(i).getCustomerId().equals(customerContact.getCustomerId()) &&
//                    list.get(i).getContacts().equals(customerContact.getContacts()) &&
//                    list.get(i).getContactsInformation().equals(customerContact.getContactsInformation())) {
//                return R.fail("你添加的联系人已存在");
//            }
//        }
        R r = customerContactService.checkContact(customerContact);
        if (r.isSuccess()) {

            // 处理标签
            List<List<CustomerContactTag>> tagsList = customerContact.getTagsList();
            if (CollectionUtil.isNotEmpty(tagsList)) {
                // 清理无效数据
                for (List<CustomerContactTag> customerContactTags : tagsList) {
                    if (CollectionUtil.isNotEmpty(customerContactTags)) {
                        Iterator<CustomerContactTag> iterator = customerContactTags.iterator();
                        while (iterator.hasNext()) {
                            CustomerContactTag next = iterator.next();
                            if (Func.isEmpty(next) || Func.isEmpty(next.getId())) {
                                iterator.remove();
                            }
                        }
                    }
                }
                customerContact.setTags(Func.toJson(tagsList));
            }

            Boolean state = customerContactService.save(customerContact);
            if (state) {
                // 新增客户和标签关联关系表
                if (CollectionUtil.isNotEmpty(tagsList)) {
                    for (List<CustomerContactTag> customerContactTags : tagsList) {
                        for (CustomerContactTag tag : customerContactTags) {
                            CustomerContactTagsMap tagsMap = new CustomerContactTagsMap();
                            tagsMap.setCode(tag.getCode());
                            tagsMap.setCustomerContactId(customerContact.getId());
                            tagsMap.setDictKey(tag.getDictKey());
                            customerContactTagsMapper.insert(tagsMap);
                        }
                    }
                }
                //                                    写入记录日志表
                CustomerConnectLog contractChangeLog = new CustomerConnectLog();
                contractChangeLog.setCustomerId(Long.valueOf(customerContact.getCustomerId()));
                contractChangeLog.setChangeTable(TableIdEnum.客户联系人.getTableId());
                contractChangeLog.setContactId(customerContact.getId());
                contractChangeLog.setCustomerContactId(customerContact.getId());
                contractChangeLog.setAboutDescription("新增了客户");
                contractChangeLog.setDescription("新增了客户");
                contractChangeLog.setChargeUser(AuthUtil.getUserId());
                customerConnectLogService.save(contractChangeLog);

                ConcentUserVO concentUser = new ConcentUserVO();
                concentUser.setContactsId(customerContact.getId());
//                if (customerContact.getShareUser() == null) {
                concentUser.setPrincipalId(customerContact.getPrincipal());
                return R.status(concentUserService.insertRepeatable(concentUser));
//                } else {
//                    String[] strings = customerContact.getShareUser().split(",");
//                    for (int i = 0; i < strings.length; i++) {
//                        concentUser.setPrincipalId(Long.valueOf(strings[i]));
//                        concentUserService.insertRepeatable(concentUser);
//                    }
//                    return R.status(true);
//                }
            }
            return R.status(state);
        }
        return r;

    }

    /**
     * 修改 客户联系人表
     */
    @ApiLog("修改 客户联系人表")
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入customerContact")
    @Transactional(rollbackFor = Exception.class)
    public R update(@Valid @RequestBody CustomerContactDTO customerContact) {
        R r = customerContactService.checkContact(customerContact);
        if (r.isSuccess()) {

            // 处理标签
            List<List<CustomerContactTag>> tagsList = customerContact.getTagsList();
            if (CollectionUtil.isNotEmpty(tagsList)) {
                // 清理无效数据
                for (List<CustomerContactTag> customerContactTags : tagsList) {
                    if (CollectionUtil.isNotEmpty(customerContactTags)) {
                        Iterator<CustomerContactTag> iterator = customerContactTags.iterator();
                        while (iterator.hasNext()) {
                            CustomerContactTag next = iterator.next();
                            if (Func.isEmpty(next) || Func.isEmpty(next.getId())) {
                                iterator.remove();
                            }
                        }
                    }
                }
                customerContact.setTags(Func.toJson(tagsList));
                customerContact.setTagsForLog(tagsList.stream().flatMap(Collection::stream).sorted(Comparator.nullsFirst(Comparator.comparing(CustomerContactTag::getDictKey))).map(CustomerContactTag::getDictKey).collect(Collectors.joining(StringPool.COMMA)));
            } else {
                customerContact.setTags(null);
                customerContact.setTagsForLog(StringPool.EMPTY);
            }

            CustomerContact old = customerContactService.getById(customerContact.getId());
            // 处理标签
            String tags = old.getTags();
            if (Func.isNotBlank(tags)) {
                JSONArray jsonArray = JSONUtil.parseArray(tags);
                List<List<CustomerContactTag>> result = new ArrayList<>(jsonArray.size());
                int size = jsonArray.size();
                for (int i = 0; i < size; i++) {
                    JSONArray subJsonArray = jsonArray.getJSONArray(i);
                    List<CustomerContactTag> subJsonArrayList = subJsonArray.toList(CustomerContactTag.class);
                    result.add(subJsonArrayList);
                }
                old.setTagsForLog(result.stream().flatMap(Collection::stream).sorted(Comparator.nullsFirst(Comparator.comparing(CustomerContactTag::getDictKey))).map(CustomerContactTag::getDictKey).collect(Collectors.joining(StringPool.COMMA)));
            } else {
                old.setTagsForLog(null);
            }

            //                                    写入记录日志表
            CustomerContact customerContactNew = BeanUtil.copyProperties(customerContact, CustomerContact.class);
            String changedFields = ChangedUtil.getUpdateFields(customerContactNew, old);
            if (changedFields != null && (!changedFields.equals(""))) {
                CustomerConnectLog contractChangeLog = new CustomerConnectLog();
                contractChangeLog.setDescription(changedFields);
                contractChangeLog.setCustomerId(Long.valueOf(customerContact.getCustomerId()));
                contractChangeLog.setChangeTable(TableIdEnum.客户联系人.getTableId());
                contractChangeLog.setContactId(customerContact.getId());
                contractChangeLog.setCustomerContactId(customerContact.getId());
                contractChangeLog.setAboutDescription("修改了客户");
                contractChangeLog.setChargeUser(AuthUtil.getUserId());
                customerConnectLogService.save(contractChangeLog);
            }

            // 补全非必填字段为空的情况
            LambdaUpdateWrapper<CustomerContact> updateWrapper = Wrappers.<CustomerContact>update().lambda().eq(CustomerContact::getId, customerContact.getId());

            updateWrapper.set(CustomerContact::getContacts, customerContact.getContacts());
            updateWrapper.set(CustomerContact::getCustomerType, customerContact.getCustomerType());
            updateWrapper.set(CustomerContact::getCustomerId, customerContact.getCustomerId());
            updateWrapper.set(CustomerContact::getSource, customerContact.getSource());
            updateWrapper.set(CustomerContact::getCategory, customerContact.getCategory());
            updateWrapper.set(CustomerContact::getPrincipal, customerContact.getPrincipal());

            updateWrapper.set(CustomerContact::getContactsInformation, customerContact.getContactsInformation());
            updateWrapper.set(CustomerContact::getWechat, customerContact.getWechat());
            updateWrapper.set(CustomerContact::getEmail, customerContact.getEmail());
            updateWrapper.set(CustomerContact::getDepartment, customerContact.getDepartment());
            updateWrapper.set(CustomerContact::getKhLevel, customerContact.getKhLevel());
            updateWrapper.set(CustomerContact::getResearchGroupContent, customerContact.getResearchGroupContent());
            updateWrapper.set(CustomerContact::getRemark, customerContact.getRemark());
            updateWrapper.set(CustomerContact::getTags, customerContact.getTags());

            updateWrapper.set(CustomerContact::getUpdateUser, AuthUtil.getUserId());
            updateWrapper.set(CustomerContact::getUpdateTime, new Date());
            Boolean state = customerContactService.update(updateWrapper);

            customerContactCollectService.updateName(customerContact.getCustomerId(), customerContact.getContacts(), customerContact.getId());
            if (state) {
                // 删除旧的客户和标签关联关系表
                customerContactTagsMapper.delete(Wrappers.<CustomerContactTagsMap>query().lambda().eq(CustomerContactTagsMap::getCustomerContactId, customerContact.getId()));
                // 新增客户和标签关联关系表
                if (CollectionUtil.isNotEmpty(tagsList)) {
                    for (List<CustomerContactTag> customerContactTags : tagsList) {
                        for (CustomerContactTag tag : customerContactTags) {
                            CustomerContactTagsMap tagsMap = new CustomerContactTagsMap();
                            tagsMap.setCode(tag.getCode());
                            tagsMap.setCustomerContactId(customerContact.getId());
                            tagsMap.setDictKey(tag.getDictKey());
                            customerContactTagsMapper.insert(tagsMap);
                        }
                    }
                }


                // 获取旧共享人
                // 共享人列表
                Map map = new HashMap();
                map.put("contactsId", customerContact.getId());
                List<ConcentUserVO> concentUserVOS = concentUserMapper.selectSharMansByAll(map);
                // 排除掉当前负责人
                List<ConcentUserVO> concentUserVO2 = new ArrayList<>();
                // 排除掉新负责人
                if (CollectionUtil.isNotEmpty(concentUserVOS)) {
                    // 只排除一次
                    boolean flag = false;
                    for (ConcentUserVO concentUser : concentUserVOS) {
                        if (old.getPrincipal().equals(concentUser.getPrincipalId()) && !flag) {
                            flag = true;
                            continue;
                        }
                        concentUserVO2.add(concentUser);
                    }
                    concentUserVOS = concentUserVO2;
                }
                concentUserService.remove(customerContact.getId(), null);
                ConcentUserVO concentUser = new ConcentUserVO();
                concentUser.setContactsId(customerContact.getId());
                concentUser.setPrincipalId(customerContact.getPrincipal());
                concentUserService.insertRepeatable(concentUser);
                if (CollectionUtil.isNotEmpty(concentUserVOS)) {
                    concentUserVOS.forEach(concentUserVO -> {
                        concentUser.setPrincipalId(concentUserVO.getPrincipalId());
                        concentUserService.insertRepeatable(concentUser);
                    });
//                    concentUser.setPrincipalId(customerContact.getPrincipal());
//                    concentUserService.insert(concentUser);
                    return R.status(true);
                }

            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
            return R.status(state);
        }
        return r;
//        List<CustomerContact> list = customerContactService.list(Condition.getQueryWrapper(customerContact));
//        for (int i = 0; i < list.size(); i++) {
//            if (list.get(i).getContactsInformation().equals(customerContact.getContactsInformation())) {
//                if (!list.get(i).getId().equals(customerContact.getId())) {
//                    return R.fail("你修改的联系人已存在");
//                }
//            }
//        }

    }

//    /**
//     * 新增或修改 客户联系人表
//     */
//    @PostMapping("/submit")
//    @ApiOperationSupport(order = 6)
//    @ApiOperation(value = "新增或修改", notes = "传入customerContact")
//    public R submit(@Valid @RequestBody CustomerContact customerContact) {
//        return R.status(customerContactService.saveOrUpdate(customerContact));
//    }


    /**
     * 删除 客户联系人表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        try {
            return R.status(customerContactService.deleteLogic(Func.toLongList(ids)));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }


    /**
     * 查询个人信息详情
     *
     * @param id 客户联系人id
     * @return
     */
    @GetMapping("/selectById")
    public R selectById(@RequestParam int id) {
        try {
            CustomerContact customerContact = customerContactService.getById(id);
            return R.data(customerContact);
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * @param seek       联系人名称关键字
     * @param customerId 客户id
     * @return 该客户名下，自己及下级负责或协同的联系人
     */
    @GetMapping("/contactPersonList")
    public R crmContactPersonList(String seek, Long customerId) {
        try {
            return R.data(customerContactService.crmContactPersonList(seek, customerId));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * @param seek       联系人名称关键字
     * @param customerId 客户id
     * @return 该客户名下，自己及下级负责或协同的联系人 返回 值只有 id 和 contacts
     */
    @GetMapping("/contactPersonLists")
    public R contactPersonLists(Query query, String seek, Long customerId) {
        try {
            return R.data(customerContactService.crmContactPersonLists((Condition.getPage(query)), seek, customerId));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * @param seek       联系人名称关键字
     * @param customerId 客户id
     * @return 该客户名下，自己及下级负责或协同的联系人 返回值有 id 和 contacts 、customerId、customerName
     */
    @GetMapping("/contact")
    public R contact(String seek, Long customerId) {
        try {
            return R.data(customerContactService.contact(seek, customerId));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    //    前端需要
    @ApiLog("客户联系人详情")
    @GetMapping("/pageDetails")
    public R pageDetails(@RequestParam String id) {
        try {
            return R.data(customerContactService.pageDetails(id));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 项目、项目机会决策链使用列表
     * 默认显示负责人为本人及下属，或者共享联系人为本人及下属的所有联系人，其他联系人支持搜索查询出来，
     * 调整为，默认显示所有的联系人，自己或下属负责的，或者自己及下属共享的，放在上面，其他联系人放在下面
     * 因为樊总和蓝总应该可以查看全部联系人，避免加载慢，默认显示30个联系人，做分页
     * <p>
     * <p>
     * <p>
     * 2024/06/27
     * 决策链>>选择决策人、技术把关者和使用者，点击加号，改为显示该项目机会所属客户下的联系人，
     * 创建人为本人及下属，或者共享联系人为本人及下属的所有联系人排在前面，其他联系人排在后面，
     * 默认显示30个联系人，做分页
     *
     * @param seek 联系人名称关键字
     * @return
     */
    @GetMapping("/contactAllList")
    public R<IPage<CustomerContactVisitVO>> contactAllList(Query query, String seek, Long customerId) {
        try {
            return R.data(customerContactService.contactAllList((Condition.getPage(query)), seek, customerId));
        } catch (Exception e) {
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 2024/06/27 新增外部直接共享联系人功能
     * id：客户联系人id
     * shareUser：共享人id字符串，逗号隔开
     * principalId：负责人id
     *
     * @param customerContact
     * @return
     */
    @PostMapping("/shareUser")
    public R shareUser(@Valid @RequestBody CustomerContact customerContact) {
        // 获取客户
        CustomerContact customerContactOld = customerContactMapper.selectById(customerContact.getId());
        if (customerContactOld == null) {
            return R.fail("未查询到数据");
        }
//        先全部删除，再重新加入
        concentUserService.remove(customerContact.getId(), null);
        ConcentUserVO concentUser = new ConcentUserVO();
        concentUser.setContactsId(customerContact.getId());
        concentUser.setPrincipalId(customerContactOld.getPrincipal());
        concentUserService.insertRepeatable(concentUser);
        if (customerContact.getShareUser() != null && (!customerContact.getShareUser().equals(""))) {
            String[] strings = customerContact.getShareUser().split(",");
            for (int i = 0; i < strings.length; i++) {
                concentUser.setPrincipalId(Long.valueOf(strings[i]));
                concentUserService.insertRepeatable(concentUser);
            }
        }
        // 记录动态
        String changedFields = StrUtil.format("共享了客户");
        CustomerConnectLog contractChangeLog = new CustomerConnectLog();
        contractChangeLog.setDescription(changedFields);
        contractChangeLog.setCustomerId(Long.valueOf(customerContactOld.getCustomerId()));
        contractChangeLog.setChangeTable(TableIdEnum.客户联系人.getTableId());
        contractChangeLog.setContactId(customerContact.getId());
        contractChangeLog.setCustomerContactId(customerContact.getId());
        contractChangeLog.setAboutDescription(changedFields);
        contractChangeLog.setChargeUser(AuthUtil.getUserId());
        customerConnectLogService.save(contractChangeLog);
        return R.status(true);
    }


    /**
     * 把联系人共享给自己，并通知联系人的负责人
     *
     * @param contactIdList 客户联系人id
     * @return
     */
    @ApiLog("把联系人共享给自己，并通知联系人的负责人")
    @PostMapping("/shareContact")
    public R shareContact(@RequestBody List<Long> contactIdList) {
        if (contactIdList == null || contactIdList.size() < 0) {
            return R.fail("请添加客户id");
        }
        try {
            return customerContactService.shareContact(contactIdList);
        } catch (Exception e) {
            log.error("把客户共享给自己------>" + e.getMessage());
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 修改标签
     */
    @ApiLog("修改标签")
    @PostMapping("/updateTags")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改标签", notes = "传入customerContact")
    @Transactional(rollbackFor = Exception.class)
    public R updateTags(@RequestBody CustomerContactDTO customerContact) {
        return customerContactService.updateTags(customerContact);
    }

    /**
     * 退回公海
     * - 退回公海，清空客户的负责销售，清空客户的共享人，清空客户的收藏人
     */
    @PostMapping("/releaseToPool")
    @ApiOperation(value = "退回公海", notes = "传入customerContact")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "客户ID", dataType = "string", required = true)})
    @ApiResponses({@ApiResponse(code = 200, message = "操作成功"), @ApiResponse(code = 400, message = "操作失败")})
    public R releaseToPool(@RequestBody CustomerContact customerContact) {
        return customerContactService.releaseToPool(customerContact.getId());
    }

    /**
     * 获取客户列表【有权限】【晶莱-商机管理-新建商机】【分页版本】
     * - 查本人及下属为负责人或共享人的客户,并附带对应的机构名称和机构ID
     */
    @GetMapping("/getContactListByLowerPersonPage")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取客户列表【晶莱】【分页版本】", notes = "适用于【晶莱-商机管理-新建商机】，支持分页")
    public R<IPage<CustomerContactVO>> getContactListByLowerPersonPage(Query query, CustomerContactQueryDTO queryDTO) {
        try {
            IPage<CustomerContactVO> page = customerContactService.getContactListByLowerPersonPage(Condition.getPage(query), queryDTO);
            return R.data(page);
        } catch (Exception e) {
            log.error("获取客户列表失败", e);
            return R.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 获取客户列表【有权限】【晶莱-商机管理-新建商机】  【根据搜索客户也使用了这个接口】
     * - 查本人及下属为负责人或共享人的客户,并附带对应的机构名称和机构ID
     */
    @GetMapping("/getContactListByLowerPerson")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "获取客户列表【晶莱】", notes = "适用于【晶莱-商机管理-新建商机】")
    public R getContactListByLowerPerson(String seek) {
        return customerContactService.getContactListByLowerPerson(seek);
    }

    /**
     * 查询本人创建过跟进记录的客户
     * - 如果seek不为空，则调用getContactListByLowerPersonPage方法（分页）
     * - 如果seek为空，则调用getCustomersWithVisitRecordsPage方法（分页）
     */
    @ApiLog("查询本人创建过跟进记录的客户")
    @GetMapping("/getCustomersWithVisitRecords")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "查询本人创建过跟进记录的客户", notes = "根据查询参数决定调用不同的方法：seek不为空时调用getContactListByLowerPersonPage，为空时调用getCustomersWithVisitRecordsPage，返回分页数据结构")
    public R<IPage<? extends Object>> getCustomersWithVisitRecords(Query query, CustomerContactQueryDTO queryDTO) {
        if (queryDTO != null && Objects.equals(queryDTO.getIsVisit(), true)) {
            IPage<CustomerContactVisitVO> page = customerContactService.getCustomersWithVisitRecordsPage(Condition.getPage(query), queryDTO);
            return R.data(page);
        } else {
            IPage<CustomerContactVO> page = customerContactService.getContactListByLowerPersonPage(Condition.getPage(query), queryDTO);
            return R.data(page);
        }
    }

    /**
     * Excel导入客户联系人专用校验方法
     * 只校验手机号和微信号的重复
     */
    private List<String> validateContactForImport(CustomerContactExcel data) {
        List<String> errors = new ArrayList<>();

        try {
            // 基础校验：电话和微信号其中一个必填
            if (StrUtil.isBlank(data.getContactsInformation()) && StrUtil.isBlank(data.getWechat())) {
                errors.add("电话和微信号至少填写一个");
                return errors;
            }

            // 校验手机号和微信号重复
            Map<String, Object> checkMap = new HashMap<>();

            // 如果有手机号，校验手机号重复
            if (StrUtil.isNotBlank(data.getContactsInformation())) {
                checkMap.put("contactsInformation", data.getContactsInformation());
            }

            // 如果没有手机号但有微信号，校验微信号重复  
            if (StrUtil.isBlank(data.getContactsInformation()) && StrUtil.isNotBlank(data.getWechat())) {
                checkMap.put("wechat", data.getWechat());
            }

            // 查询是否存在重复联系人
            List<CustomerContactVO> existingContacts = customerContactMapper.checkContact(checkMap);
            if (CollectionUtil.isNotEmpty(existingContacts)) {
                CustomerContactVO existingContact = existingContacts.get(0);
                if (StrUtil.isNotBlank(data.getContactsInformation())) {
                    errors.add(String.format("手机号 %s 已被【%s】使用，请勿重复创建",
                            data.getContactsInformation(), existingContact.getContacts()));
                } else {
                    errors.add(String.format("微信号 %s 已被【%s】使用，请勿重复创建",
                            data.getWechat(), existingContact.getContacts()));
                }
            }

        } catch (Exception e) {
            log.error("校验联系人数据时发生错误", e);
            errors.add("数据校验失败");
        }

        return errors;
    }

    /**
     * Excel导入客户联系人
     */
    @ApiLog("Excel导入客户联系人")
    @PostMapping("/importExcel")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "Excel导入客户联系人", notes = "上传Excel文件导入客户联系人数据")
    public R<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file,
                                              @RequestParam(value = "headRowNumber", defaultValue = "2") Integer headRowNumber) {
        try {
            // 使用通用Excel导入助手类，代码更简洁
            return ExcelImportHelper.builder(CustomerContactExcel.class, CustomerContact.class, customerContactService)
                    .errorFileName("客户联系人导入错误数据")
                    .headRowNumber(headRowNumber)
                    .validator(this::validateContactForImport)
                    .importExcel(file);
        } catch (Exception e) {
            log.error("Excel导入客户联系人失败", e);
            return R.fail("Excel导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出Excel导入数据
     * 支持两种导出模式：
     * 1. 只导出错误数据 (exportAll=false，默认)
     * 2. 导出所有数据并标红错误数据 (exportAll=true)
     */
    @ApiLog("导出Excel导入数据")
    @GetMapping("/exportImportError")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel导入数据", notes = "导出Excel导入数据，支持选择导出模式")
    public void exportImportError(@RequestParam("errorKey") String errorKey,
                                  @RequestParam(value = "exportAll", defaultValue = "true") boolean exportAll,
                                  HttpServletResponse response) {

        if (exportAll) {
            // 导出所有数据并标红错误数据（不包含行号列）
            ExcelImportUtil.exportAllImportDataWithErrorHighlightNoRowIndex(
                    errorKey,
                    response,
                    CustomerContactExcel.class,
                    "客户联系人导入数据(含标红错误)",
                    "导入数据",
                    this::convertToCustomerContactExcelForAll
            );
        } else {
            // 只导出错误数据（包含行号列，保持向后兼容）
            ExcelImportUtil.exportImportErrorData(
                    errorKey,
                    response,
                    CustomerContactExcel.class,
                    "客户联系人导入错误数据",
                    "错误数据",
                    this::convertToCustomerContactExcelForError
            );
        }
    }


    /**
     * 转换错误数据为CustomerContactExcel格式（用于错误数据导出，包含行号）
     */
    private CustomerContactExcel convertToCustomerContactExcelForError(ExcelImportResult.ExcelImportError error) {
        CustomerContactExcel errorData = new CustomerContactExcel();

        // 如果原始数据存在，则从原始数据中获取
        if (error.getOriginalData() instanceof CustomerContactExcel) {
            // 原始数据就是Excel格式，直接复制
            CustomerContactExcel originalData = (CustomerContactExcel) error.getOriginalData();
            errorData = BeanUtil.copyProperties(originalData, CustomerContactExcel.class);
        } else {
            // 如果原始数据格式不识别，创建一个基本的错误记录
            errorData.setContacts("未知数据");
            errorData.setContactsInformation("数据格式错误");
            log.warn("未识别的原始数据格式，数据类型: {}", error.getOriginalData() != null ? error.getOriginalData().getClass().getSimpleName() : "null");
        }

        // 设置错误信息和行号
        errorData.setErrorMessage(error.getErrorMessage());
        errorData.setRowIndex(error.getRowIndex());

        return errorData;
    }

    /**
     * 转换错误数据为CustomerContactExcel格式（用于全部数据导出，不包含行号）
     */
    private CustomerContactExcel convertToCustomerContactExcelForAll(ExcelImportResult.ExcelImportError error) {
        CustomerContactExcel errorData = new CustomerContactExcel();

        // 如果原始数据存在，则从原始数据中获取
        if (error.getOriginalData() instanceof CustomerContactExcel) {
            // 原始数据就是Excel格式，直接复制
            CustomerContactExcel originalData = (CustomerContactExcel) error.getOriginalData();
            errorData = BeanUtil.copyProperties(originalData, CustomerContactExcel.class);
        } else {
            // 如果原始数据格式不识别，创建一个基本的错误记录
            errorData.setContacts("未知数据");
            errorData.setContactsInformation("数据格式错误");
            log.warn("未识别的原始数据格式，数据类型: {}", error.getOriginalData() != null ? error.getOriginalData().getClass().getSimpleName() : "null");
        }

        // 只设置错误信息，不设置行号
        errorData.setErrorMessage(error.getErrorMessage());
        // 不设置行号：errorData.setRowIndex(error.getRowIndex());

        return errorData;
    }

    /**
     * 下载Excel导入模板
     */
    @ApiLog("下载Excel导入模板")
    @GetMapping("/downloadTemplate")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "下载Excel导入模板", notes = "下载客户联系人Excel导入模板")
    public void downloadTemplate(HttpServletResponse response) {
        // 创建模板数据
        List<CustomerContactExcel> templateData = createTemplateData();

        ExcelImportUtil.downloadTemplate(response, templateData, CustomerContactExcel.class, "客户联系人导入模板", "客户联系人导入模板");
    }

    /**
     * 创建模板数据
     */
    private List<CustomerContactExcel> createTemplateData() {
        List<CustomerContactExcel> templateData = new ArrayList<>();
        CustomerContactExcel template = new CustomerContactExcel();
        template.setContacts("张三");
        template.setContactsInformation("13800138000");
        template.setWechat("zhangsan123");
        template.setSource("网络");
        template.setCategory("技术");
        templateData.add(template);
        return templateData;
    }

}
