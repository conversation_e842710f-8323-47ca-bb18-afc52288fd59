// 日志阅读记录
PUT /ding_task_log_view
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "logId": {
        "type": "keyword"
      },
      "userId": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date"
      }
    }
  }
}

// 消息阅读记录
PUT /ding_task_message_view
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "messageId": {
        "type": "keyword"
      },
      "userId": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date"
      }
    }
  }
}



PUT /ding_task_system_message
{
  "settings": {
    "index": {
      "number_of_shards": 1,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "outTrackId": {
        "type": "keyword"
      },
      "title": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date"
      },
      "content": {
        "type": "keyword"
      },
      "img": {
        "type": "keyword"
      },
      "button": {
        "type": "keyword"
      }
    }
  }
}






Api:
    save:
        List<BulkOperation> bulkOperations = new ArrayList<>();
        CompletePlanLog completePlanLog = new CompletePlanLog();
        elasticsearchClient.index( i -> i.index("ding_task_log_view").document(completePlanLog));

        List<BulkOperation> bulkOperations = new ArrayList<>();
        for(ViewMessageLog messageLog : list) {
            bulkOperations.add(BulkOperation.of(o -> o.index(i -> i.document(messageLog))));
        }
        elasticsearchClient.bulk(b -> b.index("ding_task_log_view").operations(bulkOperations));
query:
        SearchResponse<ViewLog> search = elasticsearchClient.search(srBuilder -> srBuilder.index("ding_task_log_view")
                                    .query(queryBuilder -> queryBuilder.term(term -> term.field("userId").value(AuthUtil.getUserId()).field("logId").value(completePlanLogListVO.getId())))
                            , ViewLog.class);
                    if (0 < search.hits().total().value()) {
                        completePlanLogListVO.setIsView(true);
                    }



//        List<BulkOperation> bulkOperations = new ArrayList<>();
//        Date date = new Date();
//        for (ViewMessageLog messageLog : list) {
//            messageLog.setUserId(AuthUtil.getUserId().toString());
//            messageLog.setCreateTime(date);
//            bulkOperations.add(BulkOperation.of(o -> o.index(i -> i.document(messageLog))));
//        }
//        try {
//            elasticsearchClient.bulk(b -> b.index("ding_task_message_view").operations(bulkOperations));
//        } catch (Exception e) {
//            log.error("保存系统消息记录异常->" + e.getMessage());
//        }
