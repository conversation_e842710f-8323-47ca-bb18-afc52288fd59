package org.springblade.common.utils;

import org.springblade.core.tool.support.Kv;
import org.springblade.modules.lankegroup.invoiceStatistics.util.CalendarWeekVo;
import org.springblade.modules.lankegroup.invoiceStatistics.util.JDBCUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * 查询销售每日任务数据
 */
public class QuerySellQuestStatistics implements Runnable {
    private CalendarWeekVo calendarWeek;
    private CountDownLatch latch;
    private Kv map;
    private JDBCUtil jdbcUtil;

    public QuerySellQuestStatistics(CalendarWeekVo calendarWeek, CountDownLatch latch, Kv map, JDBCUtil jdbcUtil) {
        this.calendarWeek = calendarWeek;
        this.latch = latch;
        this.map = map;
        this.jdbcUtil = jdbcUtil;
    }

    @Override
    public void run() {
        CompletableFuture<Kv> estimatedCollectMoneyFuture = CompletableFuture.supplyAsync(() -> jdbcUtil.queryData(map,1));
        CompletableFuture<Kv> actualPaymentBackFuture = CompletableFuture.supplyAsync(() -> jdbcUtil.queryData(map,2));
        try {
            double money = "{}".equals(estimatedCollectMoneyFuture.get().toString()) ? 0.0 : estimatedCollectMoneyFuture.get().getDouble("estimatedCollectMoney");
            double back = "{}".equals(actualPaymentBackFuture.get().toString()) ? 0.0 : actualPaymentBackFuture.get().getDouble("actualPaymentBack");
            calendarWeek.setMoney(money);
            calendarWeek.setBack(back);
            calendarWeek.setEstimatedCollectMoney(BigDecimal.valueOf(money / 10000).setScale(2, RoundingMode.HALF_UP) + "万");
            calendarWeek.setActualPaymentBack(BigDecimal.valueOf(back / 10000).setScale(2, RoundingMode.HALF_UP) + "万");
        } catch (Exception e) {
            e.printStackTrace();
        }
        latch.countDown();
    }
}
