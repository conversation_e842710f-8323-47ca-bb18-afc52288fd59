package org.springblade.common.service;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 创建所有的业务方法类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/28 9:28
 */
public interface IForm {

    void UpdateFormData(String formName, String columnKey, String columnValue, String id);

    /**
     * 根据类型查询mongodb上的流程实例ID和单据状态
     *
     * @param formName 类型
     * @param id       单据编码
     * @return
     */
    void updateMongoFormDate(String formName, String columnKey, String columnValue, String id);

    void updateFormStutusAndTaskUser(String tableName, String status, String taskUser, String id);

    void updateFormStatus(String tableName, String status, String id);

    /**
     * 修改关联关系表中关联关系可以状态
     * @param tableName
     * @param enable
     * @param fieldName
     * @param id
     */
    void updateFormRelStatus(String tableName, Integer enable,String fieldName, String id);

    /**
     * 根据类型查询流程实例ID和单据状态
     *
     * @param formName 类型
     * @param id       主键ID
     * @return
     */
    Map selectFromData(String formName, String id);

    /**
     * 根据类型查询mongodb上的流程实例ID和单据状态
     *
     * @param formName 类型
     * @param id       单据编码
     * @return
     */
    Map selectMongoFromData(String formName, String id);

    /**
     * 根据流程实力ID修改单据数据
     *
     * @param formName
     * @return
     */
    Boolean updateFromDataByReceipt(String formName, String id, String bankType, String bankAccount, String bankAccountName);

    /**
     * 获取页面跳转地址
     *
     * @return
     */
    String getPageSkipUrl(@Param("query") Map map);
}
