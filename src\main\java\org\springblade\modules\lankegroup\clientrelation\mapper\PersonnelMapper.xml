<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.PersonnelMapper">
    <update id="updatePersonnelByProfession">
        update blade_personnel
        set profession_name = #{name}
        where profession_id = #{id}
    </update>
    <update id="updateByMarketplaceData">
        update blade_personnel_market
        set market_name = #{param.name}
        where market_id = #{param.id}
    </update>
    <update id="updatePersonnelData">
        update blade_personnel
        set actual_client = #{param.actualClient},
            ought_client  = #{param.oughtClient}
        where id = #{param.id}
    </update>
    <delete id="deleteByMarketplaceData">
        delete
        from blade_personnel_market
        where market_id = #{param.id}
    </delete>

    <select id="getPersonnelList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelListVO">
        select per.id,per.user_id,per.user_name,per.profession_id,per.profession_name,user.avatar
        photoUrl,cust.actualClient
        actualClient,cust.oughtClient oughtClient
        from blade_personnel per
        join personnel_all_customer cust
        on cust.personnelId = per.id
        join blade_user user
        on user.id = per.user_id
        where per.is_deleted = 0
        <if test="query.userName != null and query.userName != ''">
            and per.user_name like concat('%',#{query.userName},'%')
        </if>
        <if test="query.id != null and query.id != ''">
            and per.id = #{query.id}
        </if>
        <if test="query.userList != null">
            and per.user_id in (
            <foreach collection="query.userList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        order by per.create_time desc
    </select>
    <select id="getActualClientCount" resultType="java.lang.Integer">
        select count(distinct customer.id)
        from blade_customer customer
        join blade_customer_industry_involved industry
        on industry.customer_id = customer.id and industry.is_deleted = 0
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        and customer.forbid_status = 'A'
        and customer.is_deleted = 0
        where charge.user_id = #{userId}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getAreaOughtList" resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        SELECT
        any_value(areaCode) areaCode,
        any_value(areaName) areaName,
        sum( num ) num
        FROM
        (
        SELECT
        market.area_id areaId,
        <if test="query.type == 1">
            any_value(market.area_id) areaCode,any_value(market.area_name) areaName,
        </if>
        <if test="query.type == 2">
            any_value(area.code) areaCode,any_value(area.name) areaName,
        </if>
        any_value ( market.num ) num
        from blade_personnel_market market
        <if test="query.type == 2">
            join blade_area area
            on area.code = LEFT(market.area_id,4)
        </if>
        where market.personnel_id = #{query.id}
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and market.market_id not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.area != null">
            and market.area_id like concat(#{query.area},'%')
        </if>
        <if test="query.list != null">
            and (
            <foreach collection="query.list" separator="or" item="item" index="index">
                (market.market_id = #{item.marketCode} and market.area_id = #{item.areaCode})
            </foreach>
            )
        </if>
        ) t
        <if test="query.type == 1">
            group by t.areaId
        </if>
        <if test="query.type == 2">
            group by LEFT(t.areaId,4)
        </if>
    </select>
    <select id="getAreaActualList" resultType="java.lang.Integer">
        select count(distinct cust.id) from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_area area on
        <if test="query.type == 1">
            area.code = cust.city
        </if>
        <if test="query.type == 2">
            area.code = cust.urban_area
        </if>
        join blade_customer_charge_user charge
        on cust.id = charge.customer_id
        and cust.forbid_status = 'A'
        where cust.is_deleted = 0 and cust.forbid_status = 'A'
        <if test="query.list != null">
            and (
            <foreach collection="query.list" separator="or" item="item" index="index">
                (industry.industry_id = #{item.marketCode} and cust.city = #{item.areaCode})
            </foreach>
            )
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and charge.user_id = #{query.userId}
    </select>
    <select id="getProvince" resultType="java.util.Map">
        select distinct code, name
        from (
                 select area.code code, area.name name
                 from blade_personnel_market per
                          join blade_area area on area.code = LEFT (per.area_id, 2)
        where per.personnel_id = #{id}
        group by area.code
        union all
        select area.code code, area.name name
        from blade_personnel per
                 join blade_customer_charge_user charge
                      on charge.user_id = per.user_id and charge.is_deleted = 0
                 join blade_customer cust
                      on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                 join blade_area area
                      on area.code = cust.province
        where per.id = #{id}) t
    </select>
    <select id="getCity" resultType="java.util.Map">
        select distinct code, name
        from (
                 select area.code code, area.name name
                 from blade_personnel_market per
                          join blade_area area on area.code = LEFT (per.area_id, 4)
        where per.personnel_id = #{id}
          and per.area_id like concat(#{code}
            , '%')
        group by area.code
        union all
        select area.code code, area.name name
        from blade_personnel per
                 join blade_customer_charge_user charge
                      on charge.user_id = per.user_id and charge.is_deleted = 0
                 join blade_customer cust
                      on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                 join blade_area area
                      on area.code = cust.urban_area
        where per.id = #{id}
          and cust.province = #{code}) t
    </select>
    <select id="getCounties" resultType="java.util.Map">
        select distinct code, name
        from (
                 select area.code code, area.name name
                 from blade_personnel_market per
                          join blade_area area on area.code = LEFT (per.area_id, 6)
        where per.personnel_id = #{id}
          and per.area_id like concat(#{code}
            , '%')
        group by area.code
        union all
        select area.code code, area.name name
        from blade_personnel per
                 join blade_customer_charge_user charge
                      on charge.user_id = per.user_id and charge.is_deleted = 0
                 join blade_customer cust
                      on cust.id = charge.customer_id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                 join blade_area area
                      on area.code = cust.city
        where per.id = #{id}
          and cust.urban_area = #{code}) t
    </select>
    <select id="getProfessionList" resultType="java.util.Map">
        select distinct marketCode, marketName
        from (
                 select market_id marketCode, any_value(market_name) marketName
                 from blade_personnel_market per
                 where per.personnel_id = #{id}
                 group by market_id
                 union all
                 select market.id marketCode, market.name marketName
                 from blade_personnel per
                          join blade_customer_charge_user charge
                               on per.user_id = charge.user_id and charge.is_deleted = 0
                          join blade_customer cust
                               on charge.customer_id = cust.id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                          join blade_marketplace market
                               on market.id = cust.industry_involved and market.status = 0 and market.is_deleted = 0
                 where per.id = #{id}
             ) t
    </select>
    <select id="getCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select distinct customer.city areaCode, industry.industry_id marketCode
        from blade_customer customer
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        and customer.forbid_status = 'A'
        and customer.is_deleted = 0
        join blade_customer_industry_involved industry
        ON industry.customer_id = customer.id
        where charge.user_id = #{userId}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getCustomerListByThree"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select customer.city areaCode,industry.industry_id marketCode
        from blade_customer customer
        join blade_customer_industry_involved industry
        ON industry.customer_id = customer.id
        join blade_customer_charge_user charge
        on customer.id = charge.customer_id
        and customer.forbid_status = 'A'
        and customer.is_deleted = 0
        where charge.user_id = #{userId}
        <if test="marketId != null and marketId.size() > 0">
            and industry.industry_id in (
            <foreach collection="marketId" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaCode != null and areaCode.size() > 0">
            and customer.city in (
            <foreach collection="areaCode" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getMarketByPersonnelList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select market.area_id areaCode, market.market_id marketCode
        from blade_personnel per
                 join blade_personnel_market market
                      on market.personnel_id = per.id and market.is_deleted = 0
        where per.is_deleted = 0
          and per.profession_id = #{id}
    </select>
    <select id="getMarketByPersonnelListT"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select distinct cust.city areaCode, industry.industry_id marketCode
        from blade_personnel per
        join blade_customer_charge_user charge
        on per.user_id = charge.user_id and charge.is_deleted = 0
        join blade_customer cust
        on cust.id = charge.customer_id and cust.is_deleted = 0
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        where per.is_deleted = 0
        and per.profession_id = #{id}
        <if test="marketId != null and marketId.size() > 0">
            and industry.industry_id IN (
            <foreach collection="marketId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaId != null and areaId.size() > 0">
            and cust.city IN (
            <foreach collection="areaId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getAreaByPersonnelListT"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select
        any_value ( area.code ) AS areaCode,
        any_value ( area.name ) AS areaName,
        0 AS num,
        group_concat(
        any_value ( industry.industry_id )) AS marketStr
        FROM
        blade_profession pro
        JOIN blade_personnel per
        on per.profession_id = pro.id and per.is_deleted = 0
        JOIN blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        join blade_customer_charge_user charge
        on charge.user_id = per.user_id and charge.is_deleted = 0
        join blade_customer cust
        on cust.id = charge.customer_id and cust.is_deleted = 0
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_area area
        <if test="type == 1">
            on area.code = left(cust.city,4)
        </if>
        <if test="type == 2">
            on area.code = left(cust.city,6)
        </if>
        where pro.is_deleted = 0 and pro.id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="type == 1">
            <if test="areaId != null and areaId.size() > 0">
                and cust.urban_area IN (
                <foreach collection="areaId" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="type == 2">
            <if test="areaId != null and areaId.size() > 0">
                and cust.city IN (
                <foreach collection="areaId" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="type == 1">
            <if test="marketId != null and marketId.size() > 0">
                and industry.industry_id IN (
                <foreach collection="marketId" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="type == 2">
            <if test="marketId != null and marketId.size() > 0">
                and industry.industry_id IN (
                <foreach collection="marketId" separator="," item="item" index="index">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        GROUP BY
        <if test="type == 1">
            left(cust.city,4)
        </if>
        <if test="type == 2">
            left(cust.city,6)
        </if>
    </select>
    <select id="getAreaByCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select
        any_value(cust.city) areaCode,
        SUBSTRING_INDEX(any_value(cust.city_name), '/', -1) areaName,
        count(cust.id) num,
        GROUP_CONCAT(industry.industry_id) marketStr
        from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_customer_industry_involved industry
        on industry.customer_id = cust.id and industry.is_deleted = 0
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        join blade_area area
        <if test="type == 1">
            on area.code = cust.urban_area
        </if>
        <if test="type == 2">
            on area.code = cust.city
        </if>
        where per.profession_id = #{id}
        <if test="type == 1">
            and cust.urban_area = #{code}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="type == 2">
            and cust.city = #{code}
        </if>
        <if test="groupType != null">
            group by cust.city
        </if>
    </select>
    <select id="getAreaByCustomerPersonnelList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select cust.city areaCode,SUBSTRING_INDEX(any_value(cust.city_name), '/', -1) areaName,0
        num,GROUP_CONCAT(industry.industry_id) marketStr
        from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        where cust.is_deleted = 0 and per.id = #{id}
        <if test="code != null">
            and left(cust.city,4) = #{code}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cust.city
    </select>
    <select id="getAreaByCustomerPersonnelListT"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaListVO">
        select area.code areaCode,area.name areaName,0 num,
        GROUP_CONCAT('{"marketCode":',any_value ( industry.industry_id ),',"areaCode":',any_value ( cust.city ),'}')
        marketStr
        -- GROUP_CONCAT(cust.industry_involved) marketStr
        from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_personnel per
        on per.user_id = charge.user_id and per.is_deleted = 0
        join blade_area area
        on area.code = cust.urban_area
        where cust.is_deleted = 0 and per.id = #{id}
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by area.code
    </select>
    <select id="getAreaLikeActualList" resultType="java.lang.Integer">
        select count(distinct cust.id) from blade_customer cust
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        join blade_area area on
        <if test="query.type == 1">
            area.code = cust.city
        </if>
        <if test="query.type == 2">
            area.code = cust.urban_area
        </if>
        join blade_customer_charge_user charge
        on cust.id = charge.customer_id
        and cust.forbid_status = 'A'
        where cust.is_deleted = 0 and cust.forbid_status = 'A'
        <if test="query.area != null">
            and cust.city like concat(#{query.area},'%')
        </if>
        <if test="query.prohibitList != null and query.prohibitList.size() > 0">
            and industry.industry_id not in(
            <foreach collection="query.prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and charge.user_id = #{query.userId}
    </select>
    <select id="getByCustomerList" resultType="java.lang.Integer">
        select count(id)
        from blade_customer_industry_involved
        where industry_id = #{id}
          and is_deleted = 0
    </select>
    <select id="getProfessionPersonnelList" resultType="java.util.Map">
        select market.id marketCode, market.name marketName
        from blade_personnel per
                 join blade_customer_charge_user charge
                      on per.user_id = charge.user_id and charge.is_deleted = 0
                 join blade_customer cust
                      on charge.customer_id = cust.id and cust.is_deleted = 0 and cust.forbid_status = 'A'
                 join blade_customer_industry_involved industry
                      ON industry.customer_id = cust.id
                 join blade_marketplace market
                      on market.id = industry.industry_id and market.status = 0 and market.is_deleted = 0
        where per.id = #{id}
    </select>

    <select id="getIdByUserId" resultType="java.lang.Long">
        select id
        from blade_personnel
        where user_id = #{userId}
          and is_deleted = 0
    </select>
    <select id="getMarketByPersonnelListTByUserId"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select distinct cust.city areaCode, industry.industry_id marketCode,
        per.user_id userId
        from blade_personnel per
        join blade_customer_charge_user charge
        on per.user_id = charge.user_id and charge.is_deleted = 0
        join blade_customer cust
        on cust.id = charge.customer_id and cust.is_deleted = 0
        join blade_customer_industry_involved industry
        ON industry.customer_id = cust.id
        where per.is_deleted = 0
        and per.profession_id = #{id}
        <if test="marketId != null and marketId.size() > 0">
            and industry.industry_id IN (
            <foreach collection="marketId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and industry.industry_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaId != null and areaId.size() > 0">
            and cust.city IN (
            <foreach collection="areaId" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getUserIdByMarketAndDeptAndProList" resultType="java.lang.String">
        SELECT DISTINCT per.user_id
        FROM blade_personnel per -- 人员管理表
                 LEFT JOIN blade_user as u on u.id = per.user_id
                 JOIN blade_personnel_market market ON market.personnel_id = per.id -- 人员管理+行业关联表
                 LEFT JOIN blade_profession_market AS m ON m.market_code = market.market_id-- 行业表
                 LEFT JOIN blade_join_profession AS j ON j.id = m.join_id -- 中间表
                 LEFT JOIN blade_profession AS p on j.profession_id = p.id-- 区域市场
                 left join blade_profession_area as a on a.join_id = j.id -- 市场区域表
        WHERE per.is_deleted = 0
          and a.counties_code = #{cityId}              -- 区域（第三级）id
          and FIND_IN_SET(p.charge_dept_id, u.dept_id) --部门id
          and m.market_code = #{marketId}              -- 行业id
          and market.is_deleted = 0
          and m.is_deleted = 0
          and (j.is_deleted = 0 or j.is_deleted is null)
          and p.is_deleted = 0
          and u.is_deleted = 0
          and a.is_deleted = 0
    </select>
    <select id="getProfessIdByUserId" resultType="java.lang.String">
        SELECT DISTINCT p.id
        FROM blade_personnel per -- 人员管理表
--                 JOIN blade_personnel_market market ON market.personnel_id = per.id -- 人员管理+行业关联表
                 LEFT JOIN blade_profession AS p ON p.id = per.profession_id -- 区域市场
        WHERE per.is_deleted = 0
          AND p.is_deleted = 0
          and per.user_id = #{userId}
    </select>
    <select id="getMarketCountByArea" resultType="java.lang.Integer">
        SELECT count(*)
        FROM blade_profession pro
                 JOIN blade_join_profession jo ON pro.id = jo.profession_id
            AND jo.is_deleted = 0
                 JOIN blade_profession_market market ON market.join_id = jo.id
            AND market.is_deleted = 0
                 JOIN blade_profession_area area ON area.join_id = jo.id and area.is_deleted = 0
        where pro.id = (select profession_id from blade_personnel where id = #{userId} limit 1)
          and area.counties_name = #{areaName}
    </select>
    <select id="getPersonnelListByAll"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelListVO">
        select personnel.id, user_id, user_name, profession_id, profession_name, user.avatar photoUrl
        from blade_personnel personnel
                 join blade_user user
        on user.id = personnel.user_id and user.is_deleted = 0
        where personnel.is_deleted = 0
    </select>
    <select id="getPersonnelMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.ProfessionPolymerization">
        select marketId                                                       id,
               marketName                                                     name,
               oughtClient                                                    oughtCount,
               case when t2.actualClient is null then 0 else actualClient end actualCount
        from (
                 select any_value(marketId)marketId, any_value(marketName)marketName, any_value(oughtClient)oughtClient, max(type)
                 from (
                          select any_value(market.market_id)   marketId,
                                           any_value(market.market_name) marketName,
                                 sum(market.num)    oughtClient,
                                 1                  type
                          from blade_personnel per
                                   join blade_personnel_market market
                                        on market.personnel_id = per.id and market.is_deleted = 0
                          where per.id = #{personnelId}
                            and per.is_deleted = 0
                          group by market.market_id
                          union all
                          select any_value(alldata.marketId)marketId, any_value(market.name) marketName, 0 oughtClient, 0 type
                          from customer_all_data alldata
                                   join blade_marketplace market
                                        on market.id = alldata.marketId
                                   join blade_personnel per
                                        on per.user_id = alldata.userId
                          where per.id = #{personnelId}
                          group by market.id
                      ) t1
                 group by marketId) t1
                 left join
             (select any_value(market.id) id, any_value(market.name) name, count(alluser.customerId) actualClient
              from customer_all_user alluser
                       join blade_personnel per
                            on per.user_id = alluser.userId and per.is_deleted = 0
                       join blade_marketplace market
                            on market.id = alluser.marketId
              where per.id = #{personnelId}
              group by alluser.marketId) t2
             on t1.marketId = t2.id
    </select>
    <select id="getPersonnelUrbanList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaResultListVO">
        select areaCode, areaName, oughtClient oughtCount, actualClient actualCount, 'true' parent
        from customer_all_personnel_area_group
        where personnelId = #{personnelId}
    </select>
    <select id="getPersonnelCityList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaResultListVO">
        select areaCode, areaName, oughtClient oughtCount, actualClient actualCount
        from customer_all_personnel_area_city_group
        where personnelId = #{personnelId}
          and cityCode = #{areaCode}
    </select>
    <select id="getAPersonerlCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceResultVO">
        select DISTINCT cust.id clientCode,cust.customer_name clientName,cust.city areaCode,cust.city_name
        areaName,market.id marketCode,market.name marketName,concat(cust.city,market.id) areaAddMarket,'true' ywData
        from customer_all_user alluser
        join blade_customer cust
        on cust.id = alluser.customerId and cust.forbid_status = 'A' and cust.is_deleted = 0
        join blade_personnel per
        on per.user_id = alluser.userId and per.is_deleted = 0
        join blade_marketplace market
        on market.id = alluser.marketId
        where per.id = #{personnelId}
        <if test="areaCode != null and areaCode.size() > 0">
            and alluser.city in(
            <foreach collection="areaCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="marketCode != null and marketCode.size() > 0">
            and alluser.marketId in(
            <foreach collection="marketCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getOPersonerlCustomerList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceResultVO">
        select null clientCode,null clientName,market.area_id areaCode,market.area_name areaName,market.market_id
        marketCode,market.market_name marketName,concat(market.area_id,market.market_id) areaAddMarket,'false' ywData
        from blade_personnel per
        join blade_personnel_market market
        on market.personnel_id = per.id and market.is_deleted = 0
        where per.id = #{personnelId}
        <if test="areaCode != null and areaCode.size() > 0">
            and market.area_id in (
            <foreach collection="areaCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="marketCode != null and marketCode.size() > 0">
            and market.market_id in(
            <foreach collection="marketCode" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
