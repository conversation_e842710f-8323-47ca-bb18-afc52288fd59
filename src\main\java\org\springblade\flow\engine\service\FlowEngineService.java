/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.engine.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.tool.api.R;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.engine.entity.FlowExecution;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.vo.FlowModelDeployVO;
import org.springblade.flow.engine.vo.WorkFlowVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * FlowService
 *
 * <AUTHOR>
 */
public interface FlowEngineService extends IService<FlowModel> {

	/**
	 * 自定义分页
	 *
	 * @param page      分页工具
	 * @param flowModel 流程模型
	 * @return
	 */
	IPage<FlowModel> selectFlowPage(IPage<FlowModel> page, FlowModel flowModel);

	/**
	 * 流程管理列表
	 *
	 * @param page     分页工具
	 * @param category 分类
	 * @param mode     形态
	 * @return
	 */
	IPage<FlowProcess> selectProcessPage(IPage<FlowProcess> page, String category, Integer mode);

	/**
	 * 流程管理列表
	 *
	 * @param page                 分页工具
	 * @param processInstanceId    流程实例id
	 * @param processDefinitionKey 流程key
	 * @return
	 */
	IPage<FlowExecution> selectFollowPage(IPage<FlowExecution> page, String processInstanceId, String processDefinitionKey);

	/**
	 * 获取流转历史列表
	 *
	 * @param processInstanceId 流程实例id
	 * @param startActivityId   开始节点id
	 * @param endActivityId     结束节点id
	 * @return
	 */
	List<BladeFlow> historyFlowList(String processInstanceId, String startActivityId, String endActivityId);

	/**
	 * 变更流程状态
	 *
	 * @param state     状态
	 * @param processId 流程ID
	 * @return
	 */
	String changeState(String state, String processId);

	/**
	 * 删除部署流程
	 *
	 * @param deploymentIds 部署流程id集合
	 * @return
	 */
	boolean deleteDeployment(String deploymentIds);

	/**
	 * 上传部署流程
	 *
	 * @param files        流程配置文件
	 * @param category     流程分类
	 * @param tenantIdList 租户id集合
	 * @return
	 */
	boolean deployUpload(List<MultipartFile> files, String category, List<String> tenantIdList);

	/**
	 * 部署流程
	 *
	 * @param modelId      模型id
	 * @param category     分类
	 * @param tenantIdList 租户id集合
	 * @return
	 */
	boolean deployModel(String modelId, String category, List<String> tenantIdList);

	boolean deployModel(String modelId);

	/**
	 * 删除流程实例
	 *
	 * @param processInstanceId 流程实例id
	 * @param deleteReason      删除原因
	 * @return
	 */
	boolean deleteProcessInstance(String processInstanceId, String deleteReason);


	/**
	 * 保存/更新模型
	 *
	 * @param model 模型
	 * @return 模型
	 */
	R submitModel(FlowModel model);

	/**
	 * 自定义保存并且部署流程
	 *
	 * @param flowModelDeployVO
	 * @return
	 */
	R saveAndDeploy(FlowModelDeployVO flowModelDeployVO);

	/**
	 * 流程部署的详情
	 * @param id
	 * @return
	 */
	 R<FlowModelDeployVO> detail(String id);

	/**
	 * 流程节点进程图
	 *
	 * @param processDefinitionId
	 * @param processInstanceId
	 * @return
	 */
	Map<String, Object> modelView(String processDefinitionId, String processInstanceId);

	/**
	 * 流程节点进程图
	 *
	 * @param processInstanceId
	 * @param httpServletResponse
	 */
	void diagramView(String processInstanceId, HttpServletResponse httpServletResponse);

	/**
	 * 流程图展示
	 *
	 * @param processDefinitionId
	 * @param processInstanceId
	 * @param resourceType
	 * @param response
	 */
	void resourceView(String processDefinitionId, String processInstanceId, String resourceType, HttpServletResponse response);

	/**
	 * 获取XML
	 *
	 * @param model
	 * @return
	 */
	byte[] getModelEditorXML(FlowModel model);

	/**
	 * 流程启动之前对审批人员进行预测
	 *
	 * @param formKey 表单key
	 * @return
	 */
	R getFLowPrediction(String formKey, String processDefinitionId);

	/**
	 * 获取所有的用户流程节点
	 *
	 * @return
	 */
	WorkFlowVO getALLFlowNode(WorkFlowDTO workFlow);

	/**
	 * 获取历史流程节点
	 *
	 * @param formKey
	 * @param formDataId
	 * @return
	 */
	WorkFlowVO getHistoryFlowNode(String formKey, Long formDataId);

	Integer getCompletedInstance(String processInstanceId, String currentActivityId, String executionId, Integer reviewRound);

	/**
	 * 获取流程实例id
	 * @param formKey
	 * @param formDataId
	 * @return
	 */
	String getProcessInstanceId(String formKey, Long formDataId);
}
