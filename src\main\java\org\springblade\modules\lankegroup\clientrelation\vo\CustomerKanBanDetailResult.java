package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;


@Data
public class CustomerKanBanDetailResult {

    /**
     * 区域ID
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 行业ID
     */
    private String marketCode;

    /**
     * 行业名称
     */
    private String marketName;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * data数组
     */
    private String dataList;

}
