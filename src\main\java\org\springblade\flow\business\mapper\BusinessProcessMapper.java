/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.flow.business.dto.BusinessProcessDTO;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.vo.BusinessProcessVO;

import java.util.List;

/**
 * 自定义流程审批中的所有业务单据任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface BusinessProcessMapper extends BaseMapper<BusinessProcessEntity> {

	/**
	 * 审批任务分页
	 *
	 * @param page
	 * @param businessProcessTaskDTO
	 * @return
	 */
	List<BusinessProcessVO> selectBusinessProcessTaskPage(IPage page, @Param("param") BusinessProcessDTO businessProcessTaskDTO);

	/**
	 * 抄送任务分页
	 *
	 * @param page
	 * @param businessProcessTaskDTO
	 * @return
	 */
	List<BusinessProcessVO> selectBusinessProcessCCTaskPage(IPage page, @Param("param") BusinessProcessDTO businessProcessTaskDTO);

	List<BusinessProcessVO> selectBusinessProcessTransferPage(IPage page, @Param("param") BusinessProcessDTO businessProcessTaskDTO);

	/**
	 * 根据业务ID获取对应数据
	 */
	BusinessProcessEntity getOneByProcessInstanceIdAndFormDataId(@Param("processInstanceId") String processInstanceId, @Param("formDataId") Long formDataId);
	/**
	 * 根据业务ID和业务表单名称获取对应数据
	 */
	BusinessProcessEntity getOneByFormKeyAndFormDataId(@Param("formKey") String formKey, @Param("formDataId") Long formDataId);
	/**
	 * 根据业务Code和业务表单名称获取对应数据
	 */
	BusinessProcessEntity getOneByFormKeyAndFormDataCode(@Param("formKey") String formKey, @Param("formDataCode") String formDataCode);

}
