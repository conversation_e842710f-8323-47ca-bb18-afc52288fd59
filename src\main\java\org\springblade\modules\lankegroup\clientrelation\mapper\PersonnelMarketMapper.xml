<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.PersonnelMarketMapper">

    <insert id="savePersonnelMarket">
        insert into
        blade_personnel_market(personnel_id,area_id,area_name,market_id,market_name,status,create_time,create_user,create_dept,update_time,update_user,is_deleted)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{list.personnelId},#{list.areaId},#{list.areaName},#{list.marketId},#{list.marketName},0,NOW(),#{list.createUser},#{list.createDept},NOW(),#{list.updateUser},0)
        </foreach>
    </insert>
    <delete id="deletePersonnelMarket">
        delete
        from blade_personnel_market
        where personnel_id = #{id}
    </delete>
    <select id="getPersonnelMarketIds"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select market_id marketCode,area_id areaCode from blade_personnel_market where personnel_id = #{id} and
        is_deleted = 0
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market_id not in (
            <foreach collection="prohibitList" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getPersonnelMarketList"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO">
        select area_id areaId, area_name areaName, market_id marketId, market_name marketName
        from blade_personnel_market
        where personnel_id = #{id}
          and is_deleted = 0
    </select>
    <select id="getPersonnelMarketByName"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select distinct areaCode,marketCode from (
        select area_id areaCode,market_id marketCode from blade_personnel_market where personnel_id = #{id}
        <if test="marketId != null">
            and market_id = #{marketId}
        </if>
        <if test="areaCode != null">
            and area_id = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and is_deleted = 0
        union all
        select cust.city areaCode,cust.industry_involved marketCode from blade_customer cust
        join blade_customer_charge_user charge
        on charge.customer_id = cust.id and charge.is_deleted = 0
        join blade_personnel per on per.user_id = charge.user_id and per.is_deleted = 0
        where per.id = #{id}
        <if test="marketId != null">
            and cust.industry_involved = #{marketId}
        </if>
        <if test="areaCode != null">
            and cust.city = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and cust.city not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ) t
    </select>

    <select id="getPersonnelMarketByNameMore"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select area_id areaCode,market_id marketCode from blade_personnel_market where personnel_id = #{id}
        <if test="marketId != null and marketId.size() > 0">
            and market_id in (
            <foreach collection="marketId" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="areaCode != null and areaCode.size() > 0">
            and area_id in (
            <foreach collection="areaCode" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and is_deleted = 0
    </select>
    <select id="getPersonnelMarketByNameStr"
            resultType="org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket">
        select area_id areaCode,market_id marketCode from blade_personnel_market where personnel_id = #{id}
        <if test="marketId != null">
            and market_id = #{marketId}
        </if>
        <if test="areaCode != null">
            and area_id = #{areaCode}
        </if>
        <if test="prohibitList != null and prohibitList.size() > 0">
            and market_id not in (
            <foreach collection="prohibitList" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and is_deleted = 0
    </select>
    <select id="selectUserId" resultType="java.util.Map">
        SELECT p.user_id     as userId,
               m.market_name as marketName
        FROM blade_personnel_market m
                 LEFT JOIN blade_personnel p on m.personnel_id = p.id
        WHERE m.area_id = #{districtCode}
          AND m.market_id = #{marketplaceId}
          AND m.is_deleted = 0
          AND p.is_deleted = 0
    </select>
    <select id="getMarketNum" resultType="java.lang.Integer">
        select market.num
        from blade_profession pro
                 join blade_join_profession jo
                      on jo.profession_id = pro.id and jo.is_deleted = 0
                 join blade_profession_market market
                      on market.join_id = jo.id and market.is_deleted = 0
                 join blade_profession_area area
                      on area.join_id = jo.id and area.is_deleted = 0
        where pro.id = #{professionId}
          and market.market_code = #{marketId}
          and area.counties_code = #{areaId}
    </select>

    <select id="getMarketListByUserId"
            resultType="org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO">
        SELECT area_id areaId, area_name areaName, market_id marketId, market_name marketName
        FROM blade_personnel_market marker
                 LEFT JOIN blade_personnel per on per.id = marker.personnel_id
        WHERE per.user_id = #{userId} and per.is_deleted=0
    </select>

    <select id="getMarketListByDeptId" resultType="java.lang.Long">
        SELECT market.market_code
        FROM blade_profession AS pro -- 区域市场
                 LEFT JOIN blade_join_profession AS j ON j.profession_id = pro.id -- 中间表
                 LEFT JOIN blade_profession_market AS market ON market.join_id = j.id -- 行业表
        WHERE pro.charge_dept_id = #{deptId} and pro.is_deleted=0
    </select>
</mapper>
