package org.springblade.flow.engine.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.flow.core.entity.WorkFlow;
import org.springblade.modules.system.vo.ButtonVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22 19:46
 */
@Data
public class WorkFlowVO {
	/**
	 * 任务编号
	 */
	@ApiModelProperty(value = "任务编号")
	private String taskId;
	/**
	 * 流程实例ID
	 */
	@ApiModelProperty(value = "流程实例ID")
	private String processInstanceId;
	/**
	 * 流程ID
	 */
	@ApiModelProperty(value = "流程ID")
	private String processDefinitionId;

	@ApiModelProperty(value = "任务列表")
	private List<WorkFlow> workFlowList;

	@ApiModelProperty(value = "任务列表")
	private List<ButtonVO> buttonList;

}
