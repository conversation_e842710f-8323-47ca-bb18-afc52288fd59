/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 签约公司表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@TableName("cm_signatory_company")
@EqualsAndHashCode(callSuper = true)
public class SignatoryCompany extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;
    /**
     * 纳税登记号
     */
    private String taxNumber;
    /**
     * 通讯地址
     */
    private String address;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 开户行名称
     */
    private String accountName;
    /**
     * 银行卡号
     */
    private String bankCode;
    /**
     * 排序序号
     */
    private Integer sortOrder;


}
