package org.springblade.modules.lankegroup.clientrelation.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.entity.Area;
import org.springblade.modules.lankegroup.clientrelation.entity.Controls;
import org.springblade.modules.lankegroup.clientrelation.mapper.AreaMapper;
import org.springblade.modules.lankegroup.clientrelation.service.AreaService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@AllArgsConstructor
public class AreaServiceImpl extends BaseServiceImpl<AreaMapper, Area> implements AreaService {

    private final ElasticsearchClient elasticsearchClient;

    private final AreaMapper areaMapper;

    @Override
    public R getAreaList(String parentCode) {
        if(null == parentCode || "".equals(parentCode)){
            parentCode = "0";
        }
        return R.data(areaMapper.getAreaList(parentCode));
    }

    @Override
    public boolean saveControlsLog(Controls controls) {
        try {
            elasticsearchClient.index( i -> i.index("ding_talk_clientrelation_log").document(controls));
            return true;
        }catch (Exception e){
            return false;
        }
    }

    @Override
    public List<Area> getList(List<String> codeList) {
        return baseMapper.getList(codeList);
    }
}
