/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 合同主表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractMainDTO extends ContractMain {
    private static final long serialVersionUID = 1L;


    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;

    /**
     * 时间类型 1.创建时间；2.签约时间
     */
    @ApiModelProperty(value = "时间类型 1.创建时间；2.签约时间")
    private Integer timeType;

    /**
     * 创建开始时间
     */
    private LocalDateTime createStartTimeSql;
    /**
     * 创建结束时间
     */
    private LocalDateTime createEndTimeSql;

    /**
     * 签约开始时间
     */
    private LocalDateTime signStartTimeSql;
    /**
     * 签约结束时间
     */
    private LocalDateTime signEndTimeSql;
    /**
     * 创建人搜索条件
     */
    @ApiModelProperty(value = "创建人搜索条件 直接选择ID或者我的（myself）或者下属的（subordinate）")
    private String createUserParam;

    /**
     * 创建人ID列表 - 用于SQL查询
     */
    private List<Long> createUserIds;
    /**
     * 业务类型数组
     */
    @ApiModelProperty(value = "业务类型数组")
    private List<String> businessTypeList;

    /**
     * 搜索关键字 处理后的列表
     */
    private List<String> seekList;

    /**
     * 当前人的级别 1.高层；2.财务；3.部门领导；4.普通员工
     */
    private Integer targetUserLevel;

    /**
     * 当前人
     */
    private Long currentUserId;

    /**
     * 本部门及其下属部门全部人员（也可适用于列表”下属的“）
     */
    private List<Long> deptUserIds;


    /**
     * 实验合同子表
     */
    private ContractExperimentDTO experimentModel;
    /**
     * 预存合同子表
     */
    private ContractPrestoreDTO prestoreModel;
    /**
     * 试剂合同子表
     */
    private ContractReagentDTO reagentModel;
    /**
     * 试剂-实验材料合同子表
     */
    private List<ContractReagentQuotationMaterialDTO> reagentQuotationMaterialDModel;
    /**
     * 标书合同子表
     */
    private ContractBidDocumentDTO contractBidDocumentModel;
    /**
     * 试剂-动物饲养合同子表
     */
    private ContractAnimalBreedingDTO animalBreedingModel;
    /**
     * 试剂-动物饲养费用合同子表
     */
    private List<ContractAnimalBreedingExpensesDTO> animalBreedingExpensesModel;

}
