package org.springblade.common.annotation.check.compare;

import org.springblade.common.annotation.check.ParamValue;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

public class ParamValueValidator implements ConstraintValidator<ParamValue,Object> {

    // 是否必填
    private boolean required;

    // 参数值
    private List<String> allowedValues;

    @Override
    public void initialize(ParamValue paramValue) {
        allowedValues = Arrays.asList(paramValue.param());
        required = paramValue.required();
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext constraintValidatorContext) {
        if (null == object) {
            return !required;
        }
        String valueAsString = String.valueOf(object);
        if(null != object && !"".equals(object)){
            if(null != allowedValues && allowedValues.size() > 0){
                if (!allowedValues.contains(valueAsString)) {
                    return false; // 参数在允许的值列表中
                }
            }
            return true;
        }
        return false;
    }
}
