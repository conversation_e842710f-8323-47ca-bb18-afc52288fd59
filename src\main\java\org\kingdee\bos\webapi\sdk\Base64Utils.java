//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.util.Base64;

public class Base64Utils {
    public Base64Utils() {
    }

    public static String encodingToBase64(byte[] buffer) {
        Object var1 = null;

        try {
            byte[] b64buffer = Base64.getEncoder().encode(buffer);
            return (new String(b64buffer)).toString();
        } catch (Exception var3) {
            throw var3;
        }
    }

    public static byte[] decodingFromBase64(String base64) {
        Object var1 = null;

        try {
            byte[] b64buffer = base64.getBytes();
            byte[] buffer = Base64.getDecoder().decode(b64buffer);
            return buffer;
        } catch (Exception var4) {
            throw var4;
        }
    }
}
