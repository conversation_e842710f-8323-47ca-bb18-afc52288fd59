/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同审批状态枚举(0审批中，1待归档，2已撤回，3已驳回，4归档审批，5已归档,6草稿，7归档已撤回，8归档已驳回,9作废）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ContractTaskStatusEnum {

    审批中("审批中", "0"),
    待归档("待归档", "1"),
    已撤回("已撤回", "2"),
    已驳回("已驳回", "3"),
    归档审批("归档审批", "4"),
    已归档("已归档", "5"),
    归档已撤回("归档已撤回", "7"),
    归档已驳回("归档已驳回", "8"),
    ;

    final String name;
    final String code;

}
