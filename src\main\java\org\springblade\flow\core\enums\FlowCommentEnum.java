package org.springblade.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程意见类型,用于区分用户任务的审批结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/17 13:56
 */
@Getter
@AllArgsConstructor
public enum FlowCommentEnum {

	/**
	 * 说明
	 */
	TODO("todo", "待审批"),//--前端显示的标志位
	APPROVE("approve", "同意"),
	REBACK("reback", "退回"),//对新销客应前端驳回按钮，统一处理节点退回到发起人
	REVOKE("revoke", "撤回"),
//	REJECT("reject", "驳回"),//驳回/拒绝/不同意,整个流程就结束了---目前没有用
//	DELEGATE("delegate", "委派"),
	TRANSFER("transfer", "转办"),
	SIGNUP("signup", "加签"),
//	STOP("7", "终止流程"),
//BEFORE_ADD_SIGN("before_add_sign", "前加签"),
//	AFTER_ADD_SIGN("after_add_sign", "后加签");
	;

	final String type;

	final String remark;
}
