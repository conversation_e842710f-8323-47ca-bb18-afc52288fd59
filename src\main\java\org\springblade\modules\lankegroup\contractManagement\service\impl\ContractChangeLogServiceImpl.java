/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import org.springblade.modules.lankegroup.contractManagement.entity.ContractChangeLog;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeLogVO;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractChangeLogMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractChangeLogService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 合同表变更记录日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Service
public class ContractChangeLogServiceImpl extends BaseServiceImpl<ContractChangeLogMapper, ContractChangeLog> implements IContractChangeLogService {

	@Override
	public IPage<ContractChangeLogVO> selectContractChangeLogPage(IPage<ContractChangeLogVO> page, ContractChangeLogVO contractChangeLog) {
		return page.setRecords(baseMapper.selectContractChangeLogPage(page, contractChangeLog));
	}

}
