package org.springblade.flow.engine.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.flow.engine.entity.CustomProcessConfigEntity;
import org.springblade.flow.engine.service.ICustomProcessConfigService;

import static org.springblade.core.cache.constant.CacheConstant.FLOW_CACHE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/6 11:39
 */
public class FlowProcessConfigCache {

	private static final String PRICESSCONFIG_DEFINITION_ID = "customProcessConfigEntity:definitionId:";

	private static ICustomProcessConfigService customProcessConfigService;

	private static ICustomProcessConfigService getProcessConfig() {
		if (customProcessConfigService == null) {
			customProcessConfigService = SpringUtil.getBean(ICustomProcessConfigService.class);
		}
		return customProcessConfigService;
	}

	public static CustomProcessConfigEntity getProcessConfig(String processDefinitionId) {
		return CacheUtil.get(FLOW_CACHE, PRICESSCONFIG_DEFINITION_ID, processDefinitionId, () -> {
			return getProcessConfig().getOne(Wrappers.<CustomProcessConfigEntity>lambdaQuery()
				.eq(CustomProcessConfigEntity::getProcessDefinitionId, processDefinitionId));
		});
	}
}
