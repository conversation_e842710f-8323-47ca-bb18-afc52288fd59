package org.springblade.common.utils;


import cn.hutool.core.date.ChineseDate;
import cn.hutool.core.date.DateUtil;
import org.springblade.common.config.HolidayConstant;


import java.util.Date;

/**
 * @program: demo-test
 * @description: 假期工具类
 * @author: czchen
 * @date: 2022-02-14 11:46:05
 */
public class HolidayUtil {

    /**
     * 获取假期时间工具类
     *
     * @param holidayName 假期名称
     * @param year        当前年份
     * @return 假期时间
     */
    public static Date getHoliday(String holidayName, Integer year) {
        switch (holidayName) {
            case HolidayConstant.NEWYEARDAY:
                return DateUtil.parse(year + "-1-1");
            case HolidayConstant.CHINESENEWYEARGREGORIANFESTIVAL:
                ChineseDate chinesenewYearFestival = new ChineseDate(year, 1, 1);
                return chinesenewYearFestival.getGregorianDate();
            case HolidayConstant.QINGMINGFESTIVAL:
                int param = year - 2000;
                int qingmingDay = (int) (param * 0.2422 + 4.81) - param / 4;
                return DateUtil.parse(year + "-4-" + qingmingDay);
            case HolidayConstant.LABORDAY:
                return DateUtil.parse(year + "-5-1");
            case HolidayConstant.DRAGONBOATGREGORIANFESTIVAL:
                ChineseDate dragonBoatFestival = new ChineseDate(year, 5, 5);
                return dragonBoatFestival.getGregorianDate();
            case HolidayConstant.MIDAUTUMNGREGORIANFESTIVAL:
                ChineseDate midAutumnFestival = new ChineseDate(year, 8, 15);
                return midAutumnFestival.getGregorianDate();
            case HolidayConstant.NATIONALDAY:
                return DateUtil.parse(year + "-10-1");
            default:
                return new Date();
        }
    }
}

