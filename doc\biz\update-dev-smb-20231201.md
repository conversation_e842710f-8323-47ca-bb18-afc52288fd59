# dev-smb-1201分支更新内容

## mysql

### 等保看板信息表
```mysql
CREATE TABLE `blade_equal_protection_kanban` (
  `id` bigint unsigned NOT NULL COMMENT 'id（主键）',
  `project_id` bigint DEFAULT NULL COMMENT '项目id',
  `kd_project_fid` bigint DEFAULT NULL COMMENT '金蝶下推后正式项目id',
  `contract_man_list` json DEFAULT NULL COMMENT '客户数组',
  `contract_man_phones` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户联系人手机号，多个英文逗号分隔',
  `login_status` int DEFAULT NULL COMMENT '客户是否登录小程序状态（0.未登录/1.已登录）',
  `allocation_status` int DEFAULT NULL COMMENT '分配状态 0.待分配；1.已分配',
  `stage_status` int DEFAULT NULL COMMENT '阶段状态（1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议/6.项目完成）',
  `closed_status` int DEFAULT NULL COMMENT '关闭状态（0未关闭/1成功关闭/2失败终止）',
  `termination_reason` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '终止原因',
  `project_source` int DEFAULT NULL COMMENT '项目来源（0.合同签订/1.项目交付）',
  `data_source` int DEFAULT NULL COMMENT '数据来源（0.导入/1.立项或走流程/2.手动添加合同签订）',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态(0.无效，1.正常)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='等保看板信息表';
```


### 等保看板阶段表
```mysql
CREATE TABLE `blade_equal_protection_stage` (
  `id` bigint NOT NULL COMMENT '主键',
  `equal_protection_id` bigint DEFAULT NULL COMMENT '等保id',
  `stage_status` bigint DEFAULT NULL COMMENT '阶段 （1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议/6.项目完成）',
  `stage_start_time` datetime DEFAULT NULL COMMENT '阶段开始时间',
  `stage_end_time` datetime DEFAULT NULL COMMENT '阶段结束时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='等保看板阶段表';
```


### 等保看板阶段成员表
```mysql
CREATE TABLE `blade_equal_protection_stage_member` (
  `id` bigint NOT NULL COMMENT '主键',
  `equal_protection_id` bigint DEFAULT NULL COMMENT '等保id',
  `stage_status` bigint DEFAULT NULL COMMENT '阶段 （1.定级备案/2.启动会议/3.测评整改/4.报告编制/5.结项会议）',
  `user_id` bigint DEFAULT NULL COMMENT '成员ID',
  `admin` int DEFAULT '0' COMMENT '是否是当前阶段负责人（0否，1是）',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='等保看板阶段成员表';
```

### 等保看板菜单入口
```mysql
INSERT INTO `blade_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1670746474654453762, 'dbkb', '等保看板', 'equalProtectionKanban', '/secureServices/gradeprotectboard', '/iconImg/dbkb.png', 6, 2, 0, 1, '/secureServices/gradeprotectboard.vue', '等保看板', 0);
```

### 等保小组成员
```mysql
CREATE TABLE `blade_equal_protection_group_member` (
  `id` bigint NOT NULL COMMENT '主键',
  `group_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小组编码',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小组名称',
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `user_type` int DEFAULT NULL COMMENT '0组长，1组员',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT '1' COMMENT '状态(0.无效，1.正常)',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='等保看板小组成员表';
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (1, '01', '一组', 103345144201361580, 0, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (2, '01', '一组', 105456914461210782, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (3, '01', '一组', 10208025135636163, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (4, '01', '一组', 118384900082103302, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (5, '02', '二组', 10807225126277572, 0, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (6, '02', '二组', 10626052029421239, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (7, '02', '二组', 101105157642036438, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (8, '02', '二组', 101005133324235713, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (9, '02', '二组', 123253339553033470, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (10, '02', '二组', 119015430212312719, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (11, '03', '三组', 117140945031001347, 0, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (12, '03', '三组', 102470737172131031, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (13, '03', '三组', 122072805172009903, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (14, '03', '三组', 142165462613650762, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (15, '03', '三组', 143556539092095247, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (16, '04', '四组', 112363753152607604, 0, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (17, '04', '四组', 11754612055689615, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (18, '04', '四组', 101133717550512520, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (19, '04', '四组', 101386016204733222, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (20, '04', '四组', 104403826543627126, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (21, '04', '四组', 163085950452089128, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (22, '01', '一组', 125122632652426257, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (23, '01', '一组', 102572338473875641, 1, NULL, NULL, now(), NULL, now(), 1, 0);
INSERT INTO `blade_equal_protection_group_member`(`id`, `group_code`, `group_name`, `user_id`, `user_type`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`) VALUES (24, '03', '三组', 122673022502603403, 1, NULL, NULL, now(), NULL, now(), 1, 0);
```
