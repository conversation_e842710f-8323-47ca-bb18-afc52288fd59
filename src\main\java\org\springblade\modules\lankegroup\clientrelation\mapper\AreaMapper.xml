<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.lankegroup.clientrelation.mapper.AreaMapper">


    <select id="getAreaList" resultType="org.springblade.modules.lankegroup.clientrelation.vo.AreaVO">
        select area.code,area.name from blade_area area
        <if test="parentCode != null and parentCode != ''">
            where area.parent_code = #{parentCode}
        </if>
        order by area.sort asc
    </select>
    <select id="getName" resultType="java.lang.String">
        select name from blade_area where code = #{code}
    </select>
    <select id="getList" resultType="org.springblade.modules.lankegroup.clientrelation.entity.Area">
        select code,name,parent_code from blade_area where code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
