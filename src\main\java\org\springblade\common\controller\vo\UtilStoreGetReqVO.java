package org.springblade.common.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UtilStoreGetReqVO implements Serializable {


    @ApiModelProperty(value = "id", required = true)
    private String id;

    @ApiModelProperty(value = "口令", required = true)
    private String pwd;


}
