package org.springblade.common.annotation.check;

import org.springblade.common.annotation.check.compare.ParamValueValidator;
import org.springframework.data.mongodb.core.mapping.Document;
import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = {ParamValueValidator.class})
@Document
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ParamValue {

    /**
     * 校验分组
     * @return
     */
    Class<?>[] groups() default {};

    /**
     * 提示消息
     * @return
     */
    String message() default "参数不能为空";

    /**
     * 参数值
     * @return
     */
    String[] param() default {};

    /**
     * 是否必填（默认必填）
     * @return
     */
    boolean required() default true;

    /**
     * 粒度校验（暂时未用）
     * @return
     */
    Class<? extends Payload>[] payload() default {};
}
