/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合同管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface ContractMapper extends BaseMapper<Contract> {

    /**
     * 自定义分页
     *
     * @param page
     * @param param
     * @return
     */
    List<ContractListResult> selectContractPage(IPage page, ContractListParam param);

    /**
     * 流程id获取
     *
     * @param processInstanceId
     * @return
     */
    String selectTaskIdByBasic(String processInstanceId);
    String selectTaskIdByBasicAndUser(String processInstanceId, String assignee);

    //	查询流程实例关联的单据信息
    Long selectByProcessInstanceId(String processInstanceId);
    String selectByProcessInstanceIdString(String processInstanceId);

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    ContractVO selectDetail(Map map);

    /**
     * 查询当前登录人，在当前合同类型是否存在草稿
     *
     * @param map
     * @return
     */
    ContractVO selectDraft(Map map);

    /**
     * 查询当天审批合同数量(从1开始)
     * @return
     */
    String selectContractCount();

    /**
     * 合同筛选页面及列表
     * @param param
     * @return
     */
    List<Map> contractFilter(ContractListParam param);

    String number(@Param("param") ContractListParam param);
    /**
     * 合同名称唯一性校验
     */
    List<ContractVO> checkContractNameSole(Map map);

    /**
     *  合同变更时，选择合同时使用
     *  填写合同变更时要求合同列表展示：
     *               销售/其他合同展示待归档状态的合同【2023-10-11 销售/其他合同增加归档撤回、归档驳回两个状态】
     *                采购合同展示已归档项目
     *   当前登录人仅可见本人及下级创建的合同
     * @param map 包含contractName 合同名称
     * @return
     */
    List<ContractVO> contractList(Map map);
//    List<ContractVO> contractList(String contractName);

    /**
     * 客户关联的合同列表
     * @param map
     * @return
     */
//    todo 合同列表仅写了sql
    List<ContractListResult>  customerContractList(IPage page, Map map);

    List<String> selectProjectName(Long projectId);
    String selectByContactId(String contactId);

    /**
     * 合同列表根据合同id查询合同（列表）详情
     * @param contractId
     * @return
     */
    List<ContractListResult> pageDetail(String contractId);

    /**
     * 获取userId的待处理合同名称列表
     * @param userId
     * @return
     */
    List<String> getPendContractNames(Long userId);

    /**
     * 根据金蝶项目id获取审核完成的合同明细信息
     * @param projectId
     * @return
     */
    @DS("sqlserver")
    List<Map> getKdContractEntry(Long projectId);

    /**
     * 项目看板，合同签订阶段，合同相关文件
     */
    List<Map<String, Object>> getProKanBanContractFileByProjectId(long projectId);

    /**
     * 根据业务类别获取合同
     * @param businessId
     * @return
     */
    List<Contract> contractListByBusinessId(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds);

    /**
     * 回款看板坏账统计数据权限单独控制
     * @param businessId
     * @return
     */
    List<Long> contractIdsForPaybackPanelBadDebt(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds);

    /**
     * 根据业务类别和项目进度获取合同ID集合
     * @param businessId
     * @return
     */
    List<Long> contractIdsByBusinessId(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds,@Param("step") Integer step);

    /**
     * 项目看板-根据业务类别和项目进度获取合同ID集合
     * @param businessId
     * @return
     */
    List<Long> contractIdsByBusinessIdForProjectPanel(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds,@Param("step") Integer step,@Param("projectIds") List<String> projectIds);

    /**
     * 看板跳转获取合同ID集合权限
     * @param businessId
     * @return
     */
    List<Long> contractIdsFormPanel(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds);

    /**
     * 项目看板回款统计数据权限控制
     * @param businessId
     * @return
     */
    List<Long> contractIdsFormProjectPanelReceiptAmount(@Param("businessId") Long businessId,@Param("userIds") List<Long> userIds);

    /**
     * 根据合同ID集合获取项目ID集合
     * @param contractIds
     * @return
     */
    List<Long> getProjectIdsByContractIds(@Param("contractIds") List<Long> contractIds);

    /**
     * 根据项目ID获取合同金额
     * @param projectId
     * @return
     */
    BigDecimal getTotalContractAmountByProjectId(@Param("projectId") Long projectId, @Param("types") List<Integer> types);

    List<Kv> getContractList(IPage<Kv> page,String keyword);
}
