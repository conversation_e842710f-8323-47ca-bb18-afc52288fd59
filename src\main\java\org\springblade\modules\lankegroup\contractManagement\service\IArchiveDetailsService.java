/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import org.springblade.core.tool.api.R;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.modules.lankegroup.contractManagement.entity.ArchiveDetails;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ArchiveDetailsVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * 归档详情 服务类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface IArchiveDetailsService extends BaseService<ArchiveDetails> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param archiveDetails
	 * @return
	 */
	IPage<ArchiveDetailsVO> selectArchiveDetailsPage(IPage<ArchiveDetailsVO> page, ArchiveDetailsVO archiveDetails);


	/**
	 * 归档详情 详情查询
	 *
	 * @param
	 * @return
	 */
	ArchiveDetailsVO selectArchiveDetails(Map map);

	Map saveArchive(ArchiveDetailsVO archiveDetailsVO);

//
//	//	上传金蝶保存
//	R saveKingDeeArchive(ArchiveDetailsVO archiveDetailsVO);

	List initContractArchiveDetails();


	/**
	 * 原流程不变，现增加一个流程：已归档后能修改系统名称（已归档后和等保看板有联系，所以不能采用先清除后创建的策略！！！）
	 */
	Boolean updateArchive(ArchiveDetailsVO archiveDetailsVO);

	//去合同变更表中查询 合同信息
	List<ContractChange> contractChange(Long contractId);
	/**
	 * 根据合同id查询当前合同是否有归档数据
	 * @param contractId 合同id
	 * @return
	 */
	ArchiveDetails queryByContractId(Long contractId);

	boolean backups();

	/**
	 * 撤回
	 * @param
	 * @return
	 */
	R rollBack(Long id);
}
