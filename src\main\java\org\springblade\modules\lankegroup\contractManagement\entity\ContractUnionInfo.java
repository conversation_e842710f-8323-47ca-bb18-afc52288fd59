package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目合同信息钉钉表+金蝶表实体类
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Data
@TableName("blade_contract_union_info")
public class ContractUnionInfo {

	private static final long serialVersionUID = 1L;
	@JsonSerialize(
			using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
			value = "id",
			type = IdType.ASSIGN_ID
	)
	private Long id;

	/**
	* 钉钉表项目id
	*/
		private Long projectId;
	/**
	* 金蝶下推后正式项目id
	*/
		private Long kdProjectFid;
	/**
	* 钉钉合同表id
	*/
		private Long contractId;
	/**
	* 金蝶合同表id
	*/
		private Long kdContractFid;
	/**
	* 钉钉合同表合同金额
	*/
		private BigDecimal contractAmount;
	/**
	* 金蝶合同表合同额
	*/
		private BigDecimal kdContractAmount;
	/**
	* 合同单据日期
	*/
		private Date kdDate;
	/**
	* 审批通过时间（钉钉表归档时间）
	*/
		private Date approveDate;
	/**
	* 合同次数
	*/
		private Integer contractNumber;
	/**
	 * 所在年份
	 */
		private Integer inyear;


}
