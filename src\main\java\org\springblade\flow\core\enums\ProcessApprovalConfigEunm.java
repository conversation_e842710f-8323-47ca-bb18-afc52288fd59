package org.springblade.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/6 11:56
 */
@Getter
@AllArgsConstructor
public enum ProcessApprovalConfigEunm {

	TRANSFER_DMIN("1","转交管理员"),
	OUTO_APPROVE("2", "自动通过"),
	OUTO_REJECT("3", "自动拒绝"),
	;

	final String key;

	final String value;

	public static ProcessApprovalConfigEunm getApprovalConfig(String key) {
		for (ProcessApprovalConfigEunm approvalConfigEunm : ProcessApprovalConfigEunm.values()) {
			if (approvalConfigEunm.getKey().equals(key)) {
				return approvalConfigEunm;
			}
		}
		return null;
	}
}

