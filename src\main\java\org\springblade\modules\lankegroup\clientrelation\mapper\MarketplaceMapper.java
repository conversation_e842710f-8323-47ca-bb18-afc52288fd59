package org.springblade.modules.lankegroup.clientrelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.clientrelation.dto.MarketplaceDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.Marketplace;
import org.springblade.modules.lankegroup.clientrelation.vo.MarketplaceVO;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketResultByGroupVO;
import org.springblade.modules.lankegroup.clientrelation.vo.PersonnelMarketVO;

import java.util.List;
import java.util.Map;

public interface MarketplaceMapper extends BaseMapper<Marketplace> {

    /**
     * 行业列表
     * @param marketplaceDTO
     * @param page
     * @return
     */
    IPage<MarketplaceVO> getMarketplaceList(@Param("query") MarketplaceDTO marketplaceDTO, IPage<MarketplaceVO> page,@Param("status") String status);

    /**
     * 客户所属行业时选择使用
     * @param marketplaceDTO
     * @return
     */
    List<MarketplaceVO> industryList(@Param("query") MarketplaceDTO marketplaceDTO);

    /**
     * 根据行业ID集合查询应有客户数量
     * @param ids
     * @return
     */
    Integer getMarketClient(List<Long> ids,Long professionId);

    /**
     * 根据行业ID集合查询应有客户数量
     * @param ids
     * @return
     */
    Integer getMarketClientByJoinId(List<Long> ids,Long professionId,String joinId);

    /**
     * 查询人员各行业应有客户
     * @param ids
     * @return
     */
    Integer getMarketClientByPersonnelId(List<Long> ids,Long personnelId,String joinId);

    /**
     * 根据行业ID集合查询应有客户数量
     * @return
     */
    Integer getMarketClientByUserId(String marketId,String areaId, Long userId);

    /**
     * 获取市场下行业列表
     * @param id
     * @return
     */
    List<PersonnelMarketVO> personnelMarketList(Long id,List<Long> prohibitList);

    /**
     * 获取市场下行业列表
     * @param id
     * @return
     */
    List<PersonnelMarketResultByGroupVO> personnelMarketListByGroup(Long id, List<Long> prohibitList,String cityCode,String areaCode);

    /**
     * 根据行业ID集合查询应有客户数量
     * @param id
     * @return
     */
    Map getMarketClientByName(@Param("marketCode") Long id,@Param("personnelId") String personnelId);

    /**
     * 获取排序位置
     * @param id
     * @return
     */
    Integer getSort(String id,String name,Integer type,Integer status);
}
