package org.springblade.flow.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springblade.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface ApproverMapper {
    String selectByApplyId(Long id);

    String selectUserByMap(Map map);

    Long selectByProcessInstanceId(String processInstanceId);

    Long selectByProcessInstanceIdArchive(String processInstanceId);

    Long selectByProcessInstanceIdReim(String processInstanceId);

    List<User> headOfDepartment(Map map);

    /**
     * 流程审批意见表更新当前操作人
     * @param taskId
     * @param userId
     * @return
     */
    Integer updateComment(String taskId,String userId);
    /**
     * 根据id查询金蝶直接上级
     */
//    User queryHeader(Long userId);
    /**
     * 根据本人钉钉id==》user.account
     * 获取上级钉钉id
     * 金蝶数据库查询金蝶岗位汇报关系里的直接上级
     * 报销审批使用
     */
    @DS("sqlserver")
    String queryKingdeeHeader(String account);
    Integer getMultipleEntityVariables(String processInstanceId, String variablesName);
    /**
     * 流程id获取
     *
     * @param processInstanceId
     * @return
     */
    String selectTaskId(String processInstanceId);
}
