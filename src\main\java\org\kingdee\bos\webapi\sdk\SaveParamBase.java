//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.kingdee.bos.webapi.sdk;

import java.util.ArrayList;

public class SaveParamBase extends JsonBase {
    int Creator;
    ArrayList<String> NeedReturnFields = new ArrayList();
    ArrayList<String> NeedUpDateFields = new ArrayList();
    int SubSystemId;

    public int getSubSystemId() {
        return SubSystemId;
    }

    public void setSubSystemId(int subSystemId) {
        SubSystemId = subSystemId;
    }

    public SaveParamBase() {
    }

    public int getCreator() {
        return this.Creator;
    }

    public void setCreator(int creator) {
        this.Creator = creator;
    }

    public ArrayList<String> getNeedReturnFields() {
        return this.NeedReturnFields;
    }

    public void setNeedReturnFields(ArrayList<String> needReturnFields) {
        this.NeedReturnFields = needReturnFields;
    }

    public ArrayList<String> getNeedUpDateFields() {
        return this.NeedUpDateFields;
    }

    public void setNeedUpDateFields(ArrayList<String> needUpDateFields) {
        this.NeedUpDateFields = needUpDateFields;
    }
}

