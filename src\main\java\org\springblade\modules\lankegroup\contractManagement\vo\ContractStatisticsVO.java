package org.springblade.modules.lankegroup.contractManagement.vo;

import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 合同看板统计
 */
@Data
public class ContractStatisticsVO extends BaseEntity {

    private String userId;

    private String userName;

    private Integer conCount;

    private String conTotal;

    private String deptId;

    private String deptName;

    private Integer sort;

    private Integer groupType;

    public BigDecimal getConTotalAsBigDecimal() {
        return new BigDecimal(getConTotal()).setScale(2, RoundingMode.HALF_UP);
    }
}
