package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DingTalkModelEnum {

    // 系统消息应用AgentId
    SYSTEM_TEST_APP_AGENT_ID("testSystemAgentId","2462352191"),
    // 系统消息应用APPKey
    SYSTEM_TEST_APP_APP_KEY("testSystemAppKey","dingkpldvmgohkys6csd"),
    // 系统消息应用AppSecret
    SYSTEM_TEST_APP_APP_SECRET("testSystemAppSecret","T6l5D3vqFhrk-Yfax_RaJezXZh7k_OKK4VWLQrMANorDMKbo9lLSmNRhaQPU0CFF"),
    // 系统消息模板 测试
//    SYSTEM_MSG_MODEL_ZERO("1","06074b54-da9a-44b6-a767-ec2d90ae3c80.schema"),
    SYSTEM_MSG_MODEL_ZERO("1","573abf5b-270f-4ca8-b04a-c8614b289e9d.schema"),
//    报销单图片消息通知 测试 (暂时未使用)
    REIM_MSG_MODEL_ZERO("11","ee82e31b-b15c-4f0c-88de-419a4f452857.schema"),
// 系统消息模板  正式
    SYSTEM_MSG_MODEL_ONE("0","5cf4e9fa-0046-4e01-96c5-7bd6d2265d11.schema")
    ;

    final String key;
    final String value;

    public static DingTalkModelEnum of(String key) {
        if (key == null) {
            return null;
        }
        DingTalkModelEnum[] values = values();
        for (DingTalkModelEnum msgEnum : values) {
            if (msgEnum.key.equals(key)) {
                return msgEnum;
            }
        }
        return null;
    }

}
