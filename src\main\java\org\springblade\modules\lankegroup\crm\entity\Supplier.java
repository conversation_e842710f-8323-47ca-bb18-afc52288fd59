/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import org.springblade.common.utils.Update;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商表实体类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@TableName("blade_supplier")
@EqualsAndHashCode(callSuper = true)
public class Supplier extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 供应商id
	*/
//		private Long id;
	/**
	* 供应商编码
	*/
		private String supplierCode;


	/**
	 * 供应商名称
	*/
		private String supplierName;

	/**
	 * 创建组织id
	 */
	private Long createOrgId;

/**
 * 供应商分组id
 */
	private String supplierGroupId;
	/**
	* 供应商分组编码
	*/
		private String supplierGroupCode;
	/**
	* 供应商分组名称
	*/
		private String supplierGroupName;
	/**
	* 供应商银行卡号
	*/
		private String supplierBankCode;
	/**
	* 供应商开户行
	*/
		private String supplierBankName;

	/**
	 * 金蝶供应商id
	 */
	private String kdSupplierId;
	/**
	 * 供应商负责人
	 */
	private Long chargeUser;
	/**
	 * 供应类别
	 */
	private String category;
	/**
	 * 通信地址
	 */
	private String address;
	/**
	 * 作废标志：A未作废，B已作废
	 */
	private String forbidStatus="A";

	/**
	 * 税号
	 */
	private String customerTaxNumber;

	/**
	 * 省区域编码
	 */
	private String provinceCode;

	/**
	 * 省区域名称
	 */
	private String provinceName;

	/**
	 * 市区域编码
	 */
	private String cityCode;

	/**
	 * 市区域名称
	 */
	private String cityName;

	/**
	 * 区县区域编码
	 */
	private String countiesCode;

	/**
	 * 区县区域名称
	 */
	private String countiesName;
}
