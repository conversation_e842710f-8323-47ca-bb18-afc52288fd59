package org.springblade.flow.business.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.entity.BusinessProcessEntity;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.business.service.IBusinessProcessService;
import org.springblade.flow.core.constant.FlowFormConstant;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.flow.core.enums.FlowResultCode;
import org.springblade.flow.core.enums.ProcessStatusEnum;
import org.springblade.flow.core.feign.IWorkFlowClient;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.flow.engine.utils.FlowApproverVariablesUtils;
import org.springblade.common.cache.DictCache;
import org.springframework.stereotype.Service;

import java.util.Map;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22 14:17
 */
@NonDS
@Service
@AllArgsConstructor
public class WorkFlowClient implements IWorkFlowClient {

	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final TaskService taskService;
	private final FlowBusinessService flowBusinessService;
	private final FlowEngineService flowEngineService;
	private final IBusinessProcessService businessProcessService;

	@Override
	public R startProcessInstance(Map<String, Object> variables, String formKey, Long fromDataId, String fromDataCode, String businessField, String businessCategory) {
		String processInstanceId=null;
		FlowModel flowModel = flowEngineService.getOne(Wrappers.lambdaQuery(FlowModel.class)
			.eq(FlowModel::getFormKey, formKey)
			.eq(FlowModel::getStatus, 0).last("limit 1"));
		if (Func.isEmpty(flowModel)) {
			return R.fail(FlowResultCode.FLOW_NOT_FOUND, "审批流不存在或已禁用");
		}
		try {
			if (Func.isEmpty(variables)) {
				variables = Kv.create();
			}
			variables.put(PROC_INSTANCE_FORM_CODE, fromDataCode);
			variables.put(PROC_INSTANCE_FORM_KEY, formKey);
			variables.put(PROC_INSTANCE_FORM_DATA_ID, fromDataId);
			variables.put(PROC_INSTANCE_NAME, DictCache.getValue(flowModel.getSubclass()));
			String businessKey = FlowUtil.getBusinessKey(formKey, fromDataId);
			//设置审批人相关流程变量
			FlowApproverVariablesUtils.initFlowApproverVariables(flowModel.getProcessDefinitionId(), variables);
			// 开启流程
			ProcessInstance processInstance = runtimeService.startProcessInstanceById(flowModel.getProcessDefinitionId(), businessKey, variables);
			processInstanceId = processInstance.getProcessInstanceId();
			//保存自定义流程任务信息
			savaBusinessProcess(formKey, fromDataId, fromDataCode, flowModel, processInstance, businessField, businessCategory);

			// 第一个用户任务为发起人，则自动完成任务
			startFirstTask(processInstance, variables);

		} catch (Exception e) {
			System.out.println("e---------------------"+e);
			return R.fail(FlowResultCode.START_PROCESS_ERROR, "审批流启动异常，请联系管理员");
		}
		return R.data(processInstanceId);
	}

	@Override
	public R initiatorCompleteTask(Map<String, Object> variables,String formKey, Long fromDataId, String businessField, Boolean businessFieldIgnore) {
		if (Func.isEmpty(businessFieldIgnore)) {
			businessFieldIgnore = true;
		}
		FlowModel flowModel = flowEngineService.getOne(Wrappers.lambdaQuery(FlowModel.class)
			.eq(FlowModel::getFormKey, formKey)
			.eq(FlowModel::getStatus, 0).last("limit 1"));
		if (Func.isEmpty(flowModel)) {
			return R.fail(FlowResultCode.FLOW_NOT_FOUND, "审批流不存在或已禁用");
		}

		BusinessProcessEntity businessProcessEntity = businessProcessService.getOne(new LambdaQueryWrapper<BusinessProcessEntity>()
			.eq(BusinessProcessEntity::getFormKey, formKey)
			.eq(BusinessProcessEntity::getFormDataId, fromDataId));

		Task task = taskService.createTaskQuery().processInstanceId(businessProcessEntity.getProcessInstanceId()).singleResult();
		taskService.addComment(task.getId(), businessProcessEntity.getProcessInstanceId(), FlowCommentEnum.APPROVE.getType(), AuthUtil.getUserName() + "重新发起流程申请");
		Integer currentRound = (Integer) taskService.getVariable(task.getId(), "reviewRound");
		if (currentRound == null) {
			currentRound = 0;
		}
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		variables.put("reviewRound", currentRound + 1);
		taskService.complete(task.getId(), variables);

		//更新任务状态
		businessProcessEntity.setStatus(ProcessStatusEnum.待审批.getCode());
		if (Func.isNotBlank(businessField)) {
			businessProcessEntity.setBusinessField(businessField);
		}
		if (Func.isBlank(businessField) && !businessFieldIgnore) {
			businessProcessEntity.setBusinessField(businessField);
		}
		businessProcessService.updateById(businessProcessEntity);

		return R.status(true);
	}

	public R removeBussinessForm(String formKey, Long formDataId) {
		BusinessProcessEntity businessProcessEntity = businessProcessService.getOne(new LambdaQueryWrapper<BusinessProcessEntity>()
				.eq(BusinessProcessEntity::getFormKey, formKey)
				.eq(BusinessProcessEntity::getFormDataId, formDataId));
		if (businessProcessEntity != null) {
			return R.status(businessProcessService.removeById(businessProcessEntity.getId()));
		} else {
			return R.fail("未找到对应业务流程");
		}
	}

	/**
	 * 若第一个用户任务为发起人，则自动完成任务
	 *
	 * @param processInstance
	 * @param variables
	 */
	public void startFirstTask(ProcessInstance processInstance, Map<String, Object> variables) {
		// 若第一个用户任务为发起人，则自动完成任务
		Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
		if (Func.isNotEmpty(task)) {
			String initiatorUser = (String) variables.get((BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR));
			if (StringUtil.equals(task.getAssignee(), initiatorUser)) {
				variables.put("reviewRound", 1);
				taskService.complete(task.getId(), variables);
			}
		}
	}

	private void savaBusinessProcess(String formKey, Long fromDataId, String fromDataCode, FlowModel flowModel, ProcessInstance processInstance, String businessField, String businessCategory) {
		BusinessProcessEntity businessProcessEntity = new BusinessProcessEntity();
		businessProcessEntity.setFormKey(formKey);
		businessProcessEntity.setFormDataId(fromDataId);
		businessProcessEntity.setFormDataCode(fromDataCode);
		businessProcessEntity.setStatus(ProcessStatusEnum.待审批.getCode());
		businessProcessEntity.setCategory(flowModel.getCategory());
		businessProcessEntity.setSubclass(flowModel.getSubclass());
		businessProcessEntity.setProcessInstanceId(processInstance.getProcessInstanceId());
		businessProcessEntity.setProcessDefinitionId(processInstance.getProcessDefinitionId());
		businessProcessEntity.setBusinessField(businessField);
		businessProcessEntity.setBusinessCategory(businessCategory);
		businessProcessService.save(businessProcessEntity);
	}
}
