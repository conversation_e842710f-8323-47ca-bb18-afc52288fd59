package org.springblade.modules.lankegroup.clientrelation.vo;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionArea;
import org.springblade.modules.lankegroup.clientrelation.entity.ProfessionMarket;

import java.util.List;

@Data
public class ProfessionJoinVO {

    /**
     * 区划列表
     */
    private List<ProfessionArea> areaList;

    /**
     * 行业列表
     */
    private List<ProfessionMarket> marketList;

    /**
     * 关联ID
     */
    private String joinId;

}
