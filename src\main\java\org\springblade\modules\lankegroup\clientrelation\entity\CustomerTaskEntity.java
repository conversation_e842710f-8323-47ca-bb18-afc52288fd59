package org.springblade.modules.lankegroup.clientrelation.entity;


import lombok.Data;

/**
 * 客户统计定时实体
 * <AUTHOR>
 */
@Data
public class CustomerTaskEntity {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String urbanArea;

    /**
     * 区县
     */
    private String city;

    /**
     * 行业
     */
    private String marketId;

    /**
     * 客户负责人
     */
    private String userId;

    /**
     * 客户重要程度
     */
    private String importanceKey;

    /**
     * 区域市场ID
     */
    private String professionId;

    /**
     * 客户创建时间
     */
    private String createTime;

    private String areaCode;

    private String areaName;

    private String oughtClient;

    private String actualClient;

    private String professionName;

    private String personnelId;

    private String userName;

}
