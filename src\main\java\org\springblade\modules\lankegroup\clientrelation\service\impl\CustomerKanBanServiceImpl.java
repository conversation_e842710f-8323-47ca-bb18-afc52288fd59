package org.springblade.modules.lankegroup.clientrelation.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.lankegroup.clientrelation.dto.CustomerKanBanDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.Profession;
import org.springblade.modules.lankegroup.clientrelation.mapper.CustomerKanBanMapper;
import org.springblade.modules.lankegroup.clientrelation.mapper.ProfessionMapper;
import org.springblade.modules.lankegroup.clientrelation.service.CustomerKanBanService;
import org.springblade.modules.lankegroup.clientrelation.vo.*;
import org.springblade.modules.lankegroup.customerVisit.vo.VisitRank;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springblade.modules.lankegroup.pro_management.service.IProjectBasicService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.mapper.UserMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class CustomerKanBanServiceImpl implements CustomerKanBanService {

    private final DeptByUserService deptByUserService;

    private final CustomerKanBanMapper customerKanBanMapper;

    private final UserMapper userMapper;

    private final ProfessionMapper professionMapper;

    private final ProjectBasicMapper projectBasicMapper;

    /**
     * 客户看板-客户数量统计
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerCount(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        if (null != customerKanBanDTO.getResultData() && 1 == customerKanBanDTO.getResultData()) {
            List<Long> result = new ArrayList<>();
            switch (customerKanBanDTO.getDataType()) {
                case 1:
                    // 设置清除时间过滤条件
                    customerKanBanDTO.setType(1);
                    List<Long> customerCount = customerKanBanMapper.getCustomerCount(customerKanBanDTO);
                    result.addAll(customerCount);
                    break;
                case 2:
                    List<Long> newCustomerCount = customerKanBanMapper.getCustomerCount(customerKanBanDTO);
                    result.addAll(newCustomerCount);
                    break;
                case 3:
                    // 设置清除时间过滤条件
                    customerKanBanDTO.setType(1);
                    List<String> callCustomerCount = customerKanBanMapper.getCallCustomerCount(customerKanBanDTO);
                    callCustomerCount.stream().forEach(customer -> {
                        List<Map> maps = JSON.parseArray(customer, Map.class);
                        if (null != maps && maps.size() > 0) {
                            maps.stream().forEach(m -> {
                                result.add(Long.valueOf(String.valueOf(m.get("customerId"))));
                            });
                        }
                    });
                    break;
                case 4:
                    List<String> newCallCustomerCount = customerKanBanMapper.getCallCustomerCount(customerKanBanDTO);
                    newCallCustomerCount.stream().forEach(customer -> {
                        List<Map> maps = JSON.parseArray(customer, Map.class);
                        if (null != maps && maps.size() > 0) {
                            maps.stream().forEach(m -> {
                                result.add(Long.valueOf(m.get("customerId").toString()));
                            });
                        }
                    });
                    break;
                case 5:
                    // 设置清除时间过滤条件
                    customerKanBanDTO.setType(1);
                    List<Long> projectApprovalCustomerCount = customerKanBanMapper.getProjectApprovalCustomerCount(customerKanBanDTO);
                    result.addAll(projectApprovalCustomerCount);
                    break;
                case 6:
                    List<Long> newProjectApprovalCustomerCount = customerKanBanMapper.getProjectApprovalCustomerCount(customerKanBanDTO);
                    result.addAll(newProjectApprovalCustomerCount);
                    break;
                case 7:
                    // 设置清除时间过滤条件
                    customerKanBanDTO.setType(1);
                    List<Long> baseDiscCustomerCount = customerKanBanMapper.getBaseDiscCustomerCount(customerKanBanDTO);
                    result.addAll(baseDiscCustomerCount);
                    break;
                case 8:
                    // 设置清除时间过滤条件
                    List<Long> newBaseDiscCustomerCount = customerKanBanMapper.getBaseDiscCustomerCount(customerKanBanDTO);
                    result.addAll(newBaseDiscCustomerCount);
                    break;
            }
            return R.data(result);
        } else {
            Map<String, Integer> result = new HashMap<>();
            if (customerKanBanDTO.getAuthUserList().size() < 1 && null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId())) {
                result.put("newCustomerCount", 0);
                result.put("newCallCustomerCount", 0);
                result.put("newProjectApprovalCustomerCount", 0);
                result.put("newBaseDiscCustomerCount", 0);
                result.put("customerCount", 0);
                result.put("callCustomerCount", 0);
                result.put("projectApprovalCustomerCount", 0);
                result.put("baseDiscCustomerCount", 0);
                return R.data(result);
            }
            // 新增客户数量
            Integer newCustomerCount = customerKanBanMapper.getCustomerCount(customerKanBanDTO).size();
            result.put("newCustomerCount", newCustomerCount);
            // 新增拜访客户数
            List<String> newCallCustomerCount = customerKanBanMapper.getCallCustomerCount(customerKanBanDTO);
            result.put("newCallCustomerCount", newCallCustomerCount.size());
            //新增立项客户数
            Integer newProjectApprovalCustomerCount = customerKanBanMapper.getProjectApprovalCustomerCount(customerKanBanDTO).size();
            result.put("newProjectApprovalCustomerCount", newProjectApprovalCustomerCount);
            // 新增基本盘客户数
            Integer newBaseDiscCustomerCount = customerKanBanMapper.getBaseDiscCustomerCount(customerKanBanDTO).size();
            result.put("newBaseDiscCustomerCount", newBaseDiscCustomerCount);
            // 设置清除时间过滤条件
            customerKanBanDTO.setType(1);
            // 全部客户数量
            Integer customerCount = customerKanBanMapper.getCustomerCount(customerKanBanDTO).size();
            result.put("customerCount", customerCount);
            // 拜访客户数
            List<String> callCustomerCount = customerKanBanMapper.getCallCustomerCountByData(customerKanBanDTO);
            result.put("callCustomerCount", callCustomerCount.size());
            // 立项客户数
            Integer projectApprovalCustomerCount = customerKanBanMapper.getProjectApprovalCustomerCount(customerKanBanDTO).size();
            result.put("projectApprovalCustomerCount", projectApprovalCustomerCount);
            // 基本盘客户数
            Integer baseDiscCustomerCount = customerKanBanMapper.getBaseDiscCustomerCount(customerKanBanDTO).size();
            result.put("baseDiscCustomerCount", baseDiscCustomerCount);
            // 拼装结果
            return R.data(result);
        }
    }

    /**
     * 客户看板-客户行业分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerProfession(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        List<CustomerKanBanResult> result = new ArrayList<>();
        getUserLoginAuth(customerKanBanDTO);
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId()) && (
                null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1
        ) && (null == customerKanBanDTO.getAuthUserList() || customerKanBanDTO.getAuthUserList().size() < 1) &&
                (null == customerKanBanDTO.getAreaAndMarketList() || customerKanBanDTO.getAreaAndMarketList().size() < 1)) {
            return R.data(result);
        }
        List<CustomerKanBanResult> customerProfessionY = new ArrayList<>();
        List<CustomerKanBanResult> customerProfessionS = new ArrayList<>();
        switch (customerKanBanDTO.getLeader()) {
            case 3:
                // 查询应有
                customerProfessionY = customerKanBanMapper.getCustomerProfessionOneY(customerKanBanDTO);
                result.addAll(mergeMarketResults(customerProfessionY));
                break;
            case 2:
                // 查询应有
                customerProfessionY = customerKanBanMapper.getCustomerProfessionOneY(customerKanBanDTO);
                result.addAll(mergeMarketResults(customerProfessionY));
                break;
            case 1:
                // 查询应有
                customerProfessionY = customerKanBanMapper.getCustomerProfessionOneY(customerKanBanDTO);
                result.addAll(customerProfessionY);
                break;
        }
        return R.data(result.stream().sorted(Comparator.comparing(CustomerKanBanResult::getSCustomerCount).reversed()).collect(Collectors.toList()));
    }

    public List<CustomerKanBanResult> mergeMarketResults(List<CustomerKanBanResult> results) {
        // 使用 Stream 对结果进行分组，按照 marketCode 进行分组
        Map<String, List<CustomerKanBanResult>> groupedResults = results.stream()
                .collect(Collectors.groupingBy(CustomerKanBanResult::getMarketCode));

        List<CustomerKanBanResult> mergedResults = new ArrayList<>();

        // 遍历分组后的结果，进行合并
        for (Map.Entry<String, List<CustomerKanBanResult>> entry : groupedResults.entrySet()) {
            List<CustomerKanBanResult> groupResults = entry.getValue();
            CustomerKanBanResult mergedResult = new CustomerKanBanResult();

            // 取出第一个结果作为基础
            CustomerKanBanResult baseResult = groupResults.get(0);
            mergedResult.setMarketCode(baseResult.getMarketCode());
            mergedResult.setMarketName(baseResult.getMarketName());
            mergedResult.setProfessionId(baseResult.getProfessionId());
            mergedResult.setUserId(baseResult.getUserId());

            // 合并 yCustomerCount 和 sCustomerCount 字段
            int totalYCustomerCount = groupResults.stream()
                    .mapToInt(result -> result.getYCustomerCount() != null ? result.getYCustomerCount() : 0)
                    .sum();
            int totalSCustomerCount = groupResults.stream()
                    .mapToInt(result -> result.getSCustomerCount() != null ? result.getSCustomerCount() : 0)
                    .sum();
            mergedResult.setYCustomerCount(totalYCustomerCount);
            mergedResult.setSCustomerCount(totalSCustomerCount);

            // 计算客户覆盖度
            if (totalYCustomerCount == 0 && totalSCustomerCount > 0) {
                mergedResult.setCustomerCoverage(100);
            } else {
                float customerCoverage = (float) totalSCustomerCount / totalYCustomerCount;
                mergedResult.setCustomerCoverage(customerCoverage * 100);
            }
            mergedResults.add(mergedResult);
        }

        return mergedResults;
    }

    /**
     * 客户看板-客户行业分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerProfessionPieChart(CustomerKanBanDTO customerKanBanDTO) {
        Map result = new HashMap();
        getUserLoginAuth(customerKanBanDTO);
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId()) && (
                null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1
        ) && (null == customerKanBanDTO.getAuthUserList() || customerKanBanDTO.getAuthUserList().size() < 1) &&
                (null == customerKanBanDTO.getAreaAndMarketList() || customerKanBanDTO.getAreaAndMarketList().size() < 1)) {
            return R.data(result);
        }
        List<CustomerProfessionPieChartVO> customerProfessionPieChart = customerKanBanMapper.getCustomerProfessionPieChart(customerKanBanDTO);
        Integer customerProfessionPieChartCount = customerKanBanMapper.getCustomerProfessionPieChartCount(customerKanBanDTO);
        result.put("count", customerProfessionPieChartCount);
        result.put("list", customerProfessionPieChart);
        return R.data(result);
    }

    /**
     * 客户看板-客户行业分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerProfessionListDetail(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        List<CustomerKanBanDetailResult> result = new ArrayList<>();
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        Map resultMap = new HashMap();
       /* switch (customerKanBanDTO.getLeader()) {
            case 3:
                resultMap.put("type", false);
                String userId = String.valueOf(AuthUtil.getUserId());
                if (null != customerKanBanDTO.getUserId() && !"".equals(customerKanBanDTO.getUserId())) {
                    userId = customerKanBanDTO.getUserId();
                }
                result = customerKanBanMapper.getPersonnelCustomerList(userId,
                        customerKanBanDTO.getMarketplaceIdList(), customerKanBanDTO.getAreaIdList(), customerKanBanDTO.getIsData());
                break;
            case 2:
                resultMap.put("type", false);
                result = customerKanBanMapper.getGroupCustomerList(customerKanBanDTO.getAuthUserList(), customerKanBanDTO.getMarketplaceIdList(),
                        customerKanBanDTO.getAreaIdList(), customerKanBanDTO.getIsData(), customerKanBanDTO.getProfessionIdList(), customerKanBanDTO.getAuthUserList());
                break;
            case 1:*/
                resultMap.put("type", true);
                result = customerKanBanMapper.getAllCustomerList(customerKanBanDTO.getMarketplaceIdList(),
                        customerKanBanDTO.getAreaIdList(), customerKanBanDTO.getIsData(),customerKanBanDTO.getAuthUserList());
           /*     break;
        }*/
        List<CustomerKanBanDetailResult> sortedList = result.stream()
                .sorted(Comparator.comparing(CustomerKanBanDetailResult::getAreaCode)
                        .thenComparing(CustomerKanBanDetailResult::getMarketCode))
                .collect(Collectors.toList());
        resultMap.put("data", sortedList);
        return R.data(resultMap);
    }

    /**
     * 客户看板-客户区域分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerRegion(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        List<CustomerKanBanProfessionResult> result = new ArrayList<>();
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId()) && (
                null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1
        ) && (null == customerKanBanDTO.getAuthUserList() || customerKanBanDTO.getAuthUserList().size() < 1) &&
                (null == customerKanBanDTO.getAreaAndMarketList() || customerKanBanDTO.getAreaAndMarketList().size() < 1)) {
            return R.data(result);
        }
        Map resultMap = new HashMap();
        List<CustomerKanBanProfessionResult> businessListY = new ArrayList<>();
        switch (customerKanBanDTO.getLeader()) {
            case 3:
                List<CustomerKanBanProfessionResult> groupAreaCustomerPersonnelListYSingle = customerKanBanMapper.getGroupAreaCustomerPersonnelSingleListY(customerKanBanDTO);
                resultMap.put("type", false);
                result.addAll(groupAreaCustomerPersonnelListYSingle);
                break;
            case 2:
                List<CustomerKanBanProfessionResult> groupAreaCustomerPersonnelListY = customerKanBanMapper.getGroupAreaCustomerPersonnelListY(customerKanBanDTO);
                CustomerKanBanProfessionResult qiTaCustomerList = customerKanBanMapper.getQiTaCustomerList(customerKanBanDTO.getProfessionIdList(), customerKanBanDTO.getAuthUserList());
                if (null != qiTaCustomerList) {
                    groupAreaCustomerPersonnelListY.add(qiTaCustomerList);
                }
                businessListY.addAll(groupAreaCustomerPersonnelListY);
                resultMap.put("type", false);
                result.addAll(businessListY);
                break;
            case 1:
                businessListY = customerKanBanMapper.getProfessionListGroup();
                resultMap.put("type", true);
                result.addAll(businessListY);
                break;
        }
        resultMap.put("data", result);
        return R.data(resultMap);
    }

    /**
     * 客户看板-客户区域分析-列表
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerRegionList(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        List<CustomerKanBanProfessionResult> result = new ArrayList<>();
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId()) && (
                null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1
        ) && (null == customerKanBanDTO.getAuthUserList() || customerKanBanDTO.getAuthUserList().size() < 1) &&
                (null == customerKanBanDTO.getAreaAndMarketList() || customerKanBanDTO.getAreaAndMarketList().size() < 1)) {
            return R.data(result);
        }
        if (3 == customerKanBanDTO.getLeader()) {
            return R.data(result);
        }
        List<Long> professionIdList = new ArrayList<>();
        professionIdList.add(Long.valueOf(customerKanBanDTO.getProfessionId()));
        customerKanBanDTO.setProfessionIdList(professionIdList);
        List<CustomerKanBanProfessionResult> groupAreaCustomerPersonnelListY = customerKanBanMapper.getGroupAreaCustomerPersonnelListY(customerKanBanDTO);
        if (!"1".equals(customerKanBanDTO.getQiTqType())) {
            // 查询其他
            // 查询当前区域市场人员列表
            List<Long> professionByUserId = customerKanBanMapper.getProfessionByUserId(customerKanBanDTO.getProfessionId());
            if (null != professionByUserId && professionByUserId.size() > 0) {
                CustomerKanBanProfessionResult qiTaCustomerList = customerKanBanMapper.getQiTaCustomerListS(customerKanBanDTO.getProfessionId(), professionByUserId);
                if (qiTaCustomerList.getSCustomerCount() > 0) {
                    groupAreaCustomerPersonnelListY.add(qiTaCustomerList);
                }
            }
        }
        result.addAll(groupAreaCustomerPersonnelListY);
        return R.data(result);
    }

    /**
     * 客户看板-客户区域分析-列表详情
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerRegionListDetail(CustomerKanBanDTO customerKanBanDTO) {
        getUserLoginAuth(customerKanBanDTO);
        List<CustomerKanBanDetailResult> result = new ArrayList<>();
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null == customerKanBanDTO.getProfessionId() || "".equals(customerKanBanDTO.getProfessionId())) {
            return R.data(result);
        }
        if ("1".equals(customerKanBanDTO.getProfessionId())) {
            if (null != customerKanBanDTO.getQiTqProfessionId() && !"".equals(customerKanBanDTO.getQiTqProfessionId())) {
                // 查询当前区域市场人员列表
                List<Long> professionByUserId = customerKanBanMapper.getProfessionByUserId(customerKanBanDTO.getQiTqProfessionId());
                if (null != professionByUserId && professionByUserId.size() > 0) {
                    customerKanBanDTO.getAuthUserList().addAll(professionByUserId);
                    customerKanBanDTO.getProfessionIdList().removeAll(customerKanBanDTO.getProfessionIdList());
                    customerKanBanDTO.getProfessionIdList().add(Long.valueOf(customerKanBanDTO.getQiTqProfessionId()));
                }
            }
            result = customerKanBanMapper.getMarketByUserIdAndProfessionIdByCustomerId(customerKanBanDTO);
        } else {
            result = customerKanBanMapper.getPersonnelByMarketList(customerKanBanDTO);
        }
        if (1 == customerKanBanDTO.getIsData()) {
            return R.data(result.stream().filter(obj -> obj.getCustomerId() != null).collect(Collectors.toList()));
        }
        if (2 == customerKanBanDTO.getIsData()) {
            return R.data(result.stream().filter(obj -> obj.getCustomerId() == null).collect(Collectors.toList()));
        }
        return R.data(result);
    }

    /**
     * 客户看板-客户区域分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerRegionPieChart(CustomerKanBanDTO customerKanBanDTO) {
        Map result = new HashMap();
        getUserLoginAuth(customerKanBanDTO);
        if (2 == customerKanBanDTO.getLeader() && (null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1)) {
            return R.data(result);
        }
        if (null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId()) && (
                null == customerKanBanDTO.getProfessionIdList() || customerKanBanDTO.getProfessionIdList().size() < 1
        ) && (null == customerKanBanDTO.getAuthUserList() || customerKanBanDTO.getAuthUserList().size() < 1) &&
                (null == customerKanBanDTO.getAreaAndMarketList() || customerKanBanDTO.getAreaAndMarketList().size() < 1)) {
            return R.data(result);
        }
        Double sumCount = 0.0;
        List<CustomerProfessionPieChartVO> marketList = new ArrayList<>();
        switch (customerKanBanDTO.getLeader()) {
            case 3:
                // 普通人员 只查询负责人是本人的数据
                // 查询客户总数
                sumCount = customerKanBanMapper.getMarketByUserIdCount(customerKanBanDTO.getAuthUserList());
                // 查询各行业客户数量
                marketList = customerKanBanMapper.getMarketByUserId(customerKanBanDTO.getAuthUserList());
                result.put("list", marketList);
                break;
            case 2:
                // 组长级别 查询自身区域市场
                sumCount = customerKanBanMapper.getMarketByUserIdAndProfessionIdCount(customerKanBanDTO);
                // 查询各人员客户数量
                marketList = customerKanBanMapper.getMarketByUserIdAndProfessionId(customerKanBanDTO);
                result.put("list", marketList);
                break;
            case 1:
                // 领导  查询全部去区域市场
                sumCount = customerKanBanMapper.getCustomerCountByAllUser();
                // 查询
                // 查询各区域市场客户数量
                marketList = customerKanBanMapper.getProfessionChartGroup();
                result.put("list", marketList);
                break;
        }
        result.put("count", sumCount);
        return R.data(result);
    }

    /**
     * 客户看板-行业产值
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerProfessionValue(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        if (customerKanBanDTO.getAuthUserList().isEmpty() && null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId())) {
            return R.data(new ArrayList<>());
        } else {
            List<OutputListVO> result = new ArrayList<>();
            List<OutputListVO> customerProfessionValue = customerKanBanMapper.getCustomerProfessionValue(customerKanBanDTO);
            if(null == customerProfessionValue || customerProfessionValue.isEmpty()){
                return R.data(result);
            }
            List<Map> maps = projectBasicMapper.contractAmountByProfession(customerProfessionValue.stream().map(OutputListVO::getProjectId).collect(Collectors.toList()),customerKanBanDTO.getStartTime(),customerKanBanDTO.getEndTime());
            customerProfessionValue.stream().forEach(obj -> maps.stream().forEach(map -> {
                if (obj.getProjectId().equals(map.get("fid").toString())) {
                    obj.setContractAmount(Double.valueOf(map.get("contractAmount").toString()));
                }
            }));
            result = customerProfessionValue.stream()
                    .collect(Collectors.groupingBy(OutputListVO::getId, Collectors.summingDouble(OutputListVO::getContractAmount)))
                    .entrySet().stream()
                    .map(entry -> {
                        OutputListVO output = new OutputListVO();
                        output.setId(entry.getKey());
                        output.setMarketplacName(customerProfessionValue.stream()
                                .filter(vo -> vo.getId().equals(entry.getKey()))
                                .map(OutputListVO::getMarketplacName)
                                .findFirst().orElse(null));
                        output.setContractAmount(entry.getValue());
                        return output;
                    })
                    .sorted(Comparator.comparingDouble(OutputListVO::getContractAmount).reversed())
                    .collect(Collectors.toList());
            return R.data(result);
        }
    }

    /**
     * 客户看板-行业产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerProfessionValueGroup(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        if (!Func.isEmpty(customerKanBanDTO.getEndTime())) {
            customerKanBanDTO.setEndTime(customerKanBanDTO.getEndTime());
        }
        List<OutputListVO> resultGroup = new ArrayList<>();
        List<OutputListVO> customerProfessionValueGroup = customerKanBanMapper.getCustomerProfessionValueGroup(customerKanBanDTO);
        if(null == customerProfessionValueGroup || customerProfessionValueGroup.size() < 1){
            return R.data(resultGroup);
        }
        List<Map> maps = projectBasicMapper.contractAmountByProfession(customerProfessionValueGroup.stream().map(OutputListVO::getProjectId).collect(Collectors.toList()),customerKanBanDTO.getStartTime(),customerKanBanDTO.getEndTime());
        maps.stream().forEach(map -> {
            customerProfessionValueGroup.stream().forEach(obj -> {
                if (obj.getProjectId().equals(map.get("fid").toString())) {
                    obj.setContractAmount(Double.valueOf(map.get("contractAmount").toString()));
                    resultGroup.add(obj);
                }
            });
        });
        Map<String, List<OutputListVO>> groupedProjects = resultGroup.stream()
                .collect(Collectors.groupingBy(OutputListVO::getGroupServiceId));
        List<OutputListVO> resultList = groupedProjects.entrySet().stream()
                .map(entry -> {
                    List<OutputListVO> group = entry.getValue();
                    String concatenatedProjectIds = group.stream()
                            .map(OutputListVO::getProjectId)
                            .collect(Collectors.joining(","));
                    Double totalContractAmount = group.stream()
                            .mapToDouble(vo -> vo.getContractAmount() == null ? 0.0 : vo.getContractAmount())
                            .sum();
                    OutputListVO representativeVO = group.get(0);
                    representativeVO.setProjectId(concatenatedProjectIds);
                    representativeVO.setContractAmount(totalContractAmount);
                    return representativeVO;
                })
                .collect(Collectors.toList());
        return R.data(resultList.stream().sorted(Comparator.comparingDouble(OutputListVO::getId).reversed()).collect(Collectors.toList()));
    }

    /**
     * 客户分析-客户产值
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerValue(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        if (!Func.isEmpty(customerKanBanDTO.getEndTime())) {
            customerKanBanDTO.setEndTime(customerKanBanDTO.getEndTime() + ":23:59:59");
        }
        return R.data(customerKanBanMapper.getCustomerValue(customerKanBanDTO));
    }

    /**
     * 客户分析-客户产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerValueGroup(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        return R.data(customerKanBanMapper.getCustomerValueGroup(customerKanBanDTO));
    }

    /**
     * 客户看板-重要程度分析
     *
     * @param customerKanBanDTO
     * @return
     */
    @Override
    public R getCustomerImportance(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        Map<String, Object> result = new HashMap<>();
//        饼图
        if (customerKanBanDTO.getAuthUserList().size() < 1 && null != customerKanBanDTO.getDeptId() && !"".equals(customerKanBanDTO.getDeptId())) {
            result.put("statistics", new ArrayList<>());
            result.put("queryList", new ArrayList<>());
            return R.data(result);
        }
        List<CustomerImportanctVO> statistics = customerKanBanMapper.getImportance(customerKanBanDTO);
        result.put("statistics", statistics);
//        客户总数
        Integer countNum = 0;
        if (statistics != null && statistics.size() > 0) {
            for (CustomerImportanctVO statistic : statistics) {
                countNum += statistic.getImportanceNum();
            }
        }
        result.put("countNum", countNum);
//        列表 默认展示拜访次数倒叙前五条

        List<CustomerImportanctVO> customerImportancePage = customerKanBanMapper.getImportancePage(customerKanBanDTO, 5);
        result.put("queryList", customerImportancePage.stream().sorted(Comparator.comparing(CustomerImportanctVO::getVisitCount).reversed()).collect(Collectors.toList()));
        return R.data(result);
    }

    @Override
    public List<CustomerImportanctVO> getCustomerImportancePage(CustomerKanBanDTO customerKanBanDTO) {
        // 赋予权限
        getUserLoginAuth(customerKanBanDTO);
        if(null != customerKanBanDTO.getSearchBox() && !"".equals(customerKanBanDTO.getSearchBox())){
            customerKanBanDTO.setCustomerList(Arrays.stream(customerKanBanDTO.getSearchBox().
                    split("\\s+")).flatMap(word -> Arrays.stream(word.split("(?<=[a-z])(?=[A-Z])")))
                    .collect(Collectors.toList()));
        }
        List<CustomerImportanctVO> importancePage = customerKanBanMapper.getImportancePage(customerKanBanDTO, null);
        return importancePage.stream().sorted(Comparator.comparing(CustomerImportanctVO::getVisitCount).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取当前登录人员可查看数据权限
     *
     * @param customerKanBanDTO
     */
    private CustomerKanBanDTO getUserLoginAuth(CustomerKanBanDTO customerKanBanDTO) {
        Long userId = AuthUtil.getUserId();
        // 获取高层权限ID列表
        List<String> specialList = deptByUserService.getSpecialList();
        List<Long> authUserList = new ArrayList<>();
//        List<Long> authProfessionList = new ArrayList<>();
//        List<AreaAndMarket> areaAndMarketList = new ArrayList<>();
        // 判断当前登录人员是否高层
        if (specialList.contains(userId.toString())) {
            customerKanBanDTO.setLeader(1);
            if (null != customerKanBanDTO.getDeptId() && !customerKanBanDTO.getDeptId().isEmpty()) {
                customerKanBanDTO.setLeader(2);
                // 查询当前人员可查看用户数据
                // 查询所有下级部门
                List<Dept> juniorDept = deptByUserService.getJuniorDept(Long.valueOf(customerKanBanDTO.getDeptId()));
                List<Long> collect = juniorDept.stream().map(Dept::getId).collect(Collectors.toList());
                collect.add(Long.valueOf(customerKanBanDTO.getDeptId()));
                // 根据部门查询下对应的人员ID
                List<Long> deptByProfessionByUserId = userMapper.getUidsByDeptIds(collect);
                authUserList.addAll(deptByProfessionByUserId);
                // 查询归属区划市场
//                List<Long> deptByProfessionId = customerKanBanMapper.getDeptByProfessionId(collect);
//                authProfessionList.addAll(deptByProfessionId);
                // 查询可查看区域行业
//                deptByProfessionId.stream().forEach(professionId -> {
//                    List<AreaAndMarket> areaAndMarket = customerKanBanMapper.getAreaAndMarket(professionId);
//                    areaAndMarketList.addAll(areaAndMarket);
//                });
//                customerKanBanDTO.setProfessionIdList(authProfessionList);
            }
            if (null != customerKanBanDTO.getUserId() && !"".equals(customerKanBanDTO.getUserId())) {
                customerKanBanDTO.setLeader(3);
                authUserList.add(Long.valueOf(customerKanBanDTO.getUserId()));
            }
            // 查询全部区域市场ID
//            if ((null == customerKanBanDTO.getUserId() || "".equals(customerKanBanDTO.getUserId())) && (null == customerKanBanDTO.getDeptId() || "".equals(customerKanBanDTO.getDeptId()))) {
//                QueryWrapper<Profession> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("is_deleted", 0);
//                List<Profession> professions = professionMapper.selectList(queryWrapper);
//                customerKanBanDTO.setProfessionIdList(professions.stream().map(Profession::getId).collect(Collectors.toList()));
//            }
            customerKanBanDTO.setAuthUserList(authUserList);
//            customerKanBanDTO.setAreaAndMarketList(areaAndMarketList);
        } else {
            // 查询当前人员非领导权限
            User user = userMapper.selectById(AuthUtil.getUserId());
            if (Func.equals(1,user.getLeader())) {
                customerKanBanDTO.setLeader(2);
                // 查询某个人员数据
                if (null != customerKanBanDTO.getUserId() && !"".equals(customerKanBanDTO.getUserId())) {
                    customerKanBanDTO.setLeader(3);
                    authUserList.add(Long.valueOf(customerKanBanDTO.getUserId()));
//                    List<AreaAndMarket> areaAndMarket = customerKanBanMapper.getAreaAndMarketByUser(customerKanBanDTO.getUserId());
//                    areaAndMarketList.addAll(areaAndMarket);
                } else {
                    // 查询当前人员可查看用户数据
                    Arrays.stream(AuthUtil.getDeptId().split(",")).forEach(dept -> {
                        // 查询所有下级部门
                        List<Dept> juniorDept = deptByUserService.getJuniorDept(Long.valueOf(dept));
                        List<Long> collect = juniorDept.stream().map(Dept::getId).collect(Collectors.toList());
                        collect.add(Long.valueOf(dept));
                        // 根据部门查询所属区域市场下对应的人员ID
                        List<Long> deptByProfessionByUserId = userMapper.getUidsByDeptIds(collect);
                        authUserList.addAll(deptByProfessionByUserId);
                        // 查询归属区划市场
//                        List<Long> deptByProfessionId = customerKanBanMapper.getDeptByProfessionId(collect);
//                        authProfessionList.addAll(deptByProfessionId);
                        // 查询可查看区域行业
//                        deptByProfessionId.stream().forEach(professionId -> {
//                            List<AreaAndMarket> areaAndMarket = customerKanBanMapper.getAreaAndMarket(professionId);
//                            areaAndMarketList.addAll(areaAndMarket);
//                        });
                    });
                }
                customerKanBanDTO.setAuthUserList(authUserList);
//                customerKanBanDTO.setProfessionIdList(authProfessionList);
//                customerKanBanDTO.setAreaAndMarketList(areaAndMarketList);
            } else {
                authUserList.add(user.getId());
                customerKanBanDTO.setLeader(3);
            }
        /*    if (null == customerKanBanDTO.getUserId() || "".equals(customerKanBanDTO.getUserId())) {
                authUserList.add(AuthUtil.getUserId());
            }*/
            customerKanBanDTO.setAuthUserList(authUserList);
        }
        return customerKanBanDTO;
    }
}
