/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同表变更记录实体类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@Data
@TableName("blade_contract_change")
@EqualsAndHashCode(callSuper = true)
public class ContractChange extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 标题
	*/
		private String name;
	/**
	* 描述原因
	*/
		private String description;
	/**
	* 操作类型id
	*/
		private String operationTypeId;
	/**
	* 操作类型名称
	*/
		private String operationTypeName;
	/**
	* 合同id
	*/
		private Long contractId;
	/**
	 * 合同名称
	 */
	private String contractName;
	/**
	* 签订公司（本公司所有组织）
	*/
		private Long partyBUnitId;
	/**
	* 签订公司编码
	*/
		private String partyBUnitCode;
	/**
	* 签订公司名称
	*/
		private String partyBUnitName;
	/**
	* 是否变更金额标志位（是1/否0）
	*/
		private String amountAdjustments;
	/**
	* 调整后金额
	*/
		private String adjustedAmount;
	/**
	 * 是否用章（1是/0否）
	 */
	private String isUseChapter;
	/**
	* 用章类型（用逗号隔开）
	*/
		private String useChapterTypeIds;
	/**
	* 用章类型名称（用逗号隔开）
	*/
		private String useChapterTypeVals;
	/**
	* 用印份数
	*/
		private Integer numOfCopies;
	/**
	* 附件地址
	*/
		private String annex;
	/**
	* 附件名称
	*/
		private String annexName;
	/**
	* 流程定义主键
	*/
		private String processDefinitionId;
	/**
	* 流程实例主键
	*/
		private String processInstanceId;
	/**
	* 当前审批人
	*/
		private String taskUser;


}
