/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChange;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeVO;
import org.springblade.modules.lankegroup.contractManagement.service.IContractChangeService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;
import java.util.Map;

/**
 * 合同表变更记录 控制器
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/change/contractchange")
@Api(value = "合同表变更记录", tags = "合同表变更记录接口")
public class ContractChangeController extends BladeController {

	private final IContractChangeService contractChangeService;
	private final FlowEngineService flowEngineService;
	private final IContractService contractService;

	/**
	 * 详情
	 * id:变更id
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入contractChange")
	public R<ContractChangeVO> detail(ContractChange contractChange) {
		ContractChange detail = contractChangeService.getOne(Condition.getQueryWrapper(contractChange));
		return R.data(contractChangeService.tidyUpContractChange(detail));
	}

	/**
	 * 分页 合同表变更记录
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入contractChange")
	public R<IPage<ContractChange>> list(ContractChange contractChange, Query query) {
		IPage<ContractChange> pages = contractChangeService.page(Condition.getPage(query), Condition.getQueryWrapper(contractChange));
		return R.data(pages);
	}

	/**
	 * 自定义分页 合同表变更记录
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入contractChange")
	public R<IPage<ContractChangeVO>> page(ContractChangeVO contractChange, Query query) {
		IPage<ContractChangeVO> pages = contractChangeService.selectContractChangePage(Condition.getPage(query), contractChange);
		return R.data(pages);
	}

	/**
	 * 新增 合同表变更记录
	 * 当点击存为草稿时，前端传递status=6来标识草稿，当草稿进行提交时修改status为0
	 * 存为草稿后会返回草稿id，当前页面再次进行草稿存储、提交时，都默认提交的是当前页面草稿
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入contractChange")
	public R save(@Valid @RequestBody ContractChangeVO contractChange) throws Exception {
		//		先判断是否要发起流程，即是否要存为草稿
		if (contractChange.getStatus() != null && contractChange.getStatus().intValue() == 6) {
			Map draftResult=contractChangeService.saveDraft(contractChange);
			if(draftResult.get("status").toString().equals("true")){
				return R.data(draftResult);
			}
			return R.fail(500,"草稿保存失败");
		} else {
			String processId = "";
			Contract contract = contractService.getById(contractChange.getContractId());
			// 根据合同类型判断使用的流程
			// TODO 刘源兴工作流
//			if(contract.getContractType().equals("0")){
//				processId = flowEngineService.getProcessId("销售合同", "salesContract");
//			}else if(contract.getContractType().equals("4")){
//				processId = flowEngineService.getProcessId("渠道合同", "channelContract");
//			}else {
//				processId = flowEngineService.getProcessId("合同管理", "contract");
//			}
			if("".equals(processId)){
				return R.fail(500, "流程不存在");
			}
			contractChange.setProcessDefinitionId(processId);
			Map map = contractChangeService.startProcess(contractChange);
			if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().equals(""))) {
				return R.data(map);
//				map = contractChangeService.approvalInPerson(map);
//				if (map.get("status").toString().equals("true")) {
//					return R.data(map);
//				} else {
//					return R.fail(500, "流程无法提交，请联系管理员");
//				}
			} else {
				return R.fail(500, "流程保存失败");
			}
		}
//		return R.status(contractChangeService.save(contractChange));
	}

	/**
	 * 修改 合同表变更记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入contractChange")
	public R update(@Valid @RequestBody ContractChange contractChange) {
		return R.status(contractChangeService.updateById(contractChange));
	}

	/**
	 * 新增或修改 合同表变更记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入contractChange")
	public R submit(@Valid @RequestBody ContractChange contractChange) {
		return R.status(contractChangeService.saveOrUpdate(contractChange));
	}


	/**
	 * 删除 合同表变更记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
//		0审批中，1已通过，2已撤回，3已驳回，6草稿
		ContractChange contractChange = contractChangeService.getById(ids);
		if(contractChange.getStatus()==3||contractChange.getStatus()==2){
			return R.status(contractChangeService.deleteLogic(Func.toLongList(ids)));
		}else {
			return R.fail(500, "只有驳回或撤回状态可删除");
		}
//		return R.status(contractChangeService.deleteLogic(Func.toLongList(ids)));
	}
	/**
	 * 初始化合同页面
	 *
	 * @param
	 * @return  返回当前登陆人最近一次保存的草稿，合同的审批流程节点，若为采购合同再加上当前登录人所涉及到的所有部门主管
	 */
	@GetMapping("/initContractChange")
	public R initContract() {
		return R.data(contractChangeService.initContract());
	}

	/**
	 * 根据合同id对合同相关的已审批结束的合同变更按列表返回
	 * @param contractId
	 * @return
	 */
	@GetMapping("/changeList")
	public R changeList(Long contractId){
		return R.data(contractChangeService.changeList(contractId));
	}

	/**
	 * 根据合同id，返回变更的审批流
	 * 变更审批流与合同流程相同
	 * 采购合同：发起人--》部门主管+2个抄送人
	 * 其他合同：发起人--》直接上级+3个抄送人
	 * 销售合同/渠道合同：直接上级--》安服项目：网盾技术部负责人；司法鉴定项目：司法鉴定部负责人；其它项目：研发部负责人和交付部负责人
	 * --》财务部负责人--》抄送人
	 * @param contractId
	 * @return
	 */
	@GetMapping("/initChangeProcess")
	public R initProcess(Long contractId){
		return contractChangeService.initChangeProcess(contractId);
	}

}
