/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractChangeLog;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractChangeLogVO;
import org.springblade.modules.lankegroup.contractManagement.service.IContractChangeLogService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 合同表变更记录日志 控制器
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/changelog/contractchangelog")
@Api(value = "合同表变更记录日志", tags = "合同表变更记录日志接口")
public class ContractChangeLogController extends BladeController {

	private final IContractChangeLogService contractChangeLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入contractChangeLog")
	public R<ContractChangeLog> detail(ContractChangeLog contractChangeLog) {
		ContractChangeLog detail = contractChangeLogService.getOne(Condition.getQueryWrapper(contractChangeLog));
		return R.data(detail);
	}

	/**
	 * 分页 合同表变更记录日志
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入contractChangeLog")
	public R<IPage<ContractChangeLog>> list(ContractChangeLog contractChangeLog, Query query) {
		IPage<ContractChangeLog> pages = contractChangeLogService.page(Condition.getPage(query), Condition.getQueryWrapper(contractChangeLog));
		return R.data(pages);
	}

	/**
	 * 自定义分页 合同表变更记录日志
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入contractChangeLog")
	public R<IPage<ContractChangeLogVO>> page(ContractChangeLogVO contractChangeLog, Query query) {
		IPage<ContractChangeLogVO> pages = contractChangeLogService.selectContractChangeLogPage(Condition.getPage(query), contractChangeLog);
		return R.data(pages);
	}

	/**
	 * 新增 合同表变更记录日志
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入contractChangeLog")
	public R save(@Valid @RequestBody ContractChangeLog contractChangeLog) {
		return R.status(contractChangeLogService.save(contractChangeLog));
	}

	/**
	 * 修改 合同表变更记录日志
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入contractChangeLog")
	public R update(@Valid @RequestBody ContractChangeLog contractChangeLog) {
		return R.status(contractChangeLogService.updateById(contractChangeLog));
	}

	/**
	 * 新增或修改 合同表变更记录日志
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入contractChangeLog")
	public R submit(@Valid @RequestBody ContractChangeLog contractChangeLog) {
		return R.status(contractChangeLogService.saveOrUpdate(contractChangeLog));
	}

	
	/**
	 * 删除 合同表变更记录日志
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(contractChangeLogService.deleteLogic(Func.toLongList(ids)));
	}

	
}
