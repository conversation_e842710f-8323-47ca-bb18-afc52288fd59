package org.springblade.common.enums;

/**
 * PREPAY,预付款,SFKYT09_SYS<br>
 * REGULAR,正常付款,SFKYT08_SYS<br>
 * */

public enum PayBillPaymentEnum {
    //采购付款单
    CGPREPAY("预付款","SFKYT09_SYS"),
    CGREGULAR("采购付款","SFKYT08_SYS"),
    //其他付款单
    QTPREPAY("预付款","SFKYT45_SYS"),
    QTREGULAR("其他支出","SFKYT12_SYS"),
    //保证金付款单
    BZJREGULAR("保证金支出","SFKYT43_SYS");
    // 成员变量
    private String name;
    private String index;

    PayBillPaymentEnum(String name, String index) {
        this.name = name;
        this.index = index;
    }
    // 普通方法
    public static String getName(String index) {
        for (PayBillPaymentEnum c : PayBillPaymentEnum.values()) {
            if (index.equalsIgnoreCase(c.getIndex())) {
                return c.name;
            }
        }
        return null;
    }
    // get set 方法
    public String getName() {
        return name;
    }
    public String getIndex() {
        return index;
    }
}
