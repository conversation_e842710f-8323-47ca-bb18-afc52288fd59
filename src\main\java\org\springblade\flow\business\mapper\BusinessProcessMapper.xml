<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.flow.business.mapper.BusinessProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BusinessProcessResultMap" type="org.springblade.flow.business.entity.BusinessProcessEntity">
        <result column="id" property="id"/>
        <result column="form_key" property="formKey"/>
        <result column="form_data_id" property="formDataId"/>
        <result column="form_data_code" property="formDataCode"/>
        <result column="business_category" property="businessCategory"/>
        <result column="business_field" property="businessField"/>
        <result column="history_assignee" property="historyAssignee"/>
        <result column="process_cc_delegate" property="processCcDelegate"/>
        <result column="category" property="category"/>
        <result column="subclass" property="subclass"/>
        <result column="approve_time" property="approveTime"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="selectBusinessProcessCCTaskPage" resultType="org.springblade.flow.business.vo.BusinessProcessVO">
        select bus.form_key,bus.form_data_id,bus.form_data_code,bus.create_user,bus.approve_time,bus.create_time,bus.status,
        bus.business_field as businessField,u1.real_name as createUserName,u1.avatar as createUserAvatar,
        bus.process_instance_id,bus.process_definition_id,bus.business_category as businessCategory
        from business_process bus
        left join blade_user as u1 on u1.id = bus.create_user
        where bus.is_deleted = 0
        <if test="param.category != null ">
            and bus.category = #{param.category}
        </if>
        <if test="param.subclass != null">
            and bus.subclass = #{param.subclass}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and bus.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and #{param.endTime} >= bus.create_time
        </if>
        <if test="param.approvedStartTime != null and param.approvedStartTime != ''">
            and bus.approve_time >= #{param.approvedStartTime}
        </if>
        <if test="param.approvedEndTime != null and param.approvedEndTime != ''">
            and #{param.approvedEndTime} >= bus.approve_time
        </if>

        <if test="param.flowTypeCode == 4">
            and bus.process_cc_delegate like concat('%',#{param.userId},'%')
        </if>
        <if test="param.auditStatus != null and param.auditStatus != ''">
            and bus.status in (${param.auditStatus})
        </if>
        <if test="param.searchName != null and param.searchName != ''">
            and (
            bus.form_data_code like cancat('%', #{param.searchName},'%')
            or
            u1.real_name like cancat('%', #{param.searchName},'%')
            )
        </if>
        group by bus.form_data_id,bus.process_instance_id
        order by bus.create_time desc
    </select>

    <select id="selectBusinessProcessTaskPage" resultType="org.springblade.flow.business.vo.BusinessProcessVO">
        select bus.form_key,bus.form_data_id,bus.form_data_code,bus.create_user,bus.approve_time,bus.create_time,bus.status,
        bus.business_field as businessField,u1.real_name as createUserName,u1.avatar as createUserAvatar,bus.business_category as businessCategory,
        bus.process_instance_id,task.ID_ as taskId,group_concat(IFNULL(task.ASSIGNEE_, il.USER_ID_)) as assigneeId
        from business_process bus
        LEFT JOIN ACT_RU_TASK task on bus.process_instance_id = task.PROC_INST_ID_
        LEFT JOIN ACT_RU_IDENTITYLINK il ON task.ID_ = il.TASK_ID_ AND il.TYPE_ = 'candidate'
        left join blade_user as u1 on u1.id = bus.create_user
        where bus.is_deleted = 0
        <if test="param.category != null ">
            and bus.category = #{param.category}
        </if>
        <if test="param.subclass != null">
            and bus.subclass = #{param.subclass}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and bus.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and #{param.endTime} >= bus.create_time
        </if>
        <if test="param.approvedStartTime != null and param.approvedStartTime != ''">
            and bus.approve_time >= #{param.approvedStartTime}
        </if>
        <if test="param.approvedEndTime != null and param.approvedEndTime != ''">
            and #{param.approvedEndTime} >= bus.approve_time
        </if>
        <if test="param.flowTypeCode == 1">
            and task.ASSIGNEE_ like concat('%',#{param.userId},'%')
            and bus.status in (0,1)
        </if>
        <if test="param.flowTypeCode == 2">
            and bus.history_assignee like concat('%',#{param.userId},'%')
            <if test="param.auditStatus != null and param.auditStatus != ''">
                and bus.status in (${param.auditStatus})
            </if>
        </if>
        <if test="param.flowTypeCode == 3">
            and bus.create_user = #{param.userId}
            <if test="param.auditStatus != null and param.auditStatus != ''">
                and bus.status in (${param.auditStatus})
            </if>
        </if>
        <if test="param.searchName != null and param.searchName != ''">
            and (
            bus.form_data_code like cancat('%', #{param.searchName},'%')
            or
            u1.real_name like cancat('%', #{param.searchName},'%')
            )
        </if>
        group by bus.form_data_id,bus.process_instance_id
        order by bus.create_time desc
    </select>

    <select id="selectBusinessProcessTransferPage" resultType="org.springblade.flow.business.vo.BusinessProcessVO">
        select bus.form_key,bus.form_data_id,bus.form_data_code,bus.create_user,bus.create_time,bus.status,
        bus.process_instance_id,transfer.task_id as taskId,bus.business_category as businessCategory
        from business_process_transfer transfer
        join business_process bus on bus.process_instance_id = transfer.process_instance_id
        where bus.is_deleted = 0 and transfer.is_deleted = 0
        <if test="param.category != null ">
            and bus.category = #{param.category}
        </if>
        <if test="param.subclass != null">
            and bus.subclass = #{param.subclass}
        </if>
        group by bus.form_data_id
        order by bus.create_time desc
    </select>

    <select id="getOneByProcessInstanceIdAndFormDataId" resultType="org.springblade.flow.business.entity.BusinessProcessEntity">
        select * from business_process
        where is_deleted = 0 and process_instance_id = #{processInstanceId} and form_data_id = #{formDataId}
    </select>
    <select id="getOneByFormKeyAndFormDataId" resultType="org.springblade.flow.business.entity.BusinessProcessEntity">
        select * from business_process
        where is_deleted = 0 and form_key = #{formKey} and form_data_id = #{formDataId}
    </select>
    <select id="getOneByFormKeyAndFormDataCode" resultType="org.springblade.flow.business.entity.BusinessProcessEntity">
        select * from business_process
        where is_deleted = 0 and form_key = #{formKey} and form_data_code = #{formDataCode}
    </select>

</mapper>
