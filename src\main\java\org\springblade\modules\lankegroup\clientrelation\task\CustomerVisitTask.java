package org.springblade.modules.lankegroup.clientrelation.task;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import org.springblade.modules.lankegroup.clientrelation.dto.CustomerKanBanDTO;
import org.springblade.modules.lankegroup.clientrelation.mapper.CustomerKanBanMapper;
import org.springblade.modules.lankegroup.customerVisit.entity.CustomerVisit;
import org.springblade.modules.lankegroup.customerVisit.entity.CustomerVisitContact;
import org.springblade.modules.lankegroup.customerVisit.mapper.CustomerVisitContactMapper;
import org.springblade.modules.lankegroup.customerVisit.mapper.CustomerVisitMapper;
import org.springblade.modules.lankegroup.customerVisit.vo.CustomerContactVO;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
//@RestController
//@RequestMapping("customerVisitTask")
@AllArgsConstructor
public class CustomerVisitTask {

    private final CustomerKanBanMapper customerKanBanMapper;

    private final CustomerVisitMapper customerVisitMapper;

    private final CustomerVisitContactMapper customerVisitContactMapper;

//    @Scheduled(cron = "0 */2 * * * ?")
//    @GetMapping
    public void customerVisitTask() {
        List<Map> newCallCustomerList = new ArrayList<>();
        List<String> strings = new ArrayList<>();
        List<Map> callCustomerCount = customerKanBanMapper.getCallCustomerCountByTask(new CustomerKanBanDTO());
        callCustomerCount.stream().forEach(customer -> {
            List<Map> maps = JSON.parseArray(customer.get("contact").toString(), Map.class);
            if (null != maps && maps.size() > 0) {
                maps.stream().forEach(m -> {
                    if (!strings.contains(m.get("customerId").toString())) {
                        Map map = new HashMap();
                        map.put("contact", m.get("customerId").toString());
                        map.put("userId", customer.get("userId"));
                        newCallCustomerList.add(map);
                        strings.add(m.get("customerId").toString());
                    }
                });
            }
        });
        customerKanBanMapper.deleteCustomerVisitData();
        customerKanBanMapper.saveCustomerVisitData(newCallCustomerList);
    }

//    @Scheduled(cron = "0 0 0 * * ?")
//    @PostMapping
    public void customerVisitByUserTask() {
        try {
            customerVisitMapper.deleteCustomerVisitContactSplit();
            List<CustomerVisit> customerVisits = customerVisitMapper.customerVisitContactSplit(null);
            if (null != customerVisits && customerVisits.size() > 0) {
                customerVisits.stream()
                        .forEach(customerVisit -> {
                            List<CustomerContactVO> customerContactVOList = JSON.parseArray(customerVisit.getCustomerContact(), CustomerContactVO.class);
                            if (null != customerContactVOList && customerContactVOList.size() > 0) {
                                customerContactVOList.stream().forEach(customerContactVO -> {
                                    if (null != customerContactVO && customerContactVO.getUseContactList().size() > 0) {
                                        customerContactVO.getUseContactList().stream().forEach(customerContactTwoVO -> {
                                            CustomerVisitContact customerVisitContact = new CustomerVisitContact();
                                            customerVisitContact.setCustomerId(customerContactVO.getCustomerId());
                                            customerVisitContact.setCustomerContactId(customerContactTwoVO.getId());
                                            customerVisitContact.setCustomerContactName(customerContactTwoVO.getContacts());
                                            customerVisitContact.setCustomerVisitId(customerVisit.getId());
                                            customerVisitContact.setCreateUser(customerVisit.getCreateUser());
                                            customerVisitContact.setCreateDept(customerVisit.getCreateDept());
                                            customerVisitContact.setCreateTime(customerVisit.getCreateTime());
                                            customerVisitContact.setStatus(1);
                                            customerVisitContactMapper.insert(customerVisitContact);
                                        });
                                    }
                                });
                            }
                        });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
