/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.crm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 报账技巧数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "ExpenseReportSkillDTO对象", description = "报账技巧数据传输对象")
@EqualsAndHashCode(callSuper = false)
public class ExpenseReportSkillDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id", required = true)
	@NotNull(message = "主键id不能为空")
	private Long id;

	/**
	 * 要求
	 */
	@ApiModelProperty(value = "要求")
	private String content;
	/**
	 * 客户id
	 */
	@ApiModelProperty(value = "客户id")
	private Long customerId;
	/**
	 * 联系人id1
	 */
	@ApiModelProperty(value = "联系人id1")
	private Long contactId;

} 