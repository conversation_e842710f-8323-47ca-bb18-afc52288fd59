package org.springblade.flow.engine.utils;

import cn.hutool.core.util.StrUtil;
import org.springblade.common.enums.DocumentType;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.constant.FlowFormConstant;
//import org.springblade.sale.common.cache.ContractCache;
//import org.springblade.sale.common.constant.TemplateConstant;
//import org.springblade.sale.common.enums.MessageTemplateEnum;
//import org.springblade.sale.common.enums.MessageTypeEnum;
//import org.springblade.sale.contract.entity.ContractManageEntity;
//import org.springblade.sale.event.OtherModuleMessagePushEvent;
//import org.springblade.sale.event.feign.IMessagePushEventClient;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.lankegroup.message.dto.SystemMessageDTO;
import org.springblade.modules.lankegroup.message.service.SystemMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29 10:45
 */
@Component
public class ProcessMessageUtils {

	@Autowired
	private SystemMessageService systemMessageService;

//	@Resource
//	private IMessagePushEventClient messagePushEventClient;

	public void sendMessage(String initiator, List<Long> approveUsers, String formDataId, String formKey, String formCode, String processName, String processInstanceId) {
		SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
		systemMessageDTO.setCrowdRelevance(Func.join(approveUsers));
		systemMessageDTO.setCrowdRelevanceName(UserCache.getUserNameList(approveUsers));
		systemMessageDTO.setTargetId(formDataId);
		systemMessageDTO.setPinnedStatus(1);
		systemMessageDTO.setStatus(0);
		systemMessageDTO.setUserList(Func.ofImmutableList(initiator));
		// 企微
		systemMessageDTO.getWxcpmsgparams().put("type", "detail");
		systemMessageDTO.getWxcpmsgparams().put("id", formDataId);
		systemMessageDTO.getWxcpmsgparams().put("processInstanceId", processInstanceId);
		switch (formKey) {
			case FlowFormConstant.FLOW_CUSTOMER_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个机构审批需要审批，单号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的机构审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.客户管理.getType());
				systemMessageDTO.setSkipUrl(DocumentType.客户管理.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_CONTRACT_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个销售合同需要审批，合同编号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的销售合同审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.销售合同.getType());
				systemMessageDTO.setSkipUrl(DocumentType.销售合同.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_TEST_BUSINESS_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个测试业务需要审批，单号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的测试业务审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.测试业务.getType());
				systemMessageDTO.setSkipUrl(DocumentType.测试业务.getUrl());
				break;
		}
		systemMessageService.sendSystemMessage(systemMessageDTO);
	}


	public void sendMessageToInitiatorByReject(String initiator, String formDataId, String formKey, String processName, String formCode, String status, String comment, BladeUser bladeUser, String processInstanceId) {
		SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
		systemMessageDTO.setCrowdRelevance(initiator);
		systemMessageDTO.setCrowdRelevanceName(UserCache.getUserName(Long.valueOf(initiator)));
		systemMessageDTO.setTargetId(formDataId);
		systemMessageDTO.setPinnedStatus(1);
		systemMessageDTO.setStatus(0);
		// 企微
		systemMessageDTO.setUserList(Func.ofImmutableList(initiator));
		systemMessageDTO.getWxcpmsgparams().put("type", "detail");
		systemMessageDTO.getWxcpmsgparams().put("processInstanceId", processInstanceId);
		systemMessageDTO.getWxcpmsgparams().put("id", formDataId);
		switch (formKey) {
			case FlowFormConstant.FLOW_CUSTOMER_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的机构审批，{}已驳回，单号为{}，驳回理由：{}", bladeUser.getUserName(), formCode, Func.toStr(comment)));
				systemMessageDTO.setMsgTitle("我的机构审批");
				systemMessageDTO.setTargetType(DocumentType.客户管理.getType());
				systemMessageDTO.setSkipUrl(DocumentType.客户管理.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_CONTRACT_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的销售合同审批，{}已驳回，合同编号为{}，驳回理由：{}", bladeUser.getUserName(), formCode, Func.toStr(comment)));
				systemMessageDTO.setMsgTitle("我的销售合同审批");
				systemMessageDTO.setTargetType(DocumentType.销售合同.getType());
				systemMessageDTO.setSkipUrl(DocumentType.销售合同.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_TEST_BUSINESS_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的测试业务审批，{}已驳回，单号为{}，驳回理由：{}", bladeUser.getUserName(), formCode, Func.toStr(comment)));
				systemMessageDTO.setMsgTitle("我的测试业务审批");
				systemMessageDTO.setTargetType(DocumentType.测试业务.getType());
				systemMessageDTO.setSkipUrl(DocumentType.测试业务.getUrl());
				break;
		}
		systemMessageService.sendSystemMessage(systemMessageDTO);

	}

	/**
	 * 转交V2消息提醒
	 *
	 * @param initiator    表单创建人ID
	 * @param targetUserId 转交人ID
	 * @param formDataId   表单ID
	 * @param formKey      表单业务标识
	 * @param formCode     表单编码
	 */
	public void sendMessageToInitiatorByTransferTaskV2(String initiator, String targetUserId, String formDataId, String formKey, String formCode, String processInstanceId) {
		SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
		systemMessageDTO.setCrowdRelevance(targetUserId);
		systemMessageDTO.setCrowdRelevanceName(UserCache.getUserName(Long.valueOf(targetUserId)));
		systemMessageDTO.setTargetId(formDataId);
		systemMessageDTO.setPinnedStatus(1);
		systemMessageDTO.setStatus(0);
		// 企微
		systemMessageDTO.setUserList(Func.ofImmutableList(targetUserId));
		systemMessageDTO.getWxcpmsgparams().put("type", "detail");
		systemMessageDTO.getWxcpmsgparams().put("processInstanceId", processInstanceId);
		systemMessageDTO.getWxcpmsgparams().put("id", formDataId);
		switch (formKey) {
			case FlowFormConstant.FLOW_CUSTOMER_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个机构审批需要审批，单号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的机构审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.客户管理.getType());
				systemMessageDTO.setSkipUrl(DocumentType.客户管理.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_CONTRACT_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个销售合同需要审批，合同编号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的销售合同审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.销售合同.getType());
				systemMessageDTO.setSkipUrl(DocumentType.销售合同.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_TEST_BUSINESS_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个测试业务需要审批，单号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("{}的测试业务审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.测试业务.getType());
				systemMessageDTO.setSkipUrl(DocumentType.测试业务.getUrl());
				break;
			default:
				systemMessageDTO.setMsgContent(StrUtil.format("您有一个业务需要审批，单号为{}", formCode));
				systemMessageDTO.setMsgTitle(StrUtil.format("您有一个业务需要审批", UserCache.getUserName(Long.valueOf(initiator))));
				systemMessageDTO.setTargetType(DocumentType.客户管理.getType());
				systemMessageDTO.setSkipUrl("/KingdeeTodo?number=");
				break;
		}
		systemMessageService.sendSystemMessage(systemMessageDTO);

	}


	public void sendMessageToInitiatorByFinish(String initiator, String formDataId, String formKey, String processName, String formCode, String status, String processInstanceId) {
		SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
		systemMessageDTO.setCrowdRelevance(initiator);
		systemMessageDTO.setCrowdRelevanceName(UserCache.getUserName(Long.valueOf(initiator)));
		systemMessageDTO.setTargetId(formDataId);
		systemMessageDTO.setPinnedStatus(1);
		systemMessageDTO.setStatus(0);
		// 企微
		systemMessageDTO.setUserList(Func.ofImmutableList(initiator));
		systemMessageDTO.getWxcpmsgparams().put("type", "detail");
		systemMessageDTO.getWxcpmsgparams().put("processInstanceId", processInstanceId);
		systemMessageDTO.getWxcpmsgparams().put("id", formDataId);
		switch (formKey) {
			case FlowFormConstant.FLOW_CUSTOMER_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的机构审批，已通过，单号为{}", formCode));
				systemMessageDTO.setMsgTitle("我的机构审批");
				systemMessageDTO.setTargetType(DocumentType.客户管理.getType());
				systemMessageDTO.setSkipUrl(DocumentType.客户管理.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_CONTRACT_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的销售合同审批，已通过，合同编号为{}", formCode));
				systemMessageDTO.setMsgTitle("我的销售合同审批");
				systemMessageDTO.setTargetType(DocumentType.销售合同.getType());
				systemMessageDTO.setSkipUrl(DocumentType.销售合同.getUrl() + StrUtil.format("?id={}&processInstanceId={}", formDataId, processInstanceId));
				break;
			case FlowFormConstant.FLOW_TEST_BUSINESS_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的测试业务审批，已通过，单号为{}", formCode));
				systemMessageDTO.setMsgTitle("我的测试业务审批");
				systemMessageDTO.setTargetType(DocumentType.测试业务.getType());
				systemMessageDTO.setSkipUrl(DocumentType.测试业务.getUrl());
				break;
			case FlowFormConstant.JL_QUOTATION_APPROVAL_KEY:
				systemMessageDTO.setMsgContent(StrUtil.format("我的报价审批，已通过，单号为{}", formCode));
				systemMessageDTO.setMsgTitle("我的报价审批");
				systemMessageDTO.setTargetType(DocumentType.报价审批.getType());
				systemMessageDTO.setSkipUrl(DocumentType.报价审批.getUrl());
				break;
		}
		systemMessageService.sendSystemMessage(systemMessageDTO);

	}
}
