/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.auth.entity.RoleDept;
import org.springblade.modules.auth.vo.RoleDeptVO;
import org.springblade.modules.auth.wrapper.RoleDeptWrapper;
import org.springblade.modules.auth.service.IRoleDeptService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 角色与部门的对照表 控制器
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/roleDept/roledept")
@Api(value = "角色与部门的对照表", tags = "角色与部门的对照表接口")
public class RoleDeptController extends BladeController {

	private final IRoleDeptService roleDeptService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入roleDept")
	public R<RoleDeptVO> detail(RoleDept roleDept) {
		RoleDept detail = roleDeptService.getOne(Condition.getQueryWrapper(roleDept));
		return R.data(RoleDeptWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 角色与部门的对照表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入roleDept")
	public R<IPage<RoleDeptVO>> list(RoleDept roleDept, Query query) {
		IPage<RoleDept> pages = roleDeptService.page(Condition.getPage(query), Condition.getQueryWrapper(roleDept));
		return R.data(RoleDeptWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 角色与部门的对照表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入roleDept")
	public R<IPage<RoleDeptVO>> page(RoleDeptVO roleDept, Query query) {
		IPage<RoleDeptVO> pages = roleDeptService.selectRoleDeptPage(Condition.getPage(query), roleDept);
		return R.data(pages);
	}

	/**
	 * 新增 角色与部门的对照表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入roleDept")
	public R save(@Valid @RequestBody RoleDept roleDept) {
		return R.status(roleDeptService.save(roleDept));
	}

	/**
	 * 修改 角色与部门的对照表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入roleDept")
	public R update(@Valid @RequestBody RoleDept roleDept) {
		return R.status(roleDeptService.updateById(roleDept));
	}

	/**
	 * 新增或修改 角色与部门的对照表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入roleDept")
	public R submit(@Valid @RequestBody RoleDept roleDept) {
		return R.status(roleDeptService.saveOrUpdate(roleDept));
	}

	
	/**
	 * 删除 角色与部门的对照表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(roleDeptService.deleteLogic(Func.toLongList(ids)));
	}

	
}
