package org.springblade.common.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/28 11:16
 */
public interface FormMapper {
    /**
     * 更新表单数据字段
     *
     * @param tableName
     * @param columnKey
     * @param columnValue
     * @return
     */
    @Update("update ${tableName} set ${columnKey} = #{columnValue} where id = #{id}")
    Boolean updateFormDate(String tableName, String columnKey, String columnValue, String id);
    /**
     *  mongodb上的付款单+借款+油卡+采购申请
     * 更新表单数据字段
     *
     * @param tableName
     * @param columnKey
     * @param columnValue
     * @return
     */
    @Update("update ${tableName} set ${columnKey} = #{columnValue} where bill_no = #{id}")
    Boolean updateMongoFormDate(String tableName, String columnKey, String columnValue, String id);
    @Update("update ${tableName} set status = #{status},task_user=#{taskUser} where id = #{id}")
    Boolean updateFormStutusAndTaskUser(String tableName, String status, String taskUser, String id);

    /**
     * 更新单据状态
     * @param tableName
     * @param status
     * @param id
     * @return
     */
    @Update("update ${tableName} set status = #{status} where id = #{id}")
    Boolean updateFormStatus(String tableName, String status,String id);

    /**
     * 修改关联关系表中关联关系可以状态
     * @param tableName
     * @param enable
     * @param fieldName
     * @param id
     */
    @Update("update ${tableName} set enable = #{enable} where ${fieldName} = #{id}")
    void updateFormRelStatus(String tableName, Integer enable,String fieldName, String id);

    /**
     * 查询流程实例ID
     * @param tableName
     * @param id
     * @return
     */
    @Select("select process_instance_id processInstanceId,status from ${tableName} where id = #{id} limit 1")
    Map selectFromData(String tableName, String id);
    /**
     * mongodb上的付款单+借款+油卡+采购申请
     * 查询流程实例ID
     * @param tableName
     * @param id
     * @return
     */
    @Select("select process_instance_id processInstanceId,status from ${tableName} where bill_no = #{id} limit 1")
    Map selectMongoFromData(String tableName, String id);
    /**
     * 根据流程实力ID修改单据数据
     *
     * @param formName
     * @return
     */
    @Update("update ${formName} set bank_type = #{bankType},pay_bank_account = #{bankAccount},pay_bank_account_name = #{bankAccountName} where id = #{id} and is_deleted = 0")
    Boolean updateFromDataByReceipt(String formName, String id, String bankType, String bankAccount, String bankAccountName);

    /**
     * 获取页面跳转地址
     * @return
     */
    @Select("select skip_url from blade_system_message where id = #{query.id} and target_type = #{query.targetType} and is_deleted = 0")
    String getPageSkipUrl(@Param("query") Map map);
}
