/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.feign;

import lombok.AllArgsConstructor;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.entity.FlowModel;
import org.springblade.flow.core.enums.FlowCommentEnum;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.engine.utils.FlowApproverVariablesUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;

/**
 * 流程远程调用实现类
 *
 * <AUTHOR>
 */
@NonDS
@Service
@AllArgsConstructor
public class FlowClient implements IFlowClient {

//	private final RuntimeService runtimeService;
//	private final IdentityService identityService;
//	private final TaskService taskService;
//	private final FlowBusinessService flowBusinessService;
//
//	@Override
//	public R<BladeFlow> startProcessInstanceById(String processDefinitionId, String businessKey, Map<String, Object> variables) {
//		//设置审批人相关流程变量
//		FlowApproverVariablesUtils.initFlowApproverVariables(processDefinitionId, variables);
//		// 开启流程
//		variables.put(ProcessConstant.PASS_KEY, true);
//		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, businessKey, variables);
//		// 第一个用户任务为发起人，则自动完成任务
//		startFirstTask(processInstance, variables);
//		// 组装流程通用类
//		BladeFlow flow = new BladeFlow();
//		flow.setProcessInstanceId(processInstance.getId());
//		flow.setProcessDefinitionId(processInstance.getProcessDefinitionId());
//		return R.data(flow);
//	}
//
//	@Override
//	public R<BladeFlow> startProcessInstanceByKey(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
//		// 设置流程启动用户
//		identityService.setAuthenticatedUserId(String.valueOf(AuthUtil.getUserId()));
//		// 设置流程发起人Id到流程中
//		variables.put(BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR, String.valueOf(AuthUtil.getUserId()));
//		// 开启流程
//		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
//		// 第一个用户任务为发起人，则自动完成任务
////		startFirstTask(processInstance, variables);
//		// 组装流程通用类
//		BladeFlow flow = new BladeFlow();
//		flow.setProcessInstanceId(processInstance.getId());
//		return R.data(flow);
//	}
//
//	@Override
//	public R completeTask(String taskId, String processInstanceId, String comment, Map<String, Object> variables) {
//		// 增加评论
//		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
//			taskService.addComment(taskId, processInstanceId, comment);
//		}
//		// 非空判断
//		if (Func.isEmpty(variables)) {
//			variables = Kv.create();
//		}
//		// 完成任务
//		taskService.complete(taskId, variables);
//		return R.success("流程提交成功");
//	}
//
//	@Override
//	public R<Object> taskVariable(String taskId, String variableName) {
//		return R.data(taskService.getVariable(taskId, variableName));
//	}
//
//	@Override
//	public R<Map<String, Object>> taskVariables(String taskId) {
//		return R.data(taskService.getVariables(taskId));
//	}
//
//	public void startFirstTask(ProcessInstance processInstance, Map<String, Object> variables) {
//		// 若第一个用户任务为发起人，则自动完成任务
//		Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
//		if (Func.isNotEmpty(task)) {
//			String initiatorUser = (String) variables.get((BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR));
//			if (StringUtil.equals(task.getAssignee(), initiatorUser)) {
////				variables.put(ProcessConstant.PASS_KEY, ProcessConstant.PASS_ALIAS);
////				taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), AuthUtil.getUserName() + "发起流程申请");
//				variables.put("reviewRound", 1);
//				taskService.complete(task.getId(), variables);
//			}
//		}
//	}
//
//	@Override
//	public R<FlowModel> selectFlowModelKey(String formKey) {
//		return flowBusinessService.selectFlowModelKey(formKey);
//	}
//
//	@Override
//	public R initiatorTask(String processInstanceId) {
//		Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
//		if (Func.isNotEmpty(task)) {
//			taskService.addComment(task.getId(), processInstanceId, FlowCommentEnum.APPROVE.getType(), AuthUtil.getUserName() + "重新发起流程申请");
//			Integer currentRound = (Integer) taskService.getVariable(task.getId(), "reviewRound");
//			if (currentRound == null) {
//				currentRound = 0;
//			}
//			taskService.complete(task.getId(), Collections.singletonMap("reviewRound", currentRound + 1));
//			return R.status(true);
//		}
//		return R.status(false);
//	}
}
