package org.springblade.modules.lankegroup.contractManagement.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.engine.service.FlowEngineService;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IChannelContractService;
import org.springblade.modules.lankegroup.contractManagement.service.IContractService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.lankegroup.pro_management.entity.ProjectBasic;
import org.springblade.modules.lankegroup.pro_management.mapper.ProjectBasicMapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 渠道合同管理表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/contract/channelCon")
@Api(value = "渠道合同管理表", tags = "渠道合同管理表接口")
public class ChannelContractController extends BladeController {
    private final IChannelContractService channelContractService;
    private final IContractService contractService;
    private final FlowEngineService flowEngineService;
    private final ProjectBasicMapper projectBasicMapper;
    private final ContractMapper contractMapper;

    /**
     * 详情
     * 对查出的数据进行数据整理后，返回
     * id:合同id
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contract")
    public R<ContractVO> detail(Contract contract) {
        Contract detail = channelContractService.getOne(Condition.getQueryWrapper(contract));
        if(null != detail){
            return R.data(channelContractService.tidyUpContract(detail));
        }else {
            return R.fail("合同已删除或撤回");
        }
    }

    /**
     * 新增 合同管理表
     * 新增合同表单时，需要注意，合同类型contractType（0销售合同/1采购合同/2其他合同/4渠道合同）必须传递
     * 当为草稿时，合同的状态status需要定值6传递，存储完成后会返回草稿id，意为当前页再次提交或者存为草稿时将id传入避免数据重复
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入contract")
    public R save(@Valid @RequestBody ContractVO contract) {
        // 先判断是否要发起流程，即是否要存为草稿
        if (contract.getStatus() != null && contract.getStatus().intValue() == 6) {
            Map map = channelContractService.saveDraft(contract);
            if(map.get("status").toString().equals("true")){
                return R.data(map);
            }else {
                return R.fail(500,"草稿保存失败");
            }

        } else {
            // 先判断合同名称是否重复
            //20240604需求:销售合同/采购合同/其他合同/渠道合同，合同名称唯一校验去掉
//            if(StringUtil.isNotBlank(contract.getName())){
//                List<ContractVO> contractVOS =contractService.checkContractNameSole(contract.getName(),contract.getId());
//                if(contractVOS!=null&&contractVOS.size()>0){
//                    return R.fail(500,"合同名称已存在，请核对");
//                }
//            }
//            ProjectBasic projectBasic = projectBasicMapper.selectById(contract.getProjectId());
//            if (!(projectBasic.getContractingUnitId().longValue() == contract.getSignCompanyId().longValue()) &&
//                    (!String.valueOf(projectBasic.getId()).equals("109120") &&                 //109120            集团公共
//                     !String.valueOf(projectBasic.getId()).equals("104859") &&                 //104859            等保项目
//                     !String.valueOf(projectBasic.getId()).equals("1630375748500062210") &&      //1630375748500062210   2023年廊坊IDZ-1000000-田帅
//                     !String.valueOf(projectBasic.getId()).equals("1745001191491928066")        //1745001191491928066   2024年廊坊居住证-500000-田帅
//                    ) ){
//                return R.fail("甲方单位与项目客户不一致，请重新选择项目");
//            }
            if(contract.getSignCompanyManId()!=null&&!contract.getSignCompanyManId().isEmpty()){
                String selectByContactId = contractMapper.selectByContactId(contract.getSignCompanyManId());
                if(selectByContactId==null||selectByContactId.equals("")){
                    return R.fail("甲方联系人已删除或修改，请重新选择甲方联系人");
                }
                if (!(Long.valueOf(selectByContactId).longValue() == contract.getSignCompanyId().longValue())){
                    return R.fail("甲方联系人不属于此甲方单位，请重新选择甲方联系人");
                }
            }
            // 保存时的时间为提交时间
            contract.setCreateTime(new Date());
            // TODO 刘源兴工作流
//            String processId = flowEngineService.getProcessId("渠道合同", "channelContract");
//
//            if("".equals(processId)){
//                return R.fail(500, "流程不存在");
//            }
//            contract.setProcessDefinitionId(processId);
//            Map map = channelContractService.startProcess(contract);
//            if (map.get("processInstanceId").toString() != null && (!map.get("processInstanceId").toString().equals(""))) {
//                return R.data(map);
////                map = channelContractService.approvalInPerson(map);
////                if (map.get("status").toString().equals("true")) {
////                    return R.data(map);
////                } else {
////                    return R.fail(500, "流程无法提交，请联系管理员");
////                }
//            } else {
//                return R.fail(500, "流程保存失败");
//            }
            return R.fail(500, "流程保存失败");
        }
    }

    /**
     * 初始化合同页面
     *
     * @param
     * @return  返回当前登陆人最近一次保存的草稿，合同的审批流程节点
     */
    @GetMapping("/initContract")
    public R initContract() {
        return channelContractService.initContract();
    }

}
