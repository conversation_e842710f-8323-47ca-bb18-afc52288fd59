/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.word;

import lombok.Data;
import org.springblade.modules.lankegroup.contractManagement.annotation.FieldToWord;

import java.io.Serializable;

/**
 * 试剂合同子表-实验材料实体类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class ContractReagentQuotationMaterialWord implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 序号
     */
    @FieldToWord
    private String serialNumber;
    /**
     * 材料名称
     */
    @FieldToWord
    private String materialName;
    /**
     * 材料规格
     */
    @FieldToWord
    private String materialSpec;
    /**
     * 数量
     */
    @FieldToWord
    private String quantityStr;
    /**
     * 最终金额
     */
    @FieldToWord
    private String finalAmountStr;
    /**
     * 货号
     */
    @FieldToWord
    private String itemNumber;


}
