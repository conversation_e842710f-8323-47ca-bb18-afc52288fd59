package org.springblade.modules.lankegroup.clientrelation.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.lankegroup.clientrelation.dto.CustomerKanBanDTO;
import org.springblade.modules.lankegroup.clientrelation.entity.AreaAndMarket;
import org.springblade.modules.lankegroup.clientrelation.entity.Personnel;
import org.springblade.modules.lankegroup.clientrelation.vo.*;
import org.springblade.modules.lankegroup.crm.entity.Customer;
import org.springblade.modules.lankegroup.crm.vo.CustomerVO;

import java.util.List;
import java.util.Map;

public interface CustomerKanBanMapper {

    /**
     * 查询客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<Long> getCustomerCount(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询拜访客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<String> getCallCustomerCount(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询拜访客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<Map> getCallCustomerCountByTask(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询拜访客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<String> getCallCustomerCountByData(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询立项客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<Long> getProjectApprovalCustomerCount(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询基本盘客户数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<Long> getBaseDiscCustomerCount(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 根据区域市场ID查询所属区划信息
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getAreaAndMarket(Long id);

    /**
     * 查询某个人负责的区划行业
     *
     * @param id
     * @return
     */
    List<AreaAndMarket> getAreaAndMarketByUser(String id);

    /**
     * 客户看板-客户行业分析-饼图
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerProfessionPieChartVO> getCustomerProfessionPieChart(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户行业分析-饼图-总数
     *
     * @param customerKanBanDTO
     * @return
     */
    Integer getCustomerProfessionPieChartCount(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 获取各行业应有客户数
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询领导层应有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionOneY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询领导层实有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionOneS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询组长层应有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionTwoY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询组长层实有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionTwoS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询员工层应有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionThreeY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询员工层实有客户
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionThreeS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 获取各行业实有客户数
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanResult> getCustomerProfessionS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-重要程度分析-客户列表
     */
    List<CustomerImportanctVO> getImportancePage(@Param("query") CustomerKanBanDTO customerKanBanDTO, Integer size);

    /**
     * 客户看板-重要程度分析-首页统计
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerImportanctVO> getImportance(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-行业产值
     *
     * @param customerKanBanDTO
     * @return
     */
    List<OutputListVO> getCustomerProfessionValue(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-行业产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    List<OutputListVO> getCustomerProfessionValueGroup(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户分析-客户产值
     *
     * @param customerKanBanDTO
     * @return
     */
    List<OutputListVO> getCustomerValue(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户分析-客户产值-通过项目类型分组
     *
     * @param customerKanBanDTO
     * @return
     */
    List<OutputListVO> getCustomerValueGroup(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-应有
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getCustomerRegionY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-个人应有
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getCustomerRegionSingleY(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-实有
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getCustomerRegionS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 客户看板-客户区域分析-个人实有
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getCustomerRegionSingleS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 根据区域市场ID查询成员ID
     *
     * @param professionId
     * @return
     */
    List<Long> getUserIdByProfessionId(Long professionId);

    /**
     * 根据成员ID查询区划及行业
     *
     * @param userIdByProfessionId
     * @return
     */
    List<CustomerKanBanPersonnelDetailResult> getPersonnelByAreaAndMarketList(List<Long> userIdByProfessionId, List<String> marketplaceIdList, List<String> areaIdList);

    /**
     * 根据区划行业用户ID查询客户信息
     *
     * @param marketCode
     * @param areaCode
     * @param userId
     * @return
     */
    CustomerKanBanPersonnelDetailResult getCustomerByAreaAndMarket(String marketCode, String areaCode, String userId);

    /**
     * 根据部门查询所属区域市场下对应的人员ID
     */
    List<Long> getDeptByProfessionByUserId(List<Long> deptId);


    /**
     * 根据部门查询所属区域市场下对应的人员ID
     */
    List<Long> getDeptByProfessionId(List<Long> deptId);

    /**
     * 查询某区域市场下人员ID
     */
    List<Long> getProfessionIdByUserId(String professionId);

    /**
     * 根据用户ID按照行业分组统计数量
     *
     * @param userId
     * @return
     */
    List<CustomerProfessionPieChartVO> getMarketByUserId(List<Long> userId);

    /**
     * 根据用户ID查询客户总数
     *
     * @param userId
     * @return
     */
    Double getMarketByUserIdCount(List<Long> userId);

    /**
     * 根据用户ID和用户ID按照人员名称分组统计数量
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerProfessionPieChartVO> getMarketByUserIdAndProfessionId(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 根据用户ID和用户ID按照人员名称统计客户ID
     *
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanDetailResult> getMarketByUserIdAndProfessionIdByCustomerId(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 根据用户ID和用户ID计算总数
     *
     * @param customerKanBanDTO
     * @return
     */
    Double getMarketByUserIdAndProfessionIdCount(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询全部符合条件的客户信息
     *
     * @return
     */
    Double getCustomerCountByAllUser();

    /**
     * 根据用户ID和用户ID按照区域市场分组统计数量
     *
     * @param userId
     * @return
     */
    List<CustomerProfessionPieChartVO> getMarketByProfessionId(List<AreaAndMarket> marketList, List<Long> userId, Long professionId);

    /**
     * 查询领导客户列表
     *
     * @param marketplaceIdList
     * @param areaIdList
     * @param isData
     * @return
     */
    List<CustomerKanBanDetailResult> getAllCustomerList(List<String> marketplaceIdList, List<String> areaIdList, Integer isData,List<Long> authUserList);

    /**
     * 查询区域市场客户列表
     *
     * @param marketplaceIdList
     * @param areaIdList
     * @param isData
     * @return
     */
    List<CustomerKanBanDetailResult> getGroupCustomerList(List<Long> userId, List<String> marketplaceIdList, List<String> areaIdList, Integer isData,List<Long> professionIdList,List<Long> authUserList);

    /**
     * 查询个人客户列表
     *
     * @param marketplaceIdList
     * @param areaIdList
     * @param isData
     * @return
     */
    List<CustomerKanBanDetailResult> getPersonnelCustomerList(String userId, List<String> marketplaceIdList, List<String> areaIdList, Integer isData);

    /**
     * 查询全部应有
     * @param customerKanBanDTO
     */
    List<CustomerKanBanProfessionResult> getAllAreaCustomerListY(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询区域应有
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getGroupAreaCustomerListY(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询区域人员应有
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getGroupAreaCustomerPersonnelListY(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询区域个人应有
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getGroupAreaCustomerPersonnelSingleListY(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询区域实有
     * @param customerKanBanDTO
     * @return
     */
    Integer getGroupAreaCustomerListS(@Param("query") CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询个人应有
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanProfessionResult> getPersonnelAreaCustomerListY(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 查询个人实有
     * @param userId
     * @return
     */
    Integer getPersonnelAreaCustomerListS(String userId);

    /**
     * 查询某个人行业信息
     * @param customerKanBanDTO
     * @return
     */
    List<CustomerKanBanDetailResult> getPersonnelByMarketList(CustomerKanBanDTO customerKanBanDTO);

    /**
     * 根据客户ID查询客户信息
     * @param list
     * @return
     */
    List<CustomerKanBanDetailResult> getCustomerListById(List<String> list);

    /**
     * 保存客户拜访记录
     */
    void saveCustomerVisitData(List<Map> list);

    /**
     * 删除客户拜访记录
     */
    void deleteCustomerVisitData();

    List<CustomerKanBanProfessionResult> getProfessionListGroup();

    List<CustomerProfessionPieChartVO> getProfessionChartGroup();

    void saveProfessionListGroup(@Param("param") CustomerKanBanProfessionResult customerKanBanProfessionResult);

    void saveProfessionChartGroup(@Param("param") CustomerProfessionPieChartVO customerProfessionPieChartVO);

    void deleteProfessionListGroup();

    void deleteProfessionChartGroup();

    /**
     * 查询其他客户列表
     * @param professionId
     * @param userList
     * @return
     */
    CustomerKanBanProfessionResult getQiTaCustomerList(List<Long> professionId,List<Long> userList);

    /**
     * 查询其他客户列表
     * @param professionId
     * @param userList
     * @return
     */
    CustomerKanBanProfessionResult getQiTaCustomerListS(String professionId,List<Long> userList);

    List<Long> getProfessionByUserId(String professionId);
}
