/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.RoleIdConstant;
import org.springblade.common.constant.TenantConstant;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.TokenInfo;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.lankegroup.dept.service.DeptByUserService;
import org.springblade.modules.system.entity.*;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 认证工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TokenUtil {

    public final static String DEPT_HEADER_KEY = "Dept-Id";
    public final static String ROLE_HEADER_KEY = "Role-Id";
    public final static String CAPTCHA_HEADER_KEY = "Captcha-Key";
    public final static String CAPTCHA_HEADER_CODE = "Captcha-Code";
    public final static String CAPTCHA_NOT_CORRECT = "验证码不正确";
    public final static String TENANT_HEADER_KEY = "Tenant-Id";
    public final static String DEFAULT_TENANT_ID = "000000";
    public final static String USER_TYPE_HEADER_KEY = "User-Type";
    public final static String DEFAULT_USER_TYPE = "web";
    public final static String TOKEN_NOT_PERMISSION = "令牌授权已过期";
    public final static String USER_NOT_FOUND = "用户名或密码错误";
    public final static String USER_HAS_NO_ROLE = "未获得用户的角色信息";
    public final static String USER_HAS_NO_TENANT = "未获得用户的租户信息";
    public final static String USER_HAS_NO_TENANT_PERMISSION = "租户授权已过期,请联系管理员";
    public final static String USER_HAS_TOO_MANY_FAILS = "登录错误次数过多,请稍后再试";
    public final static String HEADER_KEY = "Authorization";
    public final static String HEADER_PREFIX = "Basic ";
    public final static String DEFAULT_AVATAR = "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png";
    private static final IUserService userService;
    private static final IDeptService deptService;
    private static final DeptByUserService getDeptByUserService;
    private static final BladeRedis bladeRedis;
    private static BladeTenantProperties tenantProperties;

    static {
        userService = SpringUtil.getBean(IUserService.class);
        deptService = SpringUtil.getBean(IDeptService.class);
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
        getDeptByUserService = SpringUtil.getBean(DeptByUserService.class);
    }

    /**
     * 获取租户配置
     *
     * @return tenantProperties
     */
    private static BladeTenantProperties getTenantProperties() {
        if (tenantProperties == null) {
            tenantProperties = SpringUtil.getBean(BladeTenantProperties.class);
        }
        return tenantProperties;
    }

    /**
     * 创建认证token
     *
     * @param userInfo 用户信息
     * @return token
     */
    public static Kv createAuthInfo(UserInfo userInfo) {
        Kv authInfo = Kv.create();
        User user = userInfo.getUser();
        String selectDl = userService.selectDl(user.getId());
        String selectZz = userService.selectZz(user.getId());
        //        查看当前登录人是否为打粮队领导55923762l/高层1631124176515588098l
//        Boolean selectFightGrainOrSenior = userService.selectDeptAOrRoleB(55923762l, 1631124176515588098l, user.getId());
        // 高层权限开放客户批量分配操作
        Boolean selectFightGrainOrSenior = user.getRoleId().contains(RoleIdConstant.MANAGER_ROLE_ID_STR);
        String SS = selectZz.equals("1") ? "是" : "否";
//        查看当前登录人是否是财务
        Boolean selectFinance = userService.selectFinance(user.getId());
        List<String> deptNames = Func.isBlank(user.getDeptId()) ? new ArrayList<>() : deptService.getDeptNames(user.getDeptId());
//        // 查询当前登录人所有一级部门
//        // 定义打粮队子部门
//        List<String> sellIdList = Arrays.asList("153217213","341775525","375733873","492971127","744288495","855001108","891130892","892163636","892306566","892311537");
//        List<Map> deptNamesByMap = new ArrayList<>();
//        Arrays.stream(user.getDeptId().split(",")).forEach(deptId ->{
//            if(!sellIdList.contains(deptId)){
//                deptId = getDeptByUserService.getDeptByOne(deptId);
//            }
//            deptNamesByMap.add(deptService.getDeptNamesByMap(deptId));
//        });
        Map deptByKingdeeOne = getDeptByUserService.getDeptByKingdeeOne(String.valueOf(user.getId()));
        //设置jwt参数
        Map<String, Object> param = new HashMap<>(22);
        boolean flag = deptType(user.getDeptId()) == 0 ? true : false;
        param.put(TokenConstant.TOKEN_TYPE, TokenConstant.ACCESS_TOKEN);
        param.put(TokenConstant.TENANT_ID, user.getTenantId());
        param.put(TokenConstant.USER_ID, Func.toStr(user.getId()));
        param.put(TokenConstant.DEPT_ID, user.getDeptId());
        param.put(TokenConstant.POST_ID, user.getPostId());
        param.put(TokenConstant.ROLE_ID, user.getRoleId());
        param.put(TokenConstant.OAUTH_ID, userInfo.getOauthId());
        param.put(TokenConstant.ACCOUNT, user.getAccount());
        param.put(TokenConstant.USER_NAME, user.getAccount());
        param.put(TokenConstant.NICK_NAME, user.getRealName());
        param.put(TokenConstant.USER_NAME, user.getName());
        param.put(TokenConstant.NICK_NAME, user.getName());
        param.put(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()));
        param.put(TokenConstant.DETAIL, userInfo.getDetail());
        param.put("leader", user.getLeader());
        param.put("dlsq", selectDl);
        param.put("market", flag);
        param.put("zzfzb", SS);
        param.put("fightGrain", selectFightGrainOrSenior);
        param.put("deptName", String.join(",", deptNames));
        param.put("finance", selectFinance);
        param.put("deptList", deptByKingdeeOne);
        //拼装accessToken
        try {
            TokenInfo accessToken = SecureUtil.createJWT(param, "audience", "issuser", TokenConstant.ACCESS_TOKEN);
            // 判断是否是网盾公司
//            List<String> shield = Arrays.asList("********", "********", "*********", "*********", "891102751", "894004169");
//            AtomicBoolean netShield = new AtomicBoolean(false);
//            User u = userInfo.getUser();
//            shield.stream().forEach(s -> {
//                if (u.getDeptId().contains(s)) {
//                    netShield.set(true);
//                    return;
//                }
//            });
            // 判断网盾公司结束
            //判断是否高层
            boolean highRise = false;
            if (user.getRoleId().contains(RoleIdConstant.MANAGER_ROLE_ID_STR)) {
                highRise = true;
            }
            //返回accessToken
            return authInfo.set(TokenConstant.TENANT_ID, user.getTenantId())
                    .set(TokenConstant.USER_ID, Func.toStr(user.getId()))
                    .set(TokenConstant.DEPT_ID, user.getDeptId())
                    .set(TokenConstant.POST_ID, user.getPostId())
                    .set(TokenConstant.ROLE_ID, user.getRoleId())
                    .set(TokenConstant.OAUTH_ID, userInfo.getOauthId())
                    .set(TokenConstant.ACCOUNT, user.getAccount())
                    .set(TokenConstant.USER_NAME, user.getAccount())
                    .set(TokenConstant.NICK_NAME, user.getRealName())
                    .set(TokenConstant.USER_NAME, user.getName())
                    .set(TokenConstant.NICK_NAME, user.getName())
                    .set(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()))
                    .set(TokenConstant.AVATAR, Func.toStr(user.getAvatar(), TokenConstant.DEFAULT_AVATAR))
                    .set(TokenConstant.ACCESS_TOKEN, accessToken.getToken())
                    .set(TokenConstant.REFRESH_TOKEN, createRefreshToken(userInfo).getToken())
                    .set(TokenConstant.TOKEN_TYPE, TokenConstant.BEARER)
                    .set(TokenConstant.EXPIRES_IN, accessToken.getExpire())
                    .set(TokenConstant.DETAIL, userInfo.getDetail())
                    .set(TokenConstant.LICENSE, TokenConstant.LICENSE_NAME)
                    .set("market", flag)
                    .set("leader", user.getLeader())
                    .set("dlsq", selectDl)
                    .set("zzfzb", SS)
                    .set("fightGrain", param.get("fightGrain"))
                    .set("deptName", String.join(",", deptNames))
//                    .set("netShield", netShield)
                    .set("finance", param.get("finance"))
                    .set("deptNameList", param.get("deptList"))
                    //todo 交付时 版本号+1
                    .set("highRise", highRise).set("systemVersion","1.1.0")
                    .set("ddUser", userInfo.getDdUser());
        } catch (Exception ex) {
            return authInfo.set("error_code", HttpServletResponse.SC_UNAUTHORIZED).set("error_description", ex.getMessage());
        }
    }

    

    /**
     * 创建认证token【临时Token】
     *
     * @param userInfo 用户信息
     * @return token
     */
    public static Kv createAuthInfoWithTemp(long accessTokenValidity) {
        Kv authInfo = Kv.create();
        User user = new User();
        
        String selectDl = StringPool.EMPTY;
        // 高层权限开放客户批量分配操作
        Boolean selectFightGrainOrSenior = false;
        String SS = "否";
        // 查看当前登录人是否是财务
        Boolean selectFinance = false;
        List<String> deptNames = new ArrayList<>();
        Map deptByKingdeeOne = new HashMap<>(0);

        //设置jwt参数
        Map<String, Object> param = new HashMap<>(22);
        boolean flag = false;
        param.put(TokenConstant.TOKEN_TYPE, TokenConstant.ACCESS_TOKEN);
        param.put(TokenConstant.TENANT_ID, user.getTenantId());
        param.put(TokenConstant.USER_ID, Func.toStr(user.getId()));
        param.put(TokenConstant.DEPT_ID, user.getDeptId());
        param.put(TokenConstant.POST_ID, user.getPostId());
        param.put(TokenConstant.ROLE_ID, user.getRoleId());
        param.put(TokenConstant.OAUTH_ID, StringPool.EMPTY);
        param.put(TokenConstant.ACCOUNT, user.getAccount());
        param.put(TokenConstant.USER_NAME, user.getAccount());
        param.put(TokenConstant.NICK_NAME, user.getRealName());
        param.put(TokenConstant.USER_NAME, user.getName());
        param.put(TokenConstant.NICK_NAME, user.getName());
        param.put(TokenConstant.ROLE_NAME, StringPool.EMPTY);
        param.put(TokenConstant.DETAIL, new HashMap<>(0));
        param.put("leader", 0);
        param.put("dlsq", selectDl);
        param.put("market", flag);
        param.put("zzfzb", SS);
        param.put("fightGrain", selectFightGrainOrSenior);
        param.put("deptName", String.join(",", deptNames));
        param.put("finance", selectFinance);
        param.put("deptList", deptByKingdeeOne);
        //拼装accessToken
        try {
            TokenInfo accessToken = ShareSecureUtil.createJWTWiothTemp(param, "audience", "issuser", TokenConstant.ACCESS_TOKEN, accessTokenValidity);
            //判断是否高层
            boolean highRise = false;
            //返回accessToken
            return authInfo.set(TokenConstant.TENANT_ID, user.getTenantId())
                    .set(TokenConstant.USER_ID, Func.toStr(user.getId()))
                    .set(TokenConstant.DEPT_ID, StringPool.EMPTY)
                    .set(TokenConstant.POST_ID, StringPool.EMPTY)
                    .set(TokenConstant.ROLE_ID, StringPool.EMPTY)
                    .set(TokenConstant.OAUTH_ID, StringPool.EMPTY)
                    .set(TokenConstant.ACCOUNT, StringPool.EMPTY)
                    .set(TokenConstant.USER_NAME, user.getAccount())
                    .set(TokenConstant.NICK_NAME, user.getRealName())
                    .set(TokenConstant.USER_NAME, user.getName())
                    .set(TokenConstant.NICK_NAME, user.getName())
                    .set(TokenConstant.ROLE_NAME, StringPool.EMPTY)
                    .set(TokenConstant.AVATAR, StringPool.EMPTY)
                    .set(TokenConstant.ACCESS_TOKEN, accessToken.getToken())
                    .set(TokenConstant.REFRESH_TOKEN, StringPool.EMPTY)
                    .set(TokenConstant.TOKEN_TYPE, TokenConstant.BEARER)
                    .set(TokenConstant.EXPIRES_IN, accessToken.getExpire())
                    .set(TokenConstant.DETAIL, new HashMap<>(0))
                    .set(TokenConstant.LICENSE, TokenConstant.LICENSE_NAME)
                    .set("market", flag)
                    .set("leader", 0)
                    .set("dlsq", selectDl)
                    .set("zzfzb", SS)
                    .set("fightGrain", param.get("fightGrain"))
                    .set("deptName", String.join(",", deptNames))
//                    .set("netShield", netShield)
                    .set("finance", param.get("finance"))
                    .set("deptNameList", param.get("deptList"))
                    //todo 交付时 版本号+1
                    .set("highRise", highRise).set("systemVersion","1.1.0")
                    .set("ddUser", StringPool.EMPTY);
        } catch (Exception ex) {
            return authInfo.set("error_code", HttpServletResponse.SC_UNAUTHORIZED).set("error_description", ex.getMessage());
        }
    }

    /**
     * 创建refreshToken
     *
     * @param userInfo 用户信息
     * @return refreshToken
     */
    private static TokenInfo createRefreshToken(UserInfo userInfo) {
        User user = userInfo.getUser();
        Map<String, Object> param = new HashMap<>(16);
        param.put(TokenConstant.TOKEN_TYPE, TokenConstant.REFRESH_TOKEN);
        param.put(TokenConstant.USER_ID, Func.toStr(user.getId()));
        param.put(TokenConstant.DEPT_ID, Func.toStr(user.getDeptId()));
        param.put(TokenConstant.ROLE_ID, Func.toStr(user.getRoleId()));
        return SecureUtil.createJWT(param, "audience", "issuser", TokenConstant.REFRESH_TOKEN);
    }

    /**
     * 判断租户权限
     *
     * @param tenant 租户信息
     * @return boolean
     */
    public static boolean judgeTenant(Tenant tenant) {
        if (tenant == null) {
            throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT);
        }
        if (StringUtil.equalsIgnoreCase(tenant.getTenantId(), BladeConstant.ADMIN_TENANT_ID)) {
            return false;
        }
        Date expireTime = tenant.getExpireTime();
        if (getTenantProperties().getLicense()) {
            String licenseKey = tenant.getLicenseKey();
            String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
            expireTime = JsonUtil.parse(decrypt, Tenant.class).getExpireTime();
        }
        if (expireTime != null && expireTime.before(DateUtil.now())) {
            throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
        }
        return false;
    }

    /**
     * 判断所有部门是不是全是打梁队下，如果不全是则按照普通部门处理
     *
     * @param dept
     * @return
     */
    public static Integer deptType(String dept) {
        if (Func.isBlank(dept)) {
            return -1;
        }
        List<String> strings = Arrays.asList(dept.split(","));
        int deptCount = strings.size();
        List<String> integers = Arrays.asList("153217213", "341775525", "375733873", "492971127", "744288495", "855001108", "891130892", "892163636", "892306566", "892311537");
        for (String deptId : strings) {
            if (integers.contains(deptId)) {
                deptCount--;
            }
        }
        return deptCount;
    }
}

