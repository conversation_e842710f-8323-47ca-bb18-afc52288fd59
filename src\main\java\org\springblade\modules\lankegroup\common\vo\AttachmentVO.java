/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用附件视图对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "AttachmentVO对象", description = "通用附件视图对象")
@EqualsAndHashCode(callSuper = true)
public class AttachmentVO extends AttachmentBaseVO {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = com.fasterxml.jackson.databind.ser.std.ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	private Long id;

	// ========== 系统字段（用于显示）==========
	
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private java.time.LocalDateTime createTime;

	/**
	 * 创建人
	 */
	@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = com.fasterxml.jackson.databind.ser.std.ToStringSerializer.class)
	@ApiModelProperty(value = "创建人")
	private Long createUser;

	/**
	 * 创建部门
	 */
	@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = com.fasterxml.jackson.databind.ser.std.ToStringSerializer.class)
	@ApiModelProperty(value = "创建部门")
	private Long createDept;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private java.time.LocalDateTime updateTime;

	/**
	 * 更新人
	 */
	@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = com.fasterxml.jackson.databind.ser.std.ToStringSerializer.class)
	@ApiModelProperty(value = "更新人")
	private Long updateUser;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Integer isDeleted;

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;

	// ========== 扩展字段（用于显示关联信息）==========
	
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;

	/**
	 * 更新人姓名
	 */
	@ApiModelProperty(value = "更新人姓名")
	private String updateUserName;

	/**
	 * 创建部门名称
	 */
	@ApiModelProperty(value = "创建部门名称")
	private String createDeptName;

} 