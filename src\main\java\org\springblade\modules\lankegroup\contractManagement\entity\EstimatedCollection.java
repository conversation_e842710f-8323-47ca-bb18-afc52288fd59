package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;

@Data
public class EstimatedCollection {
    /**
     * 预计回款金额
     */
    private Double estimatedCollectionAmount;
    /**
     * 预计回款日期
     */
    private String estimatedCollectionDate;
    /**
     * 预计回款百分比
     */
    private String expectedPaymentPercentage;
    /**
     * 付款条件
     */
    private String paymentCondition;
}
