# 新增SQL表



- 系统任务-超期统计表
```sql
【涉及修改的操作，先备份！！！】
【涉及修改的操作，先备份！！！】
【涉及修改的操作，先备份！！！】

CREATE TABLE `blade_equal_protection_system_task_cqtj` (
  `id` bigint NOT NULL COMMENT '主键',
  `project_id` bigint DEFAULT NULL COMMENT '项目id',
  `gdsj` datetime DEFAULT NULL COMMENT '项目的签约时间（合同归档审批通过时间）',
  `lx` int DEFAULT NULL COMMENT '类型（1.超过一个月、2.两个月、3.三个月）',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除 0.未删除/1.已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统任务-超期统计表';
```


# sql新增、修改数据
```mysql
# 新增业务字典表
INSERT INTO `bladex_boot`.`blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1730136175165839394, '000000', 1730136175165839387, 'equal_protection_system_stage', '7', '整改阶段', 4, NULL, 0, 0);

# 更新业务字典表
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '1', `dict_value` = '定级备案', `sort` = 1, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839388;
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '2', `dict_value` = '启动会议', `sort` = 2, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839389;
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '3', `dict_value` = '测评阶段', `sort` = 3, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839390;
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '4', `dict_value` = '报告编制', `sort` = 5, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839391;
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '5', `dict_value` = '结项会议', `sort` = 6, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839392;
UPDATE `bladex_boot`.`blade_dict_biz` SET `tenant_id` = '000000', `parent_id` = 1730136175165839387, `code` = 'equal_protection_system_stage', `dict_key` = '6', `dict_value` = '系统完成', `sort` = 7, `remark` = NULL, `is_sealed` = 0, `is_deleted` = 0 WHERE `id` = 1730136175165839393;



# 修改系统阶段表status字段
ALTER TABLE `blade_equal_protection_system_task_stage` 
MODIFY COLUMN `status` int DEFAULT NULL COMMENT '状态 0.进行中；1.已完成；2.已终止；3.未开始(该状态在数据库体现不出来，为了和系统详情接口阶段列表回显一致)';



# 新增等保看板-解昊/马天午角色
INSERT INTO `bladex_boot`.`blade_role`(`id`, `tenant_id`, `parent_id`, `role_name`, `sort`, `role_alias`, `is_deleted`) 
VALUES (1732584277284188162, '000000', 0, '等保看板-解昊权限', 26, '等保看板-解昊权限', 0);
INSERT INTO `bladex_boot`.`blade_role`(`id`, `tenant_id`, `parent_id`, `role_name`, `sort`, `role_alias`, `is_deleted`) 
VALUES (1732584277284188163, '000000', 0, '等保看板-马天午权限', 26, '等保看板-马天午权限', 0);

# 为解昊添加角色“等保看板-解昊角色” 
UPDATE blade_user 
SET role_id = CONCAT( role_id, ',1732584277284188162' )
WHERE
	id = 132325753691120391;






```

# 历史数据处理

```sql



# 将阶段表中status字段改成 【状态 0.进行中；1.已完成；2.已终止；3.未开始】 对应的值
update `blade_equal_protection_system_task_stage` s
join blade_equal_protection_system_task t on s.system_task_id = t.id
set
s.`status` = 
case
            when t.closed_status = 0 and s.stage_end_time is null then 0
            when t.closed_status = 0 and s.stage_end_time is not null then 1
            when t.closed_status = 1 then 1
            when t.closed_status = 2 and t.stage_status != s.stage_status then 1
            when t.closed_status = 2 and t.stage_status = s.stage_status then 2
            else null
            end
where s.is_deleted = 0 ;


```
