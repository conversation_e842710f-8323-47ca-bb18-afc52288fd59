package org.springblade.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/22 13:36
 * 用章类型
 */
public enum SealEnum {
    HTZ("合同章", "contract_chapter"),
    GZ("公章", "official_seal"),
    FRZ("法人章", "corporate_seal");

    private String name;

    private String value;

    SealEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }


    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
