package org.springblade.modules.lankegroup.clientrelation.dto;

import lombok.Data;
import org.springblade.modules.lankegroup.clientrelation.entity.Personnel;
import org.springblade.modules.lankegroup.clientrelation.entity.PersonnelMarket;

import java.util.List;

/**
 * 人员
 */
@Data
public class PersonnelDTO extends Personnel {

    /**
     * 区域/行业
     */
    private List<PersonnelMarket> list;

    private List<Long> userList;

    private List<PersonnelMarket> deleteList;



}
