/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.lankegroup.contractManagement.entity.Approval;
import org.springblade.modules.lankegroup.contractManagement.entity.Contract;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListParam;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractListResult;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractVO;
import org.springblade.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合同管理表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface IContractService extends BaseService<Contract> {

    /**
     * 自定义分页
     *
     * @param page
     * @param param
     * @return
     */
    IPage<ContractListResult> selectContractPage(IPage<ContractListResult> page, ContractListParam param);

    /**
     * 合同开启流程
     *
     * @param contract 合同实体
     * @return
     */
    Map startProcess(ContractVO contract);

    /**
     * 根据id查询合同详细的基础信息
     *
     * @param map
     * @return
     */
    ContractVO selectDetail(Map map);

    /**
     * 查询当前登录人的草稿
     *
     * @return
     */
    ContractVO selectDraft(Map map);

    /**
     * 初始化草稿
     *
     * @param type
     * @return
     */
    R initContract(String type);

    /**
     * 合同筛选页面及列表
     *
     * @param param
     * @return
     */
    List<Map> contractFilter(ContractListParam param);

    /**
     * 详情接口参数整理
     *
     * @param contract
     * @return
     */
    ContractVO tidyUpContract(Contract contract);

    /**
     * 合同草稿存储
     *
     * @param contract
     * @return
     */
    Map saveDraft(ContractVO contract);


    Map number(Long projectId);

    /**
     * 合同名称唯一性校验
     */
    List<ContractVO> checkContractNameSole(String contractName, Long contractId);

    /**
     * 合同变更时，选择合同时使用
     * 填写合同变更时要求合同列表展示：
     * 销售/其他合同展示待归档状态的合同【2023-10-11 销售/其他合同增加归档撤回、归档驳回两个状态】
     * 采购合同展示已归档项目
     *
     * @param contractName
     * @return
     */
    List<ContractVO> contractList(String contractName);

    /**
     * user转为审批人
     *
     * @param user
     * @return
     */
    public Approval userToApproval(User user);



    /**
     * 客户列表关联的所有合同列表
     *
     * @param customerId 客户id
     * @return
     */
    IPage<ContractListResult> customerContractList(IPage<ContractListResult> page, Long customerId);


    /**
     * 合同列表根据合同id查询合同（列表）详情
     *
     * @param contractId
     * @return
     */
    List<ContractListResult> pageDetail(String contractId);

    /**
     * 根据金蝶项目id获取审核完成的合同明细信息
     *
     * @param projectId
     * @return
     */
    List<Map> getKdContractEntry(Long projectId);
    /**
     *
     * @param taskId  节点id
     * @param userId  要转给的人的id
     * @param type    1是无变更/0是有变更
     * @return  消息通知转办人，并更新本地表中当前审批人id
     */
    String transferTaskToUser(String taskId, String userId,int type);

    String selectContractCount();

    /**
     * 根据业务类别获取合同
     * @param businessId
     * @return
     */
    List<Contract> contractListByBusinessId(Long businessId,List<Long> userIds);

    /**
     * 根据业务类别获取合同ID集合
     * @param businessId
     * @return
     */
    List<Long> contractIdsByBusinessId(Long businessId,List<Long> userIds);

    /**
     * 根据合同ID集合获取项目ID集合
     * @param contractIds
     * @return
     */
    List<Long> getProjectIdsByContractIds(List<Long> contractIds);

    /**
     * 回款看板坏账统计数据权限单独控制
     * @param businessId
     * @return
     */
    List<Long> contractIdsForPaybackPanelBadDebt(Long businessId,List<Long> userIds);

    /**
     * 根据项目ID获取合同金额
     * @param projectId
     * @return
     */
    BigDecimal getTotalContractAmountByProjectId(Long projectId);
}
