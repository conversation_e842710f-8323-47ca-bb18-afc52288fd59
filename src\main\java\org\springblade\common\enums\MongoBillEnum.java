package org.springblade.common.enums;

/**
 * mongo放的单据对应表名枚举
 */
public enum MongoBillEnum {
    采购申请单("purchaseRequisitionParams","cgsq"),
    油卡充值("oilCardRechargeParams","ykcz"),
    付款单("receiptParams","fk"),
    借款单("loanParam","jk");
    /**
     * mongo集合名称
     */
    String collectionName;

    /**
     *表单标识
     */
    String billType;

    MongoBillEnum(String collectionName,String billType) {
        this.collectionName = collectionName;
        this.billType=billType;
    }

    public String getCollectionName() {
        return collectionName;
    }
    public String getBillType() {
        return billType;
    }
}
