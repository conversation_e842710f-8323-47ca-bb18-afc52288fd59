package org.springblade.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.common.enums.ErrorCodeEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 统一异常响应模型
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@ApiModel("异常响应模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    @ApiModelProperty("错误码")
    private int code;
    
    @ApiModelProperty("错误消息")
    private String message;
    
    @ApiModelProperty("详细错误信息")
    private String details;
    
    @ApiModelProperty("异常类型")
    private String exceptionType;
    
    @ApiModelProperty("请求路径")
    private String path;
    
    @ApiModelProperty("请求方法")
    private String method;
    
    @ApiModelProperty("时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    @ApiModelProperty("请求ID")
    private String requestId;
    
    @ApiModelProperty("用户ID")
    private String userId;
    
    @ApiModelProperty("租户ID")
    private String tenantId;
    

    
    @ApiModelProperty("修复建议")
    private String suggestion;
    
    public ErrorResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ErrorResponse(ErrorCodeEnum errorCode) {
        this();
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
    
    public ErrorResponse(ErrorCodeEnum errorCode, String details) {
        this(errorCode);
        this.details = details;
    }
    
    public ErrorResponse(Exception exception) {
        this();
        ErrorCodeEnum errorCode = ErrorCodeEnum.getByException(exception);
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.exceptionType = exception.getClass().getSimpleName();
        this.details = exception.getMessage();
        
        // 获取当前用户信息
        try {
            if (AuthUtil.getUser() != null) {
                this.userId = String.valueOf(AuthUtil.getUserId());
                this.tenantId = AuthUtil.getUser().getTenantId();
            }
        } catch (Exception ignored) {
            // 忽略获取用户信息时的异常
        }
    }
    

    
    /**
     * 生成修复建议
     */
    public void generateSuggestion() {
        if (StringUtil.isBlank(this.exceptionType)) {
            return;
        }
        
        StringBuilder suggestion = new StringBuilder();
        
        switch (this.code) {
            case 7002: // TENANT_DATASOURCE_ERROR
                suggestion.append("租户数据源异常修复建议：\n");
                suggestion.append("1. 检查租户ID是否正确：").append(this.tenantId).append("\n");
                suggestion.append("2. 验证租户数据源配置是否存在\n");
                suggestion.append("3. 检查数据库连接是否正常\n");
                suggestion.append("4. 确认当前用户是否有访问该租户的权限\n");
                if (this.details != null && this.details.contains("null")) {
                    suggestion.append("5. 检查租户数据源映射表是否有对应记录");
                }
                break;
                
            case 501: // DATABASE_ERROR
                suggestion.append("数据库异常修复建议：\n");
                suggestion.append("1. 检查数据库连接是否正常\n");
                suggestion.append("2. 验证SQL语句是否正确\n");
                suggestion.append("3. 检查数据表结构是否匹配\n");
                suggestion.append("4. 确认数据库用户权限是否足够");
                break;
                
            case 422: // VALIDATION_ERROR
                suggestion.append("参数校验异常修复建议：\n");
                suggestion.append("1. 检查请求参数格式是否正确\n");
                suggestion.append("2. 验证必填参数是否完整\n");
                suggestion.append("3. 确认参数类型是否匹配");
                break;
                
            default:
                suggestion.append("通用修复建议：\n");
                suggestion.append("1. 查看详细错误日志定位问题\n");
                suggestion.append("2. 检查网络连接是否正常\n");
                suggestion.append("3. 确认系统配置是否正确\n");
                suggestion.append("4. 如问题持续，请联系技术支持");
                break;
        }
        
        this.suggestion = suggestion.toString();
    }
    
    // Getters and Setters
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getDetails() {
        return details;
    }
    
    public void setDetails(String details) {
        this.details = details;
    }
    
    public String getExceptionType() {
        return exceptionType;
    }
    
    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getMethod() {
        return method;
    }
    
    public void setMethod(String method) {
        this.method = method;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    

    
    public String getSuggestion() {
        return suggestion;
    }
    
    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }
} 