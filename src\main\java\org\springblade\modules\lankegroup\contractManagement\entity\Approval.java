package org.springblade.modules.lankegroup.contractManagement.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 流程审批人节点类
 */
@Data
public class Approval {

    private String id;
    private String account;
    private String name;
    private String postId;
    private String post;
    private String avatar= "";
    private String deptId;
    private String parentId;
    private String sort;
    //报销单是否为第二个需要隐藏的总经理级别
    private Boolean isHide=false;
    //审批状态  -1 未审批 ;0 审批中  ;1 已通过;2 已拒绝;3 已撤销
    private Integer approvedStatus = -1;
    //驳回/作废理由
    private String reason;
//    节点id
    private String historyActivityId;
    // 节点名称
    private  String historyActivityName;
//    审批时间
    private String approvalTime;
    // 审批时间：年月日时分秒
    private Date approveDateStr;
/**
 * 部分流程有抄送人，用此字段给前端区分当前人员是抄送人还是审批人
 * true==》流程中的审批人
 */
    private Boolean personType=true;

    private CarbonApproval carbonApproval = null;
    /**
     * 抄送人列表
     */
    private List<Approval> carbonList=new ArrayList<>();
    /**
     * 抄送人数
     */
    private Integer num;
    /**
     * 类型
     */
    private String approvalType;
}
