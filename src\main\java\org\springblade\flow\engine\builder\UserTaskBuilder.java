package org.springblade.flow.engine.builder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableMap;
import org.flowable.bpmn.model.*;
import org.springblade.flow.engine.builder.entity.ApproverFlowNode;

import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.flow.core.constant.WorkFlowConstants.*;


public class UserTaskBuilder {

	/**
	 * 指定审批人
	 */
	private static void designated(UserTask userTask, JSONObject flowNode) {
		// 设置办理人
		if (!flowNode.isNull("nodeUserList")) {
			userTask.setCandidateUsers(flowNode.getJSONArray("nodeUserList").toList(JSONObject.class).stream().map(item -> item.get("id").toString()).collect(Collectors.toList()));
		}
		// 候选组 增加前缀 避免角色 和岗位Id重复
		List<String> candidateGroups = CollUtil.newArrayList();
		if (!flowNode.isNull("nodeRoleList")) {
			List<String> nodeRoleList = flowNode.getJSONArray("nodeRoleList").toList(JSONObject.class).stream().map(item -> TASK_CANDIDATE_GROUP_ROLE + item.get("id").toString()).collect(Collectors.toList());
			candidateGroups.addAll(nodeRoleList);
		}
		if (!flowNode.isNull("nodePostList")) {
			List<String> nodePostList = flowNode.getJSONArray("nodePostList").toList(JSONObject.class).stream().map(item -> TASK_CANDIDATE_GROUP_POST + item.get("id").toString()).collect(Collectors.toList());
			candidateGroups.addAll(nodePostList);
		}
		userTask.setCandidateGroups(candidateGroups);
	}

	private static void designatedUser(UserTask userTask, JSONObject flowNode) {
		// 设置办理人
		if (!flowNode.isNull("nodeUserList")) {
			userTask.setCandidateUsers(flowNode.getJSONArray("nodeUserList").toList(JSONObject.class).stream().map(item -> item.get("id").toString()).collect(Collectors.toList()));
		}
	}

	private static void designatedRole(UserTask userTask, JSONObject flowNode) {
		// 设置办理人
		// 候选组 增加前缀 避免角色 和岗位Id重复
		List<String> candidateGroups = CollUtil.newArrayList();
		if (!flowNode.isNull("nodeRoleList")) {
			List<String> nodeRoleList = flowNode.getJSONArray("nodeRoleList").toList(JSONObject.class).stream().map(item -> TASK_CANDIDATE_GROUP_ROLE + item.get("id").toString()).collect(Collectors.toList());
			candidateGroups.addAll(nodeRoleList);
//			List<User> userList = SpringContextUtils.getBean(IUserSearchClient.class).listByRole(nodeRoleList.stream().collect(Collectors.joining(StrUtil.COMMA))).getData();
//			if (Func.isEmpty(userList)) {
//				// 角色下无人员，交给虚拟用户作为候选用户
//				userTask.setCandidateUsers(Collections.singletonList(ProcessConfigConstant.VIRTUAL_APPROVER));
//			}else {
//				userTask.setCandidateGroups(candidateGroups);
//			}
			userTask.setCandidateGroups(candidateGroups);
		}
	}

	/**
	 * 部门主管
	 *
	 * @param userTask 任务
	 * @param flowNode 节点
	 */
	private static void deptLeader(UserTask userTask, JSONObject flowNode) {
		// 设置办理人 使用动态参数 几级主管
//		Integer approveDirectorLevel = flowNode.getInt("approveDirectorLevel");
//		userTask.setAssignee(StrUtil.format("${{}{}}", DEPT_LEVEL_LEADER, approveDirectorLevel));
		userTask.setAssignee(StrUtil.format("${{}}", DEPT_LEVEL_LEADER));
	}

	/**
	 * 创建人直属领导
	 *
	 * @param userTask
	 * @param flowNode
	 */
	private static void userImmediateSupervisor(UserTask userTask, JSONObject flowNode) {
		userTask.setAssignee(StrUtil.format("${{}}", USER_IMMEDIATE_SUPERVISOR));
	}

	/**
	 * 发起人自己
	 *
	 * @param userTask 任务
	 * @param flowNode 节点
	 */
	private static void initiator(UserTask userTask, JSONObject flowNode) {
		// 流程发起时会将发起人设置为流程变量 initiator变量名
		userTask.setAssignee("${initiator}");
	}
	private static void initiatorOptional(UserTask userTask, JSONObject flowNode) {
		userTask.setAssignee("${" + userTask.getId() + "}");
		userTask.setCandidateUsers(flowNode.getJSONArray("sponsorUserList").toList(JSONObject.class).stream().map(item -> item.get("id").toString()).collect(Collectors.toList()));
	}


	/**
	 * 从表单获取
	 *
	 * @param userTask 任务
	 * @param flowNode 节点
	 */
	private static void formUserField(UserTask userTask, JSONObject flowNode) {
		// 流程发起时会将发起人设置为流程变量 initiator变量名
		String formUserFields = flowNode.getStr("formUserFields");
		userTask.setCandidateUsers(CollUtil.newArrayList(StrUtil.format("${{}{}}", APPROVE_FORM_VAR, formUserFields)));
	}


	public static String id(String prefix) {
		return prefix + "_" + IdUtil.objectId();
	}

	/**
	 * 插件用户任务
	 *
	 * @param flowNode 节点
	 * @return 任务
	 */
	public static UserTask createUserTask(JSONObject flowNode) {
		Integer type = flowNode.getInt("type");
		if (type == 0) {
			return createStartTask(flowNode);
		}
		UserTask userTask = new UserTask();
		// 审批人类型
		Integer settype = flowNode.getInt("settype");
		// 自动生成id
		String id = id("userTask");
		userTask.setId(id);
		userTask.setName(flowNode.getStr("nodeName"));
		// 0串行会签 1 并行会签 2 或签 （这个值后台传的有点乱，无论什么情况这个字段都有值，应该改成单人审批的时候没有值才对）
		Integer multipleApprovalType = flowNode.getInt("multipleApprovalType");
		Boolean isSequential = false;// = flowNode.getBool("isSequential", false); //false 代表并行，true代表串行
		if (multipleApprovalType == null || 0 == multipleApprovalType) {
			multipleApprovalType = 0;
			isSequential = true;
		}
		ApproverTypeEnum approverType = EnumUtil.getBy(ApproverTypeEnum.class, item -> Objects.equals(item.getValue(), settype));

		//单独处理发起人自选一个人
//		if (approverType == ApproverTypeEnum.INITIATOR_OPTIONAL && flowNode.getStr("sponsorType").startsWith("1-")) {
//			userTask.setAssignee("${"+id+"}");
//			userTask.setCandidateUsers(flowNode.getJSONArray("sponsorUserList").toList(JSONObject.class).stream().map(item -> item.get("id").toString()).collect(Collectors.toList()));
//		}else {
		// 是否是会签
		if (approverType != ApproverTypeEnum.INITIATOR && approverType != ApproverTypeEnum.DEPT_LEADER && approverType != ApproverTypeEnum.USER_IMMEDIATE_SUPERVISOR) {
			// 会签需要把参数传递 查询出所有审批人给到流程变量
			setUserTaskMultiInstance(userTask, isSequential, multipleApprovalType);
			//  需要把部门组织等转成具体指定用户 设置为审批人
			// 把具体的审批方式设置到监听器的参数
			setUserTaskMultiInstanceListener(userTask, flowNode);
		} else {
			List<String> incoming = flowNode.getJSONArray("incoming").toList(String.class);
			if (incoming != null && !incoming.isEmpty()) {
//				userTask.setName(flowNode.getStr("nodeName"));
				switch (approverType) {
					case DESIGNATED:
						designatedUser(userTask, flowNode);
					case ROLE:
						designatedRole(userTask, flowNode);
						break;
					case DEPT_LEADER:
						deptLeader(userTask, flowNode);
						break;
					case USER_IMMEDIATE_SUPERVISOR:
						userImmediateSupervisor(userTask, flowNode);
						break;
					case INITIATOR:
						initiator(userTask, flowNode);
						break;
					case INITIATOR_OPTIONAL:
						if(flowNode.getStr("sponsorType").startsWith("1-")) {
							initiatorOptional(userTask, flowNode);
						}
						break;
//					case FORM_USER_FIELD:
//						formUserField(userTask, flowNode);
//						break;
					default:
						break;
				}
			}
		}
//		}
		// 节点表单配置属性
		createFormProperties(flowNode, userTask);
		// 节点事件属性
		createEventsProperties(flowNode, userTask);
		return userTask;
	}


	/**
	 * 设置多实例加签的监听器
	 *
	 * @param userTask 用户任务
	 * @param flowNode 流程节点配置
	 */
	private static void setUserTaskMultiInstanceListener(UserTask userTask, JSONObject flowNode) {
//          新建监听器 用于设置审批人列表
		//设置执行监听器
		List<FlowableListener> executionListeners = new ArrayList<>();
		FlowableListener flowableListener = new FlowableListener();
		//事件：start/end/
		flowableListener.setEvent("start");
		//设置表达式或java类路径
		//Spring配置以变量形式调用无法写入，只能通过继承TaskListener方法，
		flowableListener.setImplementationType("delegateExpression");
		flowableListener.setImplementation("${multiInstanceExecutionListener}");

		//监听器参数
		FieldExtension file2 = new FieldExtension();
		//会签操作组
		file2.setFieldName("assignee");
		Map<String, Object> result = BeanUtil.beanToMap(flowNode);
		// 只保留指定字段
		result.remove(ApproverFlowNode.Fields.formProperties);
		result.remove(ApproverFlowNode.Fields.taskEvents);
		result.remove(ApproverFlowNode.Fields.incoming);
		file2.setStringValue(JSONUtil.toJsonStr(result));
		List<FieldExtension> list2 = new ArrayList<>();
		list2.add(file2);
		//写入参数
		flowableListener.setFieldExtensions(list2);
		executionListeners.add(flowableListener);
		userTask.setExecutionListeners(executionListeners);

	}


	/**
	 * 多实例任务
	 * 实现加签等
	 * multipleApprovalType : 0串行会签 1 并行会签 2 或签
	 */
	public static void setUserTaskMultiInstance(UserTask userTask, Boolean isSequential, Integer multipleApprovalType) {
		MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = new MultiInstanceLoopCharacteristics();
		// false 代表并行，true代表串行，是什么意思呢？并行就是上面这5个任务可以同时执行，不受约束，串行就是一个个轮着来执行
//		multiInstanceLoopCharacteristics.setSequential(false);
		multiInstanceLoopCharacteristics.setSequential(isSequential);
		// 运行时变量名
		multiInstanceLoopCharacteristics.setInputDataItem(MULTI_INSTANCE_ASSIGNEE_LIST_VAR);
		multiInstanceLoopCharacteristics.setElementVariable("assignee");
		// 比如我们需要五个人审批投票，然后就在这里设置个5就行了，为什么叫循环基数呢？就是因为如果我们的通过条件是3个人通过就ok的话，那么可能只需要3个实例就结束，进入到下一个环节了，而不是要5个实例。
//        multiInstanceLoopCharacteristics.setLoopCardinality("${assigneeList.length}");
		// 设置完成条件
		if (multipleApprovalType == 2) {
			//moveExecutionsToSingleActivityId方法导致nrOfCompletedInstances不准确，所以自己写监听方法判断
//			multiInstanceLoopCharacteristics.setCompletionCondition("${nrOfCompletedInstances > 0}");
			multiInstanceLoopCharacteristics.setCompletionCondition("${OneSignMultiInstance.accessCondition(execution)}");
		} else {
			multiInstanceLoopCharacteristics.setCompletionCondition("${multiInstance.accessCondition(execution)}");
		}
		userTask.setLoopCharacteristics(multiInstanceLoopCharacteristics);
		userTask.setAssignee("${assignee}");
	}

	/**
	 * 创建发起人节点
	 */
	public static UserTask createStartTask(JSONObject flowNode) {
		UserTask userTask = new UserTask();
		userTask.setId(id("startTask"));
		userTask.setName(flowNode.getStr("nodeName"));
		userTask.setAssignee("${initiator}");
		return userTask;
	}

	/**
	 * 设置表单自定义属性
	 *
	 * @param flowNode 节点
	 * @param userTask 任务
	 */
	private static void createFormProperties(JSONObject flowNode, UserTask userTask) {
		if (flowNode.isNull("formProperties")) {
			return;
		}
		List<JSONObject> formProperties = flowNode.getJSONArray("formProperties").toList(JSONObject.class);
		List<FormProperty> formPropertyList = formProperties.stream().map(item -> {
			FormProperty formProperty = new FormProperty();
			formProperty.setName(item.getStr("name"));
			formProperty.setId(item.getStr("id"));
			formProperty.setReadable(item.getBool("readable"));
			formProperty.setWriteable(item.getBool("writeable"));
			return formProperty;
		}).collect(Collectors.toList());
		userTask.setFormProperties(formPropertyList);
	}

	/**
	 * 设置自定义事件属性
	 *
	 * @param flowNode 节点
	 * @param userTask 任务
	 */
	private static void createEventsProperties(JSONObject flowNode, UserTask userTask) {
		if (flowNode.isNull("taskEvents")) {
			return;
		}
		JSONObject taskEvents = flowNode.getJSONObject("taskEvents");
		// 通过审批事件
		Boolean agree = taskEvents.getBool("agree");
		String agreeNotifyUrl = taskEvents.getStr("agreeNotifyUrl");
		userTask.addExtensionElement(createTaskExtensionElement(BPMN_AGREE_EVENT, ImmutableMap.of("state", agree.toString(), "notifyUrl", agreeNotifyUrl)));
		// 拒绝审批事件
		Boolean reject = taskEvents.getBool("reject");
		String rejectNotifyUrl = taskEvents.getStr("rejectNotifyUrl");
		userTask.addExtensionElement(createTaskExtensionElement(BPMN_REJECT_EVENT, ImmutableMap.of("state", reject.toString(), "notifyUrl", rejectNotifyUrl)));
		// 创建
		FlowableListener createListener = new FlowableListener();
		createListener.setEvent("create");
		//Spring配置以变量形式调用无法写入，只能通ga过继承TaskListener方法，
		createListener.setImplementationType("delegateExpression");
		createListener.setImplementation("${userTaskCreateListener}");
		// 审核结束事件
		FlowableListener listener = new FlowableListener();
		listener.setEvent("complete");
		//Spring配置以变量形式调用无法写入，只能通过继承TaskListener方法，
		listener.setImplementationType("delegateExpression");
		listener.setImplementation("${userTaskCompleteListener}");

		List<FlowableListener> taskListeners = userTask.getTaskListeners();
		if (null != taskListeners) {
			taskListeners.add(listener);
			taskListeners.add(createListener);
		} else {
			taskListeners = CollUtil.newArrayList(createListener);
		}
		userTask.setTaskListeners(taskListeners);
	}

	/**
	 * 创建节点自定义属性
	 *
	 * @param extensionName 自定义属性名称4
	 * @return 自定义节点
	 */
	public static ExtensionElement createTaskExtensionElement(String extensionName, Map<String, String> attrs) {
		//        自定义属性
		ExtensionElement ee = new ExtensionElement();
		ee.setName(extensionName);
		ee.setNamespacePrefix("flowable");
		Map<String, List<ExtensionAttribute>> attributes = new HashMap<>();
		// 这里就是bpmn里自定义的assigneeType
		List<ExtensionAttribute> extensionAttributes = new ArrayList<>(1);
		for (String key : attrs.keySet()) {
			ExtensionAttribute attribute = new ExtensionAttribute();
			attribute.setName(key);
			attribute.setValue(attrs.get(key));
			extensionAttributes.add(attribute);
		}
		attributes.put(extensionName, extensionAttributes);
		ee.setAttributes(attributes);
		return ee;
	}


}
