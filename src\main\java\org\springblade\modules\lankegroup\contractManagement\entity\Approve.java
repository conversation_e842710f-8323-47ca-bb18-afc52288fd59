/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.NoArgsConstructor;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程审批记录操作表实体类
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
@NoArgsConstructor
@TableName("blade_approve")
@EqualsAndHashCode(callSuper = true)
public class Approve extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 流程实例主键
	*/
		private String processInstanceId;
	/**
	* 是否同意
	*/
		private String flag;
	/**
	* 通过/驳回的原因
	*/
		private String comment;

	/**
	 * 节点id
	 */
	private String historyActivityId;
	/**
	 * 备注（1是初始人添加/2流程写入）
	 */

	private Integer remarks;
	public Approve(String processInstanceId, String flag, String comment, String status, Integer remarks) {
		this.processInstanceId = processInstanceId;
		this.flag = flag;
		this.comment = comment;
		super.setStatus(Integer.valueOf(status));
		this.remarks = remarks;
	}
}
