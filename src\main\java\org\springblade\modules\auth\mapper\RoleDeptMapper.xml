<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.auth.mapper.RoleDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleDeptResultMap" type="org.springblade.modules.auth.entity.RoleDept">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="dept_id" property="deptId"/>
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
    </resultMap>


    <select id="selectRoleDeptPage" resultMap="roleDeptResultMap">
        select *
        from blade_role_dept
        where is_deleted = 0
    </select>
    <select id="roleSelectByDeptId" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
        rd.role_id
        FROM
        blade_role_dept AS rd
        LEFT JOIN blade_dept AS d ON d.id = rd.dept_id
        WHERE
        rd.is_deleted = 0
        AND (
        d.id IN (
        WITH recursive temp AS (
        SELECT
        t.*
        FROM
        blade_dept t
        WHERE
        id IN
        <foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        UNION
        SELECT
        t.*
        FROM
        blade_dept t
        INNER JOIN temp ON t.id = temp.parent_id
        ) SELECT
        id
        FROM
        temp
        WHERE
        is_deleted = 0
        AND parent_id = 1
        )
        OR d.id IN
        <foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>
</mapper>
