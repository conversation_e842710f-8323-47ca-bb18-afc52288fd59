/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractMain;

import java.util.List;

/**
 * 合同主表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractMainVO", description = "合同主表视图实体类")
public class ContractMainVO extends ContractMain {
    private static final long serialVersionUID = 1L;
    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 创建人称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    /**
     * 审批状态(0:待审批,1:审批中,2:已驳回,3:已撤回,4:已通过)
     */
    private String approvalStatusName;


    /**
     * 实验合同子表
     */
    private ContractExperimentVO experimentModel;
    /**
     * 实验合同子表
     */
    private ContractPrestoreVO prestoreModel;
    /**
     * 试剂合同子表
     */
    private ContractReagentVO reagentModel;
    /**
     * 试剂-实验材料合同子表
     */
    private List<ContractReagentQuotationMaterialVO> reagentQuotationMaterialDModel;
    /**
     * 实验合同子表
     */
    private ContractBidDocumentVO contractBidDocumentModel;
    /**
     * 试剂-动物饲养合同子表
     */
    private ContractAnimalBreedingVO animalBreedingModel;
    /**
     * 试剂-动物饲养费用合同子表
     */
    private List<ContractAnimalBreedingExpensesVO> animalBreedingExpensesModel;





    /**
     * 详情按钮权限
     */
    @ApiModelProperty(value = "详情按钮权限 true/false" +
            ", {updateBtn-编辑}" +
            ": {deleteBtn-删除}" +
            ", {downloadBtn-下载合同}"
    )
    private Kv buttonAuthByCustomerContact = Kv.create()
            // 编辑
            .set("updateBtn", false)
            // 删除
            .set("deleteBtn", false)
            // 下载合同
            .set("downloadBtn", false);


}
