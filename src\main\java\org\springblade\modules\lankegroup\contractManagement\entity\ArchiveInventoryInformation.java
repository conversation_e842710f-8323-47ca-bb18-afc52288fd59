/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同归档 --清单信息实体类
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Data
@TableName("blade_archive_Inventory_Information")
@EqualsAndHashCode(callSuper = true)
public class ArchiveInventoryInformation extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 合同归档id
	*/
		private Long detailsId;
	/**
	* 等保测评二级服务系统个数
	*/
		private String secondaryServiceNumber;
	/**
	* 等保测评二级服务单价
	*/
		private String secondaryServicePrice;
	/**
	* 等保测评二级服务次数
	*/
		private String secondaryServiceOrder;
	/**
	* 等保测评三级服务系统个数
	*/
		private String tertiaryServiceNumber;
	/**
	* 等保测评三级服务单价
	*/
		private String tertiaryServicePrice;
	/**
	* 等保测评三级服务次数
	*/
		private String tertiaryServiceOrder;

	/**
	 * 等级
	 */
		private String grade;
}
