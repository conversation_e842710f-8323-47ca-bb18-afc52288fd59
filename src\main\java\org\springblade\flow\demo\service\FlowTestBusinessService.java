package org.springblade.flow.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.flow.demo.dto.FlowTestBusinessDTO;
import org.springblade.flow.demo.entity.FlowTestBusinessEntity;

public interface FlowTestBusinessService extends IService<FlowTestBusinessEntity> {

    R saveModle(FlowTestBusinessDTO flowTestBusinessEntity);

    R updateModle(FlowTestBusinessDTO flowTestBusinessEntity);

    R listModel(IPage<FlowTestBusinessEntity> page, FlowTestBusinessEntity flowTestBusinessEntity);

    R detailModel(FlowTestBusinessEntity flowTestBusinessEntity);

}
