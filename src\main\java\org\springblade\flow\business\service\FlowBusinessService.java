/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiParam;
import org.springblade.core.tool.api.R;
import org.springblade.flow.business.dto.WorkFlowDTO;
import org.springblade.flow.core.entity.BladeFlow;

/**
 * 流程业务类
 *
 * <AUTHOR>
 */
public interface FlowBusinessService {

	/**
	 * 流程待签列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectClaimPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程待办列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectTodoPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程已发列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectSendPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程办结列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectDonePage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 完成任务
	 *
	 * @param
	 * @return boolean
	 */
	boolean completeTask(BladeFlow flow);
	R approveTask(WorkFlowDTO flow);

	/**
	 * 驳回任务
	 * @param flow
	 * @return
	 */
	R rejectTask(WorkFlowDTO flow);

	/**
	 * 撤销任务
	 * @param flow
	 * @return
	 */
	R revokeTask(WorkFlowDTO flow);

	/**
	 * 根据表单名称获取流程模型
	 * @param formKey 表单名称
	 * @return 流程key
	 */
	R selectFlowModelKey(String formKey);

	/**
	 * 新销客 流程转办
	 * @param taskId
	 * @param userId
	 * @return
	 */
	R transferTask( String taskId, String userId) ;

	/**
	 * 晶莱 流程转办
	 * @param taskId
	 * @param userId
	 * @return
	 */
	R transferTaskV2(String taskId, String userId, String reason);
	/**
	 * 晶莱 加签任务
	 *
	 * @param taskId 任务id
	 * @param userIds 转交用户
	 * @param signType 加签方式 1.前加签；2.后加签
	 * @param approvalType 审批方式 1.会签；2.或签
	 */
	R signTask(String taskId, String userIds, Integer signType, Integer approvalType, String reason);

	/**
	 * 删除任务
	 */
	R deleteTask(String processInstanceId, Long formDataId);
}
