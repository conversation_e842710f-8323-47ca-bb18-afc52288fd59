/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.lankegroup.contractManagement.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.lankegroup.contractManagement.entity.ContractReagent;
import org.springblade.modules.lankegroup.contractManagement.mapper.ContractReagentMapper;
import org.springblade.modules.lankegroup.contractManagement.service.IContractReagentService;
import org.springblade.modules.lankegroup.contractManagement.vo.ContractReagentVO;
import org.springframework.stereotype.Service;

/**
 * 试剂合同子表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Service
public class ContractReagentServiceImpl extends BaseServiceImpl<ContractReagentMapper, ContractReagent> implements IContractReagentService {

    @Override
    public IPage<ContractReagentVO> selectContractReagentPage(IPage<ContractReagentVO> page, ContractReagentVO contractReagent) {
        return page.setRecords(baseMapper.selectContractReagentPage(page, contractReagent));
    }

}
